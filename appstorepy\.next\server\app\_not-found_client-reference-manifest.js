globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(home)/page.js":{"*":{"id":"(ssr)/./src/app/(home)/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js":{"*":{"id":"(ssr)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/bootstrap/dist/css/bootstrap.min.css":{"*":{"id":"(ssr)/./node_modules/bootstrap/dist/css/bootstrap.min.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/BootstrapClient.js":{"*":{"id":"(ssr)/./src/app/components/BootstrapClient.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/Layout.jsx":{"*":{"id":"(ssr)/./src/app/components/Layout.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/components/navbar/navbar.jsx":{"*":{"id":"(ssr)/./src/app/components/navbar/navbar.jsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\src\\app\\(home)\\page.js":{"id":"(app-pages-browser)/./src/app/(home)/page.js","name":"*","chunks":["app/(home)/page","static/chunks/app/(home)/page.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\@mui\\material-nextjs\\v13-appRouter\\appRouterV13.js":{"id":"(app-pages-browser)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js","name":"*","chunks":["app/(home)/layout","static/chunks/app/(home)/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\node_modules\\bootstrap\\dist\\css\\bootstrap.min.css":{"id":"(app-pages-browser)/./node_modules/bootstrap/dist/css/bootstrap.min.css","name":"*","chunks":["app/(home)/layout","static/chunks/app/(home)/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\src\\app\\components\\BootstrapClient.js":{"id":"(app-pages-browser)/./src/app/components/BootstrapClient.js","name":"*","chunks":["app/(home)/layout","static/chunks/app/(home)/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\src\\app\\components\\Layout.jsx":{"id":"(app-pages-browser)/./src/app/components/Layout.jsx","name":"*","chunks":["app/(home)/layout","static/chunks/app/(home)/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\src\\app\\components\\navbar\\navbar.jsx":{"id":"(app-pages-browser)/./src/app/components/navbar/navbar.jsx","name":"*","chunks":["app/(home)/layout","static/chunks/app/(home)/layout.js"],"async":false},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/(home)/layout","static/chunks/app/(home)/layout.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\src\\app\\(home)\\page":[],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\src\\app\\(home)\\layout":["static/css/app/(home)/layout.css"],"C:\\Users\\<USER>\\OneDrive\\Desktop\\Anchoring\\appstorepy\\src\\app\\not-found":[]}}