"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cacheable-lookup";
exports.ids = ["vendor-chunks/cacheable-lookup"];
exports.modules = {

/***/ "(rsc)/./node_modules/cacheable-lookup/source/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/cacheable-lookup/source/index.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst {\n\tV4MAPPED,\n\tADDRCONFIG,\n\tALL,\n\tpromises: {\n\t\tResolver: AsyncResolver\n\t},\n\tlookup: dnsLookup\n} = __webpack_require__(/*! dns */ \"dns\");\nconst {promisify} = __webpack_require__(/*! util */ \"util\");\nconst os = __webpack_require__(/*! os */ \"os\");\n\nconst kCacheableLookupCreateConnection = Symbol('cacheableLookupCreateConnection');\nconst kCacheableLookupInstance = Symbol('cacheableLookupInstance');\nconst kExpires = Symbol('expires');\n\nconst supportsALL = typeof ALL === 'number';\n\nconst verifyAgent = agent => {\n\tif (!(agent && typeof agent.createConnection === 'function')) {\n\t\tthrow new Error('Expected an Agent instance as the first argument');\n\t}\n};\n\nconst map4to6 = entries => {\n\tfor (const entry of entries) {\n\t\tif (entry.family === 6) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tentry.address = `::ffff:${entry.address}`;\n\t\tentry.family = 6;\n\t}\n};\n\nconst getIfaceInfo = () => {\n\tlet has4 = false;\n\tlet has6 = false;\n\n\tfor (const device of Object.values(os.networkInterfaces())) {\n\t\tfor (const iface of device) {\n\t\t\tif (iface.internal) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (iface.family === 'IPv6') {\n\t\t\t\thas6 = true;\n\t\t\t} else {\n\t\t\t\thas4 = true;\n\t\t\t}\n\n\t\t\tif (has4 && has6) {\n\t\t\t\treturn {has4, has6};\n\t\t\t}\n\t\t}\n\t}\n\n\treturn {has4, has6};\n};\n\nconst isIterable = map => {\n\treturn Symbol.iterator in map;\n};\n\nconst ttl = {ttl: true};\nconst all = {all: true};\n\nclass CacheableLookup {\n\tconstructor({\n\t\tcache = new Map(),\n\t\tmaxTtl = Infinity,\n\t\tfallbackDuration = 3600,\n\t\terrorTtl = 0.15,\n\t\tresolver = new AsyncResolver(),\n\t\tlookup = dnsLookup\n\t} = {}) {\n\t\tthis.maxTtl = maxTtl;\n\t\tthis.errorTtl = errorTtl;\n\n\t\tthis._cache = cache;\n\t\tthis._resolver = resolver;\n\t\tthis._dnsLookup = promisify(lookup);\n\n\t\tif (this._resolver instanceof AsyncResolver) {\n\t\t\tthis._resolve4 = this._resolver.resolve4.bind(this._resolver);\n\t\t\tthis._resolve6 = this._resolver.resolve6.bind(this._resolver);\n\t\t} else {\n\t\t\tthis._resolve4 = promisify(this._resolver.resolve4.bind(this._resolver));\n\t\t\tthis._resolve6 = promisify(this._resolver.resolve6.bind(this._resolver));\n\t\t}\n\n\t\tthis._iface = getIfaceInfo();\n\n\t\tthis._pending = {};\n\t\tthis._nextRemovalTime = false;\n\t\tthis._hostnamesToFallback = new Set();\n\n\t\tif (fallbackDuration < 1) {\n\t\t\tthis._fallback = false;\n\t\t} else {\n\t\t\tthis._fallback = true;\n\n\t\t\tconst interval = setInterval(() => {\n\t\t\t\tthis._hostnamesToFallback.clear();\n\t\t\t}, fallbackDuration * 1000);\n\n\t\t\t/* istanbul ignore next: There is no `interval.unref()` when running inside an Electron renderer */\n\t\t\tif (interval.unref) {\n\t\t\t\tinterval.unref();\n\t\t\t}\n\t\t}\n\n\t\tthis.lookup = this.lookup.bind(this);\n\t\tthis.lookupAsync = this.lookupAsync.bind(this);\n\t}\n\n\tset servers(servers) {\n\t\tthis.clear();\n\n\t\tthis._resolver.setServers(servers);\n\t}\n\n\tget servers() {\n\t\treturn this._resolver.getServers();\n\t}\n\n\tlookup(hostname, options, callback) {\n\t\tif (typeof options === 'function') {\n\t\t\tcallback = options;\n\t\t\toptions = {};\n\t\t} else if (typeof options === 'number') {\n\t\t\toptions = {\n\t\t\t\tfamily: options\n\t\t\t};\n\t\t}\n\n\t\tif (!callback) {\n\t\t\tthrow new Error('Callback must be a function.');\n\t\t}\n\n\t\t// eslint-disable-next-line promise/prefer-await-to-then\n\t\tthis.lookupAsync(hostname, options).then(result => {\n\t\t\tif (options.all) {\n\t\t\t\tcallback(null, result);\n\t\t\t} else {\n\t\t\t\tcallback(null, result.address, result.family, result.expires, result.ttl);\n\t\t\t}\n\t\t}, callback);\n\t}\n\n\tasync lookupAsync(hostname, options = {}) {\n\t\tif (typeof options === 'number') {\n\t\t\toptions = {\n\t\t\t\tfamily: options\n\t\t\t};\n\t\t}\n\n\t\tlet cached = await this.query(hostname);\n\n\t\tif (options.family === 6) {\n\t\t\tconst filtered = cached.filter(entry => entry.family === 6);\n\n\t\t\tif (options.hints & V4MAPPED) {\n\t\t\t\tif ((supportsALL && options.hints & ALL) || filtered.length === 0) {\n\t\t\t\t\tmap4to6(cached);\n\t\t\t\t} else {\n\t\t\t\t\tcached = filtered;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcached = filtered;\n\t\t\t}\n\t\t} else if (options.family === 4) {\n\t\t\tcached = cached.filter(entry => entry.family === 4);\n\t\t}\n\n\t\tif (options.hints & ADDRCONFIG) {\n\t\t\tconst {_iface} = this;\n\t\t\tcached = cached.filter(entry => entry.family === 6 ? _iface.has6 : _iface.has4);\n\t\t}\n\n\t\tif (cached.length === 0) {\n\t\t\tconst error = new Error(`cacheableLookup ENOTFOUND ${hostname}`);\n\t\t\terror.code = 'ENOTFOUND';\n\t\t\terror.hostname = hostname;\n\n\t\t\tthrow error;\n\t\t}\n\n\t\tif (options.all) {\n\t\t\treturn cached;\n\t\t}\n\n\t\treturn cached[0];\n\t}\n\n\tasync query(hostname) {\n\t\tlet cached = await this._cache.get(hostname);\n\n\t\tif (!cached) {\n\t\t\tconst pending = this._pending[hostname];\n\n\t\t\tif (pending) {\n\t\t\t\tcached = await pending;\n\t\t\t} else {\n\t\t\t\tconst newPromise = this.queryAndCache(hostname);\n\t\t\t\tthis._pending[hostname] = newPromise;\n\n\t\t\t\ttry {\n\t\t\t\t\tcached = await newPromise;\n\t\t\t\t} finally {\n\t\t\t\t\tdelete this._pending[hostname];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tcached = cached.map(entry => {\n\t\t\treturn {...entry};\n\t\t});\n\n\t\treturn cached;\n\t}\n\n\tasync _resolve(hostname) {\n\t\tconst wrap = async promise => {\n\t\t\ttry {\n\t\t\t\treturn await promise;\n\t\t\t} catch (error) {\n\t\t\t\tif (error.code === 'ENODATA' || error.code === 'ENOTFOUND') {\n\t\t\t\t\treturn [];\n\t\t\t\t}\n\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t};\n\n\t\t// ANY is unsafe as it doesn't trigger new queries in the underlying server.\n\t\tconst [A, AAAA] = await Promise.all([\n\t\t\tthis._resolve4(hostname, ttl),\n\t\t\tthis._resolve6(hostname, ttl)\n\t\t].map(promise => wrap(promise)));\n\n\t\tlet aTtl = 0;\n\t\tlet aaaaTtl = 0;\n\t\tlet cacheTtl = 0;\n\n\t\tconst now = Date.now();\n\n\t\tfor (const entry of A) {\n\t\t\tentry.family = 4;\n\t\t\tentry.expires = now + (entry.ttl * 1000);\n\n\t\t\taTtl = Math.max(aTtl, entry.ttl);\n\t\t}\n\n\t\tfor (const entry of AAAA) {\n\t\t\tentry.family = 6;\n\t\t\tentry.expires = now + (entry.ttl * 1000);\n\n\t\t\taaaaTtl = Math.max(aaaaTtl, entry.ttl);\n\t\t}\n\n\t\tif (A.length > 0) {\n\t\t\tif (AAAA.length > 0) {\n\t\t\t\tcacheTtl = Math.min(aTtl, aaaaTtl);\n\t\t\t} else {\n\t\t\t\tcacheTtl = aTtl;\n\t\t\t}\n\t\t} else {\n\t\t\tcacheTtl = aaaaTtl;\n\t\t}\n\n\t\treturn {\n\t\t\tentries: [\n\t\t\t\t...A,\n\t\t\t\t...AAAA\n\t\t\t],\n\t\t\tcacheTtl\n\t\t};\n\t}\n\n\tasync _lookup(hostname) {\n\t\ttry {\n\t\t\tconst entries = await this._dnsLookup(hostname, {\n\t\t\t\tall: true\n\t\t\t});\n\n\t\t\treturn {\n\t\t\t\tentries,\n\t\t\t\tcacheTtl: 0\n\t\t\t};\n\t\t} catch (_) {\n\t\t\treturn {\n\t\t\t\tentries: [],\n\t\t\t\tcacheTtl: 0\n\t\t\t};\n\t\t}\n\t}\n\n\tasync _set(hostname, data, cacheTtl) {\n\t\tif (this.maxTtl > 0 && cacheTtl > 0) {\n\t\t\tcacheTtl = Math.min(cacheTtl, this.maxTtl) * 1000;\n\t\t\tdata[kExpires] = Date.now() + cacheTtl;\n\n\t\t\ttry {\n\t\t\t\tawait this._cache.set(hostname, data, cacheTtl);\n\t\t\t} catch (error) {\n\t\t\t\tthis.lookupAsync = async () => {\n\t\t\t\t\tconst cacheError = new Error('Cache Error. Please recreate the CacheableLookup instance.');\n\t\t\t\t\tcacheError.cause = error;\n\n\t\t\t\t\tthrow cacheError;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (isIterable(this._cache)) {\n\t\t\t\tthis._tick(cacheTtl);\n\t\t\t}\n\t\t}\n\t}\n\n\tasync queryAndCache(hostname) {\n\t\tif (this._hostnamesToFallback.has(hostname)) {\n\t\t\treturn this._dnsLookup(hostname, all);\n\t\t}\n\n\t\tlet query = await this._resolve(hostname);\n\n\t\tif (query.entries.length === 0 && this._fallback) {\n\t\t\tquery = await this._lookup(hostname);\n\n\t\t\tif (query.entries.length !== 0) {\n\t\t\t\t// Use `dns.lookup(...)` for that particular hostname\n\t\t\t\tthis._hostnamesToFallback.add(hostname);\n\t\t\t}\n\t\t}\n\n\t\tconst cacheTtl = query.entries.length === 0 ? this.errorTtl : query.cacheTtl;\n\t\tawait this._set(hostname, query.entries, cacheTtl);\n\n\t\treturn query.entries;\n\t}\n\n\t_tick(ms) {\n\t\tconst nextRemovalTime = this._nextRemovalTime;\n\n\t\tif (!nextRemovalTime || ms < nextRemovalTime) {\n\t\t\tclearTimeout(this._removalTimeout);\n\n\t\t\tthis._nextRemovalTime = ms;\n\n\t\t\tthis._removalTimeout = setTimeout(() => {\n\t\t\t\tthis._nextRemovalTime = false;\n\n\t\t\t\tlet nextExpiry = Infinity;\n\n\t\t\t\tconst now = Date.now();\n\n\t\t\t\tfor (const [hostname, entries] of this._cache) {\n\t\t\t\t\tconst expires = entries[kExpires];\n\n\t\t\t\t\tif (now >= expires) {\n\t\t\t\t\t\tthis._cache.delete(hostname);\n\t\t\t\t\t} else if (expires < nextExpiry) {\n\t\t\t\t\t\tnextExpiry = expires;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (nextExpiry !== Infinity) {\n\t\t\t\t\tthis._tick(nextExpiry - now);\n\t\t\t\t}\n\t\t\t}, ms);\n\n\t\t\t/* istanbul ignore next: There is no `timeout.unref()` when running inside an Electron renderer */\n\t\t\tif (this._removalTimeout.unref) {\n\t\t\t\tthis._removalTimeout.unref();\n\t\t\t}\n\t\t}\n\t}\n\n\tinstall(agent) {\n\t\tverifyAgent(agent);\n\n\t\tif (kCacheableLookupCreateConnection in agent) {\n\t\t\tthrow new Error('CacheableLookup has been already installed');\n\t\t}\n\n\t\tagent[kCacheableLookupCreateConnection] = agent.createConnection;\n\t\tagent[kCacheableLookupInstance] = this;\n\n\t\tagent.createConnection = (options, callback) => {\n\t\t\tif (!('lookup' in options)) {\n\t\t\t\toptions.lookup = this.lookup;\n\t\t\t}\n\n\t\t\treturn agent[kCacheableLookupCreateConnection](options, callback);\n\t\t};\n\t}\n\n\tuninstall(agent) {\n\t\tverifyAgent(agent);\n\n\t\tif (agent[kCacheableLookupCreateConnection]) {\n\t\t\tif (agent[kCacheableLookupInstance] !== this) {\n\t\t\t\tthrow new Error('The agent is not owned by this CacheableLookup instance');\n\t\t\t}\n\n\t\t\tagent.createConnection = agent[kCacheableLookupCreateConnection];\n\n\t\t\tdelete agent[kCacheableLookupCreateConnection];\n\t\t\tdelete agent[kCacheableLookupInstance];\n\t\t}\n\t}\n\n\tupdateInterfaceInfo() {\n\t\tconst {_iface} = this;\n\n\t\tthis._iface = getIfaceInfo();\n\n\t\tif ((_iface.has4 && !this._iface.has4) || (_iface.has6 && !this._iface.has6)) {\n\t\t\tthis._cache.clear();\n\t\t}\n\t}\n\n\tclear(hostname) {\n\t\tif (hostname) {\n\t\t\tthis._cache.delete(hostname);\n\t\t\treturn;\n\t\t}\n\n\t\tthis._cache.clear();\n\t}\n}\n\nmodule.exports = CacheableLookup;\nmodule.exports[\"default\"] = CacheableLookup;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cacheable-lookup/source/index.js\n");

/***/ })

};
;