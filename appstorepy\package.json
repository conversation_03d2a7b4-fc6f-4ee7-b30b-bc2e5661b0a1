{"name": "storespy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.6", "@mui/material": "^5.15.6", "@mui/material-nextjs": "^5.15.6", "@mui/styles": "^5.15.6", "@reduxjs/toolkit": "^2.1.0", "app-store-scraper": "^0.18.0", "axios": "^1.6.6", "bcryptjs": "^2.4.3", "bootstrap": "^5.0.1", "chart.js": "^4.4.1", "google-play-scraper": "^10.0.0", "material-ui-popup-state": "^5.1.0", "mongoose": "^8.1.1", "next": "14.1.0", "next-auth": "^4.24.6", "nodemailer": "^6.9.13", "react": "^18", "react-chartjs-2": "^5.2.0", "react-dom": "^18", "react-redux": "^9.1.0", "react-slick": "^0.30.1", "react-spinners": "^0.13.8", "react-svg-worldmap": "^2.0.0-alpha.16", "react-toastify": "^10.0.5", "server-only-context": "^0.1.0", "sweetalert2": "^11.10.6"}, "devDependencies": {"file-loader": "^6.2.0"}, "overrides": {"undici": "5.28.4"}}