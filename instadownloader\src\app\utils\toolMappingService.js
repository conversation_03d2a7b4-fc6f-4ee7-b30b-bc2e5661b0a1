/**
 * Intelligent AI-Tools-to-App Mapping Service
 * Provides smart matching between AI tools and APK apps
 */

/**
 * Enhanced AI-tool-to-app mappings with keyword matching
 */
export const TOOL_APP_MAPPINGS = {
  // Direct tool name mappings to specific apps
  direct: {
    'chatgpt': ['com.openai.chatgpt', 'ai.chat.gpt.bot'],
    'claude': ['com.anthropic.claude'],
    'gemini': ['com.google.android.apps.bard', 'com.google.android.apps.assistant'],
    'copilot': ['com.microsoft.copilot', 'com.microsoft.office.outlook'],
    'midjourney': ['com.midjourney.android', 'com.adobe.photoshop.express'],
    'canva': ['com.canva.editor'],
    'photoshop': ['com.adobe.photoshop.express', 'com.adobe.psmobile'],
    'lightroom': ['com.adobe.lrmobile'],
    'figma': ['com.figma.mirror'],
    'notion': ['notion.id'],
    'slack': ['com.Slack'],
    'discord': ['com.discord'],
    'zoom': ['us.zoom.videomeetings'],
    'teams': ['com.microsoft.teams'],
    'telegram': ['org.telegram.messenger'],
    'whatsapp': ['com.whatsapp'],
    'instagram': ['com.instagram.android'],
    'tiktok': ['com.zhiliaoapp.musically'],
    'youtube': ['com.google.android.youtube'],
    'spotify': ['com.spotify.music'],
    'netflix': ['com.netflix.mediaclient'],
    'gmail': ['com.google.android.gm'],
    'outlook': ['com.microsoft.office.outlook'],
    'chrome': ['com.android.chrome'],
    'firefox': ['org.mozilla.firefox'],
    'office': ['com.microsoft.office.word', 'com.microsoft.office.excel'],
    'word': ['com.microsoft.office.word'],
    'excel': ['com.microsoft.office.excel'],
    'powerpoint': ['com.microsoft.office.powerpoint'],
    'adobe': ['com.adobe.photoshop.express', 'com.adobe.lrmobile'],
    'grammarly': ['com.grammarly.android.keyboard'],
    'translate': ['com.google.android.apps.translate'],
    'maps': ['com.google.android.apps.maps'],
    'calendar': ['com.google.android.calendar'],
    'notes': ['com.google.android.keep', 'com.samsung.android.app.notes'],
    'weather': ['com.google.android.apps.weather', 'com.weather.Weather'],
    'calculator': ['com.google.android.calculator', 'com.samsung.android.calculator'],
    'camera': ['com.google.android.GoogleCamera', 'com.samsung.android.camera2'],
    'gallery': ['com.google.android.apps.photos', 'com.samsung.android.gallery3d'],
    'music': ['com.google.android.music', 'com.samsung.android.music'],
    'video': ['com.google.android.videos', 'com.samsung.android.video']
  },

  // Category-based mappings
  category: {
    'social-media': ['communication', 'social'],
    'email-generator': ['communication', 'productivity'],
    'writing-generator': ['productivity', 'education'],
    'blog-generator': ['news-magazines', 'productivity'],
    'business': ['business', 'productivity'],
    'productivity': ['productivity', 'tools'],
    'automation': ['tools', 'productivity'],
    'art': ['art-design', 'photography'],
    'graphic-design': ['art-design', 'photography'],
    'image-generation': ['art-design', 'photography'],
    'video-generation': ['video-players', 'entertainment'],
    'music': ['music-audio', 'entertainment'],
    'education': ['education', 'books-reference'],
    'research': ['education', 'books-reference'],
    'health': ['health-fitness', 'medical'],
    'lifestyle': ['lifestyle', 'health-fitness'],
    'finace': ['finance', 'business'],
    'startup': ['business', 'productivity'],
    'marketing': ['business', 'social'],
    'developer-tools': ['tools', 'libraries-demo'],
    'communication': ['communication', 'social'],
    'translation': ['education', 'tools'],
    'avatar': ['personalization', 'art-design'],
    'anime': ['entertainment', 'art-design'],
    'gaming': ['games', 'entertainment'],
    'transcription': ['productivity', 'tools'],
    'audio': ['music-audio', 'tools'],
    'podcast': ['music-audio', 'news-magazines'],
    'presentation': ['productivity', 'business'],
    'document-generation': ['productivity', 'business'],
    'note-taking': ['productivity', 'education'],
    'organization': ['productivity', 'lifestyle'],
    'scheduling': ['productivity', 'lifestyle'],
    'monitoring': ['health-fitness', 'tools'],
    'analysis': ['business', 'tools'],
    'security': ['tools', 'productivity'],
    'privacy': ['tools', 'productivity'],
    'optimization': ['tools', 'productivity'],
    'customization': ['personalization', 'tools'],
    'navigation': ['maps-navigation', 'travel-local'],
    'travel': ['travel-local', 'lifestyle'],
    'e-commerce': ['shopping', 'business'],
    'recommendation': ['shopping', 'lifestyle'],
    'information': ['news-magazines', 'education'],
    'prediction': ['tools', 'lifestyle']
  },

  // Keyword-based mappings for tool descriptions/titles
  keywords: {
    'chat': ['communication', 'social'],
    'message': ['communication', 'social'],
    'photo': ['art-design', 'photography'],
    'image': ['art-design', 'photography'],
    'video': ['video-players', 'entertainment'],
    'music': ['music-audio', 'entertainment'],
    'edit': ['art-design', 'video-players'],
    'design': ['art-design', 'productivity'],
    'write': ['productivity', 'education'],
    'text': ['productivity', 'education'],
    'document': ['productivity', 'business'],
    'note': ['productivity', 'education'],
    'task': ['productivity', 'business'],
    'calendar': ['productivity', 'lifestyle'],
    'schedule': ['productivity', 'lifestyle'],
    'email': ['communication', 'productivity'],
    'translate': ['education', 'tools'],
    'language': ['education', 'tools'],
    'learn': ['education', 'books-reference'],
    'fitness': ['health-fitness', 'lifestyle'],
    'health': ['health-fitness', 'medical'],
    'game': ['games', 'entertainment'],
    'social': ['social', 'communication'],
    'business': ['business', 'productivity'],
    'finance': ['finance', 'business'],
    'money': ['finance', 'business'],
    'shop': ['shopping', 'lifestyle'],
    'buy': ['shopping', 'lifestyle'],
    'travel': ['travel-local', 'lifestyle'],
    'news': ['news-magazines', 'education'],
    'weather': ['weather', 'lifestyle'],
    'security': ['tools', 'productivity'],
    'clean': ['tools', 'productivity'],
    'optimize': ['tools', 'productivity'],
    'backup': ['tools', 'productivity'],
    'scan': ['tools', 'productivity'],
    'voice': ['music-audio', 'communication'],
    'speech': ['music-audio', 'communication'],
    'ai': ['tools', 'productivity'],
    'smart': ['tools', 'productivity'],
    'auto': ['tools', 'productivity'],
    'generator': ['productivity', 'tools'],
    'creator': ['art-design', 'productivity'],
    'maker': ['art-design', 'productivity'],
    'builder': ['productivity', 'tools']
  }
};

/**
 * Get relevant APK categories for an AI tool
 */
export const getRelevantAPKCategories = (toolDetails) => {
  const categories = new Set();
  
  if (!toolDetails) return ['productivity'];
  
  const toolId = (toolDetails.appId || '').toLowerCase();
  const title = (toolDetails.title || '').toLowerCase();
  const category = (toolDetails.subCategory || '').toLowerCase();
  const description = (toolDetails.overview || toolDetails.summary || '').toLowerCase();
  
  // Check direct tool mappings
  for (const [toolName, apkCategories] of Object.entries(TOOL_APP_MAPPINGS.direct)) {
    if (toolId.includes(toolName) || title.includes(toolName)) {
      // For direct mappings, we get specific app IDs, so we map to general categories
      categories.add('productivity');
      categories.add('tools');
    }
  }
  
  // Check category mappings
  const normalizedCategory = category.replace(/[^a-z0-9]/g, '-');
  if (TOOL_APP_MAPPINGS.category[normalizedCategory]) {
    TOOL_APP_MAPPINGS.category[normalizedCategory].forEach(cat => categories.add(cat));
  }
  
  // Check keyword mappings
  const searchText = `${title} ${description}`.toLowerCase();
  for (const [keyword, apkCategories] of Object.entries(TOOL_APP_MAPPINGS.keywords)) {
    if (searchText.includes(keyword)) {
      apkCategories.forEach(cat => categories.add(cat));
    }
  }
  
  // Return array with most relevant first, fallback to productivity
  const result = Array.from(categories);
  return result.length > 0 ? result : ['productivity'];
};

/**
 * Get specific app recommendations for an AI tool
 */
export const getRecommendedApps = (toolDetails) => {
  const apps = new Set();
  
  if (!toolDetails) return [];
  
  const toolId = (toolDetails.appId || '').toLowerCase();
  const title = (toolDetails.title || '').toLowerCase();
  
  // Check direct tool mappings for specific apps
  for (const [toolName, appIds] of Object.entries(TOOL_APP_MAPPINGS.direct)) {
    if (toolId.includes(toolName) || title.includes(toolName)) {
      appIds.forEach(appId => apps.add(appId));
    }
  }
  
  return Array.from(apps);
};

/**
 * Get the best APK category for an AI tool
 */
export const getBestAPKCategory = (toolDetails) => {
  const categories = getRelevantAPKCategories(toolDetails);
  return categories[0] || 'productivity';
};

/**
 * Generate smart APK URL with best matching category or specific app
 */
export const generateSmartAPKURL = (toolDetails, baseURL) => {
  const recommendedApps = getRecommendedApps(toolDetails);
  const toolId = toolDetails?.appId || '';
  const toolTitle = toolDetails?.title || '';
  
  // If we have a specific app recommendation, link to it
  if (recommendedApps.length > 0) {
    const primaryApp = recommendedApps[0];
    return `${baseURL}/apps/appdetails/${primaryApp}?ref=ai-tools&tool=${encodeURIComponent(toolId)}&title=${encodeURIComponent(toolTitle)}`;
  }
  
  // Otherwise, link to the best category
  const bestCategory = getBestAPKCategory(toolDetails);
  return `${baseURL}/apps/${bestCategory}?ref=ai-tools&tool=${encodeURIComponent(toolId)}&title=${encodeURIComponent(toolTitle)}`;
};
