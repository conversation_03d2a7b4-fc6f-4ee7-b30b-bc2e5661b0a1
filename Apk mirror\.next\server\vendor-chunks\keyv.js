"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/keyv";
exports.ids = ["vendor-chunks/keyv"];
exports.modules = {

/***/ "(rsc)/./node_modules/keyv/src/index.js":
/*!****************************************!*\
  !*** ./node_modules/keyv/src/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst EventEmitter = __webpack_require__(/*! events */ \"events\");\nconst JSONB = __webpack_require__(/*! json-buffer */ \"(rsc)/./node_modules/json-buffer/index.js\");\n\nconst loadStore = options => {\n\tconst adapters = {\n\t\tredis: '@keyv/redis',\n\t\trediss: '@keyv/redis',\n\t\tmongodb: '@keyv/mongo',\n\t\tmongo: '@keyv/mongo',\n\t\tsqlite: '@keyv/sqlite',\n\t\tpostgresql: '@keyv/postgres',\n\t\tpostgres: '@keyv/postgres',\n\t\tmysql: '@keyv/mysql',\n\t\tetcd: '@keyv/etcd',\n\t\toffline: '@keyv/offline',\n\t\ttiered: '@keyv/tiered',\n\t};\n\tif (options.adapter || options.uri) {\n\t\tconst adapter = options.adapter || /^[^:+]*/.exec(options.uri)[0];\n\t\treturn new (__webpack_require__(\"(rsc)/./node_modules/keyv/src sync recursive\")(adapters[adapter]))(options);\n\t}\n\n\treturn new Map();\n};\n\nconst iterableAdapters = [\n\t'sqlite',\n\t'postgres',\n\t'mysql',\n\t'mongo',\n\t'redis',\n\t'tiered',\n];\n\nclass Keyv extends EventEmitter {\n\tconstructor(uri, {emitErrors = true, ...options} = {}) {\n\t\tsuper();\n\t\tthis.opts = {\n\t\t\tnamespace: 'keyv',\n\t\t\tserialize: JSONB.stringify,\n\t\t\tdeserialize: JSONB.parse,\n\t\t\t...((typeof uri === 'string') ? {uri} : uri),\n\t\t\t...options,\n\t\t};\n\n\t\tif (!this.opts.store) {\n\t\t\tconst adapterOptions = {...this.opts};\n\t\t\tthis.opts.store = loadStore(adapterOptions);\n\t\t}\n\n\t\tif (this.opts.compression) {\n\t\t\tconst compression = this.opts.compression;\n\t\t\tthis.opts.serialize = compression.serialize.bind(compression);\n\t\t\tthis.opts.deserialize = compression.deserialize.bind(compression);\n\t\t}\n\n\t\tif (typeof this.opts.store.on === 'function' && emitErrors) {\n\t\t\tthis.opts.store.on('error', error => this.emit('error', error));\n\t\t}\n\n\t\tthis.opts.store.namespace = this.opts.namespace;\n\n\t\tconst generateIterator = iterator => async function * () {\n\t\t\tfor await (const [key, raw] of typeof iterator === 'function'\n\t\t\t\t? iterator(this.opts.store.namespace)\n\t\t\t\t: iterator) {\n\t\t\t\tconst data = await this.opts.deserialize(raw);\n\t\t\t\tif (this.opts.store.namespace && !key.includes(this.opts.store.namespace)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (typeof data.expires === 'number' && Date.now() > data.expires) {\n\t\t\t\t\tthis.delete(key);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tyield [this._getKeyUnprefix(key), data.value];\n\t\t\t}\n\t\t};\n\n\t\t// Attach iterators\n\t\tif (typeof this.opts.store[Symbol.iterator] === 'function' && this.opts.store instanceof Map) {\n\t\t\tthis.iterator = generateIterator(this.opts.store);\n\t\t} else if (typeof this.opts.store.iterator === 'function' && this.opts.store.opts\n\t\t\t&& this._checkIterableAdaptar()) {\n\t\t\tthis.iterator = generateIterator(this.opts.store.iterator.bind(this.opts.store));\n\t\t}\n\t}\n\n\t_checkIterableAdaptar() {\n\t\treturn iterableAdapters.includes(this.opts.store.opts.dialect)\n\t\t\t|| iterableAdapters.findIndex(element => this.opts.store.opts.url.includes(element)) >= 0;\n\t}\n\n\t_getKeyPrefix(key) {\n\t\treturn `${this.opts.namespace}:${key}`;\n\t}\n\n\t_getKeyPrefixArray(keys) {\n\t\treturn keys.map(key => `${this.opts.namespace}:${key}`);\n\t}\n\n\t_getKeyUnprefix(key) {\n\t\treturn key\n\t\t\t.split(':')\n\t\t\t.splice(1)\n\t\t\t.join(':');\n\t}\n\n\tget(key, options) {\n\t\tconst {store} = this.opts;\n\t\tconst isArray = Array.isArray(key);\n\t\tconst keyPrefixed = isArray ? this._getKeyPrefixArray(key) : this._getKeyPrefix(key);\n\t\tif (isArray && store.getMany === undefined) {\n\t\t\tconst promises = [];\n\t\t\tfor (const key of keyPrefixed) {\n\t\t\t\tpromises.push(Promise.resolve()\n\t\t\t\t\t.then(() => store.get(key))\n\t\t\t\t\t.then(data => (typeof data === 'string') ? this.opts.deserialize(data) : (this.opts.compression ? this.opts.deserialize(data) : data))\n\t\t\t\t\t.then(data => {\n\t\t\t\t\t\tif (data === undefined || data === null) {\n\t\t\t\t\t\t\treturn undefined;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (typeof data.expires === 'number' && Date.now() > data.expires) {\n\t\t\t\t\t\t\treturn this.delete(key).then(() => undefined);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn (options && options.raw) ? data : data.value;\n\t\t\t\t\t}),\n\t\t\t\t);\n\t\t\t}\n\n\t\t\treturn Promise.allSettled(promises)\n\t\t\t\t.then(values => {\n\t\t\t\t\tconst data = [];\n\t\t\t\t\tfor (const value of values) {\n\t\t\t\t\t\tdata.push(value.value);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn data;\n\t\t\t\t});\n\t\t}\n\n\t\treturn Promise.resolve()\n\t\t\t.then(() => isArray ? store.getMany(keyPrefixed) : store.get(keyPrefixed))\n\t\t\t.then(data => (typeof data === 'string') ? this.opts.deserialize(data) : (this.opts.compression ? this.opts.deserialize(data) : data))\n\t\t\t.then(data => {\n\t\t\t\tif (data === undefined || data === null) {\n\t\t\t\t\treturn undefined;\n\t\t\t\t}\n\n\t\t\t\tif (isArray) {\n\t\t\t\t\treturn data.map((row, index) => {\n\t\t\t\t\t\tif ((typeof row === 'string')) {\n\t\t\t\t\t\t\trow = this.opts.deserialize(row);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (row === undefined || row === null) {\n\t\t\t\t\t\t\treturn undefined;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (typeof row.expires === 'number' && Date.now() > row.expires) {\n\t\t\t\t\t\t\tthis.delete(key[index]).then(() => undefined);\n\t\t\t\t\t\t\treturn undefined;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn (options && options.raw) ? row : row.value;\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tif (typeof data.expires === 'number' && Date.now() > data.expires) {\n\t\t\t\t\treturn this.delete(key).then(() => undefined);\n\t\t\t\t}\n\n\t\t\t\treturn (options && options.raw) ? data : data.value;\n\t\t\t});\n\t}\n\n\tset(key, value, ttl) {\n\t\tconst keyPrefixed = this._getKeyPrefix(key);\n\t\tif (typeof ttl === 'undefined') {\n\t\t\tttl = this.opts.ttl;\n\t\t}\n\n\t\tif (ttl === 0) {\n\t\t\tttl = undefined;\n\t\t}\n\n\t\tconst {store} = this.opts;\n\n\t\treturn Promise.resolve()\n\t\t\t.then(() => {\n\t\t\t\tconst expires = (typeof ttl === 'number') ? (Date.now() + ttl) : null;\n\t\t\t\tif (typeof value === 'symbol') {\n\t\t\t\t\tthis.emit('error', 'symbol cannot be serialized');\n\t\t\t\t}\n\n\t\t\t\tvalue = {value, expires};\n\t\t\t\treturn this.opts.serialize(value);\n\t\t\t})\n\t\t\t.then(value => store.set(keyPrefixed, value, ttl))\n\t\t\t.then(() => true);\n\t}\n\n\tdelete(key) {\n\t\tconst {store} = this.opts;\n\t\tif (Array.isArray(key)) {\n\t\t\tconst keyPrefixed = this._getKeyPrefixArray(key);\n\t\t\tif (store.deleteMany === undefined) {\n\t\t\t\tconst promises = [];\n\t\t\t\tfor (const key of keyPrefixed) {\n\t\t\t\t\tpromises.push(store.delete(key));\n\t\t\t\t}\n\n\t\t\t\treturn Promise.allSettled(promises)\n\t\t\t\t\t.then(values => values.every(x => x.value === true));\n\t\t\t}\n\n\t\t\treturn Promise.resolve()\n\t\t\t\t.then(() => store.deleteMany(keyPrefixed));\n\t\t}\n\n\t\tconst keyPrefixed = this._getKeyPrefix(key);\n\t\treturn Promise.resolve()\n\t\t\t.then(() => store.delete(keyPrefixed));\n\t}\n\n\tclear() {\n\t\tconst {store} = this.opts;\n\t\treturn Promise.resolve()\n\t\t\t.then(() => store.clear());\n\t}\n\n\thas(key) {\n\t\tconst keyPrefixed = this._getKeyPrefix(key);\n\t\tconst {store} = this.opts;\n\t\treturn Promise.resolve()\n\t\t\t.then(async () => {\n\t\t\t\tif (typeof store.has === 'function') {\n\t\t\t\t\treturn store.has(keyPrefixed);\n\t\t\t\t}\n\n\t\t\t\tconst value = await store.get(keyPrefixed);\n\t\t\t\treturn value !== undefined;\n\t\t\t});\n\t}\n\n\tdisconnect() {\n\t\tconst {store} = this.opts;\n\t\tif (typeof store.disconnect === 'function') {\n\t\t\treturn store.disconnect();\n\t\t}\n\t}\n}\n\nmodule.exports = Keyv;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/keyv/src/index.js\n");

/***/ })

};
;