"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/google-play-scraper";
exports.ids = ["vendor-chunks/google-play-scraper"];
exports.modules = {

/***/ "(rsc)/./node_modules/google-play-scraper/index.js":
/*!***************************************************!*\
  !*** ./node_modules/google-play-scraper/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/partial.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/map.js\");\n/* harmony import */ var _lib_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n/* harmony import */ var memoizee__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! memoizee */ \"(rsc)/./node_modules/memoizee/index.js\");\n/* harmony import */ var _lib_app_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/app.js */ \"(rsc)/./node_modules/google-play-scraper/lib/app.js\");\n/* harmony import */ var _lib_list_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/list.js */ \"(rsc)/./node_modules/google-play-scraper/lib/list.js\");\n/* harmony import */ var _lib_search_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/search.js */ \"(rsc)/./node_modules/google-play-scraper/lib/search.js\");\n/* harmony import */ var _lib_suggest_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/suggest.js */ \"(rsc)/./node_modules/google-play-scraper/lib/suggest.js\");\n/* harmony import */ var _lib_developer_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/developer.js */ \"(rsc)/./node_modules/google-play-scraper/lib/developer.js\");\n/* harmony import */ var _lib_reviews_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/reviews.js */ \"(rsc)/./node_modules/google-play-scraper/lib/reviews.js\");\n/* harmony import */ var _lib_similar_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/similar.js */ \"(rsc)/./node_modules/google-play-scraper/lib/similar.js\");\n/* harmony import */ var _lib_permissions_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/permissions.js */ \"(rsc)/./node_modules/google-play-scraper/lib/permissions.js\");\n/* harmony import */ var _lib_datasafety_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./lib/datasafety.js */ \"(rsc)/./node_modules/google-play-scraper/lib/datasafety.js\");\n/* harmony import */ var _lib_categories_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./lib/categories.js */ \"(rsc)/./node_modules/google-play-scraper/lib/categories.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst methods = {\n  app: _lib_app_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n  list: _lib_list_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n  search: ramda__WEBPACK_IMPORTED_MODULE_12__[\"default\"](_lib_search_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"], [_lib_app_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]]),\n  suggest: _lib_suggest_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n  developer: _lib_developer_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n  reviews: _lib_reviews_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n  similar: _lib_similar_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n  permissions: _lib_permissions_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n  datasafety: _lib_datasafety_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n  categories: _lib_categories_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n};\n\nfunction memoized (opts) {\n  const cacheOpts = Object.assign({\n    primitive: true,\n    normalizer: JSON.stringify,\n    maxAge: 1000 * 60 * 5, // cache for 5 minutes\n    max: 1000 // save up to 1k results to avoid memory issues\n  }, opts);\n\n  // need to rebuild the methods so they all share the same memoized appMethod\n  const doMemoize = (fn) => memoizee__WEBPACK_IMPORTED_MODULE_1__(fn, cacheOpts);\n  const mAppMethod = memoizee__WEBPACK_IMPORTED_MODULE_1__(_lib_app_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"], cacheOpts);\n\n  const otherMethods = {\n    list: _lib_list_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    search: ramda__WEBPACK_IMPORTED_MODULE_12__[\"default\"](_lib_search_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"], [mAppMethod]),\n    suggest: _lib_suggest_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    developer: _lib_developer_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    reviews: _lib_reviews_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    similar: _lib_similar_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    permissions: _lib_permissions_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n    datasafety: _lib_datasafety_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    categories: _lib_categories_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n  };\n\n  return Object.assign({ app: mAppMethod },\n    _lib_constants_js__WEBPACK_IMPORTED_MODULE_0__.constants,\n    ramda__WEBPACK_IMPORTED_MODULE_13__[\"default\"](doMemoize, otherMethods));\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Object.assign({ memoized }, _lib_constants_js__WEBPACK_IMPORTED_MODULE_0__.constants, methods));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/app.js":
/*!*****************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/app.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/assoc.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/path.js\");\n/* harmony import */ var querystring__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! querystring */ \"querystring\");\n/* harmony import */ var _utils_request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/request.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/request.js\");\n/* harmony import */ var _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/scriptData.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/scriptData.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n/* harmony import */ var _utils_mappingHelpers_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/mappingHelpers.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/mappingHelpers.js\");\n\n\n\n\n\n\n\nconst PLAYSTORE_URL = `${_constants_js__WEBPACK_IMPORTED_MODULE_3__.BASE_URL}/store/apps/details`;\n\nfunction app (opts) {\n  return new Promise(function (resolve, reject) {\n    if (!opts || !opts.appId) {\n      throw Error('appId missing');\n    }\n\n    opts.lang = opts.lang || 'en';\n    opts.country = opts.country || 'us';\n\n    const qs = querystring__WEBPACK_IMPORTED_MODULE_0__.stringify({\n      id: opts.appId,\n      hl: opts.lang,\n      gl: opts.country\n    });\n    const reqUrl = `${PLAYSTORE_URL}?${qs}`;\n\n    const options = Object.assign({\n      url: reqUrl,\n      followRedirect: true\n    }, opts.requestOptions);\n\n    (0,_utils_request_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(options, opts.throttle)\n      .then(_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].parse)\n    // comment next line to get raw data\n      .then(_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].extractor(MAPPINGS))\n      .then(ramda__WEBPACK_IMPORTED_MODULE_5__[\"default\"]('appId', opts.appId))\n      .then(ramda__WEBPACK_IMPORTED_MODULE_5__[\"default\"]('url', reqUrl))\n      .then(resolve)\n      .catch(reject);\n  });\n}\n\nconst MAPPINGS = {\n  title: ['ds:5', 1, 2, 0, 0],\n  description: {\n    path: ['ds:5', 1, 2],\n    fun: (val) => _utils_mappingHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].descriptionText(_utils_mappingHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].descriptionHtmlLocalized(val))\n  },\n  descriptionHTML: {\n    path: ['ds:5', 1, 2],\n    fun: _utils_mappingHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].descriptionHtmlLocalized\n  },\n  summary: ['ds:5', 1, 2, 73, 0, 1],\n  installs: ['ds:5', 1, 2, 13, 0],\n  minInstalls: ['ds:5', 1, 2, 13, 1],\n  maxInstalls: ['ds:5', 1, 2, 13, 2],\n  score: ['ds:5', 1, 2, 51, 0, 1],\n  scoreText: ['ds:5', 1, 2, 51, 0, 0],\n  ratings: ['ds:5', 1, 2, 51, 2, 1],\n  reviews: ['ds:5', 1, 2, 51, 3, 1],\n  histogram: {\n    path: ['ds:5', 1, 2, 51, 1],\n    fun: _utils_mappingHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].buildHistogram\n  },\n  price: {\n    path: ['ds:5', 1, 2, 57, 0, 0, 0, 0, 1, 0, 0],\n    fun: (val) => val / 1000000 || 0\n  },\n  // If there is a discount, originalPrice if filled.\n  originalPrice: {\n    path: ['ds:5', 1, 2, 57, 0, 0, 0, 0, 1, 1, 0],\n    fun: (price) => price ? price / 1000000 : undefined\n  },\n  discountEndDate: ['ds:5', 1, 2, 57, 0, 0, 0, 0, 14, 1],\n  free: {\n    path: ['ds:5', 1, 2, 57, 0, 0, 0, 0, 1, 0, 0],\n    // considered free only if price is exactly zero\n    fun: (val) => val === 0\n  },\n  currency: ['ds:5', 1, 2, 57, 0, 0, 0, 0, 1, 0, 1],\n  priceText: {\n    path: ['ds:5', 1, 2, 57, 0, 0, 0, 0, 1, 0, 2],\n    fun: _utils_mappingHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].priceText\n  },\n  available: {\n    path: ['ds:5', 1, 2, 18, 0],\n    fun: Boolean\n  },\n  offersIAP: {\n    path: ['ds:5', 1, 2, 19, 0],\n    fun: Boolean\n  },\n  IAPRange: ['ds:5', 1, 2, 19, 0],\n  androidVersion: {\n    path: ['ds:5', 1, 2, 140, 1, 1, 0, 0, 1],\n    fun: _utils_mappingHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].normalizeAndroidVersion\n  },\n  androidVersionText: {\n    path: ['ds:5', 1, 2, 140, 1, 1, 0, 0, 1],\n    fun: (version) => version || 'Varies with device'\n  },\n  androidMaxVersion: {\n    path: ['ds:5', 1, 2, 140, 1, 1, 0, 1, 1],\n    fun: _utils_mappingHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].normalizeAndroidVersion\n  },\n  developer: ['ds:5', 1, 2, 68, 0],\n  developerId: {\n    path: ['ds:5', 1, 2, 68, 1, 4, 2],\n    fun: (devUrl) => devUrl.split('id=')[1]\n  },\n  developerEmail: ['ds:5', 1, 2, 69, 1, 0],\n  developerWebsite: ['ds:5', 1, 2, 69, 0, 5, 2],\n  developerAddress: ['ds:5', 1, 2, 69, 2, 0],\n  developerLegalName: ['ds:5', 1, 2, 69, 4, 0],\n  developerLegalEmail: ['ds:5', 1, 2, 69, 4, 1, 0],\n  developerLegalAddress: {\n    path: ['ds:5', 1, 2, 69],\n    fun: (searchArray) => {\n      return ramda__WEBPACK_IMPORTED_MODULE_6__[\"default\"]([4, 2, 0], searchArray)?.replace(/\\n/g, ', ');\n    }\n  },\n  developerLegalPhoneNumber: ['ds:5', 1, 2, 69, 4, 3],\n  privacyPolicy: ['ds:5', 1, 2, 99, 0, 5, 2],\n  developerInternalID: {\n    path: ['ds:5', 1, 2, 68, 1, 4, 2],\n    fun: (devUrl) => devUrl.split('id=')[1]\n  },\n  genre: ['ds:5', 1, 2, 79, 0, 0, 0],\n  genreId: ['ds:5', 1, 2, 79, 0, 0, 2],\n  categories: {\n    path: ['ds:5', 1, 2],\n    fun: (searchArray) => {\n      const categories = _utils_mappingHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].extractCategories(ramda__WEBPACK_IMPORTED_MODULE_6__[\"default\"]([118], searchArray));\n      if (categories.length === 0) {\n        // add genre and genreId like GP does when there're no categories available\n        categories.push({\n          name: ramda__WEBPACK_IMPORTED_MODULE_6__[\"default\"]([79, 0, 0, 0], searchArray),\n          id: ramda__WEBPACK_IMPORTED_MODULE_6__[\"default\"]([79, 0, 0, 2], searchArray)\n        });\n      }\n      return categories;\n    }\n  },\n  icon: ['ds:5', 1, 2, 95, 0, 3, 2],\n  headerImage: ['ds:5', 1, 2, 96, 0, 3, 2],\n  screenshots: {\n    path: ['ds:5', 1, 2, 78, 0],\n    fun: (screenshots) => {\n      if (!screenshots?.length) return [];\n      return screenshots.map(ramda__WEBPACK_IMPORTED_MODULE_6__[\"default\"]([3, 2]));\n    }\n  },\n  video: ['ds:5', 1, 2, 100, 0, 0, 3, 2],\n  videoImage: ['ds:5', 1, 2, 100, 1, 0, 3, 2],\n  previewVideo: ['ds:5', 1, 2, 100, 1, 2, 0, 2],\n  contentRating: ['ds:5', 1, 2, 9, 0],\n  contentRatingDescription: ['ds:5', 1, 2, 9, 2, 1],\n  adSupported: {\n    path: ['ds:5', 1, 2, 48],\n    fun: Boolean\n  },\n  released: ['ds:5', 1, 2, 10, 0],\n  updated: {\n    path: ['ds:5', 1, 2, 145, 0, 1, 0],\n    fun: (ts) => ts * 1000\n  },\n  version: {\n    path: ['ds:5', 1, 2, 140, 0, 0, 0],\n    fun: (val) => val || 'VARY'\n  },\n  recentChanges: ['ds:5', 1, 2, 144, 1, 1],\n  comments: {\n    path: [],\n    isArray: true,\n    fun: _utils_mappingHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].extractComments\n  },\n  preregister: {\n    path: ['ds:5', 1, 2, 18, 0],\n    fun: (val) => val === 1\n  },\n  earlyAccessEnabled: {\n    path: ['ds:5', 1, 2, 18, 2],\n    fun: (val) => typeof val === 'string'\n  },\n  isAvailableInPlayPass: {\n    path: ['ds:5', 1, 2, 62],\n    fun: (field) => !!field\n  }\n\n};\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/app.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/categories.js":
/*!************************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/categories.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_request_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/request.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/request.js\");\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n\n\n\n\nconst PLAYSTORE_URL = `${_constants_js__WEBPACK_IMPORTED_MODULE_2__.BASE_URL}/store/apps`;\nconst CATEGORY_URL_PREFIX = '/store/apps/category/';\n\nfunction categories (opts) {\n  opts = Object.assign({}, opts);\n\n  return new Promise(function (resolve, reject) {\n    const options = Object.assign(\n      {\n        url: PLAYSTORE_URL\n      },\n      opts.requestOptions\n    );\n\n    (0,_utils_request_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(options, opts.throttle)\n      .then(cheerio__WEBPACK_IMPORTED_MODULE_1__.load)\n      .then(extractCategories)\n      .then(resolve)\n      .catch(reject);\n  });\n}\n\nfunction extractCategories ($) {\n  const categoryIds = $('ul li a')\n    .toArray()\n    .map((el) => $(el).attr('href'))\n    .filter((url) => url.startsWith(CATEGORY_URL_PREFIX) && !url.includes('?age='))\n    .map((url) => url.substr(CATEGORY_URL_PREFIX.length));\n  categoryIds.push('APPLICATION');\n\n  return categoryIds;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (categories);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ29vZ2xlLXBsYXktc2NyYXBlci9saWIvY2F0ZWdvcmllcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXlDO0FBQ047QUFDTzs7QUFFMUMseUJBQXlCLG1EQUFRLENBQUM7QUFDbEM7O0FBRUE7QUFDQSx5QkFBeUI7O0FBRXpCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7O0FBRUEsSUFBSSw2REFBTztBQUNYLFlBQVkseUNBQVk7QUFDeEI7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsaUVBQWUsVUFBVSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL25vZGVfbW9kdWxlcy9nb29nbGUtcGxheS1zY3JhcGVyL2xpYi9jYXRlZ29yaWVzLmpzPzliYzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHJlcXVlc3QgZnJvbSAnLi91dGlscy9yZXF1ZXN0LmpzJztcbmltcG9ydCAqIGFzIGNoZWVyaW8gZnJvbSAnY2hlZXJpbyc7XG5pbXBvcnQgeyBCQVNFX1VSTCB9IGZyb20gJy4vY29uc3RhbnRzLmpzJztcblxuY29uc3QgUExBWVNUT1JFX1VSTCA9IGAke0JBU0VfVVJMfS9zdG9yZS9hcHBzYDtcbmNvbnN0IENBVEVHT1JZX1VSTF9QUkVGSVggPSAnL3N0b3JlL2FwcHMvY2F0ZWdvcnkvJztcblxuZnVuY3Rpb24gY2F0ZWdvcmllcyAob3B0cykge1xuICBvcHRzID0gT2JqZWN0LmFzc2lnbih7fSwgb3B0cyk7XG5cbiAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHtcbiAgICBjb25zdCBvcHRpb25zID0gT2JqZWN0LmFzc2lnbihcbiAgICAgIHtcbiAgICAgICAgdXJsOiBQTEFZU1RPUkVfVVJMXG4gICAgICB9LFxuICAgICAgb3B0cy5yZXF1ZXN0T3B0aW9uc1xuICAgICk7XG5cbiAgICByZXF1ZXN0KG9wdGlvbnMsIG9wdHMudGhyb3R0bGUpXG4gICAgICAudGhlbihjaGVlcmlvLmxvYWQpXG4gICAgICAudGhlbihleHRyYWN0Q2F0ZWdvcmllcylcbiAgICAgIC50aGVuKHJlc29sdmUpXG4gICAgICAuY2F0Y2gocmVqZWN0KTtcbiAgfSk7XG59XG5cbmZ1bmN0aW9uIGV4dHJhY3RDYXRlZ29yaWVzICgkKSB7XG4gIGNvbnN0IGNhdGVnb3J5SWRzID0gJCgndWwgbGkgYScpXG4gICAgLnRvQXJyYXkoKVxuICAgIC5tYXAoKGVsKSA9PiAkKGVsKS5hdHRyKCdocmVmJykpXG4gICAgLmZpbHRlcigodXJsKSA9PiB1cmwuc3RhcnRzV2l0aChDQVRFR09SWV9VUkxfUFJFRklYKSAmJiAhdXJsLmluY2x1ZGVzKCc/YWdlPScpKVxuICAgIC5tYXAoKHVybCkgPT4gdXJsLnN1YnN0cihDQVRFR09SWV9VUkxfUFJFRklYLmxlbmd0aCkpO1xuICBjYXRlZ29yeUlkcy5wdXNoKCdBUFBMSUNBVElPTicpO1xuXG4gIHJldHVybiBjYXRlZ29yeUlkcztcbn1cblxuZXhwb3J0IGRlZmF1bHQgY2F0ZWdvcmllcztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/categories.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/constants.js":
/*!***********************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/constants.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BASE_URL: () => (/* binding */ BASE_URL),\n/* harmony export */   constants: () => (/* binding */ constants)\n/* harmony export */ });\nconst BASE_URL = 'https://play.google.com';\n\nconst constants = {\n  clusters: {\n    new: 'new',\n    top: 'top'\n  },\n  category: {\n    APPLICATION: 'APPLICATION',\n    ANDROID_WEAR: 'ANDROID_WEAR',\n    ART_AND_DESIGN: 'ART_AND_DESIGN',\n    AUTO_AND_VEHICLES: 'AUTO_AND_VEHICLES',\n    BEAUTY: 'BEAUTY',\n    BOOKS_AND_REFERENCE: 'BOOKS_AND_REFERENCE',\n    BUSINESS: 'BUSINESS',\n    COMICS: 'COMICS',\n    COMMUNICATION: 'COMMUNICATION',\n    DATING: 'DATING',\n    EDUCATION: 'EDUCATION',\n    ENTERTAINMENT: 'ENTERTAINMENT',\n    EVENTS: 'EVENTS',\n    FINANCE: 'FINANCE',\n    FOOD_AND_DRINK: 'FOOD_AND_DRINK',\n    HEALTH_AND_FITNESS: 'HEALTH_AND_FITNESS',\n    HOUSE_AND_HOME: 'HOUSE_AND_HOME',\n    LIBRARIES_AND_DEMO: 'LIBRARIES_AND_DEMO',\n    LIFESTYLE: 'LIFESTYLE',\n    MAPS_AND_NAVIGATION: 'MAPS_AND_NAVIGATION',\n    MEDICAL: 'MEDICAL',\n    MUSIC_AND_AUDIO: 'MUSIC_AND_AUDIO',\n    NEWS_AND_MAGAZINES: 'NEWS_AND_MAGAZINES',\n    PARENTING: 'PARENTING',\n    PERSONALIZATION: 'PERSONALIZATION',\n    PHOTOGRAPHY: 'PHOTOGRAPHY',\n    PRODUCTIVITY: 'PRODUCTIVITY',\n    SHOPPING: 'SHOPPING',\n    SOCIAL: 'SOCIAL',\n    SPORTS: 'SPORTS',\n    TOOLS: 'TOOLS',\n    TRAVEL_AND_LOCAL: 'TRAVEL_AND_LOCAL',\n    VIDEO_PLAYERS: 'VIDEO_PLAYERS',\n    WATCH_FACE: 'WATCH_FACE',\n    WEATHER: 'WEATHER',\n    GAME: 'GAME',\n    GAME_ACTION: 'GAME_ACTION',\n    GAME_ADVENTURE: 'GAME_ADVENTURE',\n    GAME_ARCADE: 'GAME_ARCADE',\n    GAME_BOARD: 'GAME_BOARD',\n    GAME_CARD: 'GAME_CARD',\n    GAME_CASINO: 'GAME_CASINO',\n    GAME_CASUAL: 'GAME_CASUAL',\n    GAME_EDUCATIONAL: 'GAME_EDUCATIONAL',\n    GAME_MUSIC: 'GAME_MUSIC',\n    GAME_PUZZLE: 'GAME_PUZZLE',\n    GAME_RACING: 'GAME_RACING',\n    GAME_ROLE_PLAYING: 'GAME_ROLE_PLAYING',\n    GAME_SIMULATION: 'GAME_SIMULATION',\n    GAME_SPORTS: 'GAME_SPORTS',\n    GAME_STRATEGY: 'GAME_STRATEGY',\n    GAME_TRIVIA: 'GAME_TRIVIA',\n    GAME_WORD: 'GAME_WORD',\n    FAMILY: 'FAMILY'\n  },\n  collection: {\n    TOP_FREE: 'TOP_FREE',\n    TOP_PAID: 'TOP_PAID',\n    GROSSING: 'GROSSING'\n  },\n  sort: {\n    NEWEST: 2,\n    RATING: 3,\n    HELPFULNESS: 1\n  },\n  age: {\n    FIVE_UNDER: 'AGE_RANGE1',\n    SIX_EIGHT: 'AGE_RANGE2',\n    NINE_UP: 'AGE_RANGE3'\n  },\n  permission: {\n    COMMON: 0,\n    OTHER: 1\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/datasafety.js":
/*!************************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/datasafety.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/path.js\");\n/* harmony import */ var _utils_request_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/request.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/request.js\");\n/* harmony import */ var _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/scriptData.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/scriptData.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n\n\n\n\n\nfunction dataSafety (opts) {\n  return new Promise(function (resolve, reject) {\n    if (!opts && !opts.appId) {\n      throw Error('appId missing');\n    }\n\n    opts.lang = opts.lang || 'en';\n\n    processDataSafety(opts)\n      .then(resolve)\n      .catch(reject);\n  });\n}\n\nfunction processDataSafety (opts) {\n  const PLAYSTORE_URL = `${_constants_js__WEBPACK_IMPORTED_MODULE_2__.BASE_URL}/store/apps/datasafety`;\n\n  const searchParams = new URLSearchParams({\n    id: opts.appId,\n    hl: opts.lang\n  });\n  const reqUrl = `${PLAYSTORE_URL}?${searchParams}`;\n\n  const options = Object.assign({\n    url: reqUrl,\n    followRedirect: true\n  }, opts.requestOptions);\n\n  return (0,_utils_request_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(options, opts.throttle)\n    .then(_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].parse)\n    .then(_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].extractor(MAPPINGS));\n}\n\nconst MAPPINGS = {\n  sharedData: {\n    path: ['ds:3', 1, 2, 137, 4, 0, 0],\n    fun: mapDataEntries\n  },\n  collectedData: {\n    path: ['ds:3', 1, 2, 137, 4, 1, 0],\n    fun: mapDataEntries\n  },\n  securityPractices: {\n    path: ['ds:3', 1, 2, 137, 9, 2],\n    fun: mapSecurityPractices\n  },\n  privacyPolicyUrl: ['ds:3', 1, 2, 99, 0, 5, 2]\n};\n\nfunction mapSecurityPractices (practices) {\n  if (!practices) {\n    return [];\n  }\n\n  return practices.map((practice) => ({\n    practice: ramda__WEBPACK_IMPORTED_MODULE_3__[\"default\"]([1], practice),\n    description: ramda__WEBPACK_IMPORTED_MODULE_3__[\"default\"]([2, 1], practice)\n  }));\n}\n\nfunction mapDataEntries (dataEntries) {\n  if (!dataEntries) {\n    return [];\n  }\n\n  return dataEntries.flatMap(data => {\n    const type = ramda__WEBPACK_IMPORTED_MODULE_3__[\"default\"]([0, 1], data);\n    const details = ramda__WEBPACK_IMPORTED_MODULE_3__[\"default\"]([4], data);\n\n    return details.map(detail => ({\n      data: ramda__WEBPACK_IMPORTED_MODULE_3__[\"default\"]([0], detail),\n      optional: ramda__WEBPACK_IMPORTED_MODULE_3__[\"default\"]([1], detail),\n      purpose: ramda__WEBPACK_IMPORTED_MODULE_3__[\"default\"]([2], detail),\n      type\n    }));\n  });\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dataSafety);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/datasafety.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/developer.js":
/*!***********************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/developer.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var querystring__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! querystring */ \"querystring\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! url */ \"url\");\n/* harmony import */ var _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/scriptData.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/scriptData.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n/* harmony import */ var _utils_request_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/request.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/request.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/is.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/map.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/path.js\");\n/* harmony import */ var _utils_processPages_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/processPages.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/processPages.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\");\n\n\n\n\n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_6__('google-play-scraper:developer');\n\nfunction buildUrl (opts) {\n  const { lang, devId, country } = opts;\n  const url = `${_constants_js__WEBPACK_IMPORTED_MODULE_3__.BASE_URL}/store/apps`;\n  const path = isNaN(opts.devId)\n    ? '/developer'\n    : '/dev';\n\n  const queryString = {\n    id: devId,\n    hl: lang,\n    gl: country\n  };\n\n  const fullURL = `${url}${path}?${querystring__WEBPACK_IMPORTED_MODULE_0__.stringify(queryString)}`;\n\n  debug('Initial request: %s', fullURL);\n\n  return fullURL;\n}\n\nfunction developer (opts) {\n  return new Promise(function (resolve, reject) {\n    if (!opts.devId) {\n      throw Error('devId missing');\n    }\n\n    opts = Object.assign({\n      num: 60,\n      lang: 'en',\n      country: 'us'\n    }, opts);\n\n    const options = Object.assign({\n      url: buildUrl(opts),\n      method: 'GET',\n      followRedirect: true\n    }, opts.requestOptions);\n\n    (0,_utils_request_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(options, opts.throttle)\n      .then(_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].parse)\n      .then(parsedObject => parseDeveloperApps(parsedObject, opts))\n      .then(resolve)\n      .catch(reject);\n  });\n}\n\nasync function parseDeveloperApps (html, opts) {\n  if (ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](String, html)) {\n    html = _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].parse(html);\n  }\n\n  const initialMappings = isNaN(opts.devId)\n    ? {\n        apps: ['ds:3', 0, 1, 0, 22, 0],\n        token: ['ds:3', 0, 1, 0, 22, 1, 3, 1]\n      }\n    : {\n        apps: ['ds:3', 0, 1, 0, 21, 0],\n        token: ['ds:3', 0, 1, 0, 21, 1, 3, 1]\n      };\n\n  const appsMappings = isNaN(opts.devId)\n    ? {\n        title: [0, 3],\n        appId: [0, 0, 0],\n        url: {\n          path: [0, 10, 4, 2],\n          fun: (path) => new url__WEBPACK_IMPORTED_MODULE_1__.URL(path, _constants_js__WEBPACK_IMPORTED_MODULE_3__.BASE_URL).toString()\n        },\n        icon: [0, 1, 3, 2],\n        developer: [0, 14],\n        currency: [0, 8, 1, 0, 1],\n        price: {\n          path: [0, 8, 1, 0, 0],\n          fun: (price) => price / 1000000\n        },\n        free: {\n          path: [0, 8, 1, 0, 0],\n          fun: (price) => price === 0\n        },\n        summary: [0, 13, 1],\n        scoreText: [0, 4, 0],\n        score: [0, 4, 1]\n      }\n    : {\n        title: [3],\n        appId: [0, 0],\n        url: {\n          path: [10, 4, 2],\n          fun: (path) => new url__WEBPACK_IMPORTED_MODULE_1__.URL(path, _constants_js__WEBPACK_IMPORTED_MODULE_3__.BASE_URL).toString()\n        },\n        icon: [1, 3, 2],\n        developer: [14],\n        currency: [8, 1, 0, 1],\n        price: {\n          path: [8, 1, 0, 0],\n          fun: (price) => price / 1000000\n        },\n        free: {\n          path: [8, 1, 0, 0],\n          fun: (price) => price === 0\n        },\n        summary: [13, 1],\n        scoreText: [4, 0],\n        score: [4, 1]\n      };\n\n  const processedApps = ramda__WEBPACK_IMPORTED_MODULE_8__[\"default\"](_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].extractor(appsMappings), ramda__WEBPACK_IMPORTED_MODULE_9__[\"default\"](initialMappings.apps, html));\n  const apps = opts.fullDetail\n    ? await (0,_utils_processPages_js__WEBPACK_IMPORTED_MODULE_5__.processFullDetailApps)(processedApps, opts)\n    : processedApps;\n\n  const token = ramda__WEBPACK_IMPORTED_MODULE_9__[\"default\"](initialMappings.token, html);\n\n  return (0,_utils_processPages_js__WEBPACK_IMPORTED_MODULE_5__.checkFinished)(opts, apps, token);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (developer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/developer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/list.js":
/*!******************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/list.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/includes.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/values.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/map.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/path.js\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! url */ \"url\");\n/* harmony import */ var _utils_request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/request.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/request.js\");\n/* harmony import */ var querystring__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! querystring */ \"querystring\");\n/* harmony import */ var _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/scriptData.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/scriptData.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n/* harmony import */ var _utils_processPages_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/processPages.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/processPages.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\");\n\n\n\n\n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_6__('google-play-scraper:list');\n\nfunction getBodyForRequests (payloadOpts) {\n  const { num, collection, category } = payloadOpts;\n  const body = `f.req=%5B%5B%5B%22vyAe2%22%2C%22%5B%5Bnull%2C%5B%5B8%2C%5B20%2C${num}%5D%5D%2Ctrue%2Cnull%2C%5B64%2C1%2C195%2C71%2C8%2C72%2C9%2C10%2C11%2C139%2C12%2C16%2C145%2C148%2C150%2C151%2C152%2C27%2C30%2C31%2C96%2C32%2C34%2C163%2C100%2C165%2C104%2C169%2C108%2C110%2C113%2C55%2C56%2C57%2C122%5D%2C%5Bnull%2Cnull%2C%5B%5B%5Btrue%5D%2Cnull%2C%5B%5Bnull%2C%5B%5D%5D%5D%2Cnull%2Cnull%2Cnull%2Cnull%2C%5Bnull%2C2%5D%2Cnull%2Cnull%2Cnull%2Cnull%2Cnull%2Cnull%2C%5B1%5D%2Cnull%2Cnull%2Cnull%2Cnull%2Cnull%2Cnull%2Cnull%2C%5B1%5D%5D%2C%5Bnull%2C%5B%5Bnull%2C%5B%5D%5D%5D%5D%2C%5Bnull%2C%5B%5Bnull%2C%5B%5D%5D%5D%2Cnull%2C%5Btrue%5D%5D%2C%5Bnull%2C%5B%5Bnull%2C%5B%5D%5D%5D%5D%2Cnull%2Cnull%2Cnull%2Cnull%2C%5B%5B%5Bnull%2C%5B%5D%5D%5D%5D%2C%5B%5B%5Bnull%2C%5B%5D%5D%5D%5D%5D%2C%5B%5B%5B%5B7%2C1%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C31%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C104%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C9%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C8%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C27%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C12%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C65%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C110%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C88%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C11%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C56%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C55%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C96%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C10%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C122%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C72%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C71%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C64%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C113%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C139%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C150%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C169%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C165%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C151%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C163%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C32%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C16%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C108%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B7%2C100%5D%2C%5B%5B1%2C73%2C96%2C103%2C97%2C58%2C50%2C92%2C52%2C112%2C69%2C19%2C31%2C101%2C123%2C74%2C49%2C80%2C38%2C20%2C10%2C14%2C79%2C43%2C42%2C139%5D%5D%5D%2C%5B%5B9%2C1%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C31%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C104%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C9%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C8%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C27%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C12%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C65%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C110%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C88%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C11%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C56%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C55%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C96%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C10%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C122%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C72%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C71%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C64%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C113%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C139%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C150%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C169%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C165%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C151%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C163%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C32%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C16%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C108%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B9%2C100%5D%2C%5B%5B1%2C7%2C9%2C24%2C12%2C31%2C5%2C15%2C27%2C8%2C13%2C10%5D%5D%5D%2C%5B%5B17%2C1%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C31%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C104%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C9%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C8%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C27%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C12%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C65%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C110%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C88%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C11%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C56%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C55%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C96%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C10%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C122%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C72%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C71%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C64%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C113%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C139%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C150%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C169%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C165%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C151%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C163%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C32%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C16%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C108%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B17%2C100%5D%2C%5B%5B1%2C7%2C9%2C25%2C13%2C31%2C5%2C41%2C27%2C8%2C14%2C10%5D%5D%5D%2C%5B%5B10%2C1%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C31%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C104%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C9%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C8%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C27%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C12%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C65%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C110%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C88%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C11%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C56%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C55%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C96%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C10%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C122%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C72%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C71%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C64%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C113%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C139%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C150%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C169%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C165%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C151%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C163%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C32%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C16%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C108%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B10%2C100%5D%2C%5B%5B1%2C7%2C6%2C9%5D%5D%5D%2C%5B%5B1%2C1%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C31%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C104%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C9%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C8%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C27%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C12%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C65%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C110%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C88%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C11%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C56%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C55%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C96%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C10%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C122%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C72%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C71%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C64%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C113%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C139%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C150%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C169%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C165%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C151%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C163%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C32%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C16%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C108%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B1%2C100%5D%2C%5B%5B1%2C5%2C14%2C38%2C19%2C29%2C34%2C4%2C12%2C11%2C6%2C30%2C43%2C40%2C42%2C16%2C10%2C7%5D%5D%5D%2C%5B%5B4%2C1%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C31%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C104%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C9%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C8%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C27%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C12%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C65%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C110%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C88%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C11%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C56%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C55%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C96%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C10%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C122%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C72%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C71%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C64%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C113%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C139%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C150%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C169%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C165%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C151%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C163%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C32%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C16%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C108%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B4%2C100%5D%2C%5B%5B1%2C3%2C5%2C4%2C7%2C6%2C11%2C19%2C21%2C17%2C15%2C12%2C16%2C20%5D%5D%5D%2C%5B%5B3%2C1%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C31%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C104%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C9%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C8%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C27%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C12%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C65%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C110%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C88%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C11%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C56%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C55%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C96%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C10%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C122%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C72%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C71%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C64%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C113%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C139%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C150%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C169%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C165%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C151%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C163%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C32%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C16%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C108%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B3%2C100%5D%2C%5B%5B1%2C5%2C14%2C4%2C10%2C17%5D%5D%5D%2C%5B%5B2%2C1%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C31%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C104%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C9%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C8%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C27%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C12%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C65%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C110%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C88%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C11%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C56%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C55%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C96%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C10%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C122%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C72%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C71%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C64%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C113%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C139%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C150%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C169%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C165%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C151%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C163%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C32%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C16%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C108%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%2C%5B%5B2%2C100%5D%2C%5B%5B1%2C5%2C7%2C4%2C13%2C16%2C12%2C18%5D%5D%5D%5D%5D%5D%2Cnull%2Cnull%2C%5B%5B%5B1%2C2%5D%2C%5B10%2C8%2C9%5D%2C%5B%5D%2C%5B%5D%5D%5D%5D%2C%5B2%2C%5C%22${collection}%5C%22%2C%5C%22${category}%5C%22%5D%5D%5D%22%2Cnull%2C%22generic%22%5D%5D%5D&at=AFSRYlx8XZfN8-O-IKASbNBDkB6T%3A1655531200971&`;\n\n  return body;\n}\n\nfunction list (opts) {\n  return new Promise(function (resolve, reject) {\n    validate(opts);\n\n    const fullListOpts = Object.assign({\n      lang: 'en',\n      country: 'us',\n      num: 500\n    }, opts);\n\n    const body = getBodyForRequests({\n      num: fullListOpts.num,\n      collection: CLUSTER_NAMES[fullListOpts.collection],\n      category: fullListOpts.category\n    });\n\n    const requestOptions = Object.assign({\n      url: buildInitialUrl(fullListOpts),\n      method: 'POST',\n      body,\n      followRedirect: true,\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'\n      }\n    }, opts.requestOptions);\n\n    (0,_utils_request_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(requestOptions, opts.throttle)\n      .then((html) => {\n        const input = JSON.parse(html.split('\\n')[3]);\n        return JSON.parse(input[0][2]);\n      })\n      .then(collectionObject => parseCollectionApps(collectionObject, opts))\n      .then(resolve)\n      .catch(reject);\n  });\n}\n\nfunction validate (opts) {\n  opts.category = opts.category || _constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.category.APPLICATION;\n  if (opts.category && !ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](opts.category, ramda__WEBPACK_IMPORTED_MODULE_8__[\"default\"](_constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.category))) {\n    throw Error('Invalid category ' + opts.category);\n  }\n\n  opts.collection = opts.collection || _constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.collection.TOP_FREE;\n  if (!ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](opts.collection, ramda__WEBPACK_IMPORTED_MODULE_8__[\"default\"](_constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.collection))) {\n    throw Error(`Invalid collection ${opts.collection}`);\n  }\n\n  if (opts.age && !ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](opts.age, ramda__WEBPACK_IMPORTED_MODULE_8__[\"default\"](_constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.age))) {\n    throw Error(`Invalid age range ${opts.age}`);\n  }\n}\n\nfunction buildInitialUrl (opts) {\n  const queryString = {\n    hl: opts.lang,\n    gl: opts.country\n  };\n\n  if (opts.age) {\n    queryString.age = opts.age;\n  }\n  const url = `${_constants_js__WEBPACK_IMPORTED_MODULE_4__.BASE_URL}/_/PlayStoreUi/data/batchexecute?rpcids=vyAe2&source-path=%2Fstore%2Fapps&f.sid=-4178618388443751758&bl=boq_playuiserver_20220612.08_p0&authuser=0&soc-app=121&soc-platform=1&soc-device=1&_reqid=82003&rt=c`;\n\n  const fullURL = `${url}&${querystring__WEBPACK_IMPORTED_MODULE_2__.stringify(queryString)}`;\n\n  debug('Initial Request URL: %s', fullURL);\n\n  return fullURL;\n}\n\nconst CLUSTER_NAMES = {\n  TOP_FREE: 'topselling_free',\n  TOP_PAID: 'topselling_paid',\n  GROSSING: 'topgrossing'\n};\n\nasync function parseCollectionApps (categoryObject, opts) {\n  const appsMappings = {\n    title: [0, 3],\n    appId: [0, 0, 0],\n    url: {\n      path: [0, 10, 4, 2],\n      fun: (path) => new url__WEBPACK_IMPORTED_MODULE_0__.URL(path, _constants_js__WEBPACK_IMPORTED_MODULE_4__.BASE_URL).toString()\n    },\n    icon: [0, 1, 3, 2],\n    developer: [0, 14],\n    currency: [0, 8, 1, 0, 1],\n    price: {\n      path: [0, 8, 1, 0, 0],\n      fun: (price) => price / 1000000\n    },\n    free: {\n      path: [0, 8, 1, 0, 0],\n      fun: (price) => price === 0\n    },\n    summary: [0, 13, 1],\n    scoreText: [0, 4, 0],\n    score: [0, 4, 1]\n  };\n  const appsPath = [0, 1, 0, 28, 0];\n  const processedApps = ramda__WEBPACK_IMPORTED_MODULE_9__[\"default\"](_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].extractor(appsMappings), ramda__WEBPACK_IMPORTED_MODULE_10__[\"default\"](appsPath, categoryObject));\n  const apps = opts.fullDetail\n    ? await (0,_utils_processPages_js__WEBPACK_IMPORTED_MODULE_5__.processFullDetailApps)(processedApps, opts)\n    : processedApps;\n  return apps;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (list);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/list.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/permissions.js":
/*!*************************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/permissions.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/is.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/chain.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/path.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/map.js\");\n/* harmony import */ var _utils_request_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/request.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/request.js\");\n/* harmony import */ var _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/scriptData.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/scriptData.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\");\n\n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__('google-play-scraper:permissions');\n\nfunction permissions (opts) {\n  return new Promise(function (resolve, reject) {\n    if (!opts && !opts.appId) {\n      throw Error('appId missing');\n    }\n\n    opts.lang = opts.lang || 'en';\n\n    processPermissions(opts)\n      .then(resolve)\n      .catch(reject);\n  });\n}\n\nfunction processPermissions (opts) {\n  const body = `f.req=%5B%5B%5B%22xdSrCf%22%2C%22%5B%5Bnull%2C%5B%5C%22${opts.appId}%5C%22%2C7%5D%2C%5B%5D%5D%5D%22%2Cnull%2C%221%22%5D%5D%5D`;\n  const url = `${_constants_js__WEBPACK_IMPORTED_MODULE_2__.BASE_URL}/_/PlayStoreUi/data/batchexecute?rpcids=qnKhOb&f.sid=-697906427155521722&bl=boq_playuiserver_20190903.08_p0&hl=${opts.lang}&authuser&soc-app=121&soc-platform=1&soc-device=1&_reqid=1065213`;\n\n  debug('batchexecute URL: %s', url);\n  debug('with body: %s', body);\n\n  const requestOptions = Object.assign({\n    url,\n    method: 'POST',\n    body,\n    followRedirect: true,\n    headers: {\n      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'\n    }\n  }, opts.requestOptions);\n\n  return (0,_utils_request_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(requestOptions, opts.throttle)\n    .then((html) => {\n      const input = JSON.parse(html.substring(5));\n      const data = JSON.parse(input[0][2]);\n\n      if (data === null) {\n        return [];\n      }\n\n      return (opts.short)\n        ? processShortPermissionsData(data)\n        : processPermissionData(data);\n    });\n}\n\nconst MAPPINGS = {\n  permissions: [2],\n  type: 0\n};\n\nfunction processShortPermissionsData (html) {\n  if (ramda__WEBPACK_IMPORTED_MODULE_4__[\"default\"](String, html)) {\n    html = _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].parse(html);\n  }\n\n  const commonPermissions = html[_constants_js__WEBPACK_IMPORTED_MODULE_2__.constants.permission.COMMON];\n\n  if (!commonPermissions) {\n    return [];\n  }\n\n  const validPermissions = commonPermissions.filter(permission => permission.length);\n  const permissionNames = ramda__WEBPACK_IMPORTED_MODULE_5__[\"default\"](permission => permission[MAPPINGS.type], validPermissions);\n  return permissionNames;\n}\n\nfunction processPermissionData (html) {\n  if (ramda__WEBPACK_IMPORTED_MODULE_4__[\"default\"](String, html)) {\n    html = _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].parse(html);\n  }\n\n  debug('html %o', html);\n\n  const permissions = Object.values(_constants_js__WEBPACK_IMPORTED_MODULE_2__.constants.permission).reduce((permissionAccummulator, permission) => {\n    if (!html[permission]) {\n      return permissionAccummulator;\n    }\n\n    permissionAccummulator.push(\n      ...ramda__WEBPACK_IMPORTED_MODULE_5__[\"default\"](flatMapPermissions, html[permission])\n    );\n\n    return permissionAccummulator;\n  }, []);\n\n  debug('Permissions %o', permissions);\n\n  return permissions;\n}\n\nfunction flatMapPermissions (permission) {\n  const input = ramda__WEBPACK_IMPORTED_MODULE_6__[\"default\"](MAPPINGS.permissions, permission);\n\n  if (typeof input === 'undefined') {\n    return [];\n  }\n\n  const mappings = getPermissionMappings(permission[MAPPINGS.type]);\n  return ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].extractor(mappings), input);\n}\n\nfunction getPermissionMappings (type) {\n  return {\n    permission: [1],\n    type: {\n      path: 0,\n      fun: () => type\n    }\n  };\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (permissions);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/permissions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/reviews.js":
/*!*********************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/reviews.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/includes.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/values.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/is.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/path.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/map.js\");\n/* harmony import */ var _utils_request_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/request.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/request.js\");\n/* harmony import */ var _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/scriptData.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/scriptData.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\");\n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__('google-play-scraper:reviews');\n\nfunction reviews (opts) {\n  return new Promise(function (resolve, reject) {\n    validate(opts);\n    const fullOptions = Object.assign({\n      sort: _constants_js__WEBPACK_IMPORTED_MODULE_2__.constants.sort.NEWEST,\n      lang: 'en',\n      country: 'us',\n      num: 150,\n      paginate: false,\n      nextPaginationToken: null\n    }, opts);\n\n    processReviews(fullOptions)\n      .then(resolve)\n      .catch(reject);\n  });\n}\n\nfunction validate (opts) {\n  if (!opts || !opts.appId) {\n    throw Error('appId missing');\n  }\n\n  if (opts.sort && !ramda__WEBPACK_IMPORTED_MODULE_4__[\"default\"](opts.sort, ramda__WEBPACK_IMPORTED_MODULE_5__[\"default\"](_constants_js__WEBPACK_IMPORTED_MODULE_2__.constants.sort))) {\n    throw new Error('Invalid sort ' + opts.sort);\n  }\n}\n\n/**\n * Format the reviews for correct and unified response model\n * @param {array} reviews The reviews to be formated\n * @param {string} token The token to be sent\n */\nfunction formatReviewsResponse ({\n  reviews,\n  num,\n  token = null\n}) {\n  const reviewsToResponse = (reviews.length >= num)\n    ? reviews.slice(0, num)\n    : reviews;\n\n  return {\n    data: reviewsToResponse,\n    nextPaginationToken: token\n  };\n}\n\n/**\n * This object allow us to differ between\n * the initial body request and the paginated ones\n */\nconst REQUEST_TYPE = {\n  initial: 'initial',\n  paginated: 'paginated'\n};\n\n/**\n * This method allow us to get the body for the review request\n *\n * @param {string} options.appId The app id for reviews\n * @param {number} options.sort The sort order for reviews\n * @param {number} options.numberOfReviewsPerRequest The number of reviews per request\n * @param {string} options.withToken The token to be used for the given request\n * @param {string} options.requestType The request type\n */\nfunction getBodyForRequests ({\n  appId,\n  sort,\n  numberOfReviewsPerRequest = 150,\n  withToken = '%token%',\n  requestType = REQUEST_TYPE.initial\n}) {\n  /* The body is slight different for the initial and paginated requests */\n  const formBody = {\n    [REQUEST_TYPE.initial]: `f.req=%5B%5B%5B%22UsvDTd%22%2C%22%5Bnull%2Cnull%2C%5B2%2C${sort}%2C%5B${numberOfReviewsPerRequest}%2Cnull%2Cnull%5D%2Cnull%2C%5B%5D%5D%2C%5B%5C%22${appId}%5C%22%2C7%5D%5D%22%2Cnull%2C%22generic%22%5D%5D%5D`,\n    [REQUEST_TYPE.paginated]: `f.req=%5B%5B%5B%22UsvDTd%22%2C%22%5Bnull%2Cnull%2C%5B2%2C${sort}%2C%5B${numberOfReviewsPerRequest}%2Cnull%2C%5C%22${withToken}%5C%22%5D%2Cnull%2C%5B%5D%5D%2C%5B%5C%22${appId}%5C%22%2C7%5D%5D%22%2Cnull%2C%22generic%22%5D%5D%5D`\n  };\n\n  return formBody[requestType];\n}\n\nconst REQUEST_MAPPINGS = {\n  reviews: [0],\n  token: [1, 1]\n};\n\n// FIXME this looks similar to the processAndRecur from other methods\nasync function processReviewsAndGetNextPage (html, opts, savedReviews) {\n  const processAndRecurOptions = Object.assign({}, opts, { requestType: REQUEST_TYPE.paginated });\n  const { appId, paginate, num } = processAndRecurOptions;\n  const parsedHtml = ramda__WEBPACK_IMPORTED_MODULE_6__[\"default\"](String, html)\n    ? _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].parse(html)\n    : html;\n\n  if (parsedHtml.length === 0) {\n    return formatReviewsResponse({ reviews: savedReviews, token: null, num });\n  }\n\n  // PROCESS REVIEWS EXTRACTION\n  const reviews = extract(REQUEST_MAPPINGS.reviews, parsedHtml, appId);\n  const token = ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](REQUEST_MAPPINGS.token, parsedHtml);\n  const reviewsAccumulator = [...savedReviews, ...reviews];\n\n  return (!paginate && token && reviewsAccumulator.length < num)\n    ? makeReviewsRequest(processAndRecurOptions, reviewsAccumulator, token)\n    : formatReviewsResponse({ reviews: reviewsAccumulator, token, num });\n}\n\n/**\n * Make a review request to Google Play Store\n * @param {object} opts The request options\n * @param {array} savedReviews The reviews accumulator array\n * @param {string} nextToken The next page token\n */\nfunction makeReviewsRequest (opts, savedReviews, nextToken) {\n  debug('nextToken: %s', nextToken);\n  debug('savedReviews length: %s', savedReviews.length);\n  debug('requestType: %s', opts.requestType);\n\n  const {\n    appId,\n    sort,\n    requestType,\n    lang,\n    country,\n    requestOptions,\n    throttle,\n    num\n  } = opts;\n  const body = getBodyForRequests({\n    appId,\n    sort,\n    withToken: nextToken,\n    requestType\n  });\n  const url = `${_constants_js__WEBPACK_IMPORTED_MODULE_2__.BASE_URL}/_/PlayStoreUi/data/batchexecute?rpcids=qnKhOb&f.sid=-697906427155521722&bl=boq_playuiserver_20190903.08_p0&hl=${lang}&gl=${country}&authuser&soc-app=121&soc-platform=1&soc-device=1&_reqid=1065213`;\n\n  debug('batchexecute URL: %s', url);\n  debug('with body: %s', body);\n\n  const reviewRequestOptions = Object.assign({\n    url,\n    method: 'POST',\n    body,\n    followRedirect: true,\n    headers: {\n      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'\n    }\n  }, requestOptions);\n\n  return (0,_utils_request_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(reviewRequestOptions, throttle)\n    .then((html) => {\n      const input = JSON.parse(html.substring(5));\n      const data = JSON.parse(input[0][2]);\n\n      return (data === null)\n        ? formatReviewsResponse({ reviews: savedReviews, token: null, num })\n        : processReviewsAndGetNextPage(data, opts, savedReviews);\n    });\n}\n\n/**\n * Process the reviews for a given app\n * @param {object} opts The options for reviews behavior\n */\nfunction processReviews (opts) {\n  const requestType = (!opts.nextPaginationToken)\n    ? REQUEST_TYPE.initial\n    : REQUEST_TYPE.paginated;\n  const token = opts.nextPaginationToken || '%token%';\n\n  const reviewsOptions = Object.assign({}, { requestType }, opts);\n  return makeReviewsRequest(reviewsOptions, [], token);\n}\n\nfunction getReviewsMappings (appId) {\n  const MAPPINGS = {\n    id: [0],\n    userName: [1, 0],\n    userImage: [1, 1, 3, 2],\n    date: {\n      path: [5],\n      fun: generateDate\n    },\n    score: [2],\n    scoreText: {\n      path: [2],\n      fun: (score) => String(score)\n    },\n    url: {\n      path: [0],\n      fun: (reviewId) => `${_constants_js__WEBPACK_IMPORTED_MODULE_2__.BASE_URL}/store/apps/details?id=${appId}&reviewId=${reviewId}`\n    },\n    title: {\n      path: [0],\n      fun: () => null\n    },\n    text: [4],\n    replyDate: {\n      path: [7, 2],\n      fun: generateDate\n    },\n    replyText: {\n      path: [7, 1],\n      fun: (text) => text || null\n    },\n    version: {\n      path: [10],\n      fun: (version) => version || null\n    },\n    thumbsUp: [6],\n    criterias: {\n      path: [12, 0],\n      fun: (criterias = []) => criterias.map(buildCriteria)\n    }\n  };\n\n  return MAPPINGS;\n}\n\nconst buildCriteria = (criteria) => ({\n  criteria: criteria[0],\n  rating: criteria[1] ? criteria[1][0] : null\n});\n\nfunction generateDate (dateArray) {\n  if (!dateArray) {\n    return null;\n  }\n\n  const millisecondsLastDigits = String(dateArray[1] || '000');\n  const millisecondsTotal = `${dateArray[0]}${millisecondsLastDigits.substring(0, 3)}`;\n  const date = new Date(Number(millisecondsTotal));\n\n  return date.toJSON();\n}\n\n/*\n * Apply MAPPINGS for each application in list from root path\n*/\nfunction extract (root, data, appId) {\n  const input = ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](root, data);\n  const MAPPINGS = getReviewsMappings(appId);\n  return ramda__WEBPACK_IMPORTED_MODULE_8__[\"default\"](_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].extractor(MAPPINGS), input);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (reviews);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/reviews.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/search.js":
/*!********************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/search.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/is.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/path.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/map.js\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! url */ \"url\");\n/* harmony import */ var _utils_request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/request.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/request.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n/* harmony import */ var _utils_processPages_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/processPages.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/processPages.js\");\n/* harmony import */ var _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/scriptData.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/scriptData.js\");\n\n\n\n\n\n\n\n/*\n * Make the first search request as in the browser and call `checkfinished` to\n * process the next pages.\n */\nfunction initialRequest (opts) {\n  const url = `${_constants_js__WEBPACK_IMPORTED_MODULE_2__.BASE_URL}/work/search?q=${opts.term}&hl=${opts.lang}&gl=${opts.country}&price=${opts.price}`;\n  return (0,_utils_request_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\n    Object.assign({ url }, opts.requestOptions),\n    opts.throttle\n  ).then((html) => processFirstPage(html, opts, [], INITIAL_MAPPINGS));\n}\n\nfunction extaractDeveloperId (link) {\n  return link.split('?id=')[1];\n}\n\nasync function processFirstPage (html, opts, savedApps, mappings) {\n  if (ramda__WEBPACK_IMPORTED_MODULE_5__[\"default\"](String, html)) {\n    html = _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].parse(html);\n  }\n\n  const appsMapping = {\n    title: [2],\n    appId: [12, 0],\n    url: {\n      path: [9, 4, 2],\n      fun: (path) => new url__WEBPACK_IMPORTED_MODULE_0__.URL(path, _constants_js__WEBPACK_IMPORTED_MODULE_2__.BASE_URL).toString()\n    },\n    icon: [1, 1, 0, 3, 2],\n    developer: [4, 0, 0, 0],\n    developerId: {\n      path: [4, 0, 0, 1, 4, 2],\n      fun: extaractDeveloperId\n    },\n    currency: [7, 0, 3, 2, 1, 0, 1],\n    price: {\n      path: [7, 0, 3, 2, 1, 0, 0],\n      fun: (price) => price / 1000000\n    },\n    free: {\n      path: [7, 0, 3, 2, 1, 0, 0],\n      fun: (price) => price === 0\n    },\n    summary: [4, 1, 1, 1, 1],\n    scoreText: [6, 0, 2, 1, 0],\n    score: [6, 0, 2, 1, 1]\n  };\n\n  const sections = ramda__WEBPACK_IMPORTED_MODULE_6__[\"default\"](mappings.sections, html) || [];\n  if (noResultsFound(sections)) return [];\n\n  const tokenSection = sections.filter((section) => isTokenSection(section))[0];\n  const appsSection = ramda__WEBPACK_IMPORTED_MODULE_6__[\"default\"](mappings.apps, html);\n\n  // parse each item in appsSection array\n  const processedApps = ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].extractor(appsMapping), appsSection);\n\n  const apps = opts.fullDetail\n    ? await (0,_utils_processPages_js__WEBPACK_IMPORTED_MODULE_3__.processFullDetailApps)(processedApps, opts)\n    : processedApps;\n  const token = ramda__WEBPACK_IMPORTED_MODULE_6__[\"default\"](SECTIONS_MAPPING.token, tokenSection);\n\n  return (0,_utils_processPages_js__WEBPACK_IMPORTED_MODULE_3__.checkFinished)(opts, [...savedApps, ...apps], token);\n}\n\nfunction isTokenSection (section) {\n  const sectionToken =\n    ramda__WEBPACK_IMPORTED_MODULE_5__[\"default\"](Array, section) && ramda__WEBPACK_IMPORTED_MODULE_6__[\"default\"](SECTIONS_MAPPING.token, section);\n  return ramda__WEBPACK_IMPORTED_MODULE_5__[\"default\"](String, sectionToken);\n}\n\nfunction noResultsFound (sections) {\n  if (sections.length === 0) {\n    return true;\n  }\n}\n\nconst INITIAL_MAPPINGS = {\n  apps: ['ds:1', 0, 1, 0, 0, 0],\n  sections: ['ds:1', 0, 1, 0, 0]\n};\n\nconst SECTIONS_MAPPING = {\n  token: [1]\n};\n\nfunction getPriceGoogleValue (value) {\n  switch (value.toLowerCase()) {\n    case 'free':\n      return 1;\n    case 'paid':\n      return 2;\n    case 'all':\n    default:\n      return 0;\n  }\n}\n\nfunction search (appData, opts) {\n  return new Promise(function (resolve, reject) {\n    if (!opts || !opts.term) {\n      throw Error('Search term missing');\n    }\n\n    if (opts.num && opts.num > 250) {\n      throw Error(\"The number of results can't exceed 250\");\n    }\n\n    opts = {\n      term: encodeURIComponent(opts.term),\n      lang: opts.lang || 'en',\n      country: opts.country || 'us',\n      num: opts.num || 20,\n      fullDetail: opts.fullDetail,\n      price: opts.price ? getPriceGoogleValue(opts.price) : 0,\n      throttle: opts.throttle,\n      cache: opts.cache,\n      requestOptions: opts.requestOptions\n    };\n\n    initialRequest(opts).then(resolve).catch(reject);\n  }).then((results) => {\n    if (opts.fullDetail) {\n      // if full detail is wanted get it from the app module\n      return Promise.all(\n        results.map((app) => appData({ ...opts, appId: app.appId }))\n      );\n    }\n    return results;\n  });\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (search);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/search.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/similar.js":
/*!*********************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/similar.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/path.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/is.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/map.js\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! url */ \"url\");\n/* harmony import */ var _utils_request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/request.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/request.js\");\n/* harmony import */ var querystring__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! querystring */ \"querystring\");\n/* harmony import */ var _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/scriptData.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/scriptData.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n/* harmony import */ var _utils_processPages_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/processPages.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/processPages.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\");\n\n\n\n\n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_6__('google-play-scraper:similar');\n\nfunction similar (opts) {\n  return new Promise(function (resolve, reject) {\n    validateSimilarParameters(opts);\n\n    const mergedOpts = Object.assign({},\n      {\n        appId: encodeURIComponent(opts.appId),\n        lang: opts.lang || 'en',\n        country: opts.country || 'us',\n        fullDetail: opts.fullDetail\n      });\n\n    const qs = querystring__WEBPACK_IMPORTED_MODULE_2__.stringify({\n      id: mergedOpts.appId,\n      hl: 'en',\n      gl: mergedOpts.country\n    });\n\n    const similarUrl = `${_constants_js__WEBPACK_IMPORTED_MODULE_4__.BASE_URL}/store/apps/details?${qs}`;\n    const options = Object.assign({\n      url: similarUrl,\n      followRedirect: true\n    }, opts.requestOptions);\n\n    debug('Similar Request URL: %s', similarUrl);\n\n    (0,_utils_request_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(options, opts.throttle)\n      .then(_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].parse)\n      .then(parsedObject => parseSimilarApps(parsedObject, mergedOpts))\n      .then(resolve)\n      .catch(reject);\n  });\n}\n\nfunction validateSimilarParameters (opts) {\n  if (!opts || !opts.appId) {\n    throw Error('appId missing');\n  }\n}\n\nconst INITIAL_MAPPINGS = {\n  clusters: {\n    path: [1, 1],\n    useServiceRequestId: 'ag2B9c'\n  },\n  apps: ['ds:3', 0, 1, 0, 21, 0],\n  token: ['ds:3', 0, 1, 0, 21, 1, 3, 1]\n};\n\nconst CLUSTER_MAPPING = {\n  title: [21, 1, 0],\n  url: [21, 1, 2, 4, 2]\n};\n\nconst SIMILAR_APPS = 'Similar apps';\nconst SIMILAR_GAMES = 'Similar games';\n\nfunction parseSimilarApps (similarObject, opts) {\n  const clusters = _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].extractDataWithServiceRequestId(similarObject, INITIAL_MAPPINGS.clusters);\n\n  if (clusters.length === 0) {\n    throw Error('Similar apps not found');\n  }\n\n  let similarAppsCluster = clusters.filter(cluster => {\n    return ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](CLUSTER_MAPPING.title, cluster) === SIMILAR_APPS ||\n      ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](CLUSTER_MAPPING.title, cluster) === SIMILAR_GAMES ||\n      clusters;\n  });\n\n  if (similarAppsCluster.length === 0) {\n    similarAppsCluster = clusters;\n  }\n\n  const clusterUrl = getParsedCluster(similarAppsCluster[0]);\n\n  const fullClusterUrl = `${_constants_js__WEBPACK_IMPORTED_MODULE_4__.BASE_URL}${clusterUrl}&gl=${opts.country}&hl=${opts.lang}`;\n  debug('Cluster Request URL: %s', fullClusterUrl);\n\n  const options = Object.assign({\n    url: fullClusterUrl,\n    followRedirect: true\n  }, opts.requestOptions);\n\n  return (0,_utils_request_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(options, opts.throttle)\n    .then(_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].parse)\n    .then((htmlParsed) => processFirstPage(htmlParsed, opts, [], INITIAL_MAPPINGS));\n}\n\nasync function processFirstPage (html, opts, savedApps, mappings) {\n  if (ramda__WEBPACK_IMPORTED_MODULE_8__[\"default\"](String, html)) {\n    html = _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].parse(html);\n  }\n\n  const mapping = {\n    title: [3],\n    appId: [0, 0],\n    url: {\n      path: [10, 4, 2],\n      fun: (path) => new url__WEBPACK_IMPORTED_MODULE_0__.URL(path, _constants_js__WEBPACK_IMPORTED_MODULE_4__.BASE_URL).toString()\n    },\n    icon: [1, 3, 2],\n    developer: [14],\n    currency: [8, 1, 0, 1],\n    price: {\n      path: [8, 1, 0, 0],\n      fun: (price) => price / 1000000\n    },\n    free: {\n      path: [8, 1, 0, 0],\n      fun: (price) => price === 0\n    },\n    summary: [13, 1],\n    scoreText: [4, 0],\n    score: [4, 1]\n  };\n\n  const processedApps = ramda__WEBPACK_IMPORTED_MODULE_9__[\"default\"](_utils_scriptData_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].extractor(mapping), ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](mappings.apps, html));\n\n  const apps = opts.fullDetail\n    ? await (0,_utils_processPages_js__WEBPACK_IMPORTED_MODULE_5__.processFullDetailApps)(processedApps, opts)\n    : processedApps;\n  const token = ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](mappings.token, html);\n\n  return (0,_utils_processPages_js__WEBPACK_IMPORTED_MODULE_5__.checkFinished)(opts, [...savedApps, ...apps], token);\n}\n\nfunction getParsedCluster (similarObject) {\n  const clusterUrl = ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](CLUSTER_MAPPING.url, similarObject);\n  return clusterUrl;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (similar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/similar.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/suggest.js":
/*!*********************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/suggest.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_request_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/request.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/request.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n\n\n\nfunction suggest (opts) {\n  return new Promise(function (resolve, reject) {\n    if (!opts && !opts.term) {\n      throw Error('term missing');\n    }\n\n    const lang = opts.lang || 'en';\n    const country = opts.country || 'us';\n    // FIXME duplicated from permissions\n    const url = `${_constants_js__WEBPACK_IMPORTED_MODULE_1__.BASE_URL}/_/PlayStoreUi/data/batchexecute?rpcids=IJ4APc&f.sid=-697906427155521722&bl=boq_playuiserver_20190903.08_p0&hl=${lang}&gl=${country}&authuser&soc-app=121&soc-platform=1&soc-device=1&_reqid=1065213`;\n\n    const term = encodeURIComponent(opts.term);\n    const body = `f.req=%5B%5B%5B%22IJ4APc%22%2C%22%5B%5Bnull%2C%5B%5C%22${term}%5C%22%5D%2C%5B10%5D%2C%5B2%5D%2C4%5D%5D%22%5D%5D%5D`;\n    const options = Object.assign({\n      url,\n      body,\n      method: 'POST',\n      followAllRedirects: true,\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'\n      }\n    }, opts.requestOptions);\n\n    (0,_utils_request_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(options, opts.throttle)\n      .then((html) => {\n        const input = JSON.parse(html.substring(5));\n        const data = JSON.parse(input[0][2]);\n\n        if (data === null) {\n          return [];\n        }\n        return data[0][0].map(s => s[0]);\n      })\n      .then(resolve)\n      .catch(reject);\n  });\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (suggest);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/suggest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/utils/appList.js":
/*!***************************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/utils/appList.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! url */ \"url\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/path.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/map.js\");\n/* harmony import */ var _scriptData_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./scriptData.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/scriptData.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n\n\n\n\n\nconst MAPPINGS = {\n  title: [2],\n  appId: [12, 0],\n  url: {\n    path: [9, 4, 2],\n    fun: (path) => new url__WEBPACK_IMPORTED_MODULE_0__.URL(path, _constants_js__WEBPACK_IMPORTED_MODULE_2__.BASE_URL).toString()\n  },\n  icon: [1, 1, 0, 3, 2],\n  developer: [4, 0, 0, 0],\n  developerId: {\n    path: [4, 0, 0, 1, 4, 2],\n    fun: extaractDeveloperId\n  },\n  priceText: {\n    path: [7, 0, 3, 2, 1, 0, 2],\n    fun: (price) => price === undefined ? 'FREE' : price\n  },\n  currency: [7, 0, 3, 2, 1, 0, 1],\n  price: {\n    path: [7, 0, 3, 2, 1, 0, 2],\n    fun: (price) => price === undefined ? 0 : parseFloat(price.match(/([0-9.,]+)/)[0])\n  },\n  free: {\n    path: [7, 0, 3, 2, 1, 0, 2],\n    fun: (price) => price === undefined\n  },\n  summary: [4, 1, 1, 1, 1],\n  scoreText: [6, 0, 2, 1, 0],\n  score: [6, 0, 2, 1, 1]\n};\n\nfunction extaractDeveloperId (link) {\n  return link.split('?id=')[1];\n}\n\n/*\n * Apply MAPPINGS for each application in list from root path\n*/\n\nfunction extract (root, data) {\n  const input = ramda__WEBPACK_IMPORTED_MODULE_3__[\"default\"](root, data);\n  if (input === undefined) return [];\n  return ramda__WEBPACK_IMPORTED_MODULE_4__[\"default\"](_scriptData_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].extractor(MAPPINGS), input);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({ MAPPINGS, extract });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/utils/appList.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/utils/mappingHelpers.js":
/*!**********************************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/utils/mappingHelpers.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/path.js\");\n\n\n\nfunction descriptionHtmlLocalized (searchArray) {\n  const descriptionTranslation = ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"]([12, 0, 0, 1], searchArray);\n  const descriptionOriginal = ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"]([72, 0, 1], searchArray);\n\n  return descriptionTranslation || descriptionOriginal;\n}\n\nfunction descriptionText (description) {\n  // preserve the line breaks when converting to text\n  const html = cheerio__WEBPACK_IMPORTED_MODULE_0__.load('<div>' + description.replace(/<br>/g, '\\r\\n') + '</div>');\n  return html('div').text();\n}\n\nfunction priceText (priceText) {\n  return priceText || 'Free';\n}\n\nfunction normalizeAndroidVersion (androidVersionText) {\n  if (!androidVersionText) return 'VARY';\n\n  const number = androidVersionText.split(' ')[0];\n  if (parseFloat(number)) {\n    return number;\n  }\n\n  return 'VARY';\n}\n\nfunction buildHistogram (container) {\n  if (!container) {\n    return { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };\n  }\n\n  return {\n    1: container[1][1],\n    2: container[2][1],\n    3: container[3][1],\n    4: container[4][1],\n    5: container[5][1]\n  };\n}\n\n/**\n * Extract the comments from google play script array\n * @param {array} comments The comments array\n */\nfunction extractComments (data) {\n  /**\n   * Comments have been found to migrate between two\n   * paths: ds:8 and ds:9. For this reason, we'll check\n   * for expected fields in both paths to determine\n   * the correct path to use.\n   */\n  let comments = [];\n\n  for (const path of ['ds:8', 'ds:9']) {\n    const authorPath = [path, 0, 0, 1, 0];\n    const versionPath = [path, 0, 0, 10];\n    const datePath = [path, 0, 0, 5, 0];\n\n    /**\n     * This logic could be further improved by checking\n     * values like `version` and `date` against expected\n     * patterns for these values.\n     */\n    if (ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"](authorPath, data)) {\n      if (ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"](versionPath, data)) {\n        if (ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"](datePath, data)) {\n          /**\n           * If we have found all expected fields, then\n           * we will dump the original comments structure\n           * into the `comments` variable for further\n           * handling.\n           */\n          comments = ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"]([path, 0], data);\n          break;\n        }\n      }\n    }\n  }\n\n  if (comments.length > 0) {\n    comments = comments.map(ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"]([4])).slice(0, 5);\n  }\n\n  return comments;\n}\n\nfunction extractFeatures (featuresArray) {\n  if (featuresArray === null) {\n    return [];\n  }\n\n  const features = featuresArray[2] || [];\n\n  return features.map(feature => ({\n    title: feature[0],\n    description: ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"]([1, 0, 0, 1], feature)\n  }));\n}\n\n/**\n * Recursively extracts the categories of the App\n * @param {array} categories The categories array\n */\nfunction extractCategories (searchArray, categories = []) {\n  if (searchArray === null || searchArray.length === 0) return categories;\n\n  if (searchArray.length >= 4 && typeof searchArray[0] === 'string') {\n    categories.push({\n      name: searchArray[0],\n      id: searchArray[2]\n    });\n  } else {\n    searchArray.forEach((sub) => {\n      extractCategories(sub, categories);\n    });\n  }\n\n  return categories;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  descriptionHtmlLocalized,\n  descriptionText,\n  priceText,\n  normalizeAndroidVersion,\n  buildHistogram,\n  extractComments,\n  extractFeatures,\n  extractCategories\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/utils/mappingHelpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/utils/processPages.js":
/*!********************************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/utils/processPages.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFinished: () => (/* binding */ checkFinished),\n/* harmony export */   processFullDetailApps: () => (/* binding */ processFullDetailApps),\n/* harmony export */   processPages: () => (/* binding */ processPages)\n/* harmony export */ });\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/is.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/path.js\");\n/* harmony import */ var _utils_request_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/request.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/request.js\");\n/* harmony import */ var _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/scriptData.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/scriptData.js\");\n/* harmony import */ var _utils_appList_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/appList.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/appList.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constants.js */ \"(rsc)/./node_modules/google-play-scraper/lib/constants.js\");\n/* harmony import */ var _app_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../app.js */ \"(rsc)/./node_modules/google-play-scraper/lib/app.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\");\n\n\n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_5__('google-play-scraper:processPages');\n\n// FIXME this should be its own helper, and live in utils\n// FIXME should receive mappings.apps and mappings.token as separate variables\n// FIXME opts should be the last element?\n// TODO add a good docstring for this one\nasync function processPages (html, opts, savedApps, mappings) {\n  if (ramda__WEBPACK_IMPORTED_MODULE_6__[\"default\"](String, html)) {\n    html = _utils_scriptData_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].parse(html);\n  }\n\n  const processedApps = _utils_appList_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].extract(mappings.apps, html);\n  const apps = opts.fullDetail\n    ? await processFullDetailApps(processedApps, opts)\n    : processedApps;\n  const token = ramda__WEBPACK_IMPORTED_MODULE_7__[\"default\"](mappings.token, html);\n\n  return checkFinished(opts, [...savedApps, ...apps], token);\n}\n\nasync function processFullDetailApps (apps, opts) {\n  const promises = apps.map(app => (\n    (0,_app_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n      appId: app.appId,\n      lang: opts.lang,\n      country: opts.country,\n      cache: opts.cache,\n      throttle: opts.throttle,\n      requestOptions: opts.requestOptions\n    })\n  ));\n\n  return Promise.all(promises);\n}\n\nconst REQUEST_MAPPINGS = {\n  apps: [0, 0, 0],\n  token: [0, 0, 7, 1]\n};\n\nfunction checkFinished (opts, savedApps, nextToken) {\n  if (savedApps.length >= opts.num || !nextToken) {\n    return savedApps.slice(0, opts.num);\n  }\n\n  const body = getBodyForRequests({\n    numberOfApps: opts.numberOfApps,\n    withToken: nextToken\n  });\n  const url = `${_constants_js__WEBPACK_IMPORTED_MODULE_3__.BASE_URL}/_/PlayStoreUi/data/batchexecute?rpcids=qnKhOb&f.sid=-697906427155521722&bl=boq_playuiserver_20190903.08_p0&hl=${opts.lang}&gl=${opts.country}&authuser&soc-app=121&soc-platform=1&soc-device=1&_reqid=1065213`;\n\n  debug('batchexecute URL: %s', url);\n  debug('with body: %s', body);\n\n  const requestOptions = Object.assign({\n    url,\n    method: 'POST',\n    body,\n    followRedirect: true,\n    headers: {\n      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'\n    }\n  }, opts.requestOptions);\n\n  return (0,_utils_request_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(requestOptions, opts.throttle)\n    .then((html) => {\n      const input = JSON.parse(html.substring(5));\n      const data = JSON.parse(input[0][2]);\n\n      return (data === null)\n        ? savedApps\n        : processPages(data, opts, savedApps, REQUEST_MAPPINGS);\n    });\n}\n\nfunction getBodyForRequests ({\n  numberOfApps = 100,\n  withToken = '%token%'\n}) {\n  const body = `f.req=%5B%5B%5B%22qnKhOb%22%2C%22%5B%5Bnull%2C%5B%5B10%2C%5B10%2C${numberOfApps}%5D%5D%2Ctrue%2Cnull%2C%5B96%2C27%2C4%2C8%2C57%2C30%2C110%2C79%2C11%2C16%2C49%2C1%2C3%2C9%2C12%2C104%2C55%2C56%2C51%2C10%2C34%2C77%5D%5D%2Cnull%2C%5C%22${withToken}%5C%22%5D%5D%22%2Cnull%2C%22generic%22%5D%5D%5D`;\n\n  return body;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/utils/processPages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/utils/request.js":
/*!***************************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/utils/request.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var got__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! got */ \"(rsc)/./node_modules/got/dist/source/index.js\");\n/* harmony import */ var _throttle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./throttle.js */ \"(rsc)/./node_modules/google-play-scraper/lib/utils/throttle.js\");\n/* harmony import */ var tough_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tough-cookie */ \"(rsc)/./node_modules/tough-cookie/lib/cookie.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\");\n\n\n\n\n\nconst cookieJar = new tough_cookie__WEBPACK_IMPORTED_MODULE_1__.CookieJar();\nconst debug = debug__WEBPACK_IMPORTED_MODULE_2__('google-play-scraper');\n\nfunction doRequest (opts, limit) {\n  let req;\n\n  // cookies are necessary for pagination to work consistently across requests\n  opts.cookieJar = cookieJar;\n\n  if (limit) {\n    req = (0,_throttle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\n      got__WEBPACK_IMPORTED_MODULE_3__, {\n        interval: 1000,\n        limit\n      }\n    );\n  } else {\n    req = got__WEBPACK_IMPORTED_MODULE_3__;\n  }\n\n  return new Promise((resolve, reject) => {\n    req(opts)\n      .then((response) => resolve(response.body))\n      .catch((error) => reject(error));\n  });\n}\n\nasync function request (opts, limit) {\n  debug('Making request: %j', opts);\n  try {\n    const response = await doRequest(opts, limit);\n    debug('Request finished');\n    return response;\n  } catch (reason) {\n    debug('Request error:', reason.message, reason.response && reason.response.statusCode);\n\n    let message = 'Error requesting Google Play:' + reason.message;\n    if (reason.response && reason.response.statusCode === 404) {\n      message = 'App not found (404)';\n    }\n    const err = Error(message);\n    err.status = reason.response && reason.response.statusCode;\n    throw err;\n  }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (request);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/utils/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/utils/scriptData.js":
/*!******************************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/utils/scriptData.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/path.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/map.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/is.js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ramda */ \"(rsc)/./node_modules/ramda/es/assoc.js\");\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_0__('google-play-scraper:scriptData');\n\n/**\n* This method looks for the mapping inside the serviceRequestData object\n* The serviceRequestData object is mapped from the AF_dataServiceRequests html var\n*\n* @param {object} parsedData The response mapped object\n* @param {object} spec The mappings spec\n*/\nfunction extractDataWithServiceRequestId (parsedData, spec) {\n  const serviceRequestMapping = Object.keys(parsedData.serviceRequestData);\n  const filteredDsRootPath = serviceRequestMapping.filter(serviceRequest => {\n    const dsValues = parsedData.serviceRequestData[serviceRequest];\n\n    return dsValues.id === spec.useServiceRequestId;\n  });\n\n  const formattedPath = (filteredDsRootPath.length)\n    ? [filteredDsRootPath[0], ...spec.path]\n    : spec.path;\n\n  return ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"](formattedPath, parsedData);\n}\n\n/**\n* Map the MAPPINGS object, applying each field spec to the parsed data.\n* If the mapping value is an array, use it as the path to the extract the\n* field's value. If it's an object, extract the value in object.path and pass\n* it to the function in object.fun\n*\n* @param {array} mappings The mappings object\n*/\nfunction extractor (mappings) {\n  return function extractFields (parsedData) {\n    debug('parsedData: %o', parsedData);\n\n    return ramda__WEBPACK_IMPORTED_MODULE_2__[\"default\"]((spec) => {\n      if (ramda__WEBPACK_IMPORTED_MODULE_3__[\"default\"](Array, spec)) {\n        return ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"](spec, parsedData);\n      }\n\n      // extractDataWithServiceRequestId explanation:\n      // https://github.com/facundoolano/google-play-scraper/pull/412\n      // assume spec object\n      const input = (spec.useServiceRequestId)\n        ? extractDataWithServiceRequestId(parsedData, spec)\n        : ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"](spec.path, parsedData);\n\n      return spec.fun(input, parsedData);\n    }, mappings);\n  };\n}\n\n/*\n * Extract the javascript objects returned by the AF_initDataCallback functions\n * in the script tags of the app detail HTML.\n */\nfunction parse (response) {\n  const scriptRegex = />AF_initDataCallback[\\s\\S]*?<\\/script/g;\n  const keyRegex = /(ds:.*?)'/;\n  const valueRegex = /data:([\\s\\S]*?), sideChannel: {}}\\);<\\//;\n\n  const matches = response.match(scriptRegex);\n\n  if (!matches) {\n    return {};\n  }\n\n  const parsedData = matches.reduce((accum, data) => {\n    const keyMatch = data.match(keyRegex);\n    const valueMatch = data.match(valueRegex);\n\n    if (keyMatch && valueMatch) {\n      const key = keyMatch[1];\n      const value = JSON.parse(valueMatch[1]);\n      return ramda__WEBPACK_IMPORTED_MODULE_4__[\"default\"](key, value, accum);\n    }\n    return accum;\n  }, {});\n\n  return Object.assign(\n    {},\n    parsedData,\n    { serviceRequestData: parseServiceRequests(response) }\n  );\n}\n\n/*\n * Extract the javascript objects returned by the AF_dataServiceRequests function\n * in the script tags of the app detail HTML.\n */\nfunction parseServiceRequests (response) {\n  const scriptRegex = /; var AF_dataServiceRequests[\\s\\S]*?; var AF_initDataChunkQueue/g;\n  const valueRegex = /{'ds:[\\s\\S]*}}/g;\n\n  const matches = response.match(scriptRegex);\n\n  if (!matches) {\n    return {};\n  }\n\n  const [data] = matches;\n  const valueMatch = data.match(valueRegex);\n\n  if (!valueMatch) {\n    return {};\n  }\n\n  // eslint-disable-next-line\n  const value = eval(`(${valueMatch[0]})`);\n  return value;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Object.assign({ parse, parseServiceRequests, extractor, extractDataWithServiceRequestId }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/utils/scriptData.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-play-scraper/lib/utils/throttle.js":
/*!****************************************************************!*\
  !*** ./node_modules/google-play-scraper/lib/utils/throttle.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));\n\n/**\n * Throttle is a first wrapper function that sets state variables for throttled requests.\n * **/\nfunction Throttle () {\n  // Setting State\n  let startedAt = null;\n  let timesCalled = 0;\n  let inThrottle = false;\n\n  /**\n   * Second wrapper function sets the parameters for throttling (interval and limit number of requests by interval)\n   * @param {function} fn function that will be used.\n   * @param {object} opts parameters interval and limit.\n   * @return decorator function for @param {function} fn\n   */\n  return function settingOptions (fn, opts) {\n    const ms = opts.interval;\n    const number = opts.limit;\n\n    /**\n     * Decorator for parent settingOption @function.\n     * Function is basically if else statement, that checks if the function could be executed right now or need to wait until the end of a delay.\n     * For the condition, it uses interval and limit options and compares them to the state variables.\n     * @return result of the executed function from parent settingOption @function\n     */\n    return async function returnedFunction (...args) {\n      // Set Date Variable if it's Empty\n      if (!startedAt) startedAt = Date.now();\n\n      if (timesCalled < number && Date.now() - startedAt < ms) {\n        // Execute Parent Function\n        timesCalled++;\n        const result = await fn(...args);\n        return result;\n      }\n\n      if (!inThrottle) {\n        inThrottle = true;\n        await sleep(ms);\n        // Reset Conditions After Delay\n        timesCalled = 0;\n        startedAt = Date.now();\n        // Return Called Function\n        const result = await returnedFunction(...args);\n        inThrottle = false;\n        return result;\n      }\n\n      // Wait Until Delay Ends\n      const checkingPromise = new Promise(resolve => {\n        const interval = setInterval(async () => {\n          if (!inThrottle) {\n            clearInterval(interval);\n            const result = await returnedFunction(...args);\n            // Resolve Executed Function\n            return resolve(result);\n          }\n        }, 1);\n      });\n      const result = await checkingPromise;\n      return result;\n    };\n  };\n}\n\nconst throttledRequest = Throttle();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (throttledRequest);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-play-scraper/lib/utils/throttle.js\n");

/***/ })

};
;