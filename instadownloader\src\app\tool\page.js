"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { recentlyUpdatedTools } from "../util/constants";
import Category from "../components/reuseable/Category";
import LoadingComponent from "../Loading";
import SideBar from "../components/Sidebar";
import Card from "../components/reuseable/Card";
import { getAllTools } from "../util/dataFetch";

const Cateogry = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [categoryTools, setCategoryTools] = useState([]);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [priceFilter, setPriceFilter] = useState("");

  useEffect(() => {
    const fetchAppDetails = async () => {
      try {
        const getTools = await  getAllTools(currentPage,priceFilter);
        setCategoryTools(getTools.tools);
        setTotalPages(getTools.totalPages)
      } catch (error) {
        console.error("Error fetching tool details:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAppDetails();
  }, [currentPage, priceFilter]);

  const handlePageChange = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };
  const handlePriceChange = (e) => {
    setPriceFilter(e.target.value);
    setCurrentPage(1);
  };

  return (
    <main className="lg:container flex flex-col items-center justify-between mt-0.5 mx-5 sm:mx-0 md:mx-20 lg:mx-auto">
      <div className="container max-w-screen-xl mx-auto">
        <div className="w-full md:px-3.5 justify-center flex flex-col lg:flex-row">
          <main className="lg:w-4/6 xl:w-4/6 relative mt-4">
            <div className="mb-3.5 p-3 bg-white rounded-md shadow-md">
              <p><Link href={"/"} prefetch={false}>Home</Link> / <span className="text-slate-500">AI Categories</span></p>
            </div>
            <Category isDetailsPage={true} />
            <div className="p-5 bg-white rounded-md shadow-lg mb-5">
              <div className="flex justify-between align-middle">

                <h1 className="uppercase mb-2.5 text-lg font-normal tracking text-gray-600">
                  AI Tools
                </h1>
                <select
                  value={priceFilter}
                  onChange={handlePriceChange}
                  className="block  p-2.5 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 bg-white text-gray-700"
                >
                  <option value="Free">Free</option>
                  <option value="Freemium">Freemium</option>
                  <option value="Paid">Paid</option>
                </select>

              </div>
              {isLoading ? <LoadingComponent length={6} md={1} lg={1} /> : <Card tools={categoryTools} grid={1} errorMsg="No tools, try after sometime" />
              }
              <div className="flex justify-between items-center mt-4">
                <button
                  className={`px-4 py-2 text-white bg-slate-900 rounded-md hover:bg-slate-800  ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <i className="fa-solid  text-white fa-chevron-left " />&nbsp;
                  Previous

                </button>
                <span className="text-lg">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  className={`px-4 py-2 text-white bg-slate-900 rounded-md hover:bg-slate-800  ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next &nbsp;
                  <i className="fa-solid  text-white fa-chevron-right " />
                </button>
              </div>
            </div>
          </main>
          <aside className="sidebar-container sm:w-auto lg:w-2/6 lg:px-3.5">
            <SideBar sideToolsDetails={recentlyUpdatedTools} isLoading={isLoading} />
          </aside>
        </div>
      </div>
    </main>
  );
};

export default Cateogry;
