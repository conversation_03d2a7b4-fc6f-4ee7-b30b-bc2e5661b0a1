/**
 * Cross-linking utilities for SEO-friendly navigation between APK and AI Tools sites
 */

import { generateSmartAIToolsURL } from './appMappingService';

// Domain configuration
export const DOMAINS = {
  APK_SITE: process.env.NEXT_PUBLIC_APK_DOMAIN || 'https://apkdemo1.com',
  AI_TOOLS_SITE: process.env.NEXT_PUBLIC_AI_TOOLS_DOMAIN || 'https://apk3.demo1.com'
};

// Check if cross-linking is enabled
export const isCrossLinkingEnabled = () => {
  return process.env.NEXT_PUBLIC_ENABLE_CROSS_LINKING === 'true';
};

/**
 * Category mapping between APK categories and AI tool categories
 */
export const CATEGORY_MAPPING = {
  // APK categories to AI tool categories
  'productivity': ['productivity', 'business', 'automation'],
  'communication': ['social-media', 'email-generator', 'communication'],
  'education': ['education', 'research', 'writing-generator'],
  'business': ['business', 'productivity', 'finace', 'startup'],
  'art-design': ['art', 'graphic-design', 'image-generation', 'avatar'],
  'photography': ['image-generation', 'art', 'graphic-design'],
  'entertainment': ['video-generation', 'music', 'anime'],
  'social': ['social-media', 'marketing'],
  'tools': ['productivity', 'automation', 'developer-tools'],
  'lifestyle': ['lifestyle', 'health'],
  'health-fitness': ['health', 'lifestyle'],
  'music-audio': ['music', 'audio'],
  'video-players': ['video-generation', 'video-editing'],
  'news-magazines': ['blog-generator', 'writing-generator'],
  'shopping': ['e-commerce', 'marketing'],
  'travel-local': ['travel', 'lifestyle'],
  'finance': ['finace', 'business'],
  'auto-vehicles': ['automation'],
  'dating': ['social-media'],
  'food-drink': ['lifestyle'],
  'house-home': ['lifestyle'],
  'libraries-demo': ['developer-tools'],
  'medical': ['health'],
  'parenting': ['education'],
  'personalization': ['avatar', 'customization'],
  'sports': ['lifestyle'],
  'weather': ['lifestyle']
};

/**
 * Get relevant AI tool categories for an APK category
 */
export const getRelevantAICategories = (apkCategory) => {
  if (!apkCategory) return ['productivity']; // Default fallback
  
  const normalizedCategory = apkCategory.toLowerCase().replace(/[^a-z0-9]/g, '-');
  return CATEGORY_MAPPING[normalizedCategory] || ['productivity'];
};

/**
 * Generate AI tools discovery URL for an app (Enhanced with smart mapping)
 */
export const generateAIToolsURL = (appDetails) => {
  if (!isCrossLinkingEnabled()) return null;

  const baseURL = DOMAINS.AI_TOOLS_SITE;

  // Use smart mapping service for better matching
  return generateSmartAIToolsURL(appDetails, baseURL);
};

/**
 * Generate APK download URL for an AI tool
 */
export const generateAPKURL = (toolDetails) => {
  if (!isCrossLinkingEnabled()) return null;
  
  const baseURL = DOMAINS.APK_SITE;
  
  // Try to find matching app based on tool name or category
  const toolName = toolDetails?.title?.toLowerCase() || '';
  const toolCategory = toolDetails?.subCategory?.toLowerCase() || '';
  
  // Common app mappings for AI tools
  const APP_MAPPINGS = {
    'chatgpt': 'com.openai.chatgpt',
    'claude': 'com.anthropic.claude',
    'gemini': 'com.google.android.apps.bard',
    'copilot': 'com.microsoft.copilot',
    'midjourney': 'com.midjourney.android',
    'canva': 'com.canva.editor',
    'photoshop': 'com.adobe.photoshop.express',
    'lightroom': 'com.adobe.lrmobile',
    'figma': 'com.figma.mirror',
    'notion': 'notion.id',
    'slack': 'com.Slack',
    'discord': 'com.discord',
    'zoom': 'us.zoom.videomeetings',
    'teams': 'com.microsoft.teams',
    'telegram': 'org.telegram.messenger',
    'whatsapp': 'com.whatsapp',
    'instagram': 'com.instagram.android',
    'tiktok': 'com.zhiliaoapp.musically',
    'youtube': 'com.google.android.youtube',
    'spotify': 'com.spotify.music',
    'netflix': 'com.netflix.mediaclient'
  };
  
  // Check for direct app mapping
  for (const [keyword, appId] of Object.entries(APP_MAPPINGS)) {
    if (toolName.includes(keyword)) {
      return `${baseURL}/apps/appdetails/${appId}?ref=ai-tools&tool=${encodeURIComponent(toolDetails?.appId || '')}`;
    }
  }
  
  // Category-based fallback
  const categoryMappings = {
    'social-media': '/apps/communication',
    'productivity': '/apps/productivity',
    'image-generation': '/apps/art-design',
    'video-generation': '/apps/video-players',
    'music': '/apps/music-audio',
    'business': '/apps/business',
    'education': '/apps/education',
    'health': '/apps/health-fitness'
  };
  
  const categoryPath = categoryMappings[toolCategory];
  if (categoryPath) {
    return `${baseURL}${categoryPath}?ref=ai-tools&tool=${encodeURIComponent(toolDetails?.appId || '')}`;
  }
  
  // Ultimate fallback to main apps page
  return `${baseURL}/apps?ref=ai-tools&tool=${encodeURIComponent(toolDetails?.appId || '')}`;
};

/**
 * Generate SEO-friendly anchor text for cross-links
 */
export const generateAnchorText = (type, details) => {
  if (type === 'ai-tools') {
    const category = details?.category || 'productivity';
    return `Discover AI ${category.charAt(0).toUpperCase() + category.slice(1)} Tools`;
  } else if (type === 'apk-download') {
    const toolName = details?.title || 'Related Apps';
    return `Download ${toolName} APK & Similar Apps`;
  }
  return 'Explore More';
};

/**
 * Track cross-link clicks for analytics
 */
export const trackCrossLinkClick = (source, target, details) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'cross_link_click', {
      event_category: 'Cross Linking',
      event_label: `${source} to ${target}`,
      custom_parameter_1: details?.appId || details?.toolId || 'unknown'
    });
  }
};
