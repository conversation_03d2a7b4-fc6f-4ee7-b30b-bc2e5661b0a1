import { AppRouterCacheProvider } from "@mui/material-nextjs/v13-appRouter";
import BootstrapClient from "../components/BootstrapClient";
import "bootstrap/dist/css/bootstrap.min.css";
import "../globals.css";
import Navbar from "../components/navbar/navbar.jsx";
import Layout from "../components/Layout.jsx";
export const metadata = {
  title: "App Store Spy",
  description: "App analytics",
};
export default function RootLayout({children}) {
  return (
    <html lang="en">
      <head>
      </head>
      <Layout>
        <body>
          <Navbar />
          <AppRouterCacheProvider>
            {children}
            <BootstrapClient />
          </AppRouterCacheProvider>
        </body>
      </Layout>
    </html>
  );
}
