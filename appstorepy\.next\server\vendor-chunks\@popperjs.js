"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@popperjs";
exports.ids = ["vendor-chunks/@popperjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/@popperjs/core/lib/createPopper.js":
/*!*********************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/createPopper.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPopper: () => (/* binding */ createPopper),\n/* harmony export */   detectOverflow: () => (/* reexport safe */ _utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   popperGenerator: () => (/* binding */ popperGenerator)\n/* harmony export */ });\n/* harmony import */ var _dom_utils_getCompositeRect_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dom-utils/getCompositeRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js\");\n/* harmony import */ var _dom_utils_getLayoutRect_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./dom-utils/getLayoutRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js\");\n/* harmony import */ var _dom_utils_listScrollParents_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dom-utils/listScrollParents.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js\");\n/* harmony import */ var _dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./dom-utils/getOffsetParent.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js\");\n/* harmony import */ var _utils_orderModifiers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/orderModifiers.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/orderModifiers.js\");\n/* harmony import */ var _utils_debounce_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/debounce.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/debounce.js\");\n/* harmony import */ var _utils_mergeByName_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/mergeByName.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/mergeByName.js\");\n/* harmony import */ var _utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/detectOverflow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dom-utils/instanceOf.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n\n\n\n\n\n\n\n\n\nvar DEFAULT_OPTIONS = {\n    placement: \"bottom\",\n    modifiers: [],\n    strategy: \"absolute\"\n};\nfunction areValidElements() {\n    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n        args[_key] = arguments[_key];\n    }\n    return !args.some(function(element) {\n        return !(element && typeof element.getBoundingClientRect === \"function\");\n    });\n}\nfunction popperGenerator(generatorOptions) {\n    if (generatorOptions === void 0) {\n        generatorOptions = {};\n    }\n    var _generatorOptions = generatorOptions, _generatorOptions$def = _generatorOptions.defaultModifiers, defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def, _generatorOptions$def2 = _generatorOptions.defaultOptions, defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n    return function createPopper(reference, popper, options) {\n        if (options === void 0) {\n            options = defaultOptions;\n        }\n        var state = {\n            placement: \"bottom\",\n            orderedModifiers: [],\n            options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n            modifiersData: {},\n            elements: {\n                reference: reference,\n                popper: popper\n            },\n            attributes: {},\n            styles: {}\n        };\n        var effectCleanupFns = [];\n        var isDestroyed = false;\n        var instance = {\n            state: state,\n            setOptions: function setOptions(setOptionsAction) {\n                var options = typeof setOptionsAction === \"function\" ? setOptionsAction(state.options) : setOptionsAction;\n                cleanupModifierEffects();\n                state.options = Object.assign({}, defaultOptions, state.options, options);\n                state.scrollParents = {\n                    reference: (0,_dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isElement)(reference) ? (0,_dom_utils_listScrollParents_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(reference) : reference.contextElement ? (0,_dom_utils_listScrollParents_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(reference.contextElement) : [],\n                    popper: (0,_dom_utils_listScrollParents_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(popper)\n                }; // Orders the modifiers based on their dependencies and `phase`\n                // properties\n                var orderedModifiers = (0,_utils_orderModifiers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_utils_mergeByName_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n                state.orderedModifiers = orderedModifiers.filter(function(m) {\n                    return m.enabled;\n                });\n                runModifierEffects();\n                return instance.update();\n            },\n            // Sync update – it will always be executed, even if not necessary. This\n            // is useful for low frequency updates where sync behavior simplifies the\n            // logic.\n            // For high frequency updates (e.g. `resize` and `scroll` events), always\n            // prefer the async Popper#update method\n            forceUpdate: function forceUpdate() {\n                if (isDestroyed) {\n                    return;\n                }\n                var _state$elements = state.elements, reference = _state$elements.reference, popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n                // anymore\n                if (!areValidElements(reference, popper)) {\n                    return;\n                } // Store the reference and popper rects to be read by modifiers\n                state.rects = {\n                    reference: (0,_dom_utils_getCompositeRect_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(reference, (0,_dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(popper), state.options.strategy === \"fixed\"),\n                    popper: (0,_dom_utils_getLayoutRect_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(popper)\n                }; // Modifiers have the ability to reset the current update cycle. The\n                // most common use case for this is the `flip` modifier changing the\n                // placement, which then needs to re-run all the modifiers, because the\n                // logic was previously ran for the previous placement and is therefore\n                // stale/incorrect\n                state.reset = false;\n                state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n                // is filled with the initial data specified by the modifier. This means\n                // it doesn't persist and is fresh on each update.\n                // To ensure persistent data, use `${name}#persistent`\n                state.orderedModifiers.forEach(function(modifier) {\n                    return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n                });\n                for(var index = 0; index < state.orderedModifiers.length; index++){\n                    if (state.reset === true) {\n                        state.reset = false;\n                        index = -1;\n                        continue;\n                    }\n                    var _state$orderedModifie = state.orderedModifiers[index], fn = _state$orderedModifie.fn, _state$orderedModifie2 = _state$orderedModifie.options, _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2, name = _state$orderedModifie.name;\n                    if (typeof fn === \"function\") {\n                        state = fn({\n                            state: state,\n                            options: _options,\n                            name: name,\n                            instance: instance\n                        }) || state;\n                    }\n                }\n            },\n            // Async and optimistically optimized update – it will not be executed if\n            // not necessary (debounced to run at most once-per-tick)\n            update: (0,_utils_debounce_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n                return new Promise(function(resolve) {\n                    instance.forceUpdate();\n                    resolve(state);\n                });\n            }),\n            destroy: function destroy() {\n                cleanupModifierEffects();\n                isDestroyed = true;\n            }\n        };\n        if (!areValidElements(reference, popper)) {\n            return instance;\n        }\n        instance.setOptions(options).then(function(state) {\n            if (!isDestroyed && options.onFirstUpdate) {\n                options.onFirstUpdate(state);\n            }\n        }); // Modifiers have the ability to execute arbitrary code before the first\n        // update cycle runs. They will be executed in the same order as the update\n        // cycle. This is useful when a modifier adds some persistent data that\n        // other modifiers need to use, but the modifier is run after the dependent\n        // one.\n        function runModifierEffects() {\n            state.orderedModifiers.forEach(function(_ref) {\n                var name = _ref.name, _ref$options = _ref.options, options = _ref$options === void 0 ? {} : _ref$options, effect = _ref.effect;\n                if (typeof effect === \"function\") {\n                    var cleanupFn = effect({\n                        state: state,\n                        name: name,\n                        instance: instance,\n                        options: options\n                    });\n                    var noopFn = function noopFn() {};\n                    effectCleanupFns.push(cleanupFn || noopFn);\n                }\n            });\n        }\n        function cleanupModifierEffects() {\n            effectCleanupFns.forEach(function(fn) {\n                return fn();\n            });\n            effectCleanupFns = [];\n        }\n        return instance;\n    };\n}\nvar createPopper = /*#__PURE__*/ popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2NyZWF0ZVBvcHBlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBK0Q7QUFDTjtBQUNRO0FBQ0o7QUFDTjtBQUNaO0FBQ007QUFDTTtBQUNEO0FBQ3RELElBQUlTLGtCQUFrQjtJQUNwQkMsV0FBVztJQUNYQyxXQUFXLEVBQUU7SUFDYkMsVUFBVTtBQUNaO0FBRUEsU0FBU0M7SUFDUCxJQUFLLElBQUlDLE9BQU9DLFVBQVVDLE1BQU0sRUFBRUMsT0FBTyxJQUFJQyxNQUFNSixPQUFPSyxPQUFPLEdBQUdBLE9BQU9MLE1BQU1LLE9BQVE7UUFDdkZGLElBQUksQ0FBQ0UsS0FBSyxHQUFHSixTQUFTLENBQUNJLEtBQUs7SUFDOUI7SUFFQSxPQUFPLENBQUNGLEtBQUtHLElBQUksQ0FBQyxTQUFVQyxPQUFPO1FBQ2pDLE9BQU8sQ0FBRUEsQ0FBQUEsV0FBVyxPQUFPQSxRQUFRQyxxQkFBcUIsS0FBSyxVQUFTO0lBQ3hFO0FBQ0Y7QUFFTyxTQUFTQyxnQkFBZ0JDLGdCQUFnQjtJQUM5QyxJQUFJQSxxQkFBcUIsS0FBSyxHQUFHO1FBQy9CQSxtQkFBbUIsQ0FBQztJQUN0QjtJQUVBLElBQUlDLG9CQUFvQkQsa0JBQ3BCRSx3QkFBd0JELGtCQUFrQkUsZ0JBQWdCLEVBQzFEQSxtQkFBbUJELDBCQUEwQixLQUFLLElBQUksRUFBRSxHQUFHQSx1QkFDM0RFLHlCQUF5Qkgsa0JBQWtCSSxjQUFjLEVBQ3pEQSxpQkFBaUJELDJCQUEyQixLQUFLLElBQUluQixrQkFBa0JtQjtJQUMzRSxPQUFPLFNBQVNFLGFBQWFDLFNBQVMsRUFBRUMsTUFBTSxFQUFFQyxPQUFPO1FBQ3JELElBQUlBLFlBQVksS0FBSyxHQUFHO1lBQ3RCQSxVQUFVSjtRQUNaO1FBRUEsSUFBSUssUUFBUTtZQUNWeEIsV0FBVztZQUNYeUIsa0JBQWtCLEVBQUU7WUFDcEJGLFNBQVNHLE9BQU9DLE1BQU0sQ0FBQyxDQUFDLEdBQUc1QixpQkFBaUJvQjtZQUM1Q1MsZUFBZSxDQUFDO1lBQ2hCQyxVQUFVO2dCQUNSUixXQUFXQTtnQkFDWEMsUUFBUUE7WUFDVjtZQUNBUSxZQUFZLENBQUM7WUFDYkMsUUFBUSxDQUFDO1FBQ1g7UUFDQSxJQUFJQyxtQkFBbUIsRUFBRTtRQUN6QixJQUFJQyxjQUFjO1FBQ2xCLElBQUlDLFdBQVc7WUFDYlYsT0FBT0E7WUFDUFcsWUFBWSxTQUFTQSxXQUFXQyxnQkFBZ0I7Z0JBQzlDLElBQUliLFVBQVUsT0FBT2EscUJBQXFCLGFBQWFBLGlCQUFpQlosTUFBTUQsT0FBTyxJQUFJYTtnQkFDekZDO2dCQUNBYixNQUFNRCxPQUFPLEdBQUdHLE9BQU9DLE1BQU0sQ0FBQyxDQUFDLEdBQUdSLGdCQUFnQkssTUFBTUQsT0FBTyxFQUFFQTtnQkFDakVDLE1BQU1jLGFBQWEsR0FBRztvQkFDcEJqQixXQUFXdkIsbUVBQVNBLENBQUN1QixhQUFhN0IsMkVBQWlCQSxDQUFDNkIsYUFBYUEsVUFBVWtCLGNBQWMsR0FBRy9DLDJFQUFpQkEsQ0FBQzZCLFVBQVVrQixjQUFjLElBQUksRUFBRTtvQkFDNUlqQixRQUFROUIsMkVBQWlCQSxDQUFDOEI7Z0JBQzVCLEdBQUcsK0RBQStEO2dCQUNsRSxhQUFhO2dCQUViLElBQUlHLG1CQUFtQi9CLG9FQUFjQSxDQUFDRSxpRUFBV0EsQ0FBQyxFQUFFLENBQUM0QyxNQUFNLENBQUN2QixrQkFBa0JPLE1BQU1ELE9BQU8sQ0FBQ3RCLFNBQVMsS0FBSywrQkFBK0I7Z0JBRXpJdUIsTUFBTUMsZ0JBQWdCLEdBQUdBLGlCQUFpQmdCLE1BQU0sQ0FBQyxTQUFVQyxDQUFDO29CQUMxRCxPQUFPQSxFQUFFQyxPQUFPO2dCQUNsQjtnQkFDQUM7Z0JBQ0EsT0FBT1YsU0FBU1csTUFBTTtZQUN4QjtZQUNBLHdFQUF3RTtZQUN4RSx5RUFBeUU7WUFDekUsU0FBUztZQUNULHlFQUF5RTtZQUN6RSx3Q0FBd0M7WUFDeENDLGFBQWEsU0FBU0E7Z0JBQ3BCLElBQUliLGFBQWE7b0JBQ2Y7Z0JBQ0Y7Z0JBRUEsSUFBSWMsa0JBQWtCdkIsTUFBTUssUUFBUSxFQUNoQ1IsWUFBWTBCLGdCQUFnQjFCLFNBQVMsRUFDckNDLFNBQVN5QixnQkFBZ0J6QixNQUFNLEVBQUUsa0VBQWtFO2dCQUN2RyxVQUFVO2dCQUVWLElBQUksQ0FBQ25CLGlCQUFpQmtCLFdBQVdDLFNBQVM7b0JBQ3hDO2dCQUNGLEVBQUUsK0RBQStEO2dCQUdqRUUsTUFBTXdCLEtBQUssR0FBRztvQkFDWjNCLFdBQVcvQiwwRUFBZ0JBLENBQUMrQixXQUFXNUIseUVBQWVBLENBQUM2QixTQUFTRSxNQUFNRCxPQUFPLENBQUNyQixRQUFRLEtBQUs7b0JBQzNGb0IsUUFBUS9CLHVFQUFhQSxDQUFDK0I7Z0JBQ3hCLEdBQUcsb0VBQW9FO2dCQUN2RSxvRUFBb0U7Z0JBQ3BFLHVFQUF1RTtnQkFDdkUsdUVBQXVFO2dCQUN2RSxrQkFBa0I7Z0JBRWxCRSxNQUFNeUIsS0FBSyxHQUFHO2dCQUNkekIsTUFBTXhCLFNBQVMsR0FBR3dCLE1BQU1ELE9BQU8sQ0FBQ3ZCLFNBQVMsRUFBRSx1RUFBdUU7Z0JBQ2xILHdFQUF3RTtnQkFDeEUsa0RBQWtEO2dCQUNsRCxzREFBc0Q7Z0JBRXREd0IsTUFBTUMsZ0JBQWdCLENBQUN5QixPQUFPLENBQUMsU0FBVUMsUUFBUTtvQkFDL0MsT0FBTzNCLE1BQU1JLGFBQWEsQ0FBQ3VCLFNBQVNDLElBQUksQ0FBQyxHQUFHMUIsT0FBT0MsTUFBTSxDQUFDLENBQUMsR0FBR3dCLFNBQVNFLElBQUk7Z0JBQzdFO2dCQUVBLElBQUssSUFBSUMsUUFBUSxHQUFHQSxRQUFROUIsTUFBTUMsZ0JBQWdCLENBQUNuQixNQUFNLEVBQUVnRCxRQUFTO29CQUNsRSxJQUFJOUIsTUFBTXlCLEtBQUssS0FBSyxNQUFNO3dCQUN4QnpCLE1BQU15QixLQUFLLEdBQUc7d0JBQ2RLLFFBQVEsQ0FBQzt3QkFDVDtvQkFDRjtvQkFFQSxJQUFJQyx3QkFBd0IvQixNQUFNQyxnQkFBZ0IsQ0FBQzZCLE1BQU0sRUFDckRFLEtBQUtELHNCQUFzQkMsRUFBRSxFQUM3QkMseUJBQXlCRixzQkFBc0JoQyxPQUFPLEVBQ3REbUMsV0FBV0QsMkJBQTJCLEtBQUssSUFBSSxDQUFDLElBQUlBLHdCQUNwREwsT0FBT0csc0JBQXNCSCxJQUFJO29CQUVyQyxJQUFJLE9BQU9JLE9BQU8sWUFBWTt3QkFDNUJoQyxRQUFRZ0MsR0FBRzs0QkFDVGhDLE9BQU9BOzRCQUNQRCxTQUFTbUM7NEJBQ1ROLE1BQU1BOzRCQUNObEIsVUFBVUE7d0JBQ1osTUFBTVY7b0JBQ1I7Z0JBQ0Y7WUFDRjtZQUNBLHlFQUF5RTtZQUN6RSx5REFBeUQ7WUFDekRxQixRQUFRbEQsOERBQVFBLENBQUM7Z0JBQ2YsT0FBTyxJQUFJZ0UsUUFBUSxTQUFVQyxPQUFPO29CQUNsQzFCLFNBQVNZLFdBQVc7b0JBQ3BCYyxRQUFRcEM7Z0JBQ1Y7WUFDRjtZQUNBcUMsU0FBUyxTQUFTQTtnQkFDaEJ4QjtnQkFDQUosY0FBYztZQUNoQjtRQUNGO1FBRUEsSUFBSSxDQUFDOUIsaUJBQWlCa0IsV0FBV0MsU0FBUztZQUN4QyxPQUFPWTtRQUNUO1FBRUFBLFNBQVNDLFVBQVUsQ0FBQ1osU0FBU3VDLElBQUksQ0FBQyxTQUFVdEMsS0FBSztZQUMvQyxJQUFJLENBQUNTLGVBQWVWLFFBQVF3QyxhQUFhLEVBQUU7Z0JBQ3pDeEMsUUFBUXdDLGFBQWEsQ0FBQ3ZDO1lBQ3hCO1FBQ0YsSUFBSSx3RUFBd0U7UUFDNUUsMkVBQTJFO1FBQzNFLHVFQUF1RTtRQUN2RSwyRUFBMkU7UUFDM0UsT0FBTztRQUVQLFNBQVNvQjtZQUNQcEIsTUFBTUMsZ0JBQWdCLENBQUN5QixPQUFPLENBQUMsU0FBVWMsSUFBSTtnQkFDM0MsSUFBSVosT0FBT1ksS0FBS1osSUFBSSxFQUNoQmEsZUFBZUQsS0FBS3pDLE9BQU8sRUFDM0JBLFVBQVUwQyxpQkFBaUIsS0FBSyxJQUFJLENBQUMsSUFBSUEsY0FDekNDLFNBQVNGLEtBQUtFLE1BQU07Z0JBRXhCLElBQUksT0FBT0EsV0FBVyxZQUFZO29CQUNoQyxJQUFJQyxZQUFZRCxPQUFPO3dCQUNyQjFDLE9BQU9BO3dCQUNQNEIsTUFBTUE7d0JBQ05sQixVQUFVQTt3QkFDVlgsU0FBU0E7b0JBQ1g7b0JBRUEsSUFBSTZDLFNBQVMsU0FBU0EsVUFBVTtvQkFFaENwQyxpQkFBaUJxQyxJQUFJLENBQUNGLGFBQWFDO2dCQUNyQztZQUNGO1FBQ0Y7UUFFQSxTQUFTL0I7WUFDUEwsaUJBQWlCa0IsT0FBTyxDQUFDLFNBQVVNLEVBQUU7Z0JBQ25DLE9BQU9BO1lBQ1Q7WUFDQXhCLG1CQUFtQixFQUFFO1FBQ3ZCO1FBRUEsT0FBT0U7SUFDVDtBQUNGO0FBQ08sSUFBSWQsZUFBZSxXQUFXLEdBQUVQLGtCQUFrQixDQUFDLG9EQUFvRDtBQUVwRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL0Bwb3BwZXJqcy9jb3JlL2xpYi9jcmVhdGVQb3BwZXIuanM/NTA5MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZ2V0Q29tcG9zaXRlUmVjdCBmcm9tIFwiLi9kb20tdXRpbHMvZ2V0Q29tcG9zaXRlUmVjdC5qc1wiO1xuaW1wb3J0IGdldExheW91dFJlY3QgZnJvbSBcIi4vZG9tLXV0aWxzL2dldExheW91dFJlY3QuanNcIjtcbmltcG9ydCBsaXN0U2Nyb2xsUGFyZW50cyBmcm9tIFwiLi9kb20tdXRpbHMvbGlzdFNjcm9sbFBhcmVudHMuanNcIjtcbmltcG9ydCBnZXRPZmZzZXRQYXJlbnQgZnJvbSBcIi4vZG9tLXV0aWxzL2dldE9mZnNldFBhcmVudC5qc1wiO1xuaW1wb3J0IG9yZGVyTW9kaWZpZXJzIGZyb20gXCIuL3V0aWxzL29yZGVyTW9kaWZpZXJzLmpzXCI7XG5pbXBvcnQgZGVib3VuY2UgZnJvbSBcIi4vdXRpbHMvZGVib3VuY2UuanNcIjtcbmltcG9ydCBtZXJnZUJ5TmFtZSBmcm9tIFwiLi91dGlscy9tZXJnZUJ5TmFtZS5qc1wiO1xuaW1wb3J0IGRldGVjdE92ZXJmbG93IGZyb20gXCIuL3V0aWxzL2RldGVjdE92ZXJmbG93LmpzXCI7XG5pbXBvcnQgeyBpc0VsZW1lbnQgfSBmcm9tIFwiLi9kb20tdXRpbHMvaW5zdGFuY2VPZi5qc1wiO1xudmFyIERFRkFVTFRfT1BUSU9OUyA9IHtcbiAgcGxhY2VtZW50OiAnYm90dG9tJyxcbiAgbW9kaWZpZXJzOiBbXSxcbiAgc3RyYXRlZ3k6ICdhYnNvbHV0ZSdcbn07XG5cbmZ1bmN0aW9uIGFyZVZhbGlkRWxlbWVudHMoKSB7XG4gIGZvciAodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCBhcmdzID0gbmV3IEFycmF5KF9sZW4pLCBfa2V5ID0gMDsgX2tleSA8IF9sZW47IF9rZXkrKykge1xuICAgIGFyZ3NbX2tleV0gPSBhcmd1bWVudHNbX2tleV07XG4gIH1cblxuICByZXR1cm4gIWFyZ3Muc29tZShmdW5jdGlvbiAoZWxlbWVudCkge1xuICAgIHJldHVybiAhKGVsZW1lbnQgJiYgdHlwZW9mIGVsZW1lbnQuZ2V0Qm91bmRpbmdDbGllbnRSZWN0ID09PSAnZnVuY3Rpb24nKTtcbiAgfSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBwb3BwZXJHZW5lcmF0b3IoZ2VuZXJhdG9yT3B0aW9ucykge1xuICBpZiAoZ2VuZXJhdG9yT3B0aW9ucyA9PT0gdm9pZCAwKSB7XG4gICAgZ2VuZXJhdG9yT3B0aW9ucyA9IHt9O1xuICB9XG5cbiAgdmFyIF9nZW5lcmF0b3JPcHRpb25zID0gZ2VuZXJhdG9yT3B0aW9ucyxcbiAgICAgIF9nZW5lcmF0b3JPcHRpb25zJGRlZiA9IF9nZW5lcmF0b3JPcHRpb25zLmRlZmF1bHRNb2RpZmllcnMsXG4gICAgICBkZWZhdWx0TW9kaWZpZXJzID0gX2dlbmVyYXRvck9wdGlvbnMkZGVmID09PSB2b2lkIDAgPyBbXSA6IF9nZW5lcmF0b3JPcHRpb25zJGRlZixcbiAgICAgIF9nZW5lcmF0b3JPcHRpb25zJGRlZjIgPSBfZ2VuZXJhdG9yT3B0aW9ucy5kZWZhdWx0T3B0aW9ucyxcbiAgICAgIGRlZmF1bHRPcHRpb25zID0gX2dlbmVyYXRvck9wdGlvbnMkZGVmMiA9PT0gdm9pZCAwID8gREVGQVVMVF9PUFRJT05TIDogX2dlbmVyYXRvck9wdGlvbnMkZGVmMjtcbiAgcmV0dXJuIGZ1bmN0aW9uIGNyZWF0ZVBvcHBlcihyZWZlcmVuY2UsIHBvcHBlciwgb3B0aW9ucykge1xuICAgIGlmIChvcHRpb25zID09PSB2b2lkIDApIHtcbiAgICAgIG9wdGlvbnMgPSBkZWZhdWx0T3B0aW9ucztcbiAgICB9XG5cbiAgICB2YXIgc3RhdGUgPSB7XG4gICAgICBwbGFjZW1lbnQ6ICdib3R0b20nLFxuICAgICAgb3JkZXJlZE1vZGlmaWVyczogW10sXG4gICAgICBvcHRpb25zOiBPYmplY3QuYXNzaWduKHt9LCBERUZBVUxUX09QVElPTlMsIGRlZmF1bHRPcHRpb25zKSxcbiAgICAgIG1vZGlmaWVyc0RhdGE6IHt9LFxuICAgICAgZWxlbWVudHM6IHtcbiAgICAgICAgcmVmZXJlbmNlOiByZWZlcmVuY2UsXG4gICAgICAgIHBvcHBlcjogcG9wcGVyXG4gICAgICB9LFxuICAgICAgYXR0cmlidXRlczoge30sXG4gICAgICBzdHlsZXM6IHt9XG4gICAgfTtcbiAgICB2YXIgZWZmZWN0Q2xlYW51cEZucyA9IFtdO1xuICAgIHZhciBpc0Rlc3Ryb3llZCA9IGZhbHNlO1xuICAgIHZhciBpbnN0YW5jZSA9IHtcbiAgICAgIHN0YXRlOiBzdGF0ZSxcbiAgICAgIHNldE9wdGlvbnM6IGZ1bmN0aW9uIHNldE9wdGlvbnMoc2V0T3B0aW9uc0FjdGlvbikge1xuICAgICAgICB2YXIgb3B0aW9ucyA9IHR5cGVvZiBzZXRPcHRpb25zQWN0aW9uID09PSAnZnVuY3Rpb24nID8gc2V0T3B0aW9uc0FjdGlvbihzdGF0ZS5vcHRpb25zKSA6IHNldE9wdGlvbnNBY3Rpb247XG4gICAgICAgIGNsZWFudXBNb2RpZmllckVmZmVjdHMoKTtcbiAgICAgICAgc3RhdGUub3B0aW9ucyA9IE9iamVjdC5hc3NpZ24oe30sIGRlZmF1bHRPcHRpb25zLCBzdGF0ZS5vcHRpb25zLCBvcHRpb25zKTtcbiAgICAgICAgc3RhdGUuc2Nyb2xsUGFyZW50cyA9IHtcbiAgICAgICAgICByZWZlcmVuY2U6IGlzRWxlbWVudChyZWZlcmVuY2UpID8gbGlzdFNjcm9sbFBhcmVudHMocmVmZXJlbmNlKSA6IHJlZmVyZW5jZS5jb250ZXh0RWxlbWVudCA/IGxpc3RTY3JvbGxQYXJlbnRzKHJlZmVyZW5jZS5jb250ZXh0RWxlbWVudCkgOiBbXSxcbiAgICAgICAgICBwb3BwZXI6IGxpc3RTY3JvbGxQYXJlbnRzKHBvcHBlcilcbiAgICAgICAgfTsgLy8gT3JkZXJzIHRoZSBtb2RpZmllcnMgYmFzZWQgb24gdGhlaXIgZGVwZW5kZW5jaWVzIGFuZCBgcGhhc2VgXG4gICAgICAgIC8vIHByb3BlcnRpZXNcblxuICAgICAgICB2YXIgb3JkZXJlZE1vZGlmaWVycyA9IG9yZGVyTW9kaWZpZXJzKG1lcmdlQnlOYW1lKFtdLmNvbmNhdChkZWZhdWx0TW9kaWZpZXJzLCBzdGF0ZS5vcHRpb25zLm1vZGlmaWVycykpKTsgLy8gU3RyaXAgb3V0IGRpc2FibGVkIG1vZGlmaWVyc1xuXG4gICAgICAgIHN0YXRlLm9yZGVyZWRNb2RpZmllcnMgPSBvcmRlcmVkTW9kaWZpZXJzLmZpbHRlcihmdW5jdGlvbiAobSkge1xuICAgICAgICAgIHJldHVybiBtLmVuYWJsZWQ7XG4gICAgICAgIH0pO1xuICAgICAgICBydW5Nb2RpZmllckVmZmVjdHMoKTtcbiAgICAgICAgcmV0dXJuIGluc3RhbmNlLnVwZGF0ZSgpO1xuICAgICAgfSxcbiAgICAgIC8vIFN5bmMgdXBkYXRlIOKAkyBpdCB3aWxsIGFsd2F5cyBiZSBleGVjdXRlZCwgZXZlbiBpZiBub3QgbmVjZXNzYXJ5LiBUaGlzXG4gICAgICAvLyBpcyB1c2VmdWwgZm9yIGxvdyBmcmVxdWVuY3kgdXBkYXRlcyB3aGVyZSBzeW5jIGJlaGF2aW9yIHNpbXBsaWZpZXMgdGhlXG4gICAgICAvLyBsb2dpYy5cbiAgICAgIC8vIEZvciBoaWdoIGZyZXF1ZW5jeSB1cGRhdGVzIChlLmcuIGByZXNpemVgIGFuZCBgc2Nyb2xsYCBldmVudHMpLCBhbHdheXNcbiAgICAgIC8vIHByZWZlciB0aGUgYXN5bmMgUG9wcGVyI3VwZGF0ZSBtZXRob2RcbiAgICAgIGZvcmNlVXBkYXRlOiBmdW5jdGlvbiBmb3JjZVVwZGF0ZSgpIHtcbiAgICAgICAgaWYgKGlzRGVzdHJveWVkKSB7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgdmFyIF9zdGF0ZSRlbGVtZW50cyA9IHN0YXRlLmVsZW1lbnRzLFxuICAgICAgICAgICAgcmVmZXJlbmNlID0gX3N0YXRlJGVsZW1lbnRzLnJlZmVyZW5jZSxcbiAgICAgICAgICAgIHBvcHBlciA9IF9zdGF0ZSRlbGVtZW50cy5wb3BwZXI7IC8vIERvbid0IHByb2NlZWQgaWYgYHJlZmVyZW5jZWAgb3IgYHBvcHBlcmAgYXJlIG5vdCB2YWxpZCBlbGVtZW50c1xuICAgICAgICAvLyBhbnltb3JlXG5cbiAgICAgICAgaWYgKCFhcmVWYWxpZEVsZW1lbnRzKHJlZmVyZW5jZSwgcG9wcGVyKSkge1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfSAvLyBTdG9yZSB0aGUgcmVmZXJlbmNlIGFuZCBwb3BwZXIgcmVjdHMgdG8gYmUgcmVhZCBieSBtb2RpZmllcnNcblxuXG4gICAgICAgIHN0YXRlLnJlY3RzID0ge1xuICAgICAgICAgIHJlZmVyZW5jZTogZ2V0Q29tcG9zaXRlUmVjdChyZWZlcmVuY2UsIGdldE9mZnNldFBhcmVudChwb3BwZXIpLCBzdGF0ZS5vcHRpb25zLnN0cmF0ZWd5ID09PSAnZml4ZWQnKSxcbiAgICAgICAgICBwb3BwZXI6IGdldExheW91dFJlY3QocG9wcGVyKVxuICAgICAgICB9OyAvLyBNb2RpZmllcnMgaGF2ZSB0aGUgYWJpbGl0eSB0byByZXNldCB0aGUgY3VycmVudCB1cGRhdGUgY3ljbGUuIFRoZVxuICAgICAgICAvLyBtb3N0IGNvbW1vbiB1c2UgY2FzZSBmb3IgdGhpcyBpcyB0aGUgYGZsaXBgIG1vZGlmaWVyIGNoYW5naW5nIHRoZVxuICAgICAgICAvLyBwbGFjZW1lbnQsIHdoaWNoIHRoZW4gbmVlZHMgdG8gcmUtcnVuIGFsbCB0aGUgbW9kaWZpZXJzLCBiZWNhdXNlIHRoZVxuICAgICAgICAvLyBsb2dpYyB3YXMgcHJldmlvdXNseSByYW4gZm9yIHRoZSBwcmV2aW91cyBwbGFjZW1lbnQgYW5kIGlzIHRoZXJlZm9yZVxuICAgICAgICAvLyBzdGFsZS9pbmNvcnJlY3RcblxuICAgICAgICBzdGF0ZS5yZXNldCA9IGZhbHNlO1xuICAgICAgICBzdGF0ZS5wbGFjZW1lbnQgPSBzdGF0ZS5vcHRpb25zLnBsYWNlbWVudDsgLy8gT24gZWFjaCB1cGRhdGUgY3ljbGUsIHRoZSBgbW9kaWZpZXJzRGF0YWAgcHJvcGVydHkgZm9yIGVhY2ggbW9kaWZpZXJcbiAgICAgICAgLy8gaXMgZmlsbGVkIHdpdGggdGhlIGluaXRpYWwgZGF0YSBzcGVjaWZpZWQgYnkgdGhlIG1vZGlmaWVyLiBUaGlzIG1lYW5zXG4gICAgICAgIC8vIGl0IGRvZXNuJ3QgcGVyc2lzdCBhbmQgaXMgZnJlc2ggb24gZWFjaCB1cGRhdGUuXG4gICAgICAgIC8vIFRvIGVuc3VyZSBwZXJzaXN0ZW50IGRhdGEsIHVzZSBgJHtuYW1lfSNwZXJzaXN0ZW50YFxuXG4gICAgICAgIHN0YXRlLm9yZGVyZWRNb2RpZmllcnMuZm9yRWFjaChmdW5jdGlvbiAobW9kaWZpZXIpIHtcbiAgICAgICAgICByZXR1cm4gc3RhdGUubW9kaWZpZXJzRGF0YVttb2RpZmllci5uYW1lXSA9IE9iamVjdC5hc3NpZ24oe30sIG1vZGlmaWVyLmRhdGEpO1xuICAgICAgICB9KTtcblxuICAgICAgICBmb3IgKHZhciBpbmRleCA9IDA7IGluZGV4IDwgc3RhdGUub3JkZXJlZE1vZGlmaWVycy5sZW5ndGg7IGluZGV4KyspIHtcbiAgICAgICAgICBpZiAoc3RhdGUucmVzZXQgPT09IHRydWUpIHtcbiAgICAgICAgICAgIHN0YXRlLnJlc2V0ID0gZmFsc2U7XG4gICAgICAgICAgICBpbmRleCA9IC0xO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgdmFyIF9zdGF0ZSRvcmRlcmVkTW9kaWZpZSA9IHN0YXRlLm9yZGVyZWRNb2RpZmllcnNbaW5kZXhdLFxuICAgICAgICAgICAgICBmbiA9IF9zdGF0ZSRvcmRlcmVkTW9kaWZpZS5mbixcbiAgICAgICAgICAgICAgX3N0YXRlJG9yZGVyZWRNb2RpZmllMiA9IF9zdGF0ZSRvcmRlcmVkTW9kaWZpZS5vcHRpb25zLFxuICAgICAgICAgICAgICBfb3B0aW9ucyA9IF9zdGF0ZSRvcmRlcmVkTW9kaWZpZTIgPT09IHZvaWQgMCA/IHt9IDogX3N0YXRlJG9yZGVyZWRNb2RpZmllMixcbiAgICAgICAgICAgICAgbmFtZSA9IF9zdGF0ZSRvcmRlcmVkTW9kaWZpZS5uYW1lO1xuXG4gICAgICAgICAgaWYgKHR5cGVvZiBmbiA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgc3RhdGUgPSBmbih7XG4gICAgICAgICAgICAgIHN0YXRlOiBzdGF0ZSxcbiAgICAgICAgICAgICAgb3B0aW9uczogX29wdGlvbnMsXG4gICAgICAgICAgICAgIG5hbWU6IG5hbWUsXG4gICAgICAgICAgICAgIGluc3RhbmNlOiBpbnN0YW5jZVxuICAgICAgICAgICAgfSkgfHwgc3RhdGU7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgLy8gQXN5bmMgYW5kIG9wdGltaXN0aWNhbGx5IG9wdGltaXplZCB1cGRhdGUg4oCTIGl0IHdpbGwgbm90IGJlIGV4ZWN1dGVkIGlmXG4gICAgICAvLyBub3QgbmVjZXNzYXJ5IChkZWJvdW5jZWQgdG8gcnVuIGF0IG1vc3Qgb25jZS1wZXItdGljaylcbiAgICAgIHVwZGF0ZTogZGVib3VuY2UoZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUpIHtcbiAgICAgICAgICBpbnN0YW5jZS5mb3JjZVVwZGF0ZSgpO1xuICAgICAgICAgIHJlc29sdmUoc3RhdGUpO1xuICAgICAgICB9KTtcbiAgICAgIH0pLFxuICAgICAgZGVzdHJveTogZnVuY3Rpb24gZGVzdHJveSgpIHtcbiAgICAgICAgY2xlYW51cE1vZGlmaWVyRWZmZWN0cygpO1xuICAgICAgICBpc0Rlc3Ryb3llZCA9IHRydWU7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGlmICghYXJlVmFsaWRFbGVtZW50cyhyZWZlcmVuY2UsIHBvcHBlcikpIHtcbiAgICAgIHJldHVybiBpbnN0YW5jZTtcbiAgICB9XG5cbiAgICBpbnN0YW5jZS5zZXRPcHRpb25zKG9wdGlvbnMpLnRoZW4oZnVuY3Rpb24gKHN0YXRlKSB7XG4gICAgICBpZiAoIWlzRGVzdHJveWVkICYmIG9wdGlvbnMub25GaXJzdFVwZGF0ZSkge1xuICAgICAgICBvcHRpb25zLm9uRmlyc3RVcGRhdGUoc3RhdGUpO1xuICAgICAgfVxuICAgIH0pOyAvLyBNb2RpZmllcnMgaGF2ZSB0aGUgYWJpbGl0eSB0byBleGVjdXRlIGFyYml0cmFyeSBjb2RlIGJlZm9yZSB0aGUgZmlyc3RcbiAgICAvLyB1cGRhdGUgY3ljbGUgcnVucy4gVGhleSB3aWxsIGJlIGV4ZWN1dGVkIGluIHRoZSBzYW1lIG9yZGVyIGFzIHRoZSB1cGRhdGVcbiAgICAvLyBjeWNsZS4gVGhpcyBpcyB1c2VmdWwgd2hlbiBhIG1vZGlmaWVyIGFkZHMgc29tZSBwZXJzaXN0ZW50IGRhdGEgdGhhdFxuICAgIC8vIG90aGVyIG1vZGlmaWVycyBuZWVkIHRvIHVzZSwgYnV0IHRoZSBtb2RpZmllciBpcyBydW4gYWZ0ZXIgdGhlIGRlcGVuZGVudFxuICAgIC8vIG9uZS5cblxuICAgIGZ1bmN0aW9uIHJ1bk1vZGlmaWVyRWZmZWN0cygpIHtcbiAgICAgIHN0YXRlLm9yZGVyZWRNb2RpZmllcnMuZm9yRWFjaChmdW5jdGlvbiAoX3JlZikge1xuICAgICAgICB2YXIgbmFtZSA9IF9yZWYubmFtZSxcbiAgICAgICAgICAgIF9yZWYkb3B0aW9ucyA9IF9yZWYub3B0aW9ucyxcbiAgICAgICAgICAgIG9wdGlvbnMgPSBfcmVmJG9wdGlvbnMgPT09IHZvaWQgMCA/IHt9IDogX3JlZiRvcHRpb25zLFxuICAgICAgICAgICAgZWZmZWN0ID0gX3JlZi5lZmZlY3Q7XG5cbiAgICAgICAgaWYgKHR5cGVvZiBlZmZlY3QgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICB2YXIgY2xlYW51cEZuID0gZWZmZWN0KHtcbiAgICAgICAgICAgIHN0YXRlOiBzdGF0ZSxcbiAgICAgICAgICAgIG5hbWU6IG5hbWUsXG4gICAgICAgICAgICBpbnN0YW5jZTogaW5zdGFuY2UsXG4gICAgICAgICAgICBvcHRpb25zOiBvcHRpb25zXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICB2YXIgbm9vcEZuID0gZnVuY3Rpb24gbm9vcEZuKCkge307XG5cbiAgICAgICAgICBlZmZlY3RDbGVhbnVwRm5zLnB1c2goY2xlYW51cEZuIHx8IG5vb3BGbik7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIGNsZWFudXBNb2RpZmllckVmZmVjdHMoKSB7XG4gICAgICBlZmZlY3RDbGVhbnVwRm5zLmZvckVhY2goZnVuY3Rpb24gKGZuKSB7XG4gICAgICAgIHJldHVybiBmbigpO1xuICAgICAgfSk7XG4gICAgICBlZmZlY3RDbGVhbnVwRm5zID0gW107XG4gICAgfVxuXG4gICAgcmV0dXJuIGluc3RhbmNlO1xuICB9O1xufVxuZXhwb3J0IHZhciBjcmVhdGVQb3BwZXIgPSAvKiNfX1BVUkVfXyovcG9wcGVyR2VuZXJhdG9yKCk7IC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBpbXBvcnQvbm8tdW51c2VkLW1vZHVsZXNcblxuZXhwb3J0IHsgZGV0ZWN0T3ZlcmZsb3cgfTsiXSwibmFtZXMiOlsiZ2V0Q29tcG9zaXRlUmVjdCIsImdldExheW91dFJlY3QiLCJsaXN0U2Nyb2xsUGFyZW50cyIsImdldE9mZnNldFBhcmVudCIsIm9yZGVyTW9kaWZpZXJzIiwiZGVib3VuY2UiLCJtZXJnZUJ5TmFtZSIsImRldGVjdE92ZXJmbG93IiwiaXNFbGVtZW50IiwiREVGQVVMVF9PUFRJT05TIiwicGxhY2VtZW50IiwibW9kaWZpZXJzIiwic3RyYXRlZ3kiLCJhcmVWYWxpZEVsZW1lbnRzIiwiX2xlbiIsImFyZ3VtZW50cyIsImxlbmd0aCIsImFyZ3MiLCJBcnJheSIsIl9rZXkiLCJzb21lIiwiZWxlbWVudCIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsInBvcHBlckdlbmVyYXRvciIsImdlbmVyYXRvck9wdGlvbnMiLCJfZ2VuZXJhdG9yT3B0aW9ucyIsIl9nZW5lcmF0b3JPcHRpb25zJGRlZiIsImRlZmF1bHRNb2RpZmllcnMiLCJfZ2VuZXJhdG9yT3B0aW9ucyRkZWYyIiwiZGVmYXVsdE9wdGlvbnMiLCJjcmVhdGVQb3BwZXIiLCJyZWZlcmVuY2UiLCJwb3BwZXIiLCJvcHRpb25zIiwic3RhdGUiLCJvcmRlcmVkTW9kaWZpZXJzIiwiT2JqZWN0IiwiYXNzaWduIiwibW9kaWZpZXJzRGF0YSIsImVsZW1lbnRzIiwiYXR0cmlidXRlcyIsInN0eWxlcyIsImVmZmVjdENsZWFudXBGbnMiLCJpc0Rlc3Ryb3llZCIsImluc3RhbmNlIiwic2V0T3B0aW9ucyIsInNldE9wdGlvbnNBY3Rpb24iLCJjbGVhbnVwTW9kaWZpZXJFZmZlY3RzIiwic2Nyb2xsUGFyZW50cyIsImNvbnRleHRFbGVtZW50IiwiY29uY2F0IiwiZmlsdGVyIiwibSIsImVuYWJsZWQiLCJydW5Nb2RpZmllckVmZmVjdHMiLCJ1cGRhdGUiLCJmb3JjZVVwZGF0ZSIsIl9zdGF0ZSRlbGVtZW50cyIsInJlY3RzIiwicmVzZXQiLCJmb3JFYWNoIiwibW9kaWZpZXIiLCJuYW1lIiwiZGF0YSIsImluZGV4IiwiX3N0YXRlJG9yZGVyZWRNb2RpZmllIiwiZm4iLCJfc3RhdGUkb3JkZXJlZE1vZGlmaWUyIiwiX29wdGlvbnMiLCJQcm9taXNlIiwicmVzb2x2ZSIsImRlc3Ryb3kiLCJ0aGVuIiwib25GaXJzdFVwZGF0ZSIsIl9yZWYiLCJfcmVmJG9wdGlvbnMiLCJlZmZlY3QiLCJjbGVhbnVwRm4iLCJub29wRm4iLCJwdXNoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/createPopper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/contains.js":
/*!***************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/contains.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ contains)\n/* harmony export */ });\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./instanceOf.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n\nfunction contains(parent, child) {\n    var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n    if (parent.contains(child)) {\n        return true;\n    } else if (rootNode && (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isShadowRoot)(rootNode)) {\n        var next = child;\n        do {\n            if (next && parent.isSameNode(next)) {\n                return true;\n            } // $FlowFixMe[prop-missing]: need a better way to handle this...\n            next = next.parentNode || next.host;\n        }while (next);\n    } // Give up, the result is false\n    return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9jb250YWlucy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQztBQUNoQyxTQUFTQyxTQUFTQyxNQUFNLEVBQUVDLEtBQUs7SUFDNUMsSUFBSUMsV0FBV0QsTUFBTUUsV0FBVyxJQUFJRixNQUFNRSxXQUFXLElBQUksMkNBQTJDO0lBRXBHLElBQUlILE9BQU9ELFFBQVEsQ0FBQ0UsUUFBUTtRQUMxQixPQUFPO0lBQ1QsT0FDSyxJQUFJQyxZQUFZSiw0REFBWUEsQ0FBQ0ksV0FBVztRQUN6QyxJQUFJRSxPQUFPSDtRQUVYLEdBQUc7WUFDRCxJQUFJRyxRQUFRSixPQUFPSyxVQUFVLENBQUNELE9BQU87Z0JBQ25DLE9BQU87WUFDVCxFQUFFLGdFQUFnRTtZQUdsRUEsT0FBT0EsS0FBS0UsVUFBVSxJQUFJRixLQUFLRyxJQUFJO1FBQ3JDLFFBQVNILE1BQU07SUFDakIsRUFBRSwrQkFBK0I7SUFHbkMsT0FBTztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9jb250YWlucy5qcz9hMjExIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzU2hhZG93Um9vdCB9IGZyb20gXCIuL2luc3RhbmNlT2YuanNcIjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNvbnRhaW5zKHBhcmVudCwgY2hpbGQpIHtcbiAgdmFyIHJvb3ROb2RlID0gY2hpbGQuZ2V0Um9vdE5vZGUgJiYgY2hpbGQuZ2V0Um9vdE5vZGUoKTsgLy8gRmlyc3QsIGF0dGVtcHQgd2l0aCBmYXN0ZXIgbmF0aXZlIG1ldGhvZFxuXG4gIGlmIChwYXJlbnQuY29udGFpbnMoY2hpbGQpKSB7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0gLy8gdGhlbiBmYWxsYmFjayB0byBjdXN0b20gaW1wbGVtZW50YXRpb24gd2l0aCBTaGFkb3cgRE9NIHN1cHBvcnRcbiAgZWxzZSBpZiAocm9vdE5vZGUgJiYgaXNTaGFkb3dSb290KHJvb3ROb2RlKSkge1xuICAgICAgdmFyIG5leHQgPSBjaGlsZDtcblxuICAgICAgZG8ge1xuICAgICAgICBpZiAobmV4dCAmJiBwYXJlbnQuaXNTYW1lTm9kZShuZXh0KSkge1xuICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9IC8vICRGbG93Rml4TWVbcHJvcC1taXNzaW5nXTogbmVlZCBhIGJldHRlciB3YXkgdG8gaGFuZGxlIHRoaXMuLi5cblxuXG4gICAgICAgIG5leHQgPSBuZXh0LnBhcmVudE5vZGUgfHwgbmV4dC5ob3N0O1xuICAgICAgfSB3aGlsZSAobmV4dCk7XG4gICAgfSAvLyBHaXZlIHVwLCB0aGUgcmVzdWx0IGlzIGZhbHNlXG5cblxuICByZXR1cm4gZmFsc2U7XG59Il0sIm5hbWVzIjpbImlzU2hhZG93Um9vdCIsImNvbnRhaW5zIiwicGFyZW50IiwiY2hpbGQiLCJyb290Tm9kZSIsImdldFJvb3ROb2RlIiwibmV4dCIsImlzU2FtZU5vZGUiLCJwYXJlbnROb2RlIiwiaG9zdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/contains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getBoundingClientRect)\n/* harmony export */ });\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./instanceOf.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n/* harmony import */ var _utils_math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/math.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/math.js\");\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getWindow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n/* harmony import */ var _isLayoutViewport_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./isLayoutViewport.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js\");\n\n\n\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy) {\n    if (includeScale === void 0) {\n        includeScale = false;\n    }\n    if (isFixedStrategy === void 0) {\n        isFixedStrategy = false;\n    }\n    var clientRect = element.getBoundingClientRect();\n    var scaleX = 1;\n    var scaleY = 1;\n    if (includeScale && (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n        scaleX = element.offsetWidth > 0 ? (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_1__.round)(clientRect.width) / element.offsetWidth || 1 : 1;\n        scaleY = element.offsetHeight > 0 ? (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_1__.round)(clientRect.height) / element.offsetHeight || 1 : 1;\n    }\n    var _ref = (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isElement)(element) ? (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(element) : window, visualViewport = _ref.visualViewport;\n    var addVisualOffsets = !(0,_isLayoutViewport_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])() && isFixedStrategy;\n    var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n    var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n    var width = clientRect.width / scaleX;\n    var height = clientRect.height / scaleY;\n    return {\n        width: width,\n        height: height,\n        top: y,\n        right: x + width,\n        bottom: y + height,\n        left: x,\n        x: x,\n        y: y\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getClippingRect)\n/* harmony export */ });\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../enums.js */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _getViewportRect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getViewportRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js\");\n/* harmony import */ var _getDocumentRect_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./getDocumentRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js\");\n/* harmony import */ var _listScrollParents_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./listScrollParents.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js\");\n/* harmony import */ var _getOffsetParent_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./getOffsetParent.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js\");\n/* harmony import */ var _getDocumentElement_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./getDocumentElement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _getComputedStyle_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./getComputedStyle.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js\");\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./instanceOf.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n/* harmony import */ var _getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getBoundingClientRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js\");\n/* harmony import */ var _getParentNode_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./getParentNode.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/contains.js\");\n/* harmony import */ var _getNodeName_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./getNodeName.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n/* harmony import */ var _utils_rectToClientRect_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/rectToClientRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/rectToClientRect.js\");\n/* harmony import */ var _utils_math_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../utils/math.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/math.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction getInnerBoundingClientRect(element, strategy) {\n    var rect = (0,_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element, false, strategy === \"fixed\");\n    rect.top = rect.top + element.clientTop;\n    rect.left = rect.left + element.clientLeft;\n    rect.bottom = rect.top + element.clientHeight;\n    rect.right = rect.left + element.clientWidth;\n    rect.width = element.clientWidth;\n    rect.height = element.clientHeight;\n    rect.x = rect.left;\n    rect.y = rect.top;\n    return rect;\n}\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n    return clippingParent === _enums_js__WEBPACK_IMPORTED_MODULE_1__.viewport ? (0,_utils_rectToClientRect_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_getViewportRect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(element, strategy)) : (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_4__.isElement)(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : (0,_utils_rectToClientRect_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_getDocumentRect_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\nfunction getClippingParents(element) {\n    var clippingParents = (0,_listScrollParents_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_getParentNode_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(element));\n    var canEscapeClipping = [\n        \"absolute\",\n        \"fixed\"\n    ].indexOf((0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(element).position) >= 0;\n    var clipperElement = canEscapeClipping && (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_4__.isHTMLElement)(element) ? (0,_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(element) : element;\n    if (!(0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_4__.isElement)(clipperElement)) {\n        return [];\n    } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n    return clippingParents.filter(function(clippingParent) {\n        return (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_4__.isElement)(clippingParent) && (0,_contains_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(clippingParent, clipperElement) && (0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(clippingParent) !== \"body\";\n    });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\nfunction getClippingRect(element, boundary, rootBoundary, strategy) {\n    var mainClippingParents = boundary === \"clippingParents\" ? getClippingParents(element) : [].concat(boundary);\n    var clippingParents = [].concat(mainClippingParents, [\n        rootBoundary\n    ]);\n    var firstClippingParent = clippingParents[0];\n    var clippingRect = clippingParents.reduce(function(accRect, clippingParent) {\n        var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n        accRect.top = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_13__.max)(rect.top, accRect.top);\n        accRect.right = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_13__.min)(rect.right, accRect.right);\n        accRect.bottom = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_13__.min)(rect.bottom, accRect.bottom);\n        accRect.left = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_13__.max)(rect.left, accRect.left);\n        return accRect;\n    }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n    clippingRect.width = clippingRect.right - clippingRect.left;\n    clippingRect.height = clippingRect.bottom - clippingRect.top;\n    clippingRect.x = clippingRect.left;\n    clippingRect.y = clippingRect.top;\n    return clippingRect;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getCompositeRect)\n/* harmony export */ });\n/* harmony import */ var _getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getBoundingClientRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js\");\n/* harmony import */ var _getNodeScroll_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./getNodeScroll.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js\");\n/* harmony import */ var _getNodeName_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getNodeName.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./instanceOf.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n/* harmony import */ var _getWindowScrollBarX_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./getWindowScrollBarX.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js\");\n/* harmony import */ var _getDocumentElement_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getDocumentElement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _isScrollParent_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./isScrollParent.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js\");\n/* harmony import */ var _utils_math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/math.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/math.js\");\n\n\n\n\n\n\n\n\nfunction isElementScaled(element) {\n    var rect = element.getBoundingClientRect();\n    var scaleX = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_0__.round)(rect.width) / element.offsetWidth || 1;\n    var scaleY = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_0__.round)(rect.height) / element.offsetHeight || 1;\n    return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\nfunction getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n    if (isFixed === void 0) {\n        isFixed = false;\n    }\n    var isOffsetParentAnElement = (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(offsetParent);\n    var offsetParentIsScaled = (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(offsetParent) && isElementScaled(offsetParent);\n    var documentElement = (0,_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(offsetParent);\n    var rect = (0,_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n    var scroll = {\n        scrollLeft: 0,\n        scrollTop: 0\n    };\n    var offsets = {\n        x: 0,\n        y: 0\n    };\n    if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n        if ((0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(offsetParent) !== \"body\" || // https://github.com/popperjs/popper-core/issues/1078\n        (0,_isScrollParent_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(documentElement)) {\n            scroll = (0,_getNodeScroll_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(offsetParent);\n        }\n        if ((0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(offsetParent)) {\n            offsets = (0,_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(offsetParent, true);\n            offsets.x += offsetParent.clientLeft;\n            offsets.y += offsetParent.clientTop;\n        } else if (documentElement) {\n            offsets.x = (0,_getWindowScrollBarX_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(documentElement);\n        }\n    }\n    return {\n        x: rect.left + scroll.scrollLeft - offsets.x,\n        y: rect.top + scroll.scrollTop - offsets.y,\n        width: rect.width,\n        height: rect.height\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getComputedStyle)\n/* harmony export */ });\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getWindow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n\nfunction getComputedStyle(element) {\n    return (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element).getComputedStyle(element);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXRDb21wdXRlZFN0eWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDO0FBQ3hCLFNBQVNDLGlCQUFpQkMsT0FBTztJQUM5QyxPQUFPRix5REFBU0EsQ0FBQ0UsU0FBU0QsZ0JBQWdCLENBQUNDO0FBQzdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXRDb21wdXRlZFN0eWxlLmpzPzA5ZmMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGdldFdpbmRvdyBmcm9tIFwiLi9nZXRXaW5kb3cuanNcIjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdldENvbXB1dGVkU3R5bGUoZWxlbWVudCkge1xuICByZXR1cm4gZ2V0V2luZG93KGVsZW1lbnQpLmdldENvbXB1dGVkU3R5bGUoZWxlbWVudCk7XG59Il0sIm5hbWVzIjpbImdldFdpbmRvdyIsImdldENvbXB1dGVkU3R5bGUiLCJlbGVtZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getDocumentElement)\n/* harmony export */ });\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./instanceOf.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n\nfunction getDocumentElement(element) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return (((0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isElement)(element) ? element.ownerDocument : element.document) || window.document).documentElement;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXREb2N1bWVudEVsZW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEM7QUFDN0IsU0FBU0MsbUJBQW1CQyxPQUFPO0lBQ2hELG1FQUFtRTtJQUNuRSxPQUFPLENBQUMsQ0FBQ0YseURBQVNBLENBQUNFLFdBQVdBLFFBQVFDLGFBQWEsR0FDbkRELFFBQVFFLFFBQVEsS0FBS0MsT0FBT0QsUUFBUSxFQUFFRSxlQUFlO0FBQ3ZEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXREb2N1bWVudEVsZW1lbnQuanM/M2E4YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc0VsZW1lbnQgfSBmcm9tIFwiLi9pbnN0YW5jZU9mLmpzXCI7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXREb2N1bWVudEVsZW1lbnQoZWxlbWVudCkge1xuICAvLyAkRmxvd0ZpeE1lW2luY29tcGF0aWJsZS1yZXR1cm5dOiBhc3N1bWUgYm9keSBpcyBhbHdheXMgYXZhaWxhYmxlXG4gIHJldHVybiAoKGlzRWxlbWVudChlbGVtZW50KSA/IGVsZW1lbnQub3duZXJEb2N1bWVudCA6IC8vICRGbG93Rml4TWVbcHJvcC1taXNzaW5nXVxuICBlbGVtZW50LmRvY3VtZW50KSB8fCB3aW5kb3cuZG9jdW1lbnQpLmRvY3VtZW50RWxlbWVudDtcbn0iXSwibmFtZXMiOlsiaXNFbGVtZW50IiwiZ2V0RG9jdW1lbnRFbGVtZW50IiwiZWxlbWVudCIsIm93bmVyRG9jdW1lbnQiLCJkb2N1bWVudCIsIndpbmRvdyIsImRvY3VtZW50RWxlbWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getDocumentRect)\n/* harmony export */ });\n/* harmony import */ var _getDocumentElement_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getDocumentElement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _getComputedStyle_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getComputedStyle.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js\");\n/* harmony import */ var _getWindowScrollBarX_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getWindowScrollBarX.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js\");\n/* harmony import */ var _getWindowScroll_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getWindowScroll.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js\");\n/* harmony import */ var _utils_math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/math.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/math.js\");\n\n\n\n\n // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\nfunction getDocumentRect(element) {\n    var _element$ownerDocumen;\n    var html = (0,_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element);\n    var winScroll = (0,_getWindowScroll_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element);\n    var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n    var width = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_2__.max)(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n    var height = (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_2__.max)(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n    var x = -winScroll.scrollLeft + (0,_getWindowScrollBarX_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(element);\n    var y = -winScroll.scrollTop;\n    if ((0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(body || html).direction === \"rtl\") {\n        x += (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_2__.max)(html.clientWidth, body ? body.clientWidth : 0) - width;\n    }\n    return {\n        width: width,\n        height: height,\n        x: x,\n        y: y\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getHTMLElementScroll)\n/* harmony export */ });\nfunction getHTMLElementScroll(element) {\n    return {\n        scrollLeft: element.scrollLeft,\n        scrollTop: element.scrollTop\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXRIVE1MRWxlbWVudFNjcm9sbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EscUJBQXFCQyxPQUFPO0lBQ2xELE9BQU87UUFDTEMsWUFBWUQsUUFBUUMsVUFBVTtRQUM5QkMsV0FBV0YsUUFBUUUsU0FBUztJQUM5QjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXRIVE1MRWxlbWVudFNjcm9sbC5qcz81NTE1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdldEhUTUxFbGVtZW50U2Nyb2xsKGVsZW1lbnQpIHtcbiAgcmV0dXJuIHtcbiAgICBzY3JvbGxMZWZ0OiBlbGVtZW50LnNjcm9sbExlZnQsXG4gICAgc2Nyb2xsVG9wOiBlbGVtZW50LnNjcm9sbFRvcFxuICB9O1xufSJdLCJuYW1lcyI6WyJnZXRIVE1MRWxlbWVudFNjcm9sbCIsImVsZW1lbnQiLCJzY3JvbGxMZWZ0Iiwic2Nyb2xsVG9wIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js":
/*!********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getLayoutRect)\n/* harmony export */ });\n/* harmony import */ var _getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getBoundingClientRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js\");\n // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\nfunction getLayoutRect(element) {\n    var clientRect = (0,_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element); // Use the clientRect sizes if it's not been transformed.\n    // Fixes https://github.com/popperjs/popper-core/issues/1223\n    var width = element.offsetWidth;\n    var height = element.offsetHeight;\n    if (Math.abs(clientRect.width - width) <= 1) {\n        width = clientRect.width;\n    }\n    if (Math.abs(clientRect.height - height) <= 1) {\n        height = clientRect.height;\n    }\n    return {\n        x: element.offsetLeft,\n        y: element.offsetTop,\n        width: width,\n        height: height\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js":
/*!******************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getNodeName)\n/* harmony export */ });\nfunction getNodeName(element) {\n    return element ? (element.nodeName || \"\").toLowerCase() : null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXROb2RlTmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsWUFBWUMsT0FBTztJQUN6QyxPQUFPQSxVQUFVLENBQUNBLFFBQVFDLFFBQVEsSUFBSSxFQUFDLEVBQUdDLFdBQVcsS0FBSztBQUM1RCIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL0Bwb3BwZXJqcy9jb3JlL2xpYi9kb20tdXRpbHMvZ2V0Tm9kZU5hbWUuanM/NzdjYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXROb2RlTmFtZShlbGVtZW50KSB7XG4gIHJldHVybiBlbGVtZW50ID8gKGVsZW1lbnQubm9kZU5hbWUgfHwgJycpLnRvTG93ZXJDYXNlKCkgOiBudWxsO1xufSJdLCJuYW1lcyI6WyJnZXROb2RlTmFtZSIsImVsZW1lbnQiLCJub2RlTmFtZSIsInRvTG93ZXJDYXNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js":
/*!********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getNodeScroll)\n/* harmony export */ });\n/* harmony import */ var _getWindowScroll_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getWindowScroll.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js\");\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getWindow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./instanceOf.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n/* harmony import */ var _getHTMLElementScroll_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getHTMLElementScroll.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js\");\n\n\n\n\nfunction getNodeScroll(node) {\n    if (node === (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node) || !(0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(node)) {\n        return (0,_getWindowScroll_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node);\n    } else {\n        return (0,_getHTMLElementScroll_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(node);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXROb2RlU2Nyb2xsLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW1EO0FBQ1o7QUFDUztBQUNhO0FBQzlDLFNBQVNJLGNBQWNDLElBQUk7SUFDeEMsSUFBSUEsU0FBU0oseURBQVNBLENBQUNJLFNBQVMsQ0FBQ0gsNkRBQWFBLENBQUNHLE9BQU87UUFDcEQsT0FBT0wsK0RBQWVBLENBQUNLO0lBQ3pCLE9BQU87UUFDTCxPQUFPRixvRUFBb0JBLENBQUNFO0lBQzlCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvZG9tLXV0aWxzL2dldE5vZGVTY3JvbGwuanM/MjgyMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZ2V0V2luZG93U2Nyb2xsIGZyb20gXCIuL2dldFdpbmRvd1Njcm9sbC5qc1wiO1xuaW1wb3J0IGdldFdpbmRvdyBmcm9tIFwiLi9nZXRXaW5kb3cuanNcIjtcbmltcG9ydCB7IGlzSFRNTEVsZW1lbnQgfSBmcm9tIFwiLi9pbnN0YW5jZU9mLmpzXCI7XG5pbXBvcnQgZ2V0SFRNTEVsZW1lbnRTY3JvbGwgZnJvbSBcIi4vZ2V0SFRNTEVsZW1lbnRTY3JvbGwuanNcIjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdldE5vZGVTY3JvbGwobm9kZSkge1xuICBpZiAobm9kZSA9PT0gZ2V0V2luZG93KG5vZGUpIHx8ICFpc0hUTUxFbGVtZW50KG5vZGUpKSB7XG4gICAgcmV0dXJuIGdldFdpbmRvd1Njcm9sbChub2RlKTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gZ2V0SFRNTEVsZW1lbnRTY3JvbGwobm9kZSk7XG4gIH1cbn0iXSwibmFtZXMiOlsiZ2V0V2luZG93U2Nyb2xsIiwiZ2V0V2luZG93IiwiaXNIVE1MRWxlbWVudCIsImdldEhUTUxFbGVtZW50U2Nyb2xsIiwiZ2V0Tm9kZVNjcm9sbCIsIm5vZGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getOffsetParent)\n/* harmony export */ });\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./getWindow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n/* harmony import */ var _getNodeName_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getNodeName.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n/* harmony import */ var _getComputedStyle_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getComputedStyle.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js\");\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./instanceOf.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n/* harmony import */ var _isTableElement_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./isTableElement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/isTableElement.js\");\n/* harmony import */ var _getParentNode_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getParentNode.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js\");\n/* harmony import */ var _utils_userAgent_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/userAgent.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/userAgent.js\");\n\n\n\n\n\n\n\nfunction getTrueOffsetParent(element) {\n    if (!(0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || // https://github.com/popperjs/popper-core/issues/837\n    (0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element).position === \"fixed\") {\n        return null;\n    }\n    return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\nfunction getContainingBlock(element) {\n    var isFirefox = /firefox/i.test((0,_utils_userAgent_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])());\n    var isIE = /Trident/i.test((0,_utils_userAgent_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])());\n    if (isIE && (0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n        // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n        var elementCss = (0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element);\n        if (elementCss.position === \"fixed\") {\n            return null;\n        }\n    }\n    var currentNode = (0,_getParentNode_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(element);\n    if ((0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isShadowRoot)(currentNode)) {\n        currentNode = currentNode.host;\n    }\n    while((0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(currentNode) && [\n        \"html\",\n        \"body\"\n    ].indexOf((0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(currentNode)) < 0){\n        var css = (0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n        // create a containing block.\n        // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n        if (css.transform !== \"none\" || css.perspective !== \"none\" || css.contain === \"paint\" || [\n            \"transform\",\n            \"perspective\"\n        ].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === \"filter\" || isFirefox && css.filter && css.filter !== \"none\") {\n            return currentNode;\n        } else {\n            currentNode = currentNode.parentNode;\n        }\n    }\n    return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element) {\n    var window = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(element);\n    var offsetParent = getTrueOffsetParent(element);\n    while(offsetParent && (0,_isTableElement_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(offsetParent) && (0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(offsetParent).position === \"static\"){\n        offsetParent = getTrueOffsetParent(offsetParent);\n    }\n    if (offsetParent && ((0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(offsetParent) === \"html\" || (0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(offsetParent) === \"body\" && (0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(offsetParent).position === \"static\")) {\n        return window;\n    }\n    return offsetParent || getContainingBlock(element) || window;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js":
/*!********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getParentNode)\n/* harmony export */ });\n/* harmony import */ var _getNodeName_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getNodeName.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n/* harmony import */ var _getDocumentElement_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getDocumentElement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./instanceOf.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n\n\n\nfunction getParentNode(element) {\n    if ((0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element) === \"html\") {\n        return element;\n    }\n    return(// $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ((0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_1__.isShadowRoot)(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    (0,_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(element) // fallback\n    );\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXRQYXJlbnROb2RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMkM7QUFDYztBQUNWO0FBQ2hDLFNBQVNHLGNBQWNDLE9BQU87SUFDM0MsSUFBSUosMkRBQVdBLENBQUNJLGFBQWEsUUFBUTtRQUNuQyxPQUFPQTtJQUNUO0lBRUEsT0FDRSxrQ0FBa0M7SUFDbEMsMkJBQTJCO0lBQzNCQSxRQUFRQyxZQUFZLElBQUksMkRBQTJEO0lBQ25GRCxRQUFRRSxVQUFVLElBQ2xCSixDQUFBQSw0REFBWUEsQ0FBQ0UsV0FBV0EsUUFBUUcsSUFBSSxHQUFHLElBQUcsS0FBTSxzQkFBc0I7SUFDdEUsdURBQXVEO0lBQ3ZETixrRUFBa0JBLENBQUNHLFNBQVMsV0FBVzs7QUFHM0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvZG9tLXV0aWxzL2dldFBhcmVudE5vZGUuanM/NWNiZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZ2V0Tm9kZU5hbWUgZnJvbSBcIi4vZ2V0Tm9kZU5hbWUuanNcIjtcbmltcG9ydCBnZXREb2N1bWVudEVsZW1lbnQgZnJvbSBcIi4vZ2V0RG9jdW1lbnRFbGVtZW50LmpzXCI7XG5pbXBvcnQgeyBpc1NoYWRvd1Jvb3QgfSBmcm9tIFwiLi9pbnN0YW5jZU9mLmpzXCI7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRQYXJlbnROb2RlKGVsZW1lbnQpIHtcbiAgaWYgKGdldE5vZGVOYW1lKGVsZW1lbnQpID09PSAnaHRtbCcpIHtcbiAgICByZXR1cm4gZWxlbWVudDtcbiAgfVxuXG4gIHJldHVybiAoLy8gdGhpcyBpcyBhIHF1aWNrZXIgKGJ1dCBsZXNzIHR5cGUgc2FmZSkgd2F5IHRvIHNhdmUgcXVpdGUgc29tZSBieXRlcyBmcm9tIHRoZSBidW5kbGVcbiAgICAvLyAkRmxvd0ZpeE1lW2luY29tcGF0aWJsZS1yZXR1cm5dXG4gICAgLy8gJEZsb3dGaXhNZVtwcm9wLW1pc3NpbmddXG4gICAgZWxlbWVudC5hc3NpZ25lZFNsb3QgfHwgLy8gc3RlcCBpbnRvIHRoZSBzaGFkb3cgRE9NIG9mIHRoZSBwYXJlbnQgb2YgYSBzbG90dGVkIG5vZGVcbiAgICBlbGVtZW50LnBhcmVudE5vZGUgfHwgKCAvLyBET00gRWxlbWVudCBkZXRlY3RlZFxuICAgIGlzU2hhZG93Um9vdChlbGVtZW50KSA/IGVsZW1lbnQuaG9zdCA6IG51bGwpIHx8IC8vIFNoYWRvd1Jvb3QgZGV0ZWN0ZWRcbiAgICAvLyAkRmxvd0ZpeE1lW2luY29tcGF0aWJsZS1jYWxsXTogSFRNTEVsZW1lbnQgaXMgYSBOb2RlXG4gICAgZ2V0RG9jdW1lbnRFbGVtZW50KGVsZW1lbnQpIC8vIGZhbGxiYWNrXG5cbiAgKTtcbn0iXSwibmFtZXMiOlsiZ2V0Tm9kZU5hbWUiLCJnZXREb2N1bWVudEVsZW1lbnQiLCJpc1NoYWRvd1Jvb3QiLCJnZXRQYXJlbnROb2RlIiwiZWxlbWVudCIsImFzc2lnbmVkU2xvdCIsInBhcmVudE5vZGUiLCJob3N0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getScrollParent)\n/* harmony export */ });\n/* harmony import */ var _getParentNode_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getParentNode.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js\");\n/* harmony import */ var _isScrollParent_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isScrollParent.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js\");\n/* harmony import */ var _getNodeName_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getNodeName.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n/* harmony import */ var _instanceOf_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./instanceOf.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n\n\n\n\nfunction getScrollParent(node) {\n    if ([\n        \"html\",\n        \"body\",\n        \"#document\"\n    ].indexOf((0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node)) >= 0) {\n        // $FlowFixMe[incompatible-return]: assume body is always available\n        return node.ownerDocument.body;\n    }\n    if ((0,_instanceOf_js__WEBPACK_IMPORTED_MODULE_1__.isHTMLElement)(node) && (0,_isScrollParent_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node)) {\n        return node;\n    }\n    return getScrollParent((0,_getParentNode_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(node));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXRTY3JvbGxQYXJlbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBK0M7QUFDRTtBQUNOO0FBQ0s7QUFDakMsU0FBU0ksZ0JBQWdCQyxJQUFJO0lBQzFDLElBQUk7UUFBQztRQUFRO1FBQVE7S0FBWSxDQUFDQyxPQUFPLENBQUNKLDJEQUFXQSxDQUFDRyxVQUFVLEdBQUc7UUFDakUsbUVBQW1FO1FBQ25FLE9BQU9BLEtBQUtFLGFBQWEsQ0FBQ0MsSUFBSTtJQUNoQztJQUVBLElBQUlMLDZEQUFhQSxDQUFDRSxTQUFTSiw4REFBY0EsQ0FBQ0ksT0FBTztRQUMvQyxPQUFPQTtJQUNUO0lBRUEsT0FBT0QsZ0JBQWdCSiw2REFBYUEsQ0FBQ0s7QUFDdkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvZG9tLXV0aWxzL2dldFNjcm9sbFBhcmVudC5qcz8xNzI0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBnZXRQYXJlbnROb2RlIGZyb20gXCIuL2dldFBhcmVudE5vZGUuanNcIjtcbmltcG9ydCBpc1Njcm9sbFBhcmVudCBmcm9tIFwiLi9pc1Njcm9sbFBhcmVudC5qc1wiO1xuaW1wb3J0IGdldE5vZGVOYW1lIGZyb20gXCIuL2dldE5vZGVOYW1lLmpzXCI7XG5pbXBvcnQgeyBpc0hUTUxFbGVtZW50IH0gZnJvbSBcIi4vaW5zdGFuY2VPZi5qc1wiO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZ2V0U2Nyb2xsUGFyZW50KG5vZGUpIHtcbiAgaWYgKFsnaHRtbCcsICdib2R5JywgJyNkb2N1bWVudCddLmluZGV4T2YoZ2V0Tm9kZU5hbWUobm9kZSkpID49IDApIHtcbiAgICAvLyAkRmxvd0ZpeE1lW2luY29tcGF0aWJsZS1yZXR1cm5dOiBhc3N1bWUgYm9keSBpcyBhbHdheXMgYXZhaWxhYmxlXG4gICAgcmV0dXJuIG5vZGUub3duZXJEb2N1bWVudC5ib2R5O1xuICB9XG5cbiAgaWYgKGlzSFRNTEVsZW1lbnQobm9kZSkgJiYgaXNTY3JvbGxQYXJlbnQobm9kZSkpIHtcbiAgICByZXR1cm4gbm9kZTtcbiAgfVxuXG4gIHJldHVybiBnZXRTY3JvbGxQYXJlbnQoZ2V0UGFyZW50Tm9kZShub2RlKSk7XG59Il0sIm5hbWVzIjpbImdldFBhcmVudE5vZGUiLCJpc1Njcm9sbFBhcmVudCIsImdldE5vZGVOYW1lIiwiaXNIVE1MRWxlbWVudCIsImdldFNjcm9sbFBhcmVudCIsIm5vZGUiLCJpbmRleE9mIiwib3duZXJEb2N1bWVudCIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getViewportRect)\n/* harmony export */ });\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getWindow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n/* harmony import */ var _getDocumentElement_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getDocumentElement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _getWindowScrollBarX_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getWindowScrollBarX.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js\");\n/* harmony import */ var _isLayoutViewport_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isLayoutViewport.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js\");\n\n\n\n\nfunction getViewportRect(element, strategy) {\n    var win = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element);\n    var html = (0,_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element);\n    var visualViewport = win.visualViewport;\n    var width = html.clientWidth;\n    var height = html.clientHeight;\n    var x = 0;\n    var y = 0;\n    if (visualViewport) {\n        width = visualViewport.width;\n        height = visualViewport.height;\n        var layoutViewport = (0,_isLayoutViewport_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        if (layoutViewport || !layoutViewport && strategy === \"fixed\") {\n            x = visualViewport.offsetLeft;\n            y = visualViewport.offsetTop;\n        }\n    }\n    return {\n        width: width,\n        height: height,\n        x: x + (0,_getWindowScrollBarX_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(element),\n        y: y\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js":
/*!****************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getWindow.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getWindow)\n/* harmony export */ });\nfunction getWindow(node) {\n    if (node == null) {\n        return window;\n    }\n    if (node.toString() !== \"[object Window]\") {\n        var ownerDocument = node.ownerDocument;\n        return ownerDocument ? ownerDocument.defaultView || window : window;\n    }\n    return node;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXRXaW5kb3cuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLFVBQVVDLElBQUk7SUFDcEMsSUFBSUEsUUFBUSxNQUFNO1FBQ2hCLE9BQU9DO0lBQ1Q7SUFFQSxJQUFJRCxLQUFLRSxRQUFRLE9BQU8sbUJBQW1CO1FBQ3pDLElBQUlDLGdCQUFnQkgsS0FBS0csYUFBYTtRQUN0QyxPQUFPQSxnQkFBZ0JBLGNBQWNDLFdBQVcsSUFBSUgsU0FBU0E7SUFDL0Q7SUFFQSxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXRXaW5kb3cuanM/NjE5NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRXaW5kb3cobm9kZSkge1xuICBpZiAobm9kZSA9PSBudWxsKSB7XG4gICAgcmV0dXJuIHdpbmRvdztcbiAgfVxuXG4gIGlmIChub2RlLnRvU3RyaW5nKCkgIT09ICdbb2JqZWN0IFdpbmRvd10nKSB7XG4gICAgdmFyIG93bmVyRG9jdW1lbnQgPSBub2RlLm93bmVyRG9jdW1lbnQ7XG4gICAgcmV0dXJuIG93bmVyRG9jdW1lbnQgPyBvd25lckRvY3VtZW50LmRlZmF1bHRWaWV3IHx8IHdpbmRvdyA6IHdpbmRvdztcbiAgfVxuXG4gIHJldHVybiBub2RlO1xufSJdLCJuYW1lcyI6WyJnZXRXaW5kb3ciLCJub2RlIiwid2luZG93IiwidG9TdHJpbmciLCJvd25lckRvY3VtZW50IiwiZGVmYXVsdFZpZXciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getWindowScroll)\n/* harmony export */ });\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getWindow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n\nfunction getWindowScroll(node) {\n    var win = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node);\n    var scrollLeft = win.pageXOffset;\n    var scrollTop = win.pageYOffset;\n    return {\n        scrollLeft: scrollLeft,\n        scrollTop: scrollTop\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXRXaW5kb3dTY3JvbGwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUM7QUFDeEIsU0FBU0MsZ0JBQWdCQyxJQUFJO0lBQzFDLElBQUlDLE1BQU1ILHlEQUFTQSxDQUFDRTtJQUNwQixJQUFJRSxhQUFhRCxJQUFJRSxXQUFXO0lBQ2hDLElBQUlDLFlBQVlILElBQUlJLFdBQVc7SUFDL0IsT0FBTztRQUNMSCxZQUFZQTtRQUNaRSxXQUFXQTtJQUNiO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvZG9tLXV0aWxzL2dldFdpbmRvd1Njcm9sbC5qcz85MjU3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBnZXRXaW5kb3cgZnJvbSBcIi4vZ2V0V2luZG93LmpzXCI7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRXaW5kb3dTY3JvbGwobm9kZSkge1xuICB2YXIgd2luID0gZ2V0V2luZG93KG5vZGUpO1xuICB2YXIgc2Nyb2xsTGVmdCA9IHdpbi5wYWdlWE9mZnNldDtcbiAgdmFyIHNjcm9sbFRvcCA9IHdpbi5wYWdlWU9mZnNldDtcbiAgcmV0dXJuIHtcbiAgICBzY3JvbGxMZWZ0OiBzY3JvbGxMZWZ0LFxuICAgIHNjcm9sbFRvcDogc2Nyb2xsVG9wXG4gIH07XG59Il0sIm5hbWVzIjpbImdldFdpbmRvdyIsImdldFdpbmRvd1Njcm9sbCIsIm5vZGUiLCJ3aW4iLCJzY3JvbGxMZWZ0IiwicGFnZVhPZmZzZXQiLCJzY3JvbGxUb3AiLCJwYWdlWU9mZnNldCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getWindowScrollBarX)\n/* harmony export */ });\n/* harmony import */ var _getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getBoundingClientRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js\");\n/* harmony import */ var _getDocumentElement_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getDocumentElement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _getWindowScroll_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getWindowScroll.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js\");\n\n\n\nfunction getWindowScrollBarX(element) {\n    // If <html> has a CSS width greater than the viewport, then this will be\n    // incorrect for RTL.\n    // Popper 1 is broken in this case and never had a bug report so let's assume\n    // it's not an issue. I don't think anyone ever specifies width on <html>\n    // anyway.\n    // Browsers where the left scrollbar doesn't cause an issue report `0` for\n    // this (e.g. Edge 2019, IE11, Safari)\n    return (0,_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element)).left + (0,_getWindowScroll_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(element).scrollLeft;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9nZXRXaW5kb3dTY3JvbGxCYXJYLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0Q7QUFDTjtBQUNOO0FBQ3BDLFNBQVNHLG9CQUFvQkMsT0FBTztJQUNqRCx5RUFBeUU7SUFDekUscUJBQXFCO0lBQ3JCLDZFQUE2RTtJQUM3RSx5RUFBeUU7SUFDekUsVUFBVTtJQUNWLDBFQUEwRTtJQUMxRSxzQ0FBc0M7SUFDdEMsT0FBT0oscUVBQXFCQSxDQUFDQyxrRUFBa0JBLENBQUNHLFVBQVVDLElBQUksR0FBR0gsK0RBQWVBLENBQUNFLFNBQVNFLFVBQVU7QUFDdEciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvZG9tLXV0aWxzL2dldFdpbmRvd1Njcm9sbEJhclguanM/ZTNkMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZ2V0Qm91bmRpbmdDbGllbnRSZWN0IGZyb20gXCIuL2dldEJvdW5kaW5nQ2xpZW50UmVjdC5qc1wiO1xuaW1wb3J0IGdldERvY3VtZW50RWxlbWVudCBmcm9tIFwiLi9nZXREb2N1bWVudEVsZW1lbnQuanNcIjtcbmltcG9ydCBnZXRXaW5kb3dTY3JvbGwgZnJvbSBcIi4vZ2V0V2luZG93U2Nyb2xsLmpzXCI7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRXaW5kb3dTY3JvbGxCYXJYKGVsZW1lbnQpIHtcbiAgLy8gSWYgPGh0bWw+IGhhcyBhIENTUyB3aWR0aCBncmVhdGVyIHRoYW4gdGhlIHZpZXdwb3J0LCB0aGVuIHRoaXMgd2lsbCBiZVxuICAvLyBpbmNvcnJlY3QgZm9yIFJUTC5cbiAgLy8gUG9wcGVyIDEgaXMgYnJva2VuIGluIHRoaXMgY2FzZSBhbmQgbmV2ZXIgaGFkIGEgYnVnIHJlcG9ydCBzbyBsZXQncyBhc3N1bWVcbiAgLy8gaXQncyBub3QgYW4gaXNzdWUuIEkgZG9uJ3QgdGhpbmsgYW55b25lIGV2ZXIgc3BlY2lmaWVzIHdpZHRoIG9uIDxodG1sPlxuICAvLyBhbnl3YXkuXG4gIC8vIEJyb3dzZXJzIHdoZXJlIHRoZSBsZWZ0IHNjcm9sbGJhciBkb2Vzbid0IGNhdXNlIGFuIGlzc3VlIHJlcG9ydCBgMGAgZm9yXG4gIC8vIHRoaXMgKGUuZy4gRWRnZSAyMDE5LCBJRTExLCBTYWZhcmkpXG4gIHJldHVybiBnZXRCb3VuZGluZ0NsaWVudFJlY3QoZ2V0RG9jdW1lbnRFbGVtZW50KGVsZW1lbnQpKS5sZWZ0ICsgZ2V0V2luZG93U2Nyb2xsKGVsZW1lbnQpLnNjcm9sbExlZnQ7XG59Il0sIm5hbWVzIjpbImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsImdldERvY3VtZW50RWxlbWVudCIsImdldFdpbmRvd1Njcm9sbCIsImdldFdpbmRvd1Njcm9sbEJhclgiLCJlbGVtZW50IiwibGVmdCIsInNjcm9sbExlZnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isElement: () => (/* binding */ isElement),\n/* harmony export */   isHTMLElement: () => (/* binding */ isHTMLElement),\n/* harmony export */   isShadowRoot: () => (/* binding */ isShadowRoot)\n/* harmony export */ });\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getWindow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n\nfunction isElement(node) {\n    var OwnElement = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node).Element;\n    return node instanceof OwnElement || node instanceof Element;\n}\nfunction isHTMLElement(node) {\n    var OwnElement = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node).HTMLElement;\n    return node instanceof OwnElement || node instanceof HTMLElement;\n}\nfunction isShadowRoot(node) {\n    // IE 11 has no ShadowRoot\n    if (typeof ShadowRoot === \"undefined\") {\n        return false;\n    }\n    var OwnElement = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(node).ShadowRoot;\n    return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9pbnN0YW5jZU9mLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBdUM7QUFFdkMsU0FBU0MsVUFBVUMsSUFBSTtJQUNyQixJQUFJQyxhQUFhSCx5REFBU0EsQ0FBQ0UsTUFBTUUsT0FBTztJQUN4QyxPQUFPRixnQkFBZ0JDLGNBQWNELGdCQUFnQkU7QUFDdkQ7QUFFQSxTQUFTQyxjQUFjSCxJQUFJO0lBQ3pCLElBQUlDLGFBQWFILHlEQUFTQSxDQUFDRSxNQUFNSSxXQUFXO0lBQzVDLE9BQU9KLGdCQUFnQkMsY0FBY0QsZ0JBQWdCSTtBQUN2RDtBQUVBLFNBQVNDLGFBQWFMLElBQUk7SUFDeEIsMEJBQTBCO0lBQzFCLElBQUksT0FBT00sZUFBZSxhQUFhO1FBQ3JDLE9BQU87SUFDVDtJQUVBLElBQUlMLGFBQWFILHlEQUFTQSxDQUFDRSxNQUFNTSxVQUFVO0lBQzNDLE9BQU9OLGdCQUFnQkMsY0FBY0QsZ0JBQWdCTTtBQUN2RDtBQUVrRCIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL0Bwb3BwZXJqcy9jb3JlL2xpYi9kb20tdXRpbHMvaW5zdGFuY2VPZi5qcz9hOTRlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBnZXRXaW5kb3cgZnJvbSBcIi4vZ2V0V2luZG93LmpzXCI7XG5cbmZ1bmN0aW9uIGlzRWxlbWVudChub2RlKSB7XG4gIHZhciBPd25FbGVtZW50ID0gZ2V0V2luZG93KG5vZGUpLkVsZW1lbnQ7XG4gIHJldHVybiBub2RlIGluc3RhbmNlb2YgT3duRWxlbWVudCB8fCBub2RlIGluc3RhbmNlb2YgRWxlbWVudDtcbn1cblxuZnVuY3Rpb24gaXNIVE1MRWxlbWVudChub2RlKSB7XG4gIHZhciBPd25FbGVtZW50ID0gZ2V0V2luZG93KG5vZGUpLkhUTUxFbGVtZW50O1xuICByZXR1cm4gbm9kZSBpbnN0YW5jZW9mIE93bkVsZW1lbnQgfHwgbm9kZSBpbnN0YW5jZW9mIEhUTUxFbGVtZW50O1xufVxuXG5mdW5jdGlvbiBpc1NoYWRvd1Jvb3Qobm9kZSkge1xuICAvLyBJRSAxMSBoYXMgbm8gU2hhZG93Um9vdFxuICBpZiAodHlwZW9mIFNoYWRvd1Jvb3QgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG5cbiAgdmFyIE93bkVsZW1lbnQgPSBnZXRXaW5kb3cobm9kZSkuU2hhZG93Um9vdDtcbiAgcmV0dXJuIG5vZGUgaW5zdGFuY2VvZiBPd25FbGVtZW50IHx8IG5vZGUgaW5zdGFuY2VvZiBTaGFkb3dSb290O1xufVxuXG5leHBvcnQgeyBpc0VsZW1lbnQsIGlzSFRNTEVsZW1lbnQsIGlzU2hhZG93Um9vdCB9OyJdLCJuYW1lcyI6WyJnZXRXaW5kb3ciLCJpc0VsZW1lbnQiLCJub2RlIiwiT3duRWxlbWVudCIsIkVsZW1lbnQiLCJpc0hUTUxFbGVtZW50IiwiSFRNTEVsZW1lbnQiLCJpc1NoYWRvd1Jvb3QiLCJTaGFkb3dSb290Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isLayoutViewport)\n/* harmony export */ });\n/* harmony import */ var _utils_userAgent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/userAgent.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/userAgent.js\");\n\nfunction isLayoutViewport() {\n    return !/^((?!chrome|android).)*safari/i.test((0,_utils_userAgent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])());\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9pc0xheW91dFZpZXdwb3J0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdEO0FBQ2pDLFNBQVNDO0lBQ3RCLE9BQU8sQ0FBQyxpQ0FBaUNDLElBQUksQ0FBQ0YsK0RBQVdBO0FBQzNEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9pc0xheW91dFZpZXdwb3J0LmpzPzEwYTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGdldFVBU3RyaW5nIGZyb20gXCIuLi91dGlscy91c2VyQWdlbnQuanNcIjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGlzTGF5b3V0Vmlld3BvcnQoKSB7XG4gIHJldHVybiAhL14oKD8hY2hyb21lfGFuZHJvaWQpLikqc2FmYXJpL2kudGVzdChnZXRVQVN0cmluZygpKTtcbn0iXSwibmFtZXMiOlsiZ2V0VUFTdHJpbmciLCJpc0xheW91dFZpZXdwb3J0IiwidGVzdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isScrollParent)\n/* harmony export */ });\n/* harmony import */ var _getComputedStyle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getComputedStyle.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js\");\n\nfunction isScrollParent(element) {\n    // Firefox wants us to check `-x` and `-y` variations as well\n    var _getComputedStyle = (0,_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element), overflow = _getComputedStyle.overflow, overflowX = _getComputedStyle.overflowX, overflowY = _getComputedStyle.overflowY;\n    return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9pc1Njcm9sbFBhcmVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUN0QyxTQUFTQyxlQUFlQyxPQUFPO0lBQzVDLDZEQUE2RDtJQUM3RCxJQUFJQyxvQkFBb0JILGdFQUFnQkEsQ0FBQ0UsVUFDckNFLFdBQVdELGtCQUFrQkMsUUFBUSxFQUNyQ0MsWUFBWUYsa0JBQWtCRSxTQUFTLEVBQ3ZDQyxZQUFZSCxrQkFBa0JHLFNBQVM7SUFFM0MsT0FBTyw2QkFBNkJDLElBQUksQ0FBQ0gsV0FBV0UsWUFBWUQ7QUFDbEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvZG9tLXV0aWxzL2lzU2Nyb2xsUGFyZW50LmpzPzQ2MDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGdldENvbXB1dGVkU3R5bGUgZnJvbSBcIi4vZ2V0Q29tcHV0ZWRTdHlsZS5qc1wiO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gaXNTY3JvbGxQYXJlbnQoZWxlbWVudCkge1xuICAvLyBGaXJlZm94IHdhbnRzIHVzIHRvIGNoZWNrIGAteGAgYW5kIGAteWAgdmFyaWF0aW9ucyBhcyB3ZWxsXG4gIHZhciBfZ2V0Q29tcHV0ZWRTdHlsZSA9IGdldENvbXB1dGVkU3R5bGUoZWxlbWVudCksXG4gICAgICBvdmVyZmxvdyA9IF9nZXRDb21wdXRlZFN0eWxlLm92ZXJmbG93LFxuICAgICAgb3ZlcmZsb3dYID0gX2dldENvbXB1dGVkU3R5bGUub3ZlcmZsb3dYLFxuICAgICAgb3ZlcmZsb3dZID0gX2dldENvbXB1dGVkU3R5bGUub3ZlcmZsb3dZO1xuXG4gIHJldHVybiAvYXV0b3xzY3JvbGx8b3ZlcmxheXxoaWRkZW4vLnRlc3Qob3ZlcmZsb3cgKyBvdmVyZmxvd1kgKyBvdmVyZmxvd1gpO1xufSJdLCJuYW1lcyI6WyJnZXRDb21wdXRlZFN0eWxlIiwiaXNTY3JvbGxQYXJlbnQiLCJlbGVtZW50IiwiX2dldENvbXB1dGVkU3R5bGUiLCJvdmVyZmxvdyIsIm92ZXJmbG93WCIsIm92ZXJmbG93WSIsInRlc3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/isTableElement.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/isTableElement.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isTableElement)\n/* harmony export */ });\n/* harmony import */ var _getNodeName_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getNodeName.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n\nfunction isTableElement(element) {\n    return [\n        \"table\",\n        \"td\",\n        \"th\"\n    ].indexOf((0,_getNodeName_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element)) >= 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9pc1RhYmxlRWxlbWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQztBQUM1QixTQUFTQyxlQUFlQyxPQUFPO0lBQzVDLE9BQU87UUFBQztRQUFTO1FBQU07S0FBSyxDQUFDQyxPQUFPLENBQUNILDJEQUFXQSxDQUFDRSxhQUFhO0FBQ2hFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL2RvbS11dGlscy9pc1RhYmxlRWxlbWVudC5qcz81NjQ0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBnZXROb2RlTmFtZSBmcm9tIFwiLi9nZXROb2RlTmFtZS5qc1wiO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gaXNUYWJsZUVsZW1lbnQoZWxlbWVudCkge1xuICByZXR1cm4gWyd0YWJsZScsICd0ZCcsICd0aCddLmluZGV4T2YoZ2V0Tm9kZU5hbWUoZWxlbWVudCkpID49IDA7XG59Il0sIm5hbWVzIjpbImdldE5vZGVOYW1lIiwiaXNUYWJsZUVsZW1lbnQiLCJlbGVtZW50IiwiaW5kZXhPZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/isTableElement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js":
/*!************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ listScrollParents)\n/* harmony export */ });\n/* harmony import */ var _getScrollParent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getScrollParent.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js\");\n/* harmony import */ var _getParentNode_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getParentNode.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getParentNode.js\");\n/* harmony import */ var _getWindow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getWindow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n/* harmony import */ var _isScrollParent_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isScrollParent.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js\");\n\n\n\n\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/ function listScrollParents(element, list) {\n    var _element$ownerDocumen;\n    if (list === void 0) {\n        list = [];\n    }\n    var scrollParent = (0,_getScrollParent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(element);\n    var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n    var win = (0,_getWindow_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(scrollParent);\n    var target = isBody ? [\n        win\n    ].concat(win.visualViewport || [], (0,_isScrollParent_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(scrollParent) ? scrollParent : []) : scrollParent;\n    var updatedList = list.concat(target);\n    return isBody ? updatedList : updatedList.concat(listScrollParents((0,_getParentNode_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(target)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/enums.js":
/*!**************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/enums.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   afterMain: () => (/* binding */ afterMain),\n/* harmony export */   afterRead: () => (/* binding */ afterRead),\n/* harmony export */   afterWrite: () => (/* binding */ afterWrite),\n/* harmony export */   auto: () => (/* binding */ auto),\n/* harmony export */   basePlacements: () => (/* binding */ basePlacements),\n/* harmony export */   beforeMain: () => (/* binding */ beforeMain),\n/* harmony export */   beforeRead: () => (/* binding */ beforeRead),\n/* harmony export */   beforeWrite: () => (/* binding */ beforeWrite),\n/* harmony export */   bottom: () => (/* binding */ bottom),\n/* harmony export */   clippingParents: () => (/* binding */ clippingParents),\n/* harmony export */   end: () => (/* binding */ end),\n/* harmony export */   left: () => (/* binding */ left),\n/* harmony export */   main: () => (/* binding */ main),\n/* harmony export */   modifierPhases: () => (/* binding */ modifierPhases),\n/* harmony export */   placements: () => (/* binding */ placements),\n/* harmony export */   popper: () => (/* binding */ popper),\n/* harmony export */   read: () => (/* binding */ read),\n/* harmony export */   reference: () => (/* binding */ reference),\n/* harmony export */   right: () => (/* binding */ right),\n/* harmony export */   start: () => (/* binding */ start),\n/* harmony export */   top: () => (/* binding */ top),\n/* harmony export */   variationPlacements: () => (/* binding */ variationPlacements),\n/* harmony export */   viewport: () => (/* binding */ viewport),\n/* harmony export */   write: () => (/* binding */ write)\n/* harmony export */ });\nvar top = \"top\";\nvar bottom = \"bottom\";\nvar right = \"right\";\nvar left = \"left\";\nvar auto = \"auto\";\nvar basePlacements = [\n    top,\n    bottom,\n    right,\n    left\n];\nvar start = \"start\";\nvar end = \"end\";\nvar clippingParents = \"clippingParents\";\nvar viewport = \"viewport\";\nvar popper = \"popper\";\nvar reference = \"reference\";\nvar variationPlacements = /*#__PURE__*/ basePlacements.reduce(function(acc, placement) {\n    return acc.concat([\n        placement + \"-\" + start,\n        placement + \"-\" + end\n    ]);\n}, []);\nvar placements = /*#__PURE__*/ [].concat(basePlacements, [\n    auto\n]).reduce(function(acc, placement) {\n    return acc.concat([\n        placement,\n        placement + \"-\" + start,\n        placement + \"-\" + end\n    ]);\n}, []); // modifiers that need to read the DOM\nvar beforeRead = \"beforeRead\";\nvar read = \"read\";\nvar afterRead = \"afterRead\"; // pure-logic modifiers\nvar beforeMain = \"beforeMain\";\nvar main = \"main\";\nvar afterMain = \"afterMain\"; // modifier with the purpose to write to the DOM (or write into a framework state)\nvar beforeWrite = \"beforeWrite\";\nvar write = \"write\";\nvar afterWrite = \"afterWrite\";\nvar modifierPhases = [\n    beforeRead,\n    read,\n    afterRead,\n    beforeMain,\n    main,\n    afterMain,\n    beforeWrite,\n    write,\n    afterWrite\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/enums.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/modifiers/applyStyles.js":
/*!******************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/applyStyles.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _dom_utils_getNodeName_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../dom-utils/getNodeName.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getNodeName.js\");\n/* harmony import */ var _dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dom-utils/instanceOf.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n\n // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\nfunction applyStyles(_ref) {\n    var state = _ref.state;\n    Object.keys(state.elements).forEach(function(name) {\n        var style = state.styles[name] || {};\n        var attributes = state.attributes[name] || {};\n        var element = state.elements[name]; // arrow is optional + virtual elements\n        if (!(0,_dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || !(0,_dom_utils_getNodeName_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element)) {\n            return;\n        } // Flow doesn't support to extend this property, but it's the most\n        // effective way to apply styles to an HTMLElement\n        // $FlowFixMe[cannot-write]\n        Object.assign(element.style, style);\n        Object.keys(attributes).forEach(function(name) {\n            var value = attributes[name];\n            if (value === false) {\n                element.removeAttribute(name);\n            } else {\n                element.setAttribute(name, value === true ? \"\" : value);\n            }\n        });\n    });\n}\nfunction effect(_ref2) {\n    var state = _ref2.state;\n    var initialStyles = {\n        popper: {\n            position: state.options.strategy,\n            left: \"0\",\n            top: \"0\",\n            margin: \"0\"\n        },\n        arrow: {\n            position: \"absolute\"\n        },\n        reference: {}\n    };\n    Object.assign(state.elements.popper.style, initialStyles.popper);\n    state.styles = initialStyles;\n    if (state.elements.arrow) {\n        Object.assign(state.elements.arrow.style, initialStyles.arrow);\n    }\n    return function() {\n        Object.keys(state.elements).forEach(function(name) {\n            var element = state.elements[name];\n            var attributes = state.attributes[name] || {};\n            var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n            var style = styleProperties.reduce(function(style, property) {\n                style[property] = \"\";\n                return style;\n            }, {}); // arrow is optional + virtual elements\n            if (!(0,_dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || !(0,_dom_utils_getNodeName_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(element)) {\n                return;\n            }\n            Object.assign(element.style, style);\n            Object.keys(attributes).forEach(function(attribute) {\n                element.removeAttribute(attribute);\n            });\n        });\n    };\n} // eslint-disable-next-line import/no-unused-modules\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    name: \"applyStyles\",\n    enabled: true,\n    phase: \"write\",\n    fn: applyStyles,\n    effect: effect,\n    requires: [\n        \"computeStyles\"\n    ]\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/modifiers/applyStyles.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/modifiers/arrow.js":
/*!************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/arrow.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/getBasePlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n/* harmony import */ var _dom_utils_getLayoutRect_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dom-utils/getLayoutRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js\");\n/* harmony import */ var _dom_utils_contains_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../dom-utils/contains.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/contains.js\");\n/* harmony import */ var _dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../dom-utils/getOffsetParent.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js\");\n/* harmony import */ var _utils_getMainAxisFromPlacement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/getMainAxisFromPlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js\");\n/* harmony import */ var _utils_within_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/within.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/within.js\");\n/* harmony import */ var _utils_mergePaddingObject_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/mergePaddingObject.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/mergePaddingObject.js\");\n/* harmony import */ var _utils_expandToHashMap_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/expandToHashMap.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/expandToHashMap.js\");\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../enums.js */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n\n\n\n\n\n\n\n\n // eslint-disable-next-line import/no-unused-modules\nvar toPaddingObject = function toPaddingObject(padding, state) {\n    padding = typeof padding === \"function\" ? padding(Object.assign({}, state.rects, {\n        placement: state.placement\n    })) : padding;\n    return (0,_utils_mergePaddingObject_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(typeof padding !== \"number\" ? padding : (0,_utils_expandToHashMap_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(padding, _enums_js__WEBPACK_IMPORTED_MODULE_2__.basePlacements));\n};\nfunction arrow(_ref) {\n    var _state$modifiersData$;\n    var state = _ref.state, name = _ref.name, options = _ref.options;\n    var arrowElement = state.elements.arrow;\n    var popperOffsets = state.modifiersData.popperOffsets;\n    var basePlacement = (0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(state.placement);\n    var axis = (0,_utils_getMainAxisFromPlacement_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(basePlacement);\n    var isVertical = [\n        _enums_js__WEBPACK_IMPORTED_MODULE_2__.left,\n        _enums_js__WEBPACK_IMPORTED_MODULE_2__.right\n    ].indexOf(basePlacement) >= 0;\n    var len = isVertical ? \"height\" : \"width\";\n    if (!arrowElement || !popperOffsets) {\n        return;\n    }\n    var paddingObject = toPaddingObject(options.padding, state);\n    var arrowRect = (0,_dom_utils_getLayoutRect_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(arrowElement);\n    var minProp = axis === \"y\" ? _enums_js__WEBPACK_IMPORTED_MODULE_2__.top : _enums_js__WEBPACK_IMPORTED_MODULE_2__.left;\n    var maxProp = axis === \"y\" ? _enums_js__WEBPACK_IMPORTED_MODULE_2__.bottom : _enums_js__WEBPACK_IMPORTED_MODULE_2__.right;\n    var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n    var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n    var arrowOffsetParent = (0,_dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(arrowElement);\n    var clientSize = arrowOffsetParent ? axis === \"y\" ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n    var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n    // outside of the popper bounds\n    var min = paddingObject[minProp];\n    var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n    var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n    var offset = (0,_utils_within_js__WEBPACK_IMPORTED_MODULE_7__.within)(min, center, max); // Prevents breaking syntax highlighting...\n    var axisProp = axis;\n    state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\nfunction effect(_ref2) {\n    var state = _ref2.state, options = _ref2.options;\n    var _options$element = options.element, arrowElement = _options$element === void 0 ? \"[data-popper-arrow]\" : _options$element;\n    if (arrowElement == null) {\n        return;\n    } // CSS selector\n    if (typeof arrowElement === \"string\") {\n        arrowElement = state.elements.popper.querySelector(arrowElement);\n        if (!arrowElement) {\n            return;\n        }\n    }\n    if (!(0,_dom_utils_contains_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(state.elements.popper, arrowElement)) {\n        return;\n    }\n    state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    name: \"arrow\",\n    enabled: true,\n    phase: \"main\",\n    fn: arrow,\n    effect: effect,\n    requires: [\n        \"popperOffsets\"\n    ],\n    requiresIfExists: [\n        \"preventOverflow\"\n    ]\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/modifiers/arrow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/modifiers/computeStyles.js":
/*!********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/computeStyles.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   mapToStyles: () => (/* binding */ mapToStyles)\n/* harmony export */ });\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../enums.js */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../dom-utils/getOffsetParent.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js\");\n/* harmony import */ var _dom_utils_getWindow_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../dom-utils/getWindow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n/* harmony import */ var _dom_utils_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../dom-utils/getDocumentElement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _dom_utils_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dom-utils/getComputedStyle.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js\");\n/* harmony import */ var _utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/getBasePlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n/* harmony import */ var _utils_getVariation_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/getVariation.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getVariation.js\");\n/* harmony import */ var _utils_math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/math.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/math.js\");\n\n\n\n\n\n\n\n // eslint-disable-next-line import/no-unused-modules\nvar unsetSides = {\n    top: \"auto\",\n    right: \"auto\",\n    bottom: \"auto\",\n    left: \"auto\"\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\nfunction roundOffsetsByDPR(_ref, win) {\n    var x = _ref.x, y = _ref.y;\n    var dpr = win.devicePixelRatio || 1;\n    return {\n        x: (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_0__.round)(x * dpr) / dpr || 0,\n        y: (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_0__.round)(y * dpr) / dpr || 0\n    };\n}\nfunction mapToStyles(_ref2) {\n    var _Object$assign2;\n    var popper = _ref2.popper, popperRect = _ref2.popperRect, placement = _ref2.placement, variation = _ref2.variation, offsets = _ref2.offsets, position = _ref2.position, gpuAcceleration = _ref2.gpuAcceleration, adaptive = _ref2.adaptive, roundOffsets = _ref2.roundOffsets, isFixed = _ref2.isFixed;\n    var _offsets$x = offsets.x, x = _offsets$x === void 0 ? 0 : _offsets$x, _offsets$y = offsets.y, y = _offsets$y === void 0 ? 0 : _offsets$y;\n    var _ref3 = typeof roundOffsets === \"function\" ? roundOffsets({\n        x: x,\n        y: y\n    }) : {\n        x: x,\n        y: y\n    };\n    x = _ref3.x;\n    y = _ref3.y;\n    var hasX = offsets.hasOwnProperty(\"x\");\n    var hasY = offsets.hasOwnProperty(\"y\");\n    var sideX = _enums_js__WEBPACK_IMPORTED_MODULE_1__.left;\n    var sideY = _enums_js__WEBPACK_IMPORTED_MODULE_1__.top;\n    var win = window;\n    if (adaptive) {\n        var offsetParent = (0,_dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(popper);\n        var heightProp = \"clientHeight\";\n        var widthProp = \"clientWidth\";\n        if (offsetParent === (0,_dom_utils_getWindow_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(popper)) {\n            offsetParent = (0,_dom_utils_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(popper);\n            if ((0,_dom_utils_getComputedStyle_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(offsetParent).position !== \"static\" && position === \"absolute\") {\n                heightProp = \"scrollHeight\";\n                widthProp = \"scrollWidth\";\n            }\n        } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n        offsetParent = offsetParent;\n        if (placement === _enums_js__WEBPACK_IMPORTED_MODULE_1__.top || (placement === _enums_js__WEBPACK_IMPORTED_MODULE_1__.left || placement === _enums_js__WEBPACK_IMPORTED_MODULE_1__.right) && variation === _enums_js__WEBPACK_IMPORTED_MODULE_1__.end) {\n            sideY = _enums_js__WEBPACK_IMPORTED_MODULE_1__.bottom;\n            var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : offsetParent[heightProp];\n            y -= offsetY - popperRect.height;\n            y *= gpuAcceleration ? 1 : -1;\n        }\n        if (placement === _enums_js__WEBPACK_IMPORTED_MODULE_1__.left || (placement === _enums_js__WEBPACK_IMPORTED_MODULE_1__.top || placement === _enums_js__WEBPACK_IMPORTED_MODULE_1__.bottom) && variation === _enums_js__WEBPACK_IMPORTED_MODULE_1__.end) {\n            sideX = _enums_js__WEBPACK_IMPORTED_MODULE_1__.right;\n            var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : offsetParent[widthProp];\n            x -= offsetX - popperRect.width;\n            x *= gpuAcceleration ? 1 : -1;\n        }\n    }\n    var commonStyles = Object.assign({\n        position: position\n    }, adaptive && unsetSides);\n    var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n        x: x,\n        y: y\n    }, (0,_dom_utils_getWindow_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(popper)) : {\n        x: x,\n        y: y\n    };\n    x = _ref4.x;\n    y = _ref4.y;\n    if (gpuAcceleration) {\n        var _Object$assign;\n        return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? \"0\" : \"\", _Object$assign[sideX] = hasX ? \"0\" : \"\", _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n    }\n    return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : \"\", _Object$assign2[sideX] = hasX ? x + \"px\" : \"\", _Object$assign2.transform = \"\", _Object$assign2));\n}\nfunction computeStyles(_ref5) {\n    var state = _ref5.state, options = _ref5.options;\n    var _options$gpuAccelerat = options.gpuAcceleration, gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat, _options$adaptive = options.adaptive, adaptive = _options$adaptive === void 0 ? true : _options$adaptive, _options$roundOffsets = options.roundOffsets, roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n    var commonStyles = {\n        placement: (0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(state.placement),\n        variation: (0,_utils_getVariation_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(state.placement),\n        popper: state.elements.popper,\n        popperRect: state.rects.popper,\n        gpuAcceleration: gpuAcceleration,\n        isFixed: state.options.strategy === \"fixed\"\n    };\n    if (state.modifiersData.popperOffsets != null) {\n        state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n            offsets: state.modifiersData.popperOffsets,\n            position: state.options.strategy,\n            adaptive: adaptive,\n            roundOffsets: roundOffsets\n        })));\n    }\n    if (state.modifiersData.arrow != null) {\n        state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n            offsets: state.modifiersData.arrow,\n            position: \"absolute\",\n            adaptive: false,\n            roundOffsets: roundOffsets\n        })));\n    }\n    state.attributes.popper = Object.assign({}, state.attributes.popper, {\n        \"data-popper-placement\": state.placement\n    });\n} // eslint-disable-next-line import/no-unused-modules\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    name: \"computeStyles\",\n    enabled: true,\n    phase: \"beforeWrite\",\n    fn: computeStyles,\n    data: {}\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/modifiers/computeStyles.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/modifiers/eventListeners.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/eventListeners.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _dom_utils_getWindow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dom-utils/getWindow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getWindow.js\");\n // eslint-disable-next-line import/no-unused-modules\nvar passive = {\n    passive: true\n};\nfunction effect(_ref) {\n    var state = _ref.state, instance = _ref.instance, options = _ref.options;\n    var _options$scroll = options.scroll, scroll = _options$scroll === void 0 ? true : _options$scroll, _options$resize = options.resize, resize = _options$resize === void 0 ? true : _options$resize;\n    var window = (0,_dom_utils_getWindow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(state.elements.popper);\n    var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n    if (scroll) {\n        scrollParents.forEach(function(scrollParent) {\n            scrollParent.addEventListener(\"scroll\", instance.update, passive);\n        });\n    }\n    if (resize) {\n        window.addEventListener(\"resize\", instance.update, passive);\n    }\n    return function() {\n        if (scroll) {\n            scrollParents.forEach(function(scrollParent) {\n                scrollParent.removeEventListener(\"scroll\", instance.update, passive);\n            });\n        }\n        if (resize) {\n            window.removeEventListener(\"resize\", instance.update, passive);\n        }\n    };\n} // eslint-disable-next-line import/no-unused-modules\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    name: \"eventListeners\",\n    enabled: true,\n    phase: \"write\",\n    fn: function fn() {},\n    effect: effect,\n    data: {}\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/modifiers/eventListeners.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/modifiers/flip.js":
/*!***********************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/flip.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_getOppositePlacement_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getOppositePlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getOppositePlacement.js\");\n/* harmony import */ var _utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getBasePlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n/* harmony import */ var _utils_getOppositeVariationPlacement_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/getOppositeVariationPlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js\");\n/* harmony import */ var _utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/detectOverflow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _utils_computeAutoPlacement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/computeAutoPlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js\");\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../enums.js */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _utils_getVariation_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/getVariation.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getVariation.js\");\n\n\n\n\n\n\n // eslint-disable-next-line import/no-unused-modules\nfunction getExpandedFallbackPlacements(placement) {\n    if ((0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(placement) === _enums_js__WEBPACK_IMPORTED_MODULE_1__.auto) {\n        return [];\n    }\n    var oppositePlacement = (0,_utils_getOppositePlacement_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(placement);\n    return [\n        (0,_utils_getOppositeVariationPlacement_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(placement),\n        oppositePlacement,\n        (0,_utils_getOppositeVariationPlacement_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(oppositePlacement)\n    ];\n}\nfunction flip(_ref) {\n    var state = _ref.state, options = _ref.options, name = _ref.name;\n    if (state.modifiersData[name]._skip) {\n        return;\n    }\n    var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis, specifiedFallbackPlacements = options.fallbackPlacements, padding = options.padding, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, _options$flipVariatio = options.flipVariations, flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio, allowedAutoPlacements = options.allowedAutoPlacements;\n    var preferredPlacement = state.options.placement;\n    var basePlacement = (0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(preferredPlacement);\n    var isBasePlacement = basePlacement === preferredPlacement;\n    var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [\n        (0,_utils_getOppositePlacement_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(preferredPlacement)\n    ] : getExpandedFallbackPlacements(preferredPlacement));\n    var placements = [\n        preferredPlacement\n    ].concat(fallbackPlacements).reduce(function(acc, placement) {\n        return acc.concat((0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(placement) === _enums_js__WEBPACK_IMPORTED_MODULE_1__.auto ? (0,_utils_computeAutoPlacement_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(state, {\n            placement: placement,\n            boundary: boundary,\n            rootBoundary: rootBoundary,\n            padding: padding,\n            flipVariations: flipVariations,\n            allowedAutoPlacements: allowedAutoPlacements\n        }) : placement);\n    }, []);\n    var referenceRect = state.rects.reference;\n    var popperRect = state.rects.popper;\n    var checksMap = new Map();\n    var makeFallbackChecks = true;\n    var firstFittingPlacement = placements[0];\n    for(var i = 0; i < placements.length; i++){\n        var placement = placements[i];\n        var _basePlacement = (0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(placement);\n        var isStartVariation = (0,_utils_getVariation_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(placement) === _enums_js__WEBPACK_IMPORTED_MODULE_1__.start;\n        var isVertical = [\n            _enums_js__WEBPACK_IMPORTED_MODULE_1__.top,\n            _enums_js__WEBPACK_IMPORTED_MODULE_1__.bottom\n        ].indexOf(_basePlacement) >= 0;\n        var len = isVertical ? \"width\" : \"height\";\n        var overflow = (0,_utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(state, {\n            placement: placement,\n            boundary: boundary,\n            rootBoundary: rootBoundary,\n            altBoundary: altBoundary,\n            padding: padding\n        });\n        var mainVariationSide = isVertical ? isStartVariation ? _enums_js__WEBPACK_IMPORTED_MODULE_1__.right : _enums_js__WEBPACK_IMPORTED_MODULE_1__.left : isStartVariation ? _enums_js__WEBPACK_IMPORTED_MODULE_1__.bottom : _enums_js__WEBPACK_IMPORTED_MODULE_1__.top;\n        if (referenceRect[len] > popperRect[len]) {\n            mainVariationSide = (0,_utils_getOppositePlacement_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(mainVariationSide);\n        }\n        var altVariationSide = (0,_utils_getOppositePlacement_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(mainVariationSide);\n        var checks = [];\n        if (checkMainAxis) {\n            checks.push(overflow[_basePlacement] <= 0);\n        }\n        if (checkAltAxis) {\n            checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n        }\n        if (checks.every(function(check) {\n            return check;\n        })) {\n            firstFittingPlacement = placement;\n            makeFallbackChecks = false;\n            break;\n        }\n        checksMap.set(placement, checks);\n    }\n    if (makeFallbackChecks) {\n        // `2` may be desired in some cases – research later\n        var numberOfChecks = flipVariations ? 3 : 1;\n        var _loop = function _loop(_i) {\n            var fittingPlacement = placements.find(function(placement) {\n                var checks = checksMap.get(placement);\n                if (checks) {\n                    return checks.slice(0, _i).every(function(check) {\n                        return check;\n                    });\n                }\n            });\n            if (fittingPlacement) {\n                firstFittingPlacement = fittingPlacement;\n                return \"break\";\n            }\n        };\n        for(var _i = numberOfChecks; _i > 0; _i--){\n            var _ret = _loop(_i);\n            if (_ret === \"break\") break;\n        }\n    }\n    if (state.placement !== firstFittingPlacement) {\n        state.modifiersData[name]._skip = true;\n        state.placement = firstFittingPlacement;\n        state.reset = true;\n    }\n} // eslint-disable-next-line import/no-unused-modules\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    name: \"flip\",\n    enabled: true,\n    phase: \"main\",\n    fn: flip,\n    requiresIfExists: [\n        \"offset\"\n    ],\n    data: {\n        _skip: false\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/modifiers/flip.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/modifiers/hide.js":
/*!***********************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/hide.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../enums.js */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/detectOverflow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n    if (preventedOffsets === void 0) {\n        preventedOffsets = {\n            x: 0,\n            y: 0\n        };\n    }\n    return {\n        top: overflow.top - rect.height - preventedOffsets.y,\n        right: overflow.right - rect.width + preventedOffsets.x,\n        bottom: overflow.bottom - rect.height + preventedOffsets.y,\n        left: overflow.left - rect.width - preventedOffsets.x\n    };\n}\nfunction isAnySideFullyClipped(overflow) {\n    return [\n        _enums_js__WEBPACK_IMPORTED_MODULE_0__.top,\n        _enums_js__WEBPACK_IMPORTED_MODULE_0__.right,\n        _enums_js__WEBPACK_IMPORTED_MODULE_0__.bottom,\n        _enums_js__WEBPACK_IMPORTED_MODULE_0__.left\n    ].some(function(side) {\n        return overflow[side] >= 0;\n    });\n}\nfunction hide(_ref) {\n    var state = _ref.state, name = _ref.name;\n    var referenceRect = state.rects.reference;\n    var popperRect = state.rects.popper;\n    var preventedOffsets = state.modifiersData.preventOverflow;\n    var referenceOverflow = (0,_utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(state, {\n        elementContext: \"reference\"\n    });\n    var popperAltOverflow = (0,_utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(state, {\n        altBoundary: true\n    });\n    var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n    var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n    var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n    var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n    state.modifiersData[name] = {\n        referenceClippingOffsets: referenceClippingOffsets,\n        popperEscapeOffsets: popperEscapeOffsets,\n        isReferenceHidden: isReferenceHidden,\n        hasPopperEscaped: hasPopperEscaped\n    };\n    state.attributes.popper = Object.assign({}, state.attributes.popper, {\n        \"data-popper-reference-hidden\": isReferenceHidden,\n        \"data-popper-escaped\": hasPopperEscaped\n    });\n} // eslint-disable-next-line import/no-unused-modules\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    name: \"hide\",\n    enabled: true,\n    phase: \"main\",\n    requiresIfExists: [\n        \"preventOverflow\"\n    ],\n    fn: hide\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/modifiers/hide.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/modifiers/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyStyles: () => (/* reexport safe */ _applyStyles_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   arrow: () => (/* reexport safe */ _arrow_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   computeStyles: () => (/* reexport safe */ _computeStyles_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   eventListeners: () => (/* reexport safe */ _eventListeners_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   flip: () => (/* reexport safe */ _flip_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   hide: () => (/* reexport safe */ _hide_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   offset: () => (/* reexport safe */ _offset_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   popperOffsets: () => (/* reexport safe */ _popperOffsets_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   preventOverflow: () => (/* reexport safe */ _preventOverflow_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _applyStyles_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./applyStyles.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/applyStyles.js\");\n/* harmony import */ var _arrow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./arrow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/arrow.js\");\n/* harmony import */ var _computeStyles_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./computeStyles.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/computeStyles.js\");\n/* harmony import */ var _eventListeners_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./eventListeners.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/eventListeners.js\");\n/* harmony import */ var _flip_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./flip.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/flip.js\");\n/* harmony import */ var _hide_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./hide.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/hide.js\");\n/* harmony import */ var _offset_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./offset.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/offset.js\");\n/* harmony import */ var _popperOffsets_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./popperOffsets.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js\");\n/* harmony import */ var _preventOverflow_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./preventOverflow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/preventOverflow.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL21vZGlmaWVycy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMEQ7QUFDWjtBQUNnQjtBQUNFO0FBQ3BCO0FBQ0E7QUFDSTtBQUNjO0FBQ0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvbW9kaWZpZXJzL2luZGV4LmpzPzRmNjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCBhcyBhcHBseVN0eWxlcyB9IGZyb20gXCIuL2FwcGx5U3R5bGVzLmpzXCI7XG5leHBvcnQgeyBkZWZhdWx0IGFzIGFycm93IH0gZnJvbSBcIi4vYXJyb3cuanNcIjtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgY29tcHV0ZVN0eWxlcyB9IGZyb20gXCIuL2NvbXB1dGVTdHlsZXMuanNcIjtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgZXZlbnRMaXN0ZW5lcnMgfSBmcm9tIFwiLi9ldmVudExpc3RlbmVycy5qc1wiO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBmbGlwIH0gZnJvbSBcIi4vZmxpcC5qc1wiO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBoaWRlIH0gZnJvbSBcIi4vaGlkZS5qc1wiO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBvZmZzZXQgfSBmcm9tIFwiLi9vZmZzZXQuanNcIjtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgcG9wcGVyT2Zmc2V0cyB9IGZyb20gXCIuL3BvcHBlck9mZnNldHMuanNcIjtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgcHJldmVudE92ZXJmbG93IH0gZnJvbSBcIi4vcHJldmVudE92ZXJmbG93LmpzXCI7Il0sIm5hbWVzIjpbImRlZmF1bHQiLCJhcHBseVN0eWxlcyIsImFycm93IiwiY29tcHV0ZVN0eWxlcyIsImV2ZW50TGlzdGVuZXJzIiwiZmxpcCIsImhpZGUiLCJvZmZzZXQiLCJwb3BwZXJPZmZzZXRzIiwicHJldmVudE92ZXJmbG93Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/modifiers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/modifiers/offset.js":
/*!*************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/offset.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   distanceAndSkiddingToXY: () => (/* binding */ distanceAndSkiddingToXY)\n/* harmony export */ });\n/* harmony import */ var _utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getBasePlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../enums.js */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n\n // eslint-disable-next-line import/no-unused-modules\nfunction distanceAndSkiddingToXY(placement, rects, offset) {\n    var basePlacement = (0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(placement);\n    var invertDistance = [\n        _enums_js__WEBPACK_IMPORTED_MODULE_1__.left,\n        _enums_js__WEBPACK_IMPORTED_MODULE_1__.top\n    ].indexOf(basePlacement) >= 0 ? -1 : 1;\n    var _ref = typeof offset === \"function\" ? offset(Object.assign({}, rects, {\n        placement: placement\n    })) : offset, skidding = _ref[0], distance = _ref[1];\n    skidding = skidding || 0;\n    distance = (distance || 0) * invertDistance;\n    return [\n        _enums_js__WEBPACK_IMPORTED_MODULE_1__.left,\n        _enums_js__WEBPACK_IMPORTED_MODULE_1__.right\n    ].indexOf(basePlacement) >= 0 ? {\n        x: distance,\n        y: skidding\n    } : {\n        x: skidding,\n        y: distance\n    };\n}\nfunction offset(_ref2) {\n    var state = _ref2.state, options = _ref2.options, name = _ref2.name;\n    var _options$offset = options.offset, offset = _options$offset === void 0 ? [\n        0,\n        0\n    ] : _options$offset;\n    var data = _enums_js__WEBPACK_IMPORTED_MODULE_1__.placements.reduce(function(acc, placement) {\n        acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n        return acc;\n    }, {});\n    var _data$state$placement = data[state.placement], x = _data$state$placement.x, y = _data$state$placement.y;\n    if (state.modifiersData.popperOffsets != null) {\n        state.modifiersData.popperOffsets.x += x;\n        state.modifiersData.popperOffsets.y += y;\n    }\n    state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    name: \"offset\",\n    enabled: true,\n    phase: \"main\",\n    requires: [\n        \"popperOffsets\"\n    ],\n    fn: offset\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/modifiers/offset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js":
/*!********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_computeOffsets_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/computeOffsets.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/computeOffsets.js\");\n\nfunction popperOffsets(_ref) {\n    var state = _ref.state, name = _ref.name;\n    // Offsets are the actual position the popper needs to have to be\n    // properly positioned near its reference element\n    // This is the most basic placement, and will be adjusted by\n    // the modifiers in the next step\n    state.modifiersData[name] = (0,_utils_computeOffsets_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        reference: state.rects.reference,\n        element: state.rects.popper,\n        strategy: \"absolute\",\n        placement: state.placement\n    });\n} // eslint-disable-next-line import/no-unused-modules\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    name: \"popperOffsets\",\n    enabled: true,\n    phase: \"read\",\n    fn: popperOffsets,\n    data: {}\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/modifiers/preventOverflow.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/modifiers/preventOverflow.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../enums.js */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getBasePlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n/* harmony import */ var _utils_getMainAxisFromPlacement_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/getMainAxisFromPlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js\");\n/* harmony import */ var _utils_getAltAxis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/getAltAxis.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getAltAxis.js\");\n/* harmony import */ var _utils_within_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../utils/within.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/within.js\");\n/* harmony import */ var _dom_utils_getLayoutRect_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../dom-utils/getLayoutRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js\");\n/* harmony import */ var _dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../dom-utils/getOffsetParent.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js\");\n/* harmony import */ var _utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/detectOverflow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _utils_getVariation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getVariation.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getVariation.js\");\n/* harmony import */ var _utils_getFreshSideObject_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/getFreshSideObject.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getFreshSideObject.js\");\n/* harmony import */ var _utils_math_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../utils/math.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/math.js\");\n\n\n\n\n\n\n\n\n\n\n\nfunction preventOverflow(_ref) {\n    var state = _ref.state, options = _ref.options, name = _ref.name;\n    var _options$mainAxis = options.mainAxis, checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis, _options$altAxis = options.altAxis, checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis, boundary = options.boundary, rootBoundary = options.rootBoundary, altBoundary = options.altBoundary, padding = options.padding, _options$tether = options.tether, tether = _options$tether === void 0 ? true : _options$tether, _options$tetherOffset = options.tetherOffset, tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n    var overflow = (0,_utils_detectOverflow_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(state, {\n        boundary: boundary,\n        rootBoundary: rootBoundary,\n        padding: padding,\n        altBoundary: altBoundary\n    });\n    var basePlacement = (0,_utils_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(state.placement);\n    var variation = (0,_utils_getVariation_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(state.placement);\n    var isBasePlacement = !variation;\n    var mainAxis = (0,_utils_getMainAxisFromPlacement_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(basePlacement);\n    var altAxis = (0,_utils_getAltAxis_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(mainAxis);\n    var popperOffsets = state.modifiersData.popperOffsets;\n    var referenceRect = state.rects.reference;\n    var popperRect = state.rects.popper;\n    var tetherOffsetValue = typeof tetherOffset === \"function\" ? tetherOffset(Object.assign({}, state.rects, {\n        placement: state.placement\n    })) : tetherOffset;\n    var normalizedTetherOffsetValue = typeof tetherOffsetValue === \"number\" ? {\n        mainAxis: tetherOffsetValue,\n        altAxis: tetherOffsetValue\n    } : Object.assign({\n        mainAxis: 0,\n        altAxis: 0\n    }, tetherOffsetValue);\n    var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n    var data = {\n        x: 0,\n        y: 0\n    };\n    if (!popperOffsets) {\n        return;\n    }\n    if (checkMainAxis) {\n        var _offsetModifierState$;\n        var mainSide = mainAxis === \"y\" ? _enums_js__WEBPACK_IMPORTED_MODULE_5__.top : _enums_js__WEBPACK_IMPORTED_MODULE_5__.left;\n        var altSide = mainAxis === \"y\" ? _enums_js__WEBPACK_IMPORTED_MODULE_5__.bottom : _enums_js__WEBPACK_IMPORTED_MODULE_5__.right;\n        var len = mainAxis === \"y\" ? \"height\" : \"width\";\n        var offset = popperOffsets[mainAxis];\n        var min = offset + overflow[mainSide];\n        var max = offset - overflow[altSide];\n        var additive = tether ? -popperRect[len] / 2 : 0;\n        var minLen = variation === _enums_js__WEBPACK_IMPORTED_MODULE_5__.start ? referenceRect[len] : popperRect[len];\n        var maxLen = variation === _enums_js__WEBPACK_IMPORTED_MODULE_5__.start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n        // outside the reference bounds\n        var arrowElement = state.elements.arrow;\n        var arrowRect = tether && arrowElement ? (0,_dom_utils_getLayoutRect_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(arrowElement) : {\n            width: 0,\n            height: 0\n        };\n        var arrowPaddingObject = state.modifiersData[\"arrow#persistent\"] ? state.modifiersData[\"arrow#persistent\"].padding : (0,_utils_getFreshSideObject_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n        var arrowPaddingMin = arrowPaddingObject[mainSide];\n        var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n        // to include its full size in the calculation. If the reference is small\n        // and near the edge of a boundary, the popper can overflow even if the\n        // reference is not overflowing as well (e.g. virtual elements with no\n        // width or height)\n        var arrowLen = (0,_utils_within_js__WEBPACK_IMPORTED_MODULE_8__.within)(0, referenceRect[len], arrowRect[len]);\n        var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n        var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n        var arrowOffsetParent = state.elements.arrow && (0,_dom_utils_getOffsetParent_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(state.elements.arrow);\n        var clientOffset = arrowOffsetParent ? mainAxis === \"y\" ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n        var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n        var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n        var tetherMax = offset + maxOffset - offsetModifierValue;\n        var preventedOffset = (0,_utils_within_js__WEBPACK_IMPORTED_MODULE_8__.within)(tether ? (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_10__.min)(min, tetherMin) : min, offset, tether ? (0,_utils_math_js__WEBPACK_IMPORTED_MODULE_10__.max)(max, tetherMax) : max);\n        popperOffsets[mainAxis] = preventedOffset;\n        data[mainAxis] = preventedOffset - offset;\n    }\n    if (checkAltAxis) {\n        var _offsetModifierState$2;\n        var _mainSide = mainAxis === \"x\" ? _enums_js__WEBPACK_IMPORTED_MODULE_5__.top : _enums_js__WEBPACK_IMPORTED_MODULE_5__.left;\n        var _altSide = mainAxis === \"x\" ? _enums_js__WEBPACK_IMPORTED_MODULE_5__.bottom : _enums_js__WEBPACK_IMPORTED_MODULE_5__.right;\n        var _offset = popperOffsets[altAxis];\n        var _len = altAxis === \"y\" ? \"height\" : \"width\";\n        var _min = _offset + overflow[_mainSide];\n        var _max = _offset - overflow[_altSide];\n        var isOriginSide = [\n            _enums_js__WEBPACK_IMPORTED_MODULE_5__.top,\n            _enums_js__WEBPACK_IMPORTED_MODULE_5__.left\n        ].indexOf(basePlacement) !== -1;\n        var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n        var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n        var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n        var _preventedOffset = tether && isOriginSide ? (0,_utils_within_js__WEBPACK_IMPORTED_MODULE_8__.withinMaxClamp)(_tetherMin, _offset, _tetherMax) : (0,_utils_within_js__WEBPACK_IMPORTED_MODULE_8__.within)(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n        popperOffsets[altAxis] = _preventedOffset;\n        data[altAxis] = _preventedOffset - _offset;\n    }\n    state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    name: \"preventOverflow\",\n    enabled: true,\n    phase: \"main\",\n    fn: preventOverflow,\n    requiresIfExists: [\n        \"offset\"\n    ]\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/modifiers/preventOverflow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/popper-lite.js":
/*!********************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/popper-lite.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPopper: () => (/* binding */ createPopper),\n/* harmony export */   defaultModifiers: () => (/* binding */ defaultModifiers),\n/* harmony export */   detectOverflow: () => (/* reexport safe */ _createPopper_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   popperGenerator: () => (/* reexport safe */ _createPopper_js__WEBPACK_IMPORTED_MODULE_4__.popperGenerator)\n/* harmony export */ });\n/* harmony import */ var _createPopper_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./createPopper.js */ \"(ssr)/./node_modules/@popperjs/core/lib/createPopper.js\");\n/* harmony import */ var _createPopper_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./createPopper.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _modifiers_eventListeners_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./modifiers/eventListeners.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/eventListeners.js\");\n/* harmony import */ var _modifiers_popperOffsets_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modifiers/popperOffsets.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js\");\n/* harmony import */ var _modifiers_computeStyles_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modifiers/computeStyles.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/computeStyles.js\");\n/* harmony import */ var _modifiers_applyStyles_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./modifiers/applyStyles.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/applyStyles.js\");\n\n\n\n\n\nvar defaultModifiers = [\n    _modifiers_eventListeners_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    _modifiers_popperOffsets_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    _modifiers_computeStyles_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    _modifiers_applyStyles_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n];\nvar createPopper = /*#__PURE__*/ (0,_createPopper_js__WEBPACK_IMPORTED_MODULE_4__.popperGenerator)({\n    defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3BvcHBlci1saXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBb0U7QUFDVDtBQUNGO0FBQ0E7QUFDSjtBQUNyRCxJQUFJTSxtQkFBbUI7SUFBQ0osb0VBQWNBO0lBQUVDLG1FQUFhQTtJQUFFQyxtRUFBYUE7SUFBRUMsaUVBQVdBO0NBQUM7QUFDbEYsSUFBSUUsZUFBZSxXQUFXLEdBQUVQLGlFQUFlQSxDQUFDO0lBQzlDTSxrQkFBa0JBO0FBQ3BCLElBQUksb0RBQW9EO0FBRW1CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3BvcHBlci1saXRlLmpzP2Y4MzciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcG9wcGVyR2VuZXJhdG9yLCBkZXRlY3RPdmVyZmxvdyB9IGZyb20gXCIuL2NyZWF0ZVBvcHBlci5qc1wiO1xuaW1wb3J0IGV2ZW50TGlzdGVuZXJzIGZyb20gXCIuL21vZGlmaWVycy9ldmVudExpc3RlbmVycy5qc1wiO1xuaW1wb3J0IHBvcHBlck9mZnNldHMgZnJvbSBcIi4vbW9kaWZpZXJzL3BvcHBlck9mZnNldHMuanNcIjtcbmltcG9ydCBjb21wdXRlU3R5bGVzIGZyb20gXCIuL21vZGlmaWVycy9jb21wdXRlU3R5bGVzLmpzXCI7XG5pbXBvcnQgYXBwbHlTdHlsZXMgZnJvbSBcIi4vbW9kaWZpZXJzL2FwcGx5U3R5bGVzLmpzXCI7XG52YXIgZGVmYXVsdE1vZGlmaWVycyA9IFtldmVudExpc3RlbmVycywgcG9wcGVyT2Zmc2V0cywgY29tcHV0ZVN0eWxlcywgYXBwbHlTdHlsZXNdO1xudmFyIGNyZWF0ZVBvcHBlciA9IC8qI19fUFVSRV9fKi9wb3BwZXJHZW5lcmF0b3Ioe1xuICBkZWZhdWx0TW9kaWZpZXJzOiBkZWZhdWx0TW9kaWZpZXJzXG59KTsgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGltcG9ydC9uby11bnVzZWQtbW9kdWxlc1xuXG5leHBvcnQgeyBjcmVhdGVQb3BwZXIsIHBvcHBlckdlbmVyYXRvciwgZGVmYXVsdE1vZGlmaWVycywgZGV0ZWN0T3ZlcmZsb3cgfTsiXSwibmFtZXMiOlsicG9wcGVyR2VuZXJhdG9yIiwiZGV0ZWN0T3ZlcmZsb3ciLCJldmVudExpc3RlbmVycyIsInBvcHBlck9mZnNldHMiLCJjb21wdXRlU3R5bGVzIiwiYXBwbHlTdHlsZXMiLCJkZWZhdWx0TW9kaWZpZXJzIiwiY3JlYXRlUG9wcGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/popper-lite.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/popper.js":
/*!***************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/popper.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyStyles: () => (/* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.applyStyles),\n/* harmony export */   arrow: () => (/* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.arrow),\n/* harmony export */   computeStyles: () => (/* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.computeStyles),\n/* harmony export */   createPopper: () => (/* binding */ createPopper),\n/* harmony export */   createPopperLite: () => (/* reexport safe */ _popper_lite_js__WEBPACK_IMPORTED_MODULE_11__.createPopper),\n/* harmony export */   defaultModifiers: () => (/* binding */ defaultModifiers),\n/* harmony export */   detectOverflow: () => (/* reexport safe */ _createPopper_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   eventListeners: () => (/* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.eventListeners),\n/* harmony export */   flip: () => (/* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.flip),\n/* harmony export */   hide: () => (/* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.hide),\n/* harmony export */   offset: () => (/* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.offset),\n/* harmony export */   popperGenerator: () => (/* reexport safe */ _createPopper_js__WEBPACK_IMPORTED_MODULE_9__.popperGenerator),\n/* harmony export */   popperOffsets: () => (/* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.popperOffsets),\n/* harmony export */   preventOverflow: () => (/* reexport safe */ _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__.preventOverflow)\n/* harmony export */ });\n/* harmony import */ var _createPopper_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./createPopper.js */ \"(ssr)/./node_modules/@popperjs/core/lib/createPopper.js\");\n/* harmony import */ var _createPopper_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./createPopper.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _modifiers_eventListeners_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./modifiers/eventListeners.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/eventListeners.js\");\n/* harmony import */ var _modifiers_popperOffsets_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modifiers/popperOffsets.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/popperOffsets.js\");\n/* harmony import */ var _modifiers_computeStyles_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modifiers/computeStyles.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/computeStyles.js\");\n/* harmony import */ var _modifiers_applyStyles_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./modifiers/applyStyles.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/applyStyles.js\");\n/* harmony import */ var _modifiers_offset_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./modifiers/offset.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/offset.js\");\n/* harmony import */ var _modifiers_flip_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./modifiers/flip.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/flip.js\");\n/* harmony import */ var _modifiers_preventOverflow_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./modifiers/preventOverflow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/preventOverflow.js\");\n/* harmony import */ var _modifiers_arrow_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./modifiers/arrow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/arrow.js\");\n/* harmony import */ var _modifiers_hide_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./modifiers/hide.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/hide.js\");\n/* harmony import */ var _popper_lite_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./popper-lite.js */ \"(ssr)/./node_modules/@popperjs/core/lib/popper-lite.js\");\n/* harmony import */ var _modifiers_index_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./modifiers/index.js */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/index.js\");\n\n\n\n\n\n\n\n\n\n\nvar defaultModifiers = [\n    _modifiers_eventListeners_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    _modifiers_popperOffsets_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    _modifiers_computeStyles_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    _modifiers_applyStyles_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    _modifiers_offset_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    _modifiers_flip_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    _modifiers_preventOverflow_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    _modifiers_arrow_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    _modifiers_hide_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n];\nvar createPopper = /*#__PURE__*/ (0,_createPopper_js__WEBPACK_IMPORTED_MODULE_9__.popperGenerator)({\n    defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n // eslint-disable-next-line import/no-unused-modules\n // eslint-disable-next-line import/no-unused-modules\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3BvcHBlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBb0U7QUFDVDtBQUNGO0FBQ0E7QUFDSjtBQUNWO0FBQ0o7QUFDc0I7QUFDcEI7QUFDRjtBQUN2QyxJQUFJVyxtQkFBbUI7SUFBQ1Qsb0VBQWNBO0lBQUVDLG1FQUFhQTtJQUFFQyxtRUFBYUE7SUFBRUMsaUVBQVdBO0lBQUVDLDREQUFNQTtJQUFFQywwREFBSUE7SUFBRUMscUVBQWVBO0lBQUVDLDJEQUFLQTtJQUFFQywwREFBSUE7Q0FBQztBQUM5SCxJQUFJRSxlQUFlLFdBQVcsR0FBRVosaUVBQWVBLENBQUM7SUFDOUNXLGtCQUFrQkE7QUFDcEIsSUFBSSxvREFBb0Q7QUFFbUIsQ0FBQyxvREFBb0Q7QUFFNUQsQ0FBQyxvREFBb0Q7QUFFcEYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvcG9wcGVyLmpzP2FmNTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcG9wcGVyR2VuZXJhdG9yLCBkZXRlY3RPdmVyZmxvdyB9IGZyb20gXCIuL2NyZWF0ZVBvcHBlci5qc1wiO1xuaW1wb3J0IGV2ZW50TGlzdGVuZXJzIGZyb20gXCIuL21vZGlmaWVycy9ldmVudExpc3RlbmVycy5qc1wiO1xuaW1wb3J0IHBvcHBlck9mZnNldHMgZnJvbSBcIi4vbW9kaWZpZXJzL3BvcHBlck9mZnNldHMuanNcIjtcbmltcG9ydCBjb21wdXRlU3R5bGVzIGZyb20gXCIuL21vZGlmaWVycy9jb21wdXRlU3R5bGVzLmpzXCI7XG5pbXBvcnQgYXBwbHlTdHlsZXMgZnJvbSBcIi4vbW9kaWZpZXJzL2FwcGx5U3R5bGVzLmpzXCI7XG5pbXBvcnQgb2Zmc2V0IGZyb20gXCIuL21vZGlmaWVycy9vZmZzZXQuanNcIjtcbmltcG9ydCBmbGlwIGZyb20gXCIuL21vZGlmaWVycy9mbGlwLmpzXCI7XG5pbXBvcnQgcHJldmVudE92ZXJmbG93IGZyb20gXCIuL21vZGlmaWVycy9wcmV2ZW50T3ZlcmZsb3cuanNcIjtcbmltcG9ydCBhcnJvdyBmcm9tIFwiLi9tb2RpZmllcnMvYXJyb3cuanNcIjtcbmltcG9ydCBoaWRlIGZyb20gXCIuL21vZGlmaWVycy9oaWRlLmpzXCI7XG52YXIgZGVmYXVsdE1vZGlmaWVycyA9IFtldmVudExpc3RlbmVycywgcG9wcGVyT2Zmc2V0cywgY29tcHV0ZVN0eWxlcywgYXBwbHlTdHlsZXMsIG9mZnNldCwgZmxpcCwgcHJldmVudE92ZXJmbG93LCBhcnJvdywgaGlkZV07XG52YXIgY3JlYXRlUG9wcGVyID0gLyojX19QVVJFX18qL3BvcHBlckdlbmVyYXRvcih7XG4gIGRlZmF1bHRNb2RpZmllcnM6IGRlZmF1bHRNb2RpZmllcnNcbn0pOyAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgaW1wb3J0L25vLXVudXNlZC1tb2R1bGVzXG5cbmV4cG9ydCB7IGNyZWF0ZVBvcHBlciwgcG9wcGVyR2VuZXJhdG9yLCBkZWZhdWx0TW9kaWZpZXJzLCBkZXRlY3RPdmVyZmxvdyB9OyAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgaW1wb3J0L25vLXVudXNlZC1tb2R1bGVzXG5cbmV4cG9ydCB7IGNyZWF0ZVBvcHBlciBhcyBjcmVhdGVQb3BwZXJMaXRlIH0gZnJvbSBcIi4vcG9wcGVyLWxpdGUuanNcIjsgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGltcG9ydC9uby11bnVzZWQtbW9kdWxlc1xuXG5leHBvcnQgKiBmcm9tIFwiLi9tb2RpZmllcnMvaW5kZXguanNcIjsiXSwibmFtZXMiOlsicG9wcGVyR2VuZXJhdG9yIiwiZGV0ZWN0T3ZlcmZsb3ciLCJldmVudExpc3RlbmVycyIsInBvcHBlck9mZnNldHMiLCJjb21wdXRlU3R5bGVzIiwiYXBwbHlTdHlsZXMiLCJvZmZzZXQiLCJmbGlwIiwicHJldmVudE92ZXJmbG93IiwiYXJyb3ciLCJoaWRlIiwiZGVmYXVsdE1vZGlmaWVycyIsImNyZWF0ZVBvcHBlciIsImNyZWF0ZVBvcHBlckxpdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/popper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ computeAutoPlacement)\n/* harmony export */ });\n/* harmony import */ var _getVariation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getVariation.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getVariation.js\");\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../enums.js */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _detectOverflow_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./detectOverflow.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/detectOverflow.js\");\n/* harmony import */ var _getBasePlacement_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getBasePlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n\n\n\n\nfunction computeAutoPlacement(state, options) {\n    if (options === void 0) {\n        options = {};\n    }\n    var _options = options, placement = _options.placement, boundary = _options.boundary, rootBoundary = _options.rootBoundary, padding = _options.padding, flipVariations = _options.flipVariations, _options$allowedAutoP = _options.allowedAutoPlacements, allowedAutoPlacements = _options$allowedAutoP === void 0 ? _enums_js__WEBPACK_IMPORTED_MODULE_0__.placements : _options$allowedAutoP;\n    var variation = (0,_getVariation_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(placement);\n    var placements = variation ? flipVariations ? _enums_js__WEBPACK_IMPORTED_MODULE_0__.variationPlacements : _enums_js__WEBPACK_IMPORTED_MODULE_0__.variationPlacements.filter(function(placement) {\n        return (0,_getVariation_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(placement) === variation;\n    }) : _enums_js__WEBPACK_IMPORTED_MODULE_0__.basePlacements;\n    var allowedPlacements = placements.filter(function(placement) {\n        return allowedAutoPlacements.indexOf(placement) >= 0;\n    });\n    if (allowedPlacements.length === 0) {\n        allowedPlacements = placements;\n    } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n    var overflows = allowedPlacements.reduce(function(acc, placement) {\n        acc[placement] = (0,_detectOverflow_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(state, {\n            placement: placement,\n            boundary: boundary,\n            rootBoundary: rootBoundary,\n            padding: padding\n        })[(0,_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(placement)];\n        return acc;\n    }, {});\n    return Object.keys(overflows).sort(function(a, b) {\n        return overflows[a] - overflows[b];\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/computeOffsets.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/computeOffsets.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ computeOffsets)\n/* harmony export */ });\n/* harmony import */ var _getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getBasePlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\");\n/* harmony import */ var _getVariation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getVariation.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getVariation.js\");\n/* harmony import */ var _getMainAxisFromPlacement_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getMainAxisFromPlacement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js\");\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../enums.js */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n\n\n\n\nfunction computeOffsets(_ref) {\n    var reference = _ref.reference, element = _ref.element, placement = _ref.placement;\n    var basePlacement = placement ? (0,_getBasePlacement_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(placement) : null;\n    var variation = placement ? (0,_getVariation_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(placement) : null;\n    var commonX = reference.x + reference.width / 2 - element.width / 2;\n    var commonY = reference.y + reference.height / 2 - element.height / 2;\n    var offsets;\n    switch(basePlacement){\n        case _enums_js__WEBPACK_IMPORTED_MODULE_2__.top:\n            offsets = {\n                x: commonX,\n                y: reference.y - element.height\n            };\n            break;\n        case _enums_js__WEBPACK_IMPORTED_MODULE_2__.bottom:\n            offsets = {\n                x: commonX,\n                y: reference.y + reference.height\n            };\n            break;\n        case _enums_js__WEBPACK_IMPORTED_MODULE_2__.right:\n            offsets = {\n                x: reference.x + reference.width,\n                y: commonY\n            };\n            break;\n        case _enums_js__WEBPACK_IMPORTED_MODULE_2__.left:\n            offsets = {\n                x: reference.x - element.width,\n                y: commonY\n            };\n            break;\n        default:\n            offsets = {\n                x: reference.x,\n                y: reference.y\n            };\n    }\n    var mainAxis = basePlacement ? (0,_getMainAxisFromPlacement_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(basePlacement) : null;\n    if (mainAxis != null) {\n        var len = mainAxis === \"y\" ? \"height\" : \"width\";\n        switch(variation){\n            case _enums_js__WEBPACK_IMPORTED_MODULE_2__.start:\n                offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n                break;\n            case _enums_js__WEBPACK_IMPORTED_MODULE_2__.end:\n                offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n                break;\n            default:\n        }\n    }\n    return offsets;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/computeOffsets.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/debounce.js":
/*!***********************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/debounce.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ debounce)\n/* harmony export */ });\nfunction debounce(fn) {\n    var pending;\n    return function() {\n        if (!pending) {\n            pending = new Promise(function(resolve) {\n                Promise.resolve().then(function() {\n                    pending = undefined;\n                    resolve(fn());\n                });\n            });\n        }\n        return pending;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL2RlYm91bmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxTQUFTQyxFQUFFO0lBQ2pDLElBQUlDO0lBQ0osT0FBTztRQUNMLElBQUksQ0FBQ0EsU0FBUztZQUNaQSxVQUFVLElBQUlDLFFBQVEsU0FBVUMsT0FBTztnQkFDckNELFFBQVFDLE9BQU8sR0FBR0MsSUFBSSxDQUFDO29CQUNyQkgsVUFBVUk7b0JBQ1ZGLFFBQVFIO2dCQUNWO1lBQ0Y7UUFDRjtRQUVBLE9BQU9DO0lBQ1Q7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL0Bwb3BwZXJqcy9jb3JlL2xpYi91dGlscy9kZWJvdW5jZS5qcz8yNjI3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGRlYm91bmNlKGZuKSB7XG4gIHZhciBwZW5kaW5nO1xuICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgIGlmICghcGVuZGluZykge1xuICAgICAgcGVuZGluZyA9IG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlKSB7XG4gICAgICAgIFByb21pc2UucmVzb2x2ZSgpLnRoZW4oZnVuY3Rpb24gKCkge1xuICAgICAgICAgIHBlbmRpbmcgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgcmVzb2x2ZShmbigpKTtcbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICByZXR1cm4gcGVuZGluZztcbiAgfTtcbn0iXSwibmFtZXMiOlsiZGVib3VuY2UiLCJmbiIsInBlbmRpbmciLCJQcm9taXNlIiwicmVzb2x2ZSIsInRoZW4iLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/debounce.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/detectOverflow.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/detectOverflow.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ detectOverflow)\n/* harmony export */ });\n/* harmony import */ var _dom_utils_getClippingRect_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../dom-utils/getClippingRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js\");\n/* harmony import */ var _dom_utils_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dom-utils/getDocumentElement.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js\");\n/* harmony import */ var _dom_utils_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../dom-utils/getBoundingClientRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js\");\n/* harmony import */ var _computeOffsets_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./computeOffsets.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/computeOffsets.js\");\n/* harmony import */ var _rectToClientRect_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./rectToClientRect.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/rectToClientRect.js\");\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../enums.js */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n/* harmony import */ var _dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../dom-utils/instanceOf.js */ \"(ssr)/./node_modules/@popperjs/core/lib/dom-utils/instanceOf.js\");\n/* harmony import */ var _mergePaddingObject_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mergePaddingObject.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/mergePaddingObject.js\");\n/* harmony import */ var _expandToHashMap_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./expandToHashMap.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/expandToHashMap.js\");\n\n\n\n\n\n\n\n\n // eslint-disable-next-line import/no-unused-modules\nfunction detectOverflow(state, options) {\n    if (options === void 0) {\n        options = {};\n    }\n    var _options = options, _options$placement = _options.placement, placement = _options$placement === void 0 ? state.placement : _options$placement, _options$strategy = _options.strategy, strategy = _options$strategy === void 0 ? state.strategy : _options$strategy, _options$boundary = _options.boundary, boundary = _options$boundary === void 0 ? _enums_js__WEBPACK_IMPORTED_MODULE_0__.clippingParents : _options$boundary, _options$rootBoundary = _options.rootBoundary, rootBoundary = _options$rootBoundary === void 0 ? _enums_js__WEBPACK_IMPORTED_MODULE_0__.viewport : _options$rootBoundary, _options$elementConte = _options.elementContext, elementContext = _options$elementConte === void 0 ? _enums_js__WEBPACK_IMPORTED_MODULE_0__.popper : _options$elementConte, _options$altBoundary = _options.altBoundary, altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary, _options$padding = _options.padding, padding = _options$padding === void 0 ? 0 : _options$padding;\n    var paddingObject = (0,_mergePaddingObject_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(typeof padding !== \"number\" ? padding : (0,_expandToHashMap_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(padding, _enums_js__WEBPACK_IMPORTED_MODULE_0__.basePlacements));\n    var altContext = elementContext === _enums_js__WEBPACK_IMPORTED_MODULE_0__.popper ? _enums_js__WEBPACK_IMPORTED_MODULE_0__.reference : _enums_js__WEBPACK_IMPORTED_MODULE_0__.popper;\n    var popperRect = state.rects.popper;\n    var element = state.elements[altBoundary ? altContext : elementContext];\n    var clippingClientRect = (0,_dom_utils_getClippingRect_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,_dom_utils_instanceOf_js__WEBPACK_IMPORTED_MODULE_4__.isElement)(element) ? element : element.contextElement || (0,_dom_utils_getDocumentElement_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(state.elements.popper), boundary, rootBoundary, strategy);\n    var referenceClientRect = (0,_dom_utils_getBoundingClientRect_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(state.elements.reference);\n    var popperOffsets = (0,_computeOffsets_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])({\n        reference: referenceClientRect,\n        element: popperRect,\n        strategy: \"absolute\",\n        placement: placement\n    });\n    var popperClientRect = (0,_rectToClientRect_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(Object.assign({}, popperRect, popperOffsets));\n    var elementClientRect = elementContext === _enums_js__WEBPACK_IMPORTED_MODULE_0__.popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n    // 0 or negative = within the clipping rect\n    var overflowOffsets = {\n        top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n        bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n        left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n        right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n    };\n    var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n    if (elementContext === _enums_js__WEBPACK_IMPORTED_MODULE_0__.popper && offsetData) {\n        var offset = offsetData[placement];\n        Object.keys(overflowOffsets).forEach(function(key) {\n            var multiply = [\n                _enums_js__WEBPACK_IMPORTED_MODULE_0__.right,\n                _enums_js__WEBPACK_IMPORTED_MODULE_0__.bottom\n            ].indexOf(key) >= 0 ? 1 : -1;\n            var axis = [\n                _enums_js__WEBPACK_IMPORTED_MODULE_0__.top,\n                _enums_js__WEBPACK_IMPORTED_MODULE_0__.bottom\n            ].indexOf(key) >= 0 ? \"y\" : \"x\";\n            overflowOffsets[key] += offset[axis] * multiply;\n        });\n    }\n    return overflowOffsets;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/detectOverflow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/expandToHashMap.js":
/*!******************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/expandToHashMap.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ expandToHashMap)\n/* harmony export */ });\nfunction expandToHashMap(value, keys) {\n    return keys.reduce(function(hashMap, key) {\n        hashMap[key] = value;\n        return hashMap;\n    }, {});\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL2V4cGFuZFRvSGFzaE1hcC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsZ0JBQWdCQyxLQUFLLEVBQUVDLElBQUk7SUFDakQsT0FBT0EsS0FBS0MsTUFBTSxDQUFDLFNBQVVDLE9BQU8sRUFBRUMsR0FBRztRQUN2Q0QsT0FBTyxDQUFDQyxJQUFJLEdBQUdKO1FBQ2YsT0FBT0c7SUFDVCxHQUFHLENBQUM7QUFDTiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL0Bwb3BwZXJqcy9jb3JlL2xpYi91dGlscy9leHBhbmRUb0hhc2hNYXAuanM/OWY4MCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBleHBhbmRUb0hhc2hNYXAodmFsdWUsIGtleXMpIHtcbiAgcmV0dXJuIGtleXMucmVkdWNlKGZ1bmN0aW9uIChoYXNoTWFwLCBrZXkpIHtcbiAgICBoYXNoTWFwW2tleV0gPSB2YWx1ZTtcbiAgICByZXR1cm4gaGFzaE1hcDtcbiAgfSwge30pO1xufSJdLCJuYW1lcyI6WyJleHBhbmRUb0hhc2hNYXAiLCJ2YWx1ZSIsImtleXMiLCJyZWR1Y2UiLCJoYXNoTWFwIiwia2V5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/expandToHashMap.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/getAltAxis.js":
/*!*************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getAltAxis.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getAltAxis)\n/* harmony export */ });\nfunction getAltAxis(axis) {\n    return axis === \"x\" ? \"y\" : \"x\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL2dldEFsdEF4aXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLFdBQVdDLElBQUk7SUFDckMsT0FBT0EsU0FBUyxNQUFNLE1BQU07QUFDOUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvdXRpbHMvZ2V0QWx0QXhpcy5qcz8zMmIyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdldEFsdEF4aXMoYXhpcykge1xuICByZXR1cm4gYXhpcyA9PT0gJ3gnID8gJ3knIDogJ3gnO1xufSJdLCJuYW1lcyI6WyJnZXRBbHRBeGlzIiwiYXhpcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/getAltAxis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/getBasePlacement.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getBasePlacement.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getBasePlacement)\n/* harmony export */ });\n\nfunction getBasePlacement(placement) {\n    return placement.split(\"-\")[0];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL2dldEJhc2VQbGFjZW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFtQztBQUNwQixTQUFTQyxpQkFBaUJDLFNBQVM7SUFDaEQsT0FBT0EsVUFBVUMsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO0FBQ2hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL2dldEJhc2VQbGFjZW1lbnQuanM/ZGU5MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBhdXRvIH0gZnJvbSBcIi4uL2VudW1zLmpzXCI7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRCYXNlUGxhY2VtZW50KHBsYWNlbWVudCkge1xuICByZXR1cm4gcGxhY2VtZW50LnNwbGl0KCctJylbMF07XG59Il0sIm5hbWVzIjpbImF1dG8iLCJnZXRCYXNlUGxhY2VtZW50IiwicGxhY2VtZW50Iiwic3BsaXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/getBasePlacement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/getFreshSideObject.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getFreshSideObject.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getFreshSideObject)\n/* harmony export */ });\nfunction getFreshSideObject() {\n    return {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL2dldEZyZXNoU2lkZU9iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0E7SUFDdEIsT0FBTztRQUNMQyxLQUFLO1FBQ0xDLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxNQUFNO0lBQ1I7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL0Bwb3BwZXJqcy9jb3JlL2xpYi91dGlscy9nZXRGcmVzaFNpZGVPYmplY3QuanM/N2U2OSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRGcmVzaFNpZGVPYmplY3QoKSB7XG4gIHJldHVybiB7XG4gICAgdG9wOiAwLFxuICAgIHJpZ2h0OiAwLFxuICAgIGJvdHRvbTogMCxcbiAgICBsZWZ0OiAwXG4gIH07XG59Il0sIm5hbWVzIjpbImdldEZyZXNoU2lkZU9iamVjdCIsInRvcCIsInJpZ2h0IiwiYm90dG9tIiwibGVmdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/getFreshSideObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getMainAxisFromPlacement)\n/* harmony export */ });\nfunction getMainAxisFromPlacement(placement) {\n    return [\n        \"top\",\n        \"bottom\"\n    ].indexOf(placement) >= 0 ? \"x\" : \"y\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL2dldE1haW5BeGlzRnJvbVBsYWNlbWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EseUJBQXlCQyxTQUFTO0lBQ3hELE9BQU87UUFBQztRQUFPO0tBQVMsQ0FBQ0MsT0FBTyxDQUFDRCxjQUFjLElBQUksTUFBTTtBQUMzRCIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL0Bwb3BwZXJqcy9jb3JlL2xpYi91dGlscy9nZXRNYWluQXhpc0Zyb21QbGFjZW1lbnQuanM/MWUzMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRNYWluQXhpc0Zyb21QbGFjZW1lbnQocGxhY2VtZW50KSB7XG4gIHJldHVybiBbJ3RvcCcsICdib3R0b20nXS5pbmRleE9mKHBsYWNlbWVudCkgPj0gMCA/ICd4JyA6ICd5Jztcbn0iXSwibmFtZXMiOlsiZ2V0TWFpbkF4aXNGcm9tUGxhY2VtZW50IiwicGxhY2VtZW50IiwiaW5kZXhPZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/getOppositePlacement.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getOppositePlacement.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getOppositePlacement)\n/* harmony export */ });\nvar hash = {\n    left: \"right\",\n    right: \"left\",\n    bottom: \"top\",\n    top: \"bottom\"\n};\nfunction getOppositePlacement(placement) {\n    return placement.replace(/left|right|bottom|top/g, function(matched) {\n        return hash[matched];\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL2dldE9wcG9zaXRlUGxhY2VtZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxJQUFJQSxPQUFPO0lBQ1RDLE1BQU07SUFDTkMsT0FBTztJQUNQQyxRQUFRO0lBQ1JDLEtBQUs7QUFDUDtBQUNlLFNBQVNDLHFCQUFxQkMsU0FBUztJQUNwRCxPQUFPQSxVQUFVQyxPQUFPLENBQUMsMEJBQTBCLFNBQVVDLE9BQU87UUFDbEUsT0FBT1IsSUFBSSxDQUFDUSxRQUFRO0lBQ3RCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvdXRpbHMvZ2V0T3Bwb3NpdGVQbGFjZW1lbnQuanM/ZTU4NCJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgaGFzaCA9IHtcbiAgbGVmdDogJ3JpZ2h0JyxcbiAgcmlnaHQ6ICdsZWZ0JyxcbiAgYm90dG9tOiAndG9wJyxcbiAgdG9wOiAnYm90dG9tJ1xufTtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdldE9wcG9zaXRlUGxhY2VtZW50KHBsYWNlbWVudCkge1xuICByZXR1cm4gcGxhY2VtZW50LnJlcGxhY2UoL2xlZnR8cmlnaHR8Ym90dG9tfHRvcC9nLCBmdW5jdGlvbiAobWF0Y2hlZCkge1xuICAgIHJldHVybiBoYXNoW21hdGNoZWRdO1xuICB9KTtcbn0iXSwibmFtZXMiOlsiaGFzaCIsImxlZnQiLCJyaWdodCIsImJvdHRvbSIsInRvcCIsImdldE9wcG9zaXRlUGxhY2VtZW50IiwicGxhY2VtZW50IiwicmVwbGFjZSIsIm1hdGNoZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/getOppositePlacement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getOppositeVariationPlacement)\n/* harmony export */ });\nvar hash = {\n    start: \"end\",\n    end: \"start\"\n};\nfunction getOppositeVariationPlacement(placement) {\n    return placement.replace(/start|end/g, function(matched) {\n        return hash[matched];\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL2dldE9wcG9zaXRlVmFyaWF0aW9uUGxhY2VtZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxJQUFJQSxPQUFPO0lBQ1RDLE9BQU87SUFDUEMsS0FBSztBQUNQO0FBQ2UsU0FBU0MsOEJBQThCQyxTQUFTO0lBQzdELE9BQU9BLFVBQVVDLE9BQU8sQ0FBQyxjQUFjLFNBQVVDLE9BQU87UUFDdEQsT0FBT04sSUFBSSxDQUFDTSxRQUFRO0lBQ3RCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvdXRpbHMvZ2V0T3Bwb3NpdGVWYXJpYXRpb25QbGFjZW1lbnQuanM/M2VlOSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgaGFzaCA9IHtcbiAgc3RhcnQ6ICdlbmQnLFxuICBlbmQ6ICdzdGFydCdcbn07XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRPcHBvc2l0ZVZhcmlhdGlvblBsYWNlbWVudChwbGFjZW1lbnQpIHtcbiAgcmV0dXJuIHBsYWNlbWVudC5yZXBsYWNlKC9zdGFydHxlbmQvZywgZnVuY3Rpb24gKG1hdGNoZWQpIHtcbiAgICByZXR1cm4gaGFzaFttYXRjaGVkXTtcbiAgfSk7XG59Il0sIm5hbWVzIjpbImhhc2giLCJzdGFydCIsImVuZCIsImdldE9wcG9zaXRlVmFyaWF0aW9uUGxhY2VtZW50IiwicGxhY2VtZW50IiwicmVwbGFjZSIsIm1hdGNoZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/getVariation.js":
/*!***************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/getVariation.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getVariation)\n/* harmony export */ });\nfunction getVariation(placement) {\n    return placement.split(\"-\")[1];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL2dldFZhcmlhdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsYUFBYUMsU0FBUztJQUM1QyxPQUFPQSxVQUFVQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7QUFDaEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvdXRpbHMvZ2V0VmFyaWF0aW9uLmpzPzYwOTIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZ2V0VmFyaWF0aW9uKHBsYWNlbWVudCkge1xuICByZXR1cm4gcGxhY2VtZW50LnNwbGl0KCctJylbMV07XG59Il0sIm5hbWVzIjpbImdldFZhcmlhdGlvbiIsInBsYWNlbWVudCIsInNwbGl0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/getVariation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/math.js":
/*!*******************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/math.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   round: () => (/* binding */ round)\n/* harmony export */ });\nvar max = Math.max;\nvar min = Math.min;\nvar round = Math.round;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL21hdGguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU8sSUFBSUEsTUFBTUMsS0FBS0QsR0FBRyxDQUFDO0FBQ25CLElBQUlFLE1BQU1ELEtBQUtDLEdBQUcsQ0FBQztBQUNuQixJQUFJQyxRQUFRRixLQUFLRSxLQUFLLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvdXRpbHMvbWF0aC5qcz8yYWYwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgbWF4ID0gTWF0aC5tYXg7XG5leHBvcnQgdmFyIG1pbiA9IE1hdGgubWluO1xuZXhwb3J0IHZhciByb3VuZCA9IE1hdGgucm91bmQ7Il0sIm5hbWVzIjpbIm1heCIsIk1hdGgiLCJtaW4iLCJyb3VuZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/math.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/mergeByName.js":
/*!**************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/mergeByName.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mergeByName)\n/* harmony export */ });\nfunction mergeByName(modifiers) {\n    var merged = modifiers.reduce(function(merged, current) {\n        var existing = merged[current.name];\n        merged[current.name] = existing ? Object.assign({}, existing, current, {\n            options: Object.assign({}, existing.options, current.options),\n            data: Object.assign({}, existing.data, current.data)\n        }) : current;\n        return merged;\n    }, {}); // IE11 does not support Object.values\n    return Object.keys(merged).map(function(key) {\n        return merged[key];\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL21lcmdlQnlOYW1lLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxZQUFZQyxTQUFTO0lBQzNDLElBQUlDLFNBQVNELFVBQVVFLE1BQU0sQ0FBQyxTQUFVRCxNQUFNLEVBQUVFLE9BQU87UUFDckQsSUFBSUMsV0FBV0gsTUFBTSxDQUFDRSxRQUFRRSxJQUFJLENBQUM7UUFDbkNKLE1BQU0sQ0FBQ0UsUUFBUUUsSUFBSSxDQUFDLEdBQUdELFdBQVdFLE9BQU9DLE1BQU0sQ0FBQyxDQUFDLEdBQUdILFVBQVVELFNBQVM7WUFDckVLLFNBQVNGLE9BQU9DLE1BQU0sQ0FBQyxDQUFDLEdBQUdILFNBQVNJLE9BQU8sRUFBRUwsUUFBUUssT0FBTztZQUM1REMsTUFBTUgsT0FBT0MsTUFBTSxDQUFDLENBQUMsR0FBR0gsU0FBU0ssSUFBSSxFQUFFTixRQUFRTSxJQUFJO1FBQ3JELEtBQUtOO1FBQ0wsT0FBT0Y7SUFDVCxHQUFHLENBQUMsSUFBSSxzQ0FBc0M7SUFFOUMsT0FBT0ssT0FBT0ksSUFBSSxDQUFDVCxRQUFRVSxHQUFHLENBQUMsU0FBVUMsR0FBRztRQUMxQyxPQUFPWCxNQUFNLENBQUNXLElBQUk7SUFDcEI7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL0Bwb3BwZXJqcy9jb3JlL2xpYi91dGlscy9tZXJnZUJ5TmFtZS5qcz85NDgwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIG1lcmdlQnlOYW1lKG1vZGlmaWVycykge1xuICB2YXIgbWVyZ2VkID0gbW9kaWZpZXJzLnJlZHVjZShmdW5jdGlvbiAobWVyZ2VkLCBjdXJyZW50KSB7XG4gICAgdmFyIGV4aXN0aW5nID0gbWVyZ2VkW2N1cnJlbnQubmFtZV07XG4gICAgbWVyZ2VkW2N1cnJlbnQubmFtZV0gPSBleGlzdGluZyA/IE9iamVjdC5hc3NpZ24oe30sIGV4aXN0aW5nLCBjdXJyZW50LCB7XG4gICAgICBvcHRpb25zOiBPYmplY3QuYXNzaWduKHt9LCBleGlzdGluZy5vcHRpb25zLCBjdXJyZW50Lm9wdGlvbnMpLFxuICAgICAgZGF0YTogT2JqZWN0LmFzc2lnbih7fSwgZXhpc3RpbmcuZGF0YSwgY3VycmVudC5kYXRhKVxuICAgIH0pIDogY3VycmVudDtcbiAgICByZXR1cm4gbWVyZ2VkO1xuICB9LCB7fSk7IC8vIElFMTEgZG9lcyBub3Qgc3VwcG9ydCBPYmplY3QudmFsdWVzXG5cbiAgcmV0dXJuIE9iamVjdC5rZXlzKG1lcmdlZCkubWFwKGZ1bmN0aW9uIChrZXkpIHtcbiAgICByZXR1cm4gbWVyZ2VkW2tleV07XG4gIH0pO1xufSJdLCJuYW1lcyI6WyJtZXJnZUJ5TmFtZSIsIm1vZGlmaWVycyIsIm1lcmdlZCIsInJlZHVjZSIsImN1cnJlbnQiLCJleGlzdGluZyIsIm5hbWUiLCJPYmplY3QiLCJhc3NpZ24iLCJvcHRpb25zIiwiZGF0YSIsImtleXMiLCJtYXAiLCJrZXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/mergeByName.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/mergePaddingObject.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/mergePaddingObject.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mergePaddingObject)\n/* harmony export */ });\n/* harmony import */ var _getFreshSideObject_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getFreshSideObject.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/getFreshSideObject.js\");\n\nfunction mergePaddingObject(paddingObject) {\n    return Object.assign({}, (0,_getFreshSideObject_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(), paddingObject);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL21lcmdlUGFkZGluZ09iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF5RDtBQUMxQyxTQUFTQyxtQkFBbUJDLGFBQWE7SUFDdEQsT0FBT0MsT0FBT0MsTUFBTSxDQUFDLENBQUMsR0FBR0osa0VBQWtCQSxJQUFJRTtBQUNqRCIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL0Bwb3BwZXJqcy9jb3JlL2xpYi91dGlscy9tZXJnZVBhZGRpbmdPYmplY3QuanM/NTc2MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZ2V0RnJlc2hTaWRlT2JqZWN0IGZyb20gXCIuL2dldEZyZXNoU2lkZU9iamVjdC5qc1wiO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWVyZ2VQYWRkaW5nT2JqZWN0KHBhZGRpbmdPYmplY3QpIHtcbiAgcmV0dXJuIE9iamVjdC5hc3NpZ24oe30sIGdldEZyZXNoU2lkZU9iamVjdCgpLCBwYWRkaW5nT2JqZWN0KTtcbn0iXSwibmFtZXMiOlsiZ2V0RnJlc2hTaWRlT2JqZWN0IiwibWVyZ2VQYWRkaW5nT2JqZWN0IiwicGFkZGluZ09iamVjdCIsIk9iamVjdCIsImFzc2lnbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/mergePaddingObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/orderModifiers.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/orderModifiers.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ orderModifiers)\n/* harmony export */ });\n/* harmony import */ var _enums_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../enums.js */ \"(ssr)/./node_modules/@popperjs/core/lib/enums.js\");\n // source: https://stackoverflow.com/questions/49875255\nfunction order(modifiers) {\n    var map = new Map();\n    var visited = new Set();\n    var result = [];\n    modifiers.forEach(function(modifier) {\n        map.set(modifier.name, modifier);\n    }); // On visiting object, check for its dependencies and visit them recursively\n    function sort(modifier) {\n        visited.add(modifier.name);\n        var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n        requires.forEach(function(dep) {\n            if (!visited.has(dep)) {\n                var depModifier = map.get(dep);\n                if (depModifier) {\n                    sort(depModifier);\n                }\n            }\n        });\n        result.push(modifier);\n    }\n    modifiers.forEach(function(modifier) {\n        if (!visited.has(modifier.name)) {\n            // check for visited object\n            sort(modifier);\n        }\n    });\n    return result;\n}\nfunction orderModifiers(modifiers) {\n    // order based on dependencies\n    var orderedModifiers = order(modifiers); // order based on phase\n    return _enums_js__WEBPACK_IMPORTED_MODULE_0__.modifierPhases.reduce(function(acc, phase) {\n        return acc.concat(orderedModifiers.filter(function(modifier) {\n            return modifier.phase === phase;\n        }));\n    }, []);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/orderModifiers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/rectToClientRect.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/rectToClientRect.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rectToClientRect)\n/* harmony export */ });\nfunction rectToClientRect(rect) {\n    return Object.assign({}, rect, {\n        left: rect.x,\n        top: rect.y,\n        right: rect.x + rect.width,\n        bottom: rect.y + rect.height\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL3JlY3RUb0NsaWVudFJlY3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBLGlCQUFpQkMsSUFBSTtJQUMzQyxPQUFPQyxPQUFPQyxNQUFNLENBQUMsQ0FBQyxHQUFHRixNQUFNO1FBQzdCRyxNQUFNSCxLQUFLSSxDQUFDO1FBQ1pDLEtBQUtMLEtBQUtNLENBQUM7UUFDWEMsT0FBT1AsS0FBS0ksQ0FBQyxHQUFHSixLQUFLUSxLQUFLO1FBQzFCQyxRQUFRVCxLQUFLTSxDQUFDLEdBQUdOLEtBQUtVLE1BQU07SUFDOUI7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL0Bwb3BwZXJqcy9jb3JlL2xpYi91dGlscy9yZWN0VG9DbGllbnRSZWN0LmpzPzMyNDEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcmVjdFRvQ2xpZW50UmVjdChyZWN0KSB7XG4gIHJldHVybiBPYmplY3QuYXNzaWduKHt9LCByZWN0LCB7XG4gICAgbGVmdDogcmVjdC54LFxuICAgIHRvcDogcmVjdC55LFxuICAgIHJpZ2h0OiByZWN0LnggKyByZWN0LndpZHRoLFxuICAgIGJvdHRvbTogcmVjdC55ICsgcmVjdC5oZWlnaHRcbiAgfSk7XG59Il0sIm5hbWVzIjpbInJlY3RUb0NsaWVudFJlY3QiLCJyZWN0IiwiT2JqZWN0IiwiYXNzaWduIiwibGVmdCIsIngiLCJ0b3AiLCJ5IiwicmlnaHQiLCJ3aWR0aCIsImJvdHRvbSIsImhlaWdodCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/rectToClientRect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/userAgent.js":
/*!************************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/userAgent.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getUAString)\n/* harmony export */ });\nfunction getUAString() {\n    var uaData = navigator.userAgentData;\n    if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n        return uaData.brands.map(function(item) {\n            return item.brand + \"/\" + item.version;\n        }).join(\" \");\n    }\n    return navigator.userAgent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL3VzZXJBZ2VudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0E7SUFDdEIsSUFBSUMsU0FBU0MsVUFBVUMsYUFBYTtJQUVwQyxJQUFJRixVQUFVLFFBQVFBLE9BQU9HLE1BQU0sSUFBSUMsTUFBTUMsT0FBTyxDQUFDTCxPQUFPRyxNQUFNLEdBQUc7UUFDbkUsT0FBT0gsT0FBT0csTUFBTSxDQUFDRyxHQUFHLENBQUMsU0FBVUMsSUFBSTtZQUNyQyxPQUFPQSxLQUFLQyxLQUFLLEdBQUcsTUFBTUQsS0FBS0UsT0FBTztRQUN4QyxHQUFHQyxJQUFJLENBQUM7SUFDVjtJQUVBLE9BQU9ULFVBQVVVLFNBQVM7QUFDNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9AcG9wcGVyanMvY29yZS9saWIvdXRpbHMvdXNlckFnZW50LmpzPzM4ZmQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZ2V0VUFTdHJpbmcoKSB7XG4gIHZhciB1YURhdGEgPSBuYXZpZ2F0b3IudXNlckFnZW50RGF0YTtcblxuICBpZiAodWFEYXRhICE9IG51bGwgJiYgdWFEYXRhLmJyYW5kcyAmJiBBcnJheS5pc0FycmF5KHVhRGF0YS5icmFuZHMpKSB7XG4gICAgcmV0dXJuIHVhRGF0YS5icmFuZHMubWFwKGZ1bmN0aW9uIChpdGVtKSB7XG4gICAgICByZXR1cm4gaXRlbS5icmFuZCArIFwiL1wiICsgaXRlbS52ZXJzaW9uO1xuICAgIH0pLmpvaW4oJyAnKTtcbiAgfVxuXG4gIHJldHVybiBuYXZpZ2F0b3IudXNlckFnZW50O1xufSJdLCJuYW1lcyI6WyJnZXRVQVN0cmluZyIsInVhRGF0YSIsIm5hdmlnYXRvciIsInVzZXJBZ2VudERhdGEiLCJicmFuZHMiLCJBcnJheSIsImlzQXJyYXkiLCJtYXAiLCJpdGVtIiwiYnJhbmQiLCJ2ZXJzaW9uIiwiam9pbiIsInVzZXJBZ2VudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/userAgent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@popperjs/core/lib/utils/within.js":
/*!*********************************************************!*\
  !*** ./node_modules/@popperjs/core/lib/utils/within.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   within: () => (/* binding */ within),\n/* harmony export */   withinMaxClamp: () => (/* binding */ withinMaxClamp)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/@popperjs/core/lib/utils/math.js\");\n\nfunction within(min, value, max) {\n    return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.max)(min, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.min)(value, max));\n}\nfunction withinMaxClamp(min, value, max) {\n    var v = within(min, value, max);\n    return v > max ? max : v;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL3dpdGhpbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMkQ7QUFDcEQsU0FBU0ksT0FBT0YsR0FBRyxFQUFFRyxLQUFLLEVBQUVMLEdBQUc7SUFDcEMsT0FBT0MsNkNBQU9BLENBQUNDLEtBQUtDLDZDQUFPQSxDQUFDRSxPQUFPTDtBQUNyQztBQUNPLFNBQVNNLGVBQWVKLEdBQUcsRUFBRUcsS0FBSyxFQUFFTCxHQUFHO0lBQzVDLElBQUlPLElBQUlILE9BQU9GLEtBQUtHLE9BQU9MO0lBQzNCLE9BQU9PLElBQUlQLE1BQU1BLE1BQU1PO0FBQ3pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvQHBvcHBlcmpzL2NvcmUvbGliL3V0aWxzL3dpdGhpbi5qcz8zN2EzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1heCBhcyBtYXRoTWF4LCBtaW4gYXMgbWF0aE1pbiB9IGZyb20gXCIuL21hdGguanNcIjtcbmV4cG9ydCBmdW5jdGlvbiB3aXRoaW4obWluLCB2YWx1ZSwgbWF4KSB7XG4gIHJldHVybiBtYXRoTWF4KG1pbiwgbWF0aE1pbih2YWx1ZSwgbWF4KSk7XG59XG5leHBvcnQgZnVuY3Rpb24gd2l0aGluTWF4Q2xhbXAobWluLCB2YWx1ZSwgbWF4KSB7XG4gIHZhciB2ID0gd2l0aGluKG1pbiwgdmFsdWUsIG1heCk7XG4gIHJldHVybiB2ID4gbWF4ID8gbWF4IDogdjtcbn0iXSwibmFtZXMiOlsibWF4IiwibWF0aE1heCIsIm1pbiIsIm1hdGhNaW4iLCJ3aXRoaW4iLCJ2YWx1ZSIsIndpdGhpbk1heENsYW1wIiwidiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@popperjs/core/lib/utils/within.js\n");

/***/ })

};
;