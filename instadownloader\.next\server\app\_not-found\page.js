/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5Cinstadownloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5Cinstadownloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5Cinstadownloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5Cinstadownloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5Cinstadownloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5Cinstadownloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5CAds.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5Cnavbar%5C%5CNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5CAds.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5Cnavbar%5C%5CNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/Ads.jsx */ \"(ssr)/./src/app/Ads.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/components/navbar/Navbar.jsx */ \"(ssr)/./src/app/components/navbar/Navbar.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5CAds.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Ccomponents%5C%5Cnavbar%5C%5CNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5Cinstadownloader%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/Ads.jsx":
/*!*************************!*\
  !*** ./src/app/Ads.jsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AdComponent({ adSlot, style = {\n    display: \"block\"\n} }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            (window.adsbygoogle = window.adsbygoogle || []).push({});\n        } catch (err) {\n            console.error(\"AdSense error:\", err);\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n        className: \"adsbygoogle\",\n        style: style,\n        \"data-ad-client\": \"ca-pub-2219975169694529\",\n        \"data-ad-slot\": adSlot,\n        \"data-ad-format\": \"auto\",\n        \"data-full-width-responsive\": \"true\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\Ads.jsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL0Fkcy5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ2tDO0FBRW5CLFNBQVNDLFlBQVksRUFBRUMsTUFBTSxFQUFFQyxRQUFRO0lBQUVDLFNBQVM7QUFBUSxDQUFDLEVBQUU7SUFDMUVKLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSTtZQUNESyxDQUFBQSxPQUFPQyxXQUFXLEdBQUdELE9BQU9DLFdBQVcsSUFBSSxFQUFFLEVBQUVDLElBQUksQ0FBQyxDQUFDO1FBQ3hELEVBQUUsT0FBT0MsS0FBSztZQUNaQyxRQUFRQyxLQUFLLENBQUMsa0JBQWtCRjtRQUNsQztJQUNGLEdBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDRztRQUNDQyxXQUFVO1FBQ1ZULE9BQU9BO1FBQ1BVLGtCQUFlO1FBQ2ZDLGdCQUFjWjtRQUNkYSxrQkFBZTtRQUNmQyw4QkFBMkI7Ozs7OztBQUdqQyIsInNvdXJjZXMiOlsid2VicGFjazovL3Rvb2xwbGF0ZS5haS8uL3NyYy9hcHAvQWRzLmpzeD82NzNiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBZENvbXBvbmVudCh7IGFkU2xvdCwgc3R5bGUgPSB7IGRpc3BsYXk6IFwiYmxvY2tcIiB9IH0pIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICB0cnkge1xuICAgICAgKHdpbmRvdy5hZHNieWdvb2dsZSA9IHdpbmRvdy5hZHNieWdvb2dsZSB8fCBbXSkucHVzaCh7fSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiQWRTZW5zZSBlcnJvcjpcIiwgZXJyKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICByZXR1cm4gKFxuICAgIDxpbnNcbiAgICAgIGNsYXNzTmFtZT1cImFkc2J5Z29vZ2xlXCJcbiAgICAgIHN0eWxlPXtzdHlsZX1cbiAgICAgIGRhdGEtYWQtY2xpZW50PVwiY2EtcHViLTIyMTk5NzUxNjk2OTQ1MjlcIlxuICAgICAgZGF0YS1hZC1zbG90PXthZFNsb3R9XG4gICAgICBkYXRhLWFkLWZvcm1hdD1cImF1dG9cIlxuICAgICAgZGF0YS1mdWxsLXdpZHRoLXJlc3BvbnNpdmU9XCJ0cnVlXCJcbiAgICA+PC9pbnM+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiQWRDb21wb25lbnQiLCJhZFNsb3QiLCJzdHlsZSIsImRpc3BsYXkiLCJ3aW5kb3ciLCJhZHNieWdvb2dsZSIsInB1c2giLCJlcnIiLCJjb25zb2xlIiwiZXJyb3IiLCJpbnMiLCJjbGFzc05hbWUiLCJkYXRhLWFkLWNsaWVudCIsImRhdGEtYWQtc2xvdCIsImRhdGEtYWQtZm9ybWF0IiwiZGF0YS1mdWxsLXdpZHRoLXJlc3BvbnNpdmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Ads.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/navbar/Navbar.jsx":
/*!**********************************************!*\
  !*** ./src/app/components/navbar/Navbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Navbar = ()=>{\n    const pathName = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 left-0 right-0 z-10 bg-white shadow\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"bg-white border-gray-200 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/\",\n                        className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/st_logo.png\",\n                            alt: \"ToolifyAI Logo\",\n                            width: 100,\n                            height: 40,\n                            className: \"h-auto w-auto\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex md:order-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            \"aria-controls\": \"navbar-search\",\n                            \"aria-expanded\": isMenuOpen,\n                            className: \"grid place-items-center md:hidden text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5\",\n                            onClick: toggleMenu,\n                            children: [\n                                isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    \"aria-hidden\": \"true\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        stroke: \"currentColor\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    \"aria-hidden\": \"true\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        stroke: \"currentColor\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M3 6h14M3 10h14m-7 4h7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Toggle navigation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-full md:flex ml-auto md:w-auto md:order-1 ${isMenuOpen ? \"block\" : \"hidden\"}`,\n                        id: \"navbar-search\",\n                        onClick: toggleMenu,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"flex flex-col p-4 md:p-0 mt-4 font-medium border text-transform: uppercase border-gray-100 rounded-lg bg-gray-50 md:space-x-6 lg:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-white dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700\r\n                    ${pathName === \"/\" && \"md:text-blue-700 decoration-blue-700\"}\r\n                 `,\n                                        \"aria-current\": \"page\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/tool\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 ${(pathName === \"/tool\" || pathName === \"/tools\") && \"md:text-blue-700 decoration-blue-700\"}`,\n                                        children: \"Tools\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/tool-search\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 ${pathName === \"/tool-search\" && \"md:text-blue-700 decoration-blue-700\"}`,\n                                        children: \"Search tools\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\components\\\\navbar\\\\Navbar.jsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/navbar/Navbar.jsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"04a81f5727e4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdG9vbHBsYXRlLmFpLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9kYzA2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDRhODFmNTcyN2U0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/Ads.jsx":
/*!*************************!*\
  !*** ./src/app/Ads.jsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring\instadownloader\src\app\Ads.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/components/navbar/Navbar.jsx":
/*!**********************************************!*\
  !*** ./src/app/components/navbar/Navbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring\instadownloader\src\app\components\navbar\Navbar.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistVF.woff\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/GeistVF.woff\\\",\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"weight\\\":\\\"100 900\\\"}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistMonoVF.woff\",\"variable\":\"--font-geist-mono\",\"weight\":\"100 900\"}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/GeistMonoVF.woff\\\",\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"weight\\\":\\\"100 900\\\"}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _fortawesome_fontawesome_svg_core_styles_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/fontawesome-svg-core/styles.css */ \"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css\");\n/* harmony import */ var _fortawesome_fontawesome_svg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/fontawesome-svg-core */ \"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/index.mjs\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_navbar_Navbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/navbar/Navbar */ \"(rsc)/./src/app/components/navbar/Navbar.jsx\");\n/* harmony import */ var _Ads__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Ads */ \"(rsc)/./src/app/Ads.jsx\");\n\n\n\n\n\n\n_fortawesome_fontawesome_svg_core__WEBPACK_IMPORTED_MODULE_2__.config.autoAddCss = false;\n\n\n\n\nconst isAdsServe = JSON.parse(\"true\");\nconst metadata = {\n    title: \"SmartTools | Discover Top AI Tools & Reviews | Your AI Resource\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/st_logo.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        strategy: \"afterInteractive\",\n                        src: \"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2219975169694529\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_8___default().variable)} ${(next_font_local_target_css_path_src_app_layout_js_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_9___default().variable)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navbar_Navbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    children,\n                    isAdsServe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Ads__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            adSlot: \"5991868361\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 49,\n                        columnNumber: 26\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-white dark:bg-gray-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto w-full max-w-screen-xl p-4 py-6 lg:py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:flex md:justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full md:w-6/12 lg:w-6/12 mb-6 md:mb-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"https://smart-tools.com/\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"/st_logo.png\",\n                                                            className: \"h-8 me-3\",\n                                                            alt: \"SmartTool Logo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 59,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"self-center text-2xl font-semibold whitespace-nowrap dark:text-white\",\n                                                            children: \"SmartTools\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 60,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"lg:me-6 p-1 dark:text-gray-400\",\n                                                    children: \"SmartTools is an AI tools platform featuring 1000+ tool reviews and value-packed blogs targeted for professionals to increase everyone's productivity and efficiency.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-8 sm:gap-6 sm:grid-cols-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white\",\n                                                            children: \"Pages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-gray-500 dark:text-gray-400 font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"/\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"Home\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 71,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 70,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"/tool\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"Tools\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 74,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 73,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white\",\n                                                            children: \"Legal\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 79,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-gray-500 dark:text-gray-400 font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"/privacy-policy\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"Privacy Policy\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 82,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 81,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"terms-conditions\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"Terms & Conditions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 85,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 84,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 80,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white\",\n                                                            children: \"Categories\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-gray-500 dark:text-gray-400 font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"/\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"AI Audio Tools\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 93,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 92,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"/\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"AI Image Tools\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 96,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 95,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"mb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        href: \"/\",\n                                                                        className: \"hover:underline\",\n                                                                        children: \"AI Video Tools\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                        lineNumber: 99,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                                    lineNumber: 98,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                    className: \"my-6 border-gray-200 sm:mx-auto dark:border-gray-700 lg:my-8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sm:flex sm:items-center sm:justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 sm:text-center dark:text-gray-400\",\n                                            children: [\n                                                \"\\xa9 2024 \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"https://smart-tools.com/\",\n                                                    className: \"hover:underline\",\n                                                    children: \"SmartTools\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 24\n                                                }, this),\n                                                \". All Rights Reserved.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex mt-4 sm:justify-center sm:mt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"#\",\n                                                    className: \"text-gray-500 hover:text-gray-900 dark:hover:text-white\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fa-brands fa-facebook\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Facebook page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"#\",\n                                                    className: \"text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fa-brands fa-discord\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Discord community\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"#\",\n                                                    className: \"text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fa-brands fa-twitter\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Twitter page\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"#\",\n                                                    className: \"text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fa-brands fa-github\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"GitHub account\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"#\",\n                                                    className: \"text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fa-brands fa-dribbble\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Dribbble account\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\instadownloader\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBWU1BO0FBS0FDO0FBakJnRDtBQUNLO0FBQzFCO0FBQ2pDQyxxRUFBTUEsQ0FBQ0UsVUFBVSxHQUFHO0FBQ1M7QUFFTjtBQUN5QjtBQUNoQjtBQUVoQyxNQUFNSSxhQUFhQyxLQUFLQyxLQUFLLENBQUNDLE1BQWlDO0FBY3hELE1BQU1HLFdBQVc7SUFDdEJDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBRTtJQUM3QyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSzs7MEJBQ1QsOERBQUNDOztrQ0FDQyw4REFBQ0M7d0JBQ0NDLEtBQUk7d0JBQ0pDLE1BQUs7Ozs7OztrQ0FFUCw4REFBQ0Y7d0JBQUtDLEtBQUk7d0JBQU9DLE1BQUs7Ozs7OztrQ0FDdEIsOERBQUNyQixtREFBTUE7d0JBQ0xzQixVQUFTO3dCQUNUQyxLQUFJO3dCQUNKQyxhQUFZOzs7Ozs7Ozs7Ozs7MEJBR2hCLDhEQUFDQztnQkFBS0MsV0FBVyxDQUFDLEVBQUU3QiwyTUFBa0IsQ0FBQyxDQUFDLEVBQUVDLCtNQUFrQixDQUFDLFlBQVksQ0FBQzs7a0NBQ3hFLDhEQUFDSyxpRUFBTUE7Ozs7O29CQUNOWTtvQkFFQ1YsNEJBQWUsOERBQUN1Qjt3QkFBSUYsV0FBVTtrQ0FDOUIsNEVBQUN0Qiw0Q0FBV0E7NEJBQUN5QixRQUFPOzs7Ozs7Ozs7OztrQ0FJdEIsOERBQUNDO3dCQUFPSixXQUFVO2tDQUNoQiw0RUFBQ0U7NEJBQUlGLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FBSUYsV0FBVTs7c0RBQ2IsOERBQUNFOzRDQUFJRixXQUFVOzs4REFDYiw4REFBQ3hCLGlEQUFJQTtvREFBQ21CLE1BQUs7b0RBQTJCSyxXQUFVOztzRUFDOUMsOERBQUNLOzREQUFJUixLQUFJOzREQUFlRyxXQUFVOzREQUFXTSxLQUFJOzs7Ozs7c0VBQ2pELDhEQUFDQzs0REFBS1AsV0FBVTtzRUFBdUU7Ozs7Ozs7Ozs7Ozs4REFFekYsOERBQUNRO29EQUFFUixXQUFVOzhEQUFpQzs7Ozs7Ozs7Ozs7O3NEQUloRCw4REFBQ0U7NENBQUlGLFdBQVU7OzhEQUNiLDhEQUFDRTs7c0VBQ0MsOERBQUNPOzREQUFHVCxXQUFVO3NFQUFxRTs7Ozs7O3NFQUNuRiw4REFBQ1U7NERBQUdWLFdBQVU7OzhFQUNaLDhEQUFDVztvRUFBR1gsV0FBVTs4RUFDWiw0RUFBQ3hCLGlEQUFJQTt3RUFBQ21CLE1BQUs7d0VBQUlLLFdBQVU7a0ZBQWtCOzs7Ozs7Ozs7Ozs4RUFFN0MsOERBQUNXOzhFQUNDLDRFQUFDbkMsaURBQUlBO3dFQUFDbUIsTUFBSzt3RUFBUUssV0FBVTtrRkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUlyRCw4REFBQ0U7O3NFQUNDLDhEQUFDTzs0REFBR1QsV0FBVTtzRUFBcUU7Ozs7OztzRUFDbkYsOERBQUNVOzREQUFHVixXQUFVOzs4RUFDWiw4REFBQ1c7b0VBQUdYLFdBQVU7OEVBQ1osNEVBQUN4QixpREFBSUE7d0VBQUNtQixNQUFLO3dFQUFrQkssV0FBVTtrRkFBa0I7Ozs7Ozs7Ozs7OzhFQUUzRCw4REFBQ1c7OEVBQ0MsNEVBQUNuQyxpREFBSUE7d0VBQUNtQixNQUFLO3dFQUFtQkssV0FBVTtrRkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUloRSw4REFBQ0U7O3NFQUNDLDhEQUFDTzs0REFBR1QsV0FBVTtzRUFBcUU7Ozs7OztzRUFDbkYsOERBQUNVOzREQUFHVixXQUFVOzs4RUFDWiw4REFBQ1c7b0VBQUdYLFdBQVU7OEVBQ1osNEVBQUN4QixpREFBSUE7d0VBQUNtQixNQUFLO3dFQUFJSyxXQUFVO2tGQUFrQjs7Ozs7Ozs7Ozs7OEVBRTdDLDhEQUFDVztvRUFBR1gsV0FBVTs4RUFDWiw0RUFBQ3hCLGlEQUFJQTt3RUFBQ21CLE1BQUs7d0VBQUlLLFdBQVU7a0ZBQWtCOzs7Ozs7Ozs7Ozs4RUFFN0MsOERBQUNXO29FQUFHWCxXQUFVOzhFQUNaLDRFQUFDeEIsaURBQUlBO3dFQUFDbUIsTUFBSzt3RUFBSUssV0FBVTtrRkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU1yRCw4REFBQ1k7b0NBQUdaLFdBQVU7Ozs7Ozs4Q0FDZCw4REFBQ0U7b0NBQUlGLFdBQVU7O3NEQUNiLDhEQUFDTzs0Q0FBS1AsV0FBVTs7Z0RBQTBEOzhEQUNqRSw4REFBQ3hCLGlEQUFJQTtvREFBQ21CLE1BQUs7b0RBQTJCSyxXQUFVOzhEQUFrQjs7Ozs7O2dEQUFpQjs7Ozs7OztzREFFNUYsOERBQUNFOzRDQUFJRixXQUFVOzs4REFDYiw4REFBQ3hCLGlEQUFJQTtvREFBQ21CLE1BQUs7b0RBQUlLLFdBQVU7O3NFQUN2Qiw4REFBQ2E7NERBQUViLFdBQVU7Ozs7OztzRUFDYiw4REFBQ087NERBQUtQLFdBQVU7c0VBQVU7Ozs7Ozs7Ozs7Ozs4REFFNUIsOERBQUN4QixpREFBSUE7b0RBQUNtQixNQUFLO29EQUFJSyxXQUFVOztzRUFDdkIsOERBQUNhOzREQUFFYixXQUFVOzs7Ozs7c0VBQ2IsOERBQUNPOzREQUFLUCxXQUFVO3NFQUFVOzs7Ozs7Ozs7Ozs7OERBRTVCLDhEQUFDeEIsaURBQUlBO29EQUFDbUIsTUFBSztvREFBSUssV0FBVTs7c0VBQ3ZCLDhEQUFDYTs0REFBRWIsV0FBVTs7Ozs7O3NFQUNiLDhEQUFDTzs0REFBS1AsV0FBVTtzRUFBVTs7Ozs7Ozs7Ozs7OzhEQUU1Qiw4REFBQ3hCLGlEQUFJQTtvREFBQ21CLE1BQUs7b0RBQUlLLFdBQVU7O3NFQUN2Qiw4REFBQ2E7NERBQUViLFdBQVU7Ozs7OztzRUFDYiw4REFBQ087NERBQUtQLFdBQVU7c0VBQVU7Ozs7Ozs7Ozs7Ozs4REFFNUIsOERBQUN4QixpREFBSUE7b0RBQUNtQixNQUFLO29EQUFJSyxXQUFVOztzRUFDdkIsOERBQUNhOzREQUFFYixXQUFVOzs7Ozs7c0VBQ2IsOERBQUNPOzREQUFLUCxXQUFVO3NFQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVM1QyIsInNvdXJjZXMiOlsid2VicGFjazovL3Rvb2xwbGF0ZS5haS8uL3NyYy9hcHAvbGF5b3V0LmpzPzViMTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFwiQGZvcnRhd2Vzb21lL2ZvbnRhd2Vzb21lLXN2Zy1jb3JlL3N0eWxlcy5jc3NcIjtcbmltcG9ydCB7IGNvbmZpZyB9IGZyb20gXCJAZm9ydGF3ZXNvbWUvZm9udGF3ZXNvbWUtc3ZnLWNvcmVcIjtcbmltcG9ydCBTY3JpcHQgZnJvbSBcIm5leHQvc2NyaXB0XCI7XG5jb25maWcuYXV0b0FkZENzcyA9IGZhbHNlO1xuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xuaW1wb3J0IGxvY2FsRm9udCBmcm9tIFwibmV4dC9mb250L2xvY2FsXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5pbXBvcnQgTmF2YmFyIGZyb20gJy4vY29tcG9uZW50cy9uYXZiYXIvTmF2YmFyJztcbmltcG9ydCBBZENvbXBvbmVudCBmcm9tIFwiLi9BZHNcIjtcblxuY29uc3QgaXNBZHNTZXJ2ZSA9IEpTT04ucGFyc2UocHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU0VSVkVfQURTKTtcblxuY29uc3QgZ2Vpc3RTYW5zID0gbG9jYWxGb250KHtcbiAgc3JjOiBcIi4vZm9udHMvR2Vpc3RWRi53b2ZmXCIsXG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1zYW5zXCIsXG4gIHdlaWdodDogXCIxMDAgOTAwXCIsXG59KTtcbmNvbnN0IGdlaXN0TW9ubyA9IGxvY2FsRm9udCh7XG4gIHNyYzogXCIuL2ZvbnRzL0dlaXN0TW9ub1ZGLndvZmZcIixcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LW1vbm9cIixcbiAgd2VpZ2h0OiBcIjEwMCA5MDBcIixcbn0pO1xuXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiU21hcnRUb29scyB8IERpc2NvdmVyIFRvcCBBSSBUb29scyAmIFJldmlld3MgfCBZb3VyIEFJIFJlc291cmNlXCIsXG4gIGRlc2NyaXB0aW9uOiBcIkdlbmVyYXRlZCBieSBjcmVhdGUgbmV4dCBhcHBcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8aGVhZD5cbiAgICAgICAgPGxpbmtcbiAgICAgICAgICByZWw9XCJzdHlsZXNoZWV0XCJcbiAgICAgICAgICBocmVmPVwiaHR0cHM6Ly9jZG5qcy5jbG91ZGZsYXJlLmNvbS9hamF4L2xpYnMvZm9udC1hd2Vzb21lLzYuNS4yL2Nzcy9hbGwubWluLmNzc1wiXG4gICAgICAgIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL3N0X2xvZ28ucG5nXCIgLz5cbiAgICAgICAgPFNjcmlwdFxuICAgICAgICAgIHN0cmF0ZWd5PVwiYWZ0ZXJJbnRlcmFjdGl2ZVwiXG4gICAgICAgICAgc3JjPVwiaHR0cHM6Ly9wYWdlYWQyLmdvb2dsZXN5bmRpY2F0aW9uLmNvbS9wYWdlYWQvanMvYWRzYnlnb29nbGUuanM/Y2xpZW50PWNhLXB1Yi0yMjE5OTc1MTY5Njk0NTI5XCJcbiAgICAgICAgICBjcm9zc09yaWdpbj1cImFub255bW91c1wiXG4gICAgICAgIC8+XG4gICAgICA8L2hlYWQ+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH0+XG4gICAgICAgIDxOYXZiYXIgLz5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgICB7XG4gICAgICAgICAgaXNBZHNTZXJ2ZSAmJiAgPGRpdiBjbGFzc05hbWU9XCJtdC04IG1iLTRcIj5cbiAgICAgICAgICA8QWRDb21wb25lbnQgYWRTbG90PVwiNTk5MTg2ODM2MVwiIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICB9XG4gICAgICAgXG4gICAgICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTkwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byB3LWZ1bGwgbWF4LXctc2NyZWVuLXhsIHAtNCBweS02IGxnOnB5LThcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWQ6ZmxleCBtZDpqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6dy02LzEyIGxnOnctNi8xMiBtYi02IG1kOm1iLTBcIj5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiaHR0cHM6Ly9zbWFydC10b29scy5jb20vXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPVwiL3N0X2xvZ28ucG5nXCIgY2xhc3NOYW1lPVwiaC04IG1lLTNcIiBhbHQ9XCJTbWFydFRvb2wgTG9nb1wiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzZWxmLWNlbnRlciB0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHdoaXRlc3BhY2Utbm93cmFwIGRhcms6dGV4dC13aGl0ZVwiPlNtYXJ0VG9vbHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImxnOm1lLTYgcC0xIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgU21hcnRUb29scyBpcyBhbiBBSSB0b29scyBwbGF0Zm9ybSBmZWF0dXJpbmcgMTAwMCsgdG9vbCByZXZpZXdzIGFuZCB2YWx1ZS1wYWNrZWQgYmxvZ3MgdGFyZ2V0ZWQgZm9yIHByb2Zlc3Npb25hbHMgdG8gaW5jcmVhc2UgZXZlcnlvbmUncyBwcm9kdWN0aXZpdHkgYW5kIGVmZmljaWVuY3kuXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC04IHNtOmdhcC02IHNtOmdyaWQtY29scy0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJtYi02IHRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIHVwcGVyY2FzZSBkYXJrOnRleHQtd2hpdGVcIj5QYWdlczwvaDI+XG4gICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImhvdmVyOnVuZGVybGluZVwiPkhvbWU8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3Rvb2xcIiBjbGFzc05hbWU9XCJob3Zlcjp1bmRlcmxpbmVcIj5Ub29sczwvTGluaz5cbiAgICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJtYi02IHRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIHVwcGVyY2FzZSBkYXJrOnRleHQtd2hpdGVcIj5MZWdhbDwvaDI+XG4gICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3ByaXZhY3ktcG9saWN5XCIgY2xhc3NOYW1lPVwiaG92ZXI6dW5kZXJsaW5lXCI+UHJpdmFjeSBQb2xpY3k8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwidGVybXMtY29uZGl0aW9uc1wiIGNsYXNzTmFtZT1cImhvdmVyOnVuZGVybGluZVwiPlRlcm1zICZhbXA7IENvbmRpdGlvbnM8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwibWItNiB0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCB1cHBlcmNhc2UgZGFyazp0ZXh0LXdoaXRlXCI+Q2F0ZWdvcmllczwvaDI+XG4gICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImhvdmVyOnVuZGVybGluZVwiPkFJIEF1ZGlvIFRvb2xzPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwiaG92ZXI6dW5kZXJsaW5lXCI+QUkgSW1hZ2UgVG9vbHM8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9XCJob3Zlcjp1bmRlcmxpbmVcIj5BSSBWaWRlbyBUb29sczwvTGluaz5cbiAgICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8aHIgY2xhc3NOYW1lPVwibXktNiBib3JkZXItZ3JheS0yMDAgc206bXgtYXV0byBkYXJrOmJvcmRlci1ncmF5LTcwMCBsZzpteS04XCIgLz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic206ZmxleCBzbTppdGVtcy1jZW50ZXIgc206anVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBzbTp0ZXh0LWNlbnRlciBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICDCqSAyMDI0IDxMaW5rIGhyZWY9XCJodHRwczovL3NtYXJ0LXRvb2xzLmNvbS9cIiBjbGFzc05hbWU9XCJob3Zlcjp1bmRlcmxpbmVcIj5TbWFydFRvb2xzPC9MaW5rPi4gQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggbXQtNCBzbTpqdXN0aWZ5LWNlbnRlciBzbTptdC0wXCI+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIGhvdmVyOnRleHQtZ3JheS05MDAgZGFyazpob3Zlcjp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYS1icmFuZHMgZmEtZmFjZWJvb2tcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3Itb25seVwiPkZhY2Vib29rIHBhZ2U8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGRhcms6aG92ZXI6dGV4dC13aGl0ZSBtcy01XCI+XG4gICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYS1icmFuZHMgZmEtZGlzY29yZFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzci1vbmx5XCI+RGlzY29yZCBjb21tdW5pdHk8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGRhcms6aG92ZXI6dGV4dC13aGl0ZSBtcy01XCI+XG4gICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYS1icmFuZHMgZmEtdHdpdHRlclwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzci1vbmx5XCI+VHdpdHRlciBwYWdlPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCBkYXJrOmhvdmVyOnRleHQtd2hpdGUgbXMtNVwiPlxuICAgICAgICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmEtYnJhbmRzIGZhLWdpdGh1YlwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzci1vbmx5XCI+R2l0SHViIGFjY291bnQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGRhcms6aG92ZXI6dGV4dC13aGl0ZSBtcy01XCI+XG4gICAgICAgICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYS1icmFuZHMgZmEtZHJpYmJibGVcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3Itb25seVwiPkRyaWJiYmxlIGFjY291bnQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvb3Rlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiZ2Vpc3RTYW5zIiwiZ2Vpc3RNb25vIiwiY29uZmlnIiwiU2NyaXB0IiwiYXV0b0FkZENzcyIsIkxpbmsiLCJOYXZiYXIiLCJBZENvbXBvbmVudCIsImlzQWRzU2VydmUiLCJKU09OIiwicGFyc2UiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU0VSVkVfQURTIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJoZWFkIiwibGluayIsInJlbCIsImhyZWYiLCJzdHJhdGVneSIsInNyYyIsImNyb3NzT3JpZ2luIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIiwiZGl2IiwiYWRTbG90IiwiZm9vdGVyIiwiaW1nIiwiYWx0Iiwic3BhbiIsInAiLCJoMiIsInVsIiwibGkiLCJociIsImkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@fortawesome","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5Cinstadownloader%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5Cinstadownloader&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();