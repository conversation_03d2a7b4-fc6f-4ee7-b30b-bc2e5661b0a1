"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-array";
exports.ids = ["vendor-chunks/d3-array"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-array/src/fsum.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-array/src/fsum.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Adder: () => (/* binding */ Adder),\n/* harmony export */   fcumsum: () => (/* binding */ fcumsum),\n/* harmony export */   fsum: () => (/* binding */ fsum)\n/* harmony export */ });\n// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nclass Adder {\n    constructor(){\n        this._partials = new Float64Array(32);\n        this._n = 0;\n    }\n    add(x) {\n        const p = this._partials;\n        let i = 0;\n        for(let j = 0; j < this._n && j < 32; j++){\n            const y = p[j], hi = x + y, lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n            if (lo) p[i++] = lo;\n            x = hi;\n        }\n        p[i] = x;\n        this._n = i + 1;\n        return this;\n    }\n    valueOf() {\n        const p = this._partials;\n        let n = this._n, x, y, lo, hi = 0;\n        if (n > 0) {\n            hi = p[--n];\n            while(n > 0){\n                x = hi;\n                y = p[--n];\n                hi = x + y;\n                lo = y - (hi - x);\n                if (lo) break;\n            }\n            if (n > 0 && (lo < 0 && p[n - 1] < 0 || lo > 0 && p[n - 1] > 0)) {\n                y = lo * 2;\n                x = hi + y;\n                if (y == x - hi) hi = x;\n            }\n        }\n        return hi;\n    }\n}\nfunction fsum(values, valueof) {\n    const adder = new Adder();\n    if (valueof === undefined) {\n        for (let value of values){\n            if (value = +value) {\n                adder.add(value);\n            }\n        }\n    } else {\n        let index = -1;\n        for (let value of values){\n            if (value = +valueof(value, ++index, values)) {\n                adder.add(value);\n            }\n        }\n    }\n    return +adder;\n}\nfunction fcumsum(values, valueof) {\n    const adder = new Adder();\n    let index = -1;\n    return Float64Array.from(values, valueof === undefined ? (v)=>adder.add(+v || 0) : (v)=>adder.add(+valueof(v, ++index, values) || 0));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/fsum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/merge.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/merge.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ merge)\n/* harmony export */ });\nfunction* flatten(arrays) {\n    for (const array of arrays){\n        yield* array;\n    }\n}\nfunction merge(arrays) {\n    return Array.from(flatten(arrays));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL21lcmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxVQUFVQSxRQUFRQyxNQUFNO0lBQ3RCLEtBQUssTUFBTUMsU0FBU0QsT0FBUTtRQUMxQixPQUFPQztJQUNUO0FBQ0Y7QUFFZSxTQUFTQyxNQUFNRixNQUFNO0lBQ2xDLE9BQU9HLE1BQU1DLElBQUksQ0FBQ0wsUUFBUUM7QUFDNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9kMy1hcnJheS9zcmMvbWVyZ2UuanM/M2IzNiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiogZmxhdHRlbihhcnJheXMpIHtcbiAgZm9yIChjb25zdCBhcnJheSBvZiBhcnJheXMpIHtcbiAgICB5aWVsZCogYXJyYXk7XG4gIH1cbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbWVyZ2UoYXJyYXlzKSB7XG4gIHJldHVybiBBcnJheS5mcm9tKGZsYXR0ZW4oYXJyYXlzKSk7XG59XG4iXSwibmFtZXMiOlsiZmxhdHRlbiIsImFycmF5cyIsImFycmF5IiwibWVyZ2UiLCJBcnJheSIsImZyb20iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-array/src/range.js":
/*!********************************************!*\
  !*** ./node_modules/d3-array/src/range.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(start, stop, step) {\n    start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n    var i = -1, n = Math.max(0, Math.ceil((stop - start) / step)) | 0, range = new Array(n);\n    while(++i < n){\n        range[i] = start + i * step;\n    }\n    return range;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JhbmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBU0EsS0FBSyxFQUFFQyxJQUFJLEVBQUVDLElBQUk7SUFDdkNGLFFBQVEsQ0FBQ0EsT0FBT0MsT0FBTyxDQUFDQSxNQUFNQyxPQUFPLENBQUNDLElBQUlDLFVBQVVDLE1BQU0sSUFBSSxJQUFLSixDQUFBQSxPQUFPRCxPQUFPQSxRQUFRLEdBQUcsS0FBS0csSUFBSSxJQUFJLElBQUksQ0FBQ0Q7SUFFOUcsSUFBSUksSUFBSSxDQUFDLEdBQ0xILElBQUlJLEtBQUtDLEdBQUcsQ0FBQyxHQUFHRCxLQUFLRSxJQUFJLENBQUMsQ0FBQ1IsT0FBT0QsS0FBSSxJQUFLRSxTQUFTLEdBQ3BEUSxRQUFRLElBQUlDLE1BQU1SO0lBRXRCLE1BQU8sRUFBRUcsSUFBSUgsRUFBRztRQUNkTyxLQUFLLENBQUNKLEVBQUUsR0FBR04sUUFBUU0sSUFBSUo7SUFDekI7SUFFQSxPQUFPUTtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvZDMtYXJyYXkvc3JjL3JhbmdlLmpzP2E5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oc3RhcnQsIHN0b3AsIHN0ZXApIHtcbiAgc3RhcnQgPSArc3RhcnQsIHN0b3AgPSArc3RvcCwgc3RlcCA9IChuID0gYXJndW1lbnRzLmxlbmd0aCkgPCAyID8gKHN0b3AgPSBzdGFydCwgc3RhcnQgPSAwLCAxKSA6IG4gPCAzID8gMSA6ICtzdGVwO1xuXG4gIHZhciBpID0gLTEsXG4gICAgICBuID0gTWF0aC5tYXgoMCwgTWF0aC5jZWlsKChzdG9wIC0gc3RhcnQpIC8gc3RlcCkpIHwgMCxcbiAgICAgIHJhbmdlID0gbmV3IEFycmF5KG4pO1xuXG4gIHdoaWxlICgrK2kgPCBuKSB7XG4gICAgcmFuZ2VbaV0gPSBzdGFydCArIGkgKiBzdGVwO1xuICB9XG5cbiAgcmV0dXJuIHJhbmdlO1xufVxuIl0sIm5hbWVzIjpbInN0YXJ0Iiwic3RvcCIsInN0ZXAiLCJuIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiaSIsIk1hdGgiLCJtYXgiLCJjZWlsIiwicmFuZ2UiLCJBcnJheSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-array/src/range.js\n");

/***/ })

};
;