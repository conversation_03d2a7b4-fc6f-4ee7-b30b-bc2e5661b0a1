import tools from "../../../tools.json";


export const getAllTools = async (currentPage, priceFilter) => {
    try {
        const filteredTools = priceFilter
            ? tools.filter(tool => tool.price === priceFilter)
            : tools;

        const limit = 10;
        const skip = (currentPage - 1) * limit;

        const totalTools = filteredTools.length;
        const paginatedTools = filteredTools
            .slice(skip, skip + limit)
            .map(({ appId, price, tags, category, iconLocalPath, title, summary }) => ({
                appId,
                price,
                tags,
                category,
                iconLocalPath,
                title,
                summary,
            }));

        const totalPages = Math.ceil(totalTools / limit);

        return { tools: paginatedTools, totalPages };
    } catch (error) {
        console.error("Error reading tools from JSON:", error);
        throw new Error("Could not retrieve tools");
    }
};



export const getTool = async (appId) => {
    try {
        const toolDetails = tools.find(tool => tool.appId === appId);

        if (!toolDetails) {
            throw new Error("Tool not found");
        }

        const similarTools = tools.filter(tool =>
            tool.subCategory === toolDetails.subCategory && tool.appId !== toolDetails.appId
        ).slice(0, 5);

        const mergeData = {
            toolDetails: toolDetails,
            similarTools: similarTools,
        };

        return mergeData;

    } catch (error) {
        console.error("Error fetching tool details:", error);
        throw new Error("Could not retrieve tool details");
    }
};

export const getToolByCategory = async (category) => {

    try {
        const filteredTools = tools.filter(tool => tool.subCategory === category);

        if (filteredTools.length === 0) {
            return { tools: [], similarTools: [] };
        }

        const mainCategory = filteredTools[0].category;

        const similarTools = tools.filter(tool =>
            tool.category === mainCategory && tool.subCategory !== category
        ).slice(0, 5);

        const selectedFilteredTools = filteredTools.map(tool => ({
            appId: tool.appId,
            price: tool.price,
            tags: tool.tags,
            category: tool.category,
            iconLocalPath: tool.iconLocalPath,
            title: tool.title,
            summary: tool.summary,
        }));

        const selectedSimilarTools = similarTools.map(tool => ({
            appId: tool.appId,
            price: tool.price,
            category: tool.category,
            iconLocalPath: tool.iconLocalPath,
            title: tool.title,
        }));

        return { tools: selectedFilteredTools, similarTools: selectedSimilarTools };

    } catch (error) {
        console.error("Error fetching category details:", error);
        throw new Error("Could not retrieve category details");
    }
};

 
export const getToolsBySearch = async (search) => {
    try {
        const regex = new RegExp(search, 'i'); 

        const filteredTools = tools.filter(tool => 
            regex.test(tool.title) ||
            regex.test(tool.appId) ||
            regex.test(tool.category) ||
            regex.test(tool.subCategory)
        );
        const selectedTools = filteredTools.map(tool => ({
            appId: tool.appId,
            iconLocalPath: tool.iconLocalPath,
            title: tool.title,
            price: tool.price,
            tags: tool.tags,
            summary: tool.summary,
            category: tool.category,
        }));

        return selectedTools;

    } catch (error) {
        console.error("Error searching for tools:", error);
        throw new Error("Could not retrieve search results");
    }
};