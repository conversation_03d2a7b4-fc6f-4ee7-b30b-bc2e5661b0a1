"use client";
import React, { useState, useEffect } from "react";
import {
  Typography,
  Container,
  Card,
  CardMedia,
  CardContent,
  Box,
  Grid,
  Pagination,
  Chip,
  IconButton,
  Tooltip,
  Button
} from "@mui/material";
import InfoIcon from '@mui/icons-material/Info';
import StarIcon from "@mui/icons-material/Star";
import AppsIcon from '@mui/icons-material/Apps';
import AssessmentIcon from '@mui/icons-material/Assessment';
import DownloadIcon from '@mui/icons-material/Download';
import RateReviewIcon from '@mui/icons-material/RateReview';
import Link from 'next/link';
import { Lock, InfoOutlined, Email, Language, LocationOn } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import { useDispatch } from 'react-redux';
import { useSession } from "next-auth/react";
import { countries, timezoneToCountryName } from "@/app/utils/countries.js";
import { selectedUserCountry } from "@/app/redux/slice/topAppSlice";
import CheckUserTypeModal from "../../components/checkUserType/checkUserType.jsx";


export default function DeveloperDetailsUI({ developerInfo }) {
  const dispatch = useDispatch();
  const router = useRouter();
  const { status } = useSession();
  const [isOpen, setIsOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 4;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  const displayedApps = developerInfo?.apps.slice(startIndex, endIndex);

  const handleAppClick = (appId) => {
    router.push(`/app-details/${appId}`);
  };
  useEffect(() => {
    if (Intl) {
      let userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      let userCountry = timezoneToCountryName[userTimeZone];
      const cc = countries.find((x) => x.label == userCountry);
      dispatch(selectedUserCountry({
        countryCode: cc?.code || 'US',
        country: userCountry || 'United States'
      }));
    }
  }, [])


  const infoItems = [
    {
      label: "Total Apps",
      value: developerInfo?.info.totalApps || '0',
      icon: <AppsIcon color="primary" />,
      tooltip: "Number of published apps"
    },
    {
      label: "Total Installs",
      value: developerInfo?.info.totalInstalls || '0',
      icon: <DownloadIcon color="primary" />,
      tooltip: "Combined installs across all apps"
    },
    {
      label: "Total Reviews",
      value: status === "authenticated" ?
        developerInfo?.info.totalReviews || '0' :
        <Tooltip title="Sign in to view">
          <IconButton onClick={() => setIsOpen(true)} size="small">
            <Lock color="action" />
          </IconButton>
        </Tooltip>,
      icon: <RateReviewIcon color="primary" />,
      tooltip: "Total user reviews"
    },
    {
      label: "Total Ratings",
      value: status === "authenticated" ?
        `${developerInfo?.info.totalRatings || '0.0'} ★` :
        <Tooltip title="Sign in to view">
          <IconButton onClick={() => setIsOpen(true)} size="small">
            <Lock color="action" />
          </IconButton>
        </Tooltip>,
      icon: <StarIcon color="primary" />,
      tooltip: "Average rating across all apps"
    },
    {
      label: "Monthly Revenue",
      value: '-',
      icon: <DownloadIcon color="primary" />,
      tooltip: "Average revenue across all apps"
    },
  ];

  const KeyValueRow = ({ label, value, isRemoved = false }) => {
  return (
    <Box 
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        py: 1.5,
        px: 2,
        borderBottom: '1px solid',
        borderColor: 'divider',
        '&:last-child': { borderBottom: 'none' },
        '&:hover': {
          backgroundColor: 'action.hover',
          '& .copy-icon': { opacity: 1 }
        },
        transition: 'background-color 0.2s ease',
        position: 'relative',
        backgroundColor:'transparent'
      }}
    >
      <Typography variant="body1" color={isRemoved ? "error.main" : "text.secondary"}>
        {label}
      </Typography>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Typography
          variant="body1"
          sx={{
            fontWeight: 600,
            color: isRemoved ? "error.main" : "text.primary"
          }}
        >
          {value}
        </Typography>
      </Box>
    </Box>
  );
};
  return (
    <Container
      sx={{
        borderRadius: "1rem",
        marginTop: "5.5rem",
        minHeight: "calc(100vh - 5.5rem)",
        padding: "1.5rem",
        boxShadow: 2,
        backgroundColor: "background.paper"
      }}
    >
      {developerInfo ? (
        <>
          <Box sx={{
            mb: 2,
            p: 3,
            display: 'flex',
            alignItems: 'center',
            gap: 3,
            flexWrap: 'wrap'
          }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2
            }}>
              <CardMedia
                component="img"
                sx={{
                  width: 80,
                  height: 80,
                  borderRadius: '16px',
                  border: '1px solid #f0f0f0',
                  objectFit: 'cover'
                }}
                image={developerInfo?.apps[0]?.icon || "https://cdn-icons-png.flaticon.com/128/300/300218.png"}
                alt={`${developerInfo.info.developerName}'s app icon`}
                onError={(e) => {
                  e.currentTarget.onerror = null;
                  e.currentTarget.src = "https://cdn-icons-png.flaticon.com/128/300/300218.png";
                }}
              />
              <Box>
                <Typography variant="h4" sx={{
                  fontWeight: 700,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  flexWrap: 'wrap'
                }}>
                  {developerInfo.info.developerName}
                  <Chip
                    label="Verified Developer"
                    color="success"
                    size="small"
                    variant="outlined"
                  />
                </Typography>
              </Box>
            </Box>
          </Box>

          <Box sx={{
            backgroundColor: 'background.paper',
            p: 2,
            mb: 3,
          }}>
            <Typography variant="h6" sx={{
              fontWeight: 600,
              mb: 2,
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}>
              <AssessmentIcon color="primary" />
              Developer Statistics
            </Typography>
            <Box sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 1,
              justifyContent: 'space-between'
            }}>

              {infoItems.map((item, index) => (
                <Tooltip key={index} title={item.tooltip} arrow>
                  <Box sx={{
                    flex: 1,
                    minWidth: 120,
                    backgroundColor: 'grey.50',
                    borderRadius: 1,
                    p: 2,
                    textAlign: 'center',
                    borderRight: item.isLast ? 'none' : { sm: '1px solid #e0e0e0' }
                  }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: 1,
                      mb: 1
                    }}>
                      {item.icon}
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {item.label}
                      </Typography>
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                      {item.value}
                    </Typography>
                  </Box>
                </Tooltip>
              ))}
            </Box>

          </Box>
          <Box sx={{
  backgroundColor: 'background.paper',
  p: 3,
  mb: 3,
  width: { xs: '100%', sm: '80%', md: '70%', lg: '60%' },
  alignSelf: 'flex-end',

}}>
  <Typography variant="h6" sx={{
    fontWeight: 600,
    mb: 2,
    display: 'flex',
    alignItems: 'center',
    gap: 1
  }}>
    <InfoIcon color="primary" />
    Developer Metadata
  </Typography>

  <Box sx={{
    display: 'flex',
    justifyContent: 'flex-start'
  }}>
    <Box sx={{
      width: '100%',
      maxWidth: 500,
      border: '1px solid',
      borderColor: 'divider',
      borderRadius: 1,
      overflow: 'hidden',
      '& > div:nth-of-type(odd)': {
        backgroundColor: 'grey.50'
      }
    }}>
      <KeyValueRow
        label="Published Apps"
        value={developerInfo.info.totalApps}
      />
      <KeyValueRow
        label="Removed Apps"
        value={0}
        isRemoved={true}
      />
      <KeyValueRow
        label="Latest Update"
        value={developerInfo.info.latestAppUpdated || '-'}
      />
      <KeyValueRow
        label="First App Released"
        value={developerInfo.info.firstAppReleasedDate || '-'}
      />
    </Box>
  </Box>
</Box>
          <Box sx={{
            mt: 1,
            p: 3,
            borderRadius: 2,
            bgcolor: 'background.paper',
          }}>
            <Typography variant="h5" sx={{
              fontWeight: 700,
              mb: 3,
              display: 'flex',
              alignItems: 'center'
            }}>
              {developerInfo.info.developerName}'s Apps
              <Chip
                label={`${developerInfo.apps.length} apps`}
                color="primary"
                size="small"
                sx={{ ml: 2 }}
              />
            </Typography>

            {displayedApps.length > 0 ? (
              <>
                <Grid container spacing={3}>
                  {displayedApps.map((app) => (
                    <Grid item xs={12} sm={4} md={3} lg={2.4} key={app.appId}>
                      <Card sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        transition: 'all 0.2s ease',
                        borderRadius: '12px',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                        '&:hover': {
                          boxShadow: '0 6px 16px rgba(0,0,0,0.15)',
                        },
                        position: 'relative',
                        overflow: 'visible'
                      }}>
                        <Box sx={{
                          p: 2,
                          pb: 0,
                          textAlign: 'center',
                          position: 'relative'
                        }}>
                          <Box sx={{
                            width: 72,
                            height: 72,
                            mx: 'auto',
                            position: 'relative',
                            zIndex: 1
                          }}>
                            <CardMedia
                              component="img"
                              sx={{
                                width: '100%',
                                height: '100%',
                                borderRadius: '16px',
                                cursor: 'pointer',
                                objectFit: 'cover',
                                border: '1px solid #f0f0f0'
                              }}
                              image={app.icon}
                              alt={app.title}
                              onClick={() => handleAppClick(app.appId)}
                              onError={(e) => {
                                e.currentTarget.onerror = null;
                                e.currentTarget.src = "https://cdn-icons-png.flaticon.com/128/300/300218.png";
                              }}
                            />
                          </Box>
                        </Box>

                        <CardContent sx={{
                          flexGrow: 1,
                          p: 2,
                          pt: 1,
                          display: 'flex',
                          flexDirection: 'column'
                        }}>
                          <Tooltip title={app.title} arrow>
                            <Typography
                              gutterBottom
                              variant="subtitle1"
                              sx={{
                                fontWeight: 600,
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                mb: 1,
                                fontSize: '0.95rem',
                                lineHeight: 1.3,
                                '& a': {
                                  color: 'text.primary',
                                  textDecoration: 'none',
                                  '&:hover': {
                                    color: 'primary.main'
                                  }
                                }
                              }}
                            >
                              <Link href={`/app-details/${app.appId}`}>
                                {app.title}
                              </Link>
                            </Typography>
                          </Tooltip>

                          {/* Developer */}
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                              mb: 1,
                              fontSize: '0.8rem',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis'
                            }}
                          >
                            <Link href={`/developer-apps/${app.appDeveloper}`}>
                              {app.showDeveloper}
                            </Link>
                          </Typography>

                          {/* Genre */}
                          <Chip
                            label={app.genre}
                            size="small"
                            sx={{
                              mb: 1.5,
                              fontSize: '0.7rem',
                              height: '24px',
                              maxWidth: '100%',
                              '& .MuiChip-label': {
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }
                            }}
                          />

                          {/* Rating */}
                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            mt: 'auto'
                          }}>
                            <StarIcon sx={{
                              color: '#FFB400',
                              fontSize: '18px',
                              mr: 0.5
                            }} />
                            <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                              {app.scoreText}
                            </Typography>
                            <Box sx={{ flexGrow: 1 }} />
                            <Chip
                              label="ACTIVE"
                              size="small"
                              sx={{
                                fontSize: '0.7rem',
                                height: '22px',
                                bgcolor: '#E3F2FD',
                                color: '#1976D2'
                              }}
                            />
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>

                {developerInfo.apps.length > itemsPerPage && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                    <Pagination
                      count={Math.ceil(developerInfo.apps.length / itemsPerPage)}
                      page={currentPage}
                      onChange={(e, value) => setCurrentPage(value)}
                      color="primary"
                      shape="rounded"
                      size="large"
                    />
                  </Box>
                )}
              </>
            ) : (
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                py: 8,
                textAlign: 'center'
              }}>
                <InfoOutlined sx={{ fontSize: 60, color: 'text.disabled', mb: 2 }} />
                <Typography variant="h6" color="text.secondary">
                  No apps found for this developer
                </Typography>
              </Box>
            )}
          </Box>

          <Box sx={{
            mt: 1,
            mb: 4,
            p: 3,
            borderRadius: 2,
            bgcolor: 'background.paper',
          }}>
            <Typography variant="h5" sx={{ fontWeight: 700, mb: 3 }}>
              Contact Information
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Card variant="outlined" sx={{ p: 2, height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Email color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6">Email</Typography>
                  </Box>
                  {status === "authenticated" ? (
                    <Button
                      href={`mailto:${developerInfo.info.showDeveloperEmail}`}
                      target="_blank"
                      variant="outlined"
                      fullWidth
                      startIcon={<Email />}
                    >
                      {developerInfo.info.developerEmail}
                    </Button>
                  ) : (
                    <Tooltip title="Sign in to view email">
                      <Button
                        variant="outlined"
                        fullWidth
                        startIcon={<Lock />}
                        onClick={() => setIsOpen(true)}
                      >
                        Sign in to view
                      </Button>
                    </Tooltip>
                  )}
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card variant="outlined" sx={{ p: 2, height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Language color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6">Website</Typography>
                  </Box>
                  <Button
                    href={developerInfo.info.showDeveloperWebsite}
                    target="_blank"
                    variant="outlined"
                    fullWidth
                    startIcon={<Language />}
                  >
                    Visit Website
                  </Button>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card variant="outlined" sx={{ p: 2, height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <LocationOn color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6">Address</Typography>
                  </Box>
                  <Typography variant="body1">
                    {developerInfo.info.developerAddress || 'Not available'}
                  </Typography>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </>
      ) : (
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '50vh',
          textAlign: 'center'
        }}>
          <Typography variant="h5" color="error">
            Developer information not found
          </Typography>
        </Box>
      )}
      <CheckUserTypeModal
        open={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </Container>
  );
}