/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/app_by_name_id/route";
exports.ids = ["app/api/app_by_name_id/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/keyv/src sync recursive":
/*!*************************************!*\
  !*** ./node_modules/keyv/src/ sync ***!
  \*************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/keyv/src sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:fs/promises":
/*!***********************************!*\
  !*** external "node:fs/promises" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs/promises");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:sqlite");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:timers":
/*!******************************!*\
  !*** external "node:timers" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:timers");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fapp_by_name_id%2Froute&page=%2Fapi%2Fapp_by_name_id%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapp_by_name_id%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fapp_by_name_id%2Froute&page=%2Fapi%2Fapp_by_name_id%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapp_by_name_id%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Bhushan_patil_OneDrive_Desktop_Anchoring_Apk_mirror_src_app_api_app_by_name_id_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/app_by_name_id/route.js */ \"(rsc)/./src/app/api/app_by_name_id/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/app_by_name_id/route\",\n        pathname: \"/api/app_by_name_id\",\n        filename: \"route\",\n        bundlePath: \"app/api/app_by_name_id/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\api\\\\app_by_name_id\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Bhushan_patil_OneDrive_Desktop_Anchoring_Apk_mirror_src_app_api_app_by_name_id_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/app_by_name_id/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fapp_by_name_id%2Froute&page=%2Fapi%2Fapp_by_name_id%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapp_by_name_id%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/app_by_name_id/route.js":
/*!*********************************************!*\
  !*** ./src/app/api/app_by_name_id/route.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var google_play_scraper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! google-play-scraper */ \"(rsc)/./node_modules/google-play-scraper/index.js\");\n/* harmony import */ var _app_database_appData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/database/appData */ \"(rsc)/./src/app/database/appData.js\");\n/* harmony import */ var _app_database_appApk__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/database/appApk */ \"(rsc)/./src/app/database/appApk.js\");\n/* harmony import */ var _app_database_mongoose__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/database/mongoose */ \"(rsc)/./src/app/database/mongoose.js\");\n\n\n\n\n\nconst GET = async (request)=>{\n    const url = new URL(request.url);\n    const appId = url.searchParams.get(\"appId\");\n    if (!appId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"App ID is required\"\n        }, {\n            status: 400\n        });\n    }\n    try {\n        await (0,_app_database_mongoose__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n        const appVersions = await getAppVersions(appId);\n        if (!appVersions) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: \"App not found\"\n            }, {\n                status: 400\n            });\n        }\n        const recentlyUpdatedApps = await getRecentlyUpdatedApps();\n        const appDetails = await getAppDetails(appId);\n        const similarApps = await getSimilarApps();\n        const mergedData = {\n            appVersions,\n            appDetails,\n            recentlyUpdatedApps,\n            similarApps\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            app: mergedData\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Error fetching app details\",\n            error\n        }, {\n            status: 500\n        });\n    }\n};\nconst getAppVersions = async (appId)=>{\n    const appVersions = await _app_database_appApk__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findOne({\n        appId\n    }).select(\"appId category type versions\");\n    if (!appVersions) return null;\n    const versions = appVersions.versions.slice(0, 11).map(({ versionNumber, size, minimum, updated, latestVersion })=>({\n            versionNumber,\n            size,\n            minimum,\n            updated,\n            latestVersion\n        }));\n    return {\n        appId: appVersions.appId,\n        category: appVersions.category,\n        type: appVersions.type,\n        versions\n    };\n};\nconst getRecentlyUpdatedApps = async ()=>{\n    const recentlyUpdated = await _app_database_appApk__WEBPACK_IMPORTED_MODULE_3__[\"default\"].find({\n        recentlyUpdated: true\n    }).limit(6).select(\"appId versions type\");\n    const appDetailsWithVersion = await Promise.all(recentlyUpdated.map(async (appApk)=>{\n        const app = await _app_database_appData__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findOne({\n            appId: appApk.appId\n        }).select(\"title icon developer scoreText\");\n        if (!app) return null;\n        const latestVersion = appApk.versions[0];\n        return {\n            title: app.title,\n            appId: appApk.appId,\n            icon: app.icon,\n            developer: app.developer,\n            scoreText: app.scoreText,\n            latestVersion: latestVersion ? latestVersion.versionNumber : null,\n            updated: latestVersion ? latestVersion.updated : null\n        };\n    }));\n    return appDetailsWithVersion.filter((app)=>app !== null);\n};\nconst getAppDetails = async (appId)=>{\n    return _app_database_appData__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findOne({\n        appId\n    }).select(\"appId title icon developer scoreText ratings maxInstalls screenshots description headerImage video summary recentChanges\");\n};\nconst getSimilarApps = async ()=>{\n    try {\n        const similarApps = await _app_database_appApk__WEBPACK_IMPORTED_MODULE_3__[\"default\"].find({\n            isSimilar: true\n        }).limit(15).select(\"appId versions type\");\n        const appDetailsWithVersion = await Promise.all(similarApps.map(async (appApk)=>{\n            const app = await _app_database_appData__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findOne({\n                appId: appApk.appId\n            }).select(\"title icon developer scoreText\");\n            if (!app) return null;\n            const latestVersion = appApk.versions[0];\n            return {\n                title: app.title,\n                appId: appApk.appId,\n                icon: app.icon,\n                developer: app.developer,\n                scoreText: app.scoreText,\n                latestVersion: latestVersion ? latestVersion.versionNumber : null,\n                updated: latestVersion ? latestVersion.updated : null\n            };\n        }));\n        return appDetailsWithVersion.filter((app)=>app !== null);\n    // const similarApps = await gplay.similar({ appId });\n    // return similarApps.slice(0, 5).map(({ appId, developer, scoreText, icon, title }) => ({\n    //   appId,\n    //   developer,\n    //   scoreText,\n    //   icon,\n    //   title,\n    // }));\n    } catch (error) {\n        console.error(\"Error fetching similar apps:\", error);\n        return [];\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/app_by_name_id/route.js\n");

/***/ }),

/***/ "(rsc)/./src/app/database/appApk.js":
/*!************************************!*\
  !*** ./src/app/database/appApk.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Schema = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema);\nconst versionSchema = new Schema({\n    versionLink: {\n        type: String,\n        required: true\n    },\n    actualLink: {\n        type: String,\n        require: true\n    },\n    versionNumber: {\n        type: String,\n        required: true\n    },\n    latestVersion: {\n        type: Boolean,\n        default: false\n    },\n    size: {\n        type: String\n    },\n    minimum: {\n        type: String\n    },\n    updated: {\n        type: String\n    }\n});\nconst categorySchema = new Schema({\n    name: {\n        type: String,\n        required: true\n    },\n    value: {\n        type: String,\n        required: true\n    }\n});\nconst appApkSchema = new Schema({\n    appId: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    type: {\n        type: String,\n        required: true\n    },\n    // category: { type: categorySchema, required: true },\n    category: {\n        type: String,\n        required: true\n    },\n    isPopular: {\n        type: Boolean,\n        default: false\n    },\n    recentlyUpdated: {\n        type: Boolean,\n        default: false\n    },\n    isSimilar: {\n        type: Boolean,\n        default: false\n    },\n    versions: [\n        versionSchema\n    ]\n});\n(mongoose__WEBPACK_IMPORTED_MODULE_0___default().models) = {};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"AppApk\", appApkSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/database/appApk.js\n");

/***/ }),

/***/ "(rsc)/./src/app/database/appData.js":
/*!*************************************!*\
  !*** ./src/app/database/appData.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n\nconst mongoose = __webpack_require__(/*! mongoose */ \"mongoose\");\nconst Schema = mongoose.Schema;\nconst appSchema = new Schema({\n    title: {\n        type: String,\n        required: true\n    },\n    appId: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    description: String,\n    descriptionHTML: String,\n    summary: String,\n    installs: String,\n    minInstalls: Number,\n    maxInstalls: Number,\n    score: Number,\n    scoreText: String,\n    ratings: Number,\n    reviews: Number,\n    histogram: {\n        type: Schema.Types.Mixed,\n        default: {}\n    },\n    price: Number,\n    free: Boolean,\n    currency: String,\n    priceText: String,\n    available: Boolean,\n    offersIAP: Boolean,\n    androidVersion: String,\n    androidVersionText: String,\n    androidMaxVersion: String,\n    developer: String,\n    developerId: String,\n    developerEmail: String,\n    developerWebsite: String,\n    developerAddress: String,\n    privacyPolicy: String,\n    developerInternalID: String,\n    genre: String,\n    genreId: String,\n    categories: [\n        {\n            name: String,\n            id: String\n        }\n    ],\n    icon: String,\n    headerImage: String,\n    screenshots: [\n        String\n    ],\n    bannerImage: String,\n    video: String,\n    VideoImage: String,\n    previewVideo: String,\n    contentRating: String,\n    adSupported: Boolean,\n    released: String,\n    recentChanges: String,\n    updated: String,\n    version: String,\n    recentChanges: String,\n    comments: [\n        String\n    ],\n    preregister: Boolean,\n    earlyAccessEnabled: Boolean,\n    isAvailableInPlayPass: Boolean,\n    editorsChoice: Boolean,\n    url: String\n});\nmongoose.models = {};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (mongoose.model(\"App\", appSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/database/appData.js\n");

/***/ }),

/***/ "(rsc)/./src/app/database/mongoose.js":
/*!**************************************!*\
  !*** ./src/app/database/mongoose.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst connectDB = async ()=>{\n    try {\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0]?.readyState) {\n            console.log(\"Using existing database connection\");\n            return;\n        }\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(process.env.MONGODB_URI, {\n            // useNewUrlParser: true,\n            useUnifiedTopology: true\n        });\n        console.log(\"MongoDB connection established successfully\");\n    } catch (error) {\n        console.error(\"Database connection error:\", error);\n        throw error;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2RhdGFiYXNlL21vbmdvb3NlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnQztBQUVoQyxNQUFNQyxZQUFZO0lBQ2hCLElBQUk7UUFDRixJQUFJRCw2REFBb0IsQ0FBQyxFQUFFLEVBQUVHLFlBQVk7WUFDdkNDLFFBQVFDLEdBQUcsQ0FBQztZQUNaO1FBQ0Y7UUFFQSxNQUFNTCx1REFBZ0IsQ0FBQ08sUUFBUUMsR0FBRyxDQUFDQyxXQUFXLEVBQUU7WUFDOUMseUJBQXlCO1lBQ3pCQyxvQkFBb0I7UUFDdEI7UUFFQU4sUUFBUUMsR0FBRyxDQUFDO0lBQ2QsRUFBRSxPQUFPTSxPQUFPO1FBQ2RQLFFBQVFPLEtBQUssQ0FBQyw4QkFBOEJBO1FBQzVDLE1BQU1BO0lBQ1I7QUFDRjtBQUVBLGlFQUFlVixTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL3NyYy9hcHAvZGF0YWJhc2UvbW9uZ29vc2UuanM/OTk3YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbW9uZ29vc2UgZnJvbSBcIm1vbmdvb3NlXCI7XHJcblxyXG5jb25zdCBjb25uZWN0REIgPSBhc3luYyAoKSA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGlmIChtb25nb29zZS5jb25uZWN0aW9uc1swXT8ucmVhZHlTdGF0ZSkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIlVzaW5nIGV4aXN0aW5nIGRhdGFiYXNlIGNvbm5lY3Rpb25cIik7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBhd2FpdCBtb25nb29zZS5jb25uZWN0KHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJLCB7XHJcbiAgICAgIC8vIHVzZU5ld1VybFBhcnNlcjogdHJ1ZSxcclxuICAgICAgdXNlVW5pZmllZFRvcG9sb2d5OiB0cnVlLFxyXG4gICAgfSk7XHJcblxyXG4gICAgY29uc29sZS5sb2coXCJNb25nb0RCIGNvbm5lY3Rpb24gZXN0YWJsaXNoZWQgc3VjY2Vzc2Z1bGx5XCIpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRGF0YWJhc2UgY29ubmVjdGlvbiBlcnJvcjpcIiwgZXJyb3IpO1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgY29ubmVjdERCO1xyXG4iXSwibmFtZXMiOlsibW9uZ29vc2UiLCJjb25uZWN0REIiLCJjb25uZWN0aW9ucyIsInJlYWR5U3RhdGUiLCJjb25zb2xlIiwibG9nIiwiY29ubmVjdCIsInByb2Nlc3MiLCJlbnYiLCJNT05HT0RCX1VSSSIsInVzZVVuaWZpZWRUb3BvbG9neSIsImVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/database/mongoose.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/debug","vendor-chunks/ms","vendor-chunks/undici","vendor-chunks/ramda","vendor-chunks/es5-ext","vendor-chunks/got","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/google-play-scraper","vendor-chunks/memoizee","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/http2-wrapper","vendor-chunks/tough-cookie","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/es6-symbol","vendor-chunks/htmlparser2","vendor-chunks/whatwg-mimetype","vendor-chunks/type","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/ext","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/timers-ext","vendor-chunks/get-stream","vendor-chunks/decompress-response","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/psl","vendor-chunks/wrappy","vendor-chunks/url-parse","vendor-chunks/universalify","vendor-chunks/safer-buffer","vendor-chunks/responselike","vendor-chunks/resolve-alpn","vendor-chunks/requires-port","vendor-chunks/quick-lru","vendor-chunks/querystringify","vendor-chunks/punycode","vendor-chunks/pump","vendor-chunks/p-cancelable","vendor-chunks/once","vendor-chunks/normalize-url","vendor-chunks/next-tick","vendor-chunks/mimic-response","vendor-chunks/lru-queue","vendor-chunks/lowercase-keys","vendor-chunks/keyv","vendor-chunks/json-buffer","vendor-chunks/is-promise","vendor-chunks/http-cache-semantics","vendor-chunks/event-emitter","vendor-chunks/end-of-stream","vendor-chunks/defer-to-connect","vendor-chunks/d","vendor-chunks/clone-response","vendor-chunks/cacheable-request","vendor-chunks/cacheable-lookup","vendor-chunks/boolbase","vendor-chunks/@szmarczak","vendor-chunks/@sindresorhus"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fapp_by_name_id%2Froute&page=%2Fapi%2Fapp_by_name_id%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fapp_by_name_id%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();