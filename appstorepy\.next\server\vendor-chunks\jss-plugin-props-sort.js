"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jss-plugin-props-sort";
exports.ids = ["vendor-chunks/jss-plugin-props-sort"];
exports.modules = {

/***/ "(ssr)/./node_modules/jss-plugin-props-sort/dist/jss-plugin-props-sort.esm.js":
/*!******************************************************************************!*\
  !*** ./node_modules/jss-plugin-props-sort/dist/jss-plugin-props-sort.esm.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * Sort props by length.\n */ function jssPropsSort() {\n    var sort = function sort(prop0, prop1) {\n        if (prop0.length === prop1.length) {\n            return prop0 > prop1 ? 1 : -1;\n        }\n        return prop0.length - prop1.length;\n    };\n    return {\n        onProcessStyle: function onProcessStyle(style, rule) {\n            if (rule.type !== \"style\") return style;\n            var newStyle = {};\n            var props = Object.keys(style).sort(sort);\n            for(var i = 0; i < props.length; i++){\n                newStyle[props[i]] = style[props[i]];\n            }\n            return newStyle;\n        }\n    };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (jssPropsSort);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jss-plugin-props-sort/dist/jss-plugin-props-sort.esm.js\n");

/***/ })

};
;