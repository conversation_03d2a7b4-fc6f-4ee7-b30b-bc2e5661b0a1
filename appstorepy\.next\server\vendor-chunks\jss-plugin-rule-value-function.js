"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jss-plugin-rule-value-function";
exports.ids = ["vendor-chunks/jss-plugin-rule-value-function"];
exports.modules = {

/***/ "(ssr)/./node_modules/jss-plugin-rule-value-function/dist/jss-plugin-rule-value-function.esm.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/jss-plugin-rule-value-function/dist/jss-plugin-rule-value-function.esm.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var tiny_warning__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tiny-warning */ \"(ssr)/./node_modules/tiny-warning/dist/tiny-warning.esm.js\");\n/* harmony import */ var jss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jss */ \"(ssr)/./node_modules/jss/dist/jss.esm.js\");\n\n\nvar now = Date.now();\nvar fnValuesNs = \"fnValues\" + now;\nvar fnRuleNs = \"fnStyle\" + ++now;\nvar functionPlugin = function functionPlugin() {\n    return {\n        onCreateRule: function onCreateRule(name, decl, options) {\n            if (typeof decl !== \"function\") return null;\n            var rule = (0,jss__WEBPACK_IMPORTED_MODULE_0__.createRule)(name, {}, options);\n            rule[fnRuleNs] = decl;\n            return rule;\n        },\n        onProcessStyle: function onProcessStyle(style, rule) {\n            // We need to extract function values from the declaration, so that we can keep core unaware of them.\n            // We need to do that only once.\n            // We don't need to extract functions on each style update, since this can happen only once.\n            // We don't support function values inside of function rules.\n            if (fnValuesNs in rule || fnRuleNs in rule) return style;\n            var fnValues = {};\n            for(var prop in style){\n                var value = style[prop];\n                if (typeof value !== \"function\") continue;\n                delete style[prop];\n                fnValues[prop] = value;\n            }\n            rule[fnValuesNs] = fnValues;\n            return style;\n        },\n        onUpdate: function onUpdate(data, rule, sheet, options) {\n            var styleRule = rule;\n            var fnRule = styleRule[fnRuleNs]; // If we have a style function, the entire rule is dynamic and style object\n            // will be returned from that function.\n            if (fnRule) {\n                // Empty object will remove all currently defined props\n                // in case function rule returns a falsy value.\n                styleRule.style = fnRule(data) || {};\n                if (true) {\n                    for(var prop in styleRule.style){\n                        if (typeof styleRule.style[prop] === \"function\") {\n                             true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(false, \"[JSS] Function values inside function rules are not supported.\") : 0;\n                            break;\n                        }\n                    }\n                }\n            }\n            var fnValues = styleRule[fnValuesNs]; // If we have a fn values map, it is a rule with function values.\n            if (fnValues) {\n                for(var _prop in fnValues){\n                    styleRule.prop(_prop, fnValues[_prop](data), options);\n                }\n            }\n        }\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (functionPlugin);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jss-plugin-rule-value-function/dist/jss-plugin-rule-value-function.esm.js\n");

/***/ })

};
;