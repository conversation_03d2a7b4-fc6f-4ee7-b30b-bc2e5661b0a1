/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/apps/page";
exports.ids = ["app/apps/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapps%2Fpage&page=%2Fapps%2Fpage&appPaths=%2Fapps%2Fpage&pagePath=private-next-app-dir%2Fapps%2Fpage.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapps%2Fpage&page=%2Fapps%2Fpage&appPaths=%2Fapps%2Fpage&pagePath=private-next-app-dir%2Fapps%2Fpage.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'apps',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/apps/page.js */ \"(rsc)/./src/app/apps/page.js\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/apps/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/apps/page\",\n        pathname: \"/apps\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapps%2Fpage&page=%2Fapps%2Fpage&appPaths=%2Fapps%2Fpage&pagePath=private-next-app-dir%2Fapps%2Fpage.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CComponents%5C%5Cnavbar%5C%5Cnavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CReduxLayout%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CComponents%5C%5Cnavbar%5C%5Cnavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CReduxLayout%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/Components/navbar/navbar.jsx */ \"(ssr)/./src/app/Components/navbar/navbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/ReduxLayout/layout.js */ \"(ssr)/./src/app/ReduxLayout/layout.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CComponents%5C%5Cnavbar%5C%5Cnavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CReduxLayout%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Capps%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Capps%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/apps/page.js */ \"(ssr)/./src/app/apps/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0JodXNoYW4lMjBwYXRpbCU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q0FuY2hvcmluZyU1QyU1Q0FwayUyMG1pcnJvciU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FwcHMlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SkFBZ0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLz9lNWFlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQmh1c2hhbiBwYXRpbFxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXEFuY2hvcmluZ1xcXFxBcGsgbWlycm9yXFxcXHNyY1xcXFxhcHBcXFxcYXBwc1xcXFxwYWdlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Capps%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/Ads.jsx":
/*!*************************!*\
  !*** ./src/app/Ads.jsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Ads = ({ slot = \"\", className })=>{\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [insSize, setInsSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const insRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setUrl(window.location.hostname);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAd = ()=>{\n            try {\n                const adsGoogle = window.adsbygoogle || [];\n                adsGoogle.push({});\n            } catch (err) {}\n        };\n        const adScript = document.createElement(\"script\");\n        adScript.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8916641928046583`;\n        adScript.async = true;\n        adScript.onload = initializeAd;\n        document.body.appendChild(adScript);\n        const checkInsSize = setTimeout(()=>{\n            if (insRef.current) {\n                const { offsetWidth, offsetHeight } = insRef.current;\n                setInsSize({\n                    width: offsetWidth,\n                    height: offsetHeight\n                });\n            }\n        }, 1500); // Delay by 1.5 seconds to allow ad to load\n        return ()=>clearTimeout(checkInsSize);\n    }, [\n        slot\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n            className: \"adsbygoogle\",\n            style: {\n                \"display\": \"block\"\n            },\n            \"data-ad-client\": \"ca-pub-8916641928046583\",\n            \"data-ad-slot\": \"8457249210\",\n            \"data-ad-format\": \"auto\",\n            \"data-full-width-responsive\": \"true\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Ads.jsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Ads.jsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Ads);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Ads.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/Components/navbar/navbar.jsx":
/*!**********************************************!*\
  !*** ./src/app/Components/navbar/navbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Navbar = ()=>{\n    const pathName = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [searchTerm, setSearchTerm] = useState(\"\");\n    const [showSearch, setShowSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSearch(!pathName.includes(\"/app-search\"));\n    }, [\n        pathName\n    ]);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    // const onHandleChange = (e) => {\n    //   setSearchTerm(e.target.value);\n    // };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 left-0 right-0 z-10 bg-white shadow\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"bg-white border-gray-200 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/\",\n                        className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"self-center text-2xl font-semibold whitespace-nowrap dark:text-white\",\n                            children: \"APKExplorer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex md:order-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            \"aria-controls\": \"navbar-search\",\n                            \"aria-expanded\": isMenuOpen,\n                            className: \"grid place-items-center md:hidden text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5\",\n                            onClick: toggleMenu,\n                            children: [\n                                isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    \"aria-hidden\": \"true\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        stroke: \"currentColor\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    \"aria-hidden\": \"true\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        stroke: \"currentColor\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M3 6h14M3 10h14m-7 4h7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Toggle navigation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-full md:flex ml-auto md:w-auto md:order-1 ${isMenuOpen ? \"block\" : \"hidden\"}`,\n                        id: \"navbar-search\",\n                        onClick: toggleMenu,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"flex flex-col p-4 md:p-0 mt-4 font-medium border text-transform: uppercase border-gray-100 rounded-lg bg-gray-50 md:space-x-6 lg:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-white dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700\n                    ${pathName === \"/\" && \"md:text-blue-700 decoration-blue-700\"}\n                 `,\n                                        \"aria-current\": \"page\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/apps\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700\n                     ${pathName.includes(\"/apps\") && \"md:text-blue-700 focus:ring-violet-300 decoration-blue-700\"}\n                  `,\n                                        children: \"Android Apps\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/games\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 ${pathName.includes(\"/games\") && \"md:text-blue-700 decoration-blue-700\"}`,\n                                        children: \"Android Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/app-search\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 ${pathName.includes(\"/app-search\") && \"md:text-blue-700 decoration-blue-700\"}`,\n                                        children: \"Search Apps & Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Components/navbar/navbar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/Constant/staticData.js":
/*!****************************************!*\
  !*** ./src/app/Constant/staticData.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   androidApps: () => (/* binding */ androidApps),\n/* harmony export */   androidGames: () => (/* binding */ androidGames)\n/* harmony export */ });\nconst androidApps = [\n    {\n        icon: \"fa-solid fa-paintbrush\",\n        name: \"Art & Design\",\n        category: \"art-design\"\n    },\n    {\n        icon: \"fa-solid fa-car\",\n        name: \"Auto & Vehicles\",\n        category: \"auto-vehicles\"\n    },\n    {\n        icon: \"fa-solid fa-person-dress\",\n        name: \"Beauty\",\n        category: \"beauty\"\n    },\n    {\n        icon: \"fa-solid fa-book\",\n        name: \"Books & Reference\",\n        category: \"books-reference\"\n    },\n    {\n        icon: \"fa-solid fa-briefcase\",\n        name: \"Business\",\n        category: \"business\"\n    },\n    {\n        icon: \"fa-solid fa-paintbrush\",\n        name: \"Comics\",\n        category: \"comics\"\n    },\n    {\n        icon: \"fa-regular fa-comments\",\n        name: \"Communication\",\n        category: \"communication\"\n    },\n    {\n        icon: \"fa-solid fa-heart\",\n        name: \"Dating\",\n        category: \"dating\"\n    },\n    {\n        icon: \"fa-solid fa-graduation-cap\",\n        name: \"Education\",\n        category: \"education\"\n    },\n    {\n        icon: \"fa-solid fa-video\",\n        name: \"Entertainment\",\n        category: \"entertainment\"\n    },\n    {\n        icon: \"fa-regular fa-money-bill-1\",\n        name: \"Finance\",\n        category: \"finance\"\n    },\n    {\n        icon: \"fa-solid fa-utensils\",\n        name: \"Food & Drink\",\n        category: \"food-drink\"\n    },\n    {\n        icon: \"fa-solid fa-heart-pulse\",\n        name: \"Health & Fitness\",\n        category: \"health-fitness\"\n    },\n    {\n        icon: \"fa-solid fa-house-chimney\",\n        name: \"House & Home\",\n        category: \"house-home\"\n    },\n    {\n        icon: \"fa-regular fa-file-code\",\n        name: \"Libraries & Demo\",\n        category: \"libraries-demo\"\n    },\n    {\n        icon: \"fa-solid fa-person\",\n        name: \"Lifestyle\",\n        category: \"lifestyle\"\n    },\n    {\n        icon: \"fa-solid fa-suitcase-medical\",\n        name: \"Medical\",\n        category: \"medical\"\n    },\n    {\n        icon: \"fa-solid fa-headphones-simple\",\n        name: \"Music & Audio\",\n        category: \"music-audio\"\n    },\n    {\n        icon: \"fa-regular fa-newspaper\",\n        name: \"News & Magazines\",\n        category: \"news-magazines\"\n    },\n    {\n        icon: \"fa-solid fa-user\",\n        name: \"Personalization\",\n        category: \"personalization\"\n    },\n    {\n        icon: \"fa-solid fa-camera-retro\",\n        name: \"Photography\",\n        category: \"photography\"\n    },\n    {\n        icon: \"fa-solid fa-gears\",\n        name: \"Productivity\",\n        category: \"productivity\"\n    },\n    {\n        icon: \"fa-solid fa-cart-shopping\",\n        name: \"Shopping\",\n        category: \"shopping\"\n    },\n    {\n        icon: \"fa-solid fa-earth-americas\",\n        name: \"Social\",\n        category: \"social\"\n    },\n    {\n        icon: \"fa-regular fa-futbol\",\n        name: \"Sports\",\n        category: \"sports\"\n    },\n    {\n        icon: \"fa-solid fa-wrench\",\n        name: \"Tools\",\n        category: \"tools\"\n    },\n    {\n        icon: \"fa-solid fa-signs-post\",\n        name: \"Travel & Local\",\n        category: \"travel-local\"\n    },\n    {\n        icon: \"fa-regular fa-sun\",\n        name: \"Weather\",\n        category: \"weather\"\n    }\n];\nconst androidGames = [\n    {\n        icon: \"fa-solid fa-gamepad\",\n        name: \"Action\",\n        category: \"action\"\n    },\n    {\n        icon: \"fa-solid fa-chess-knight\",\n        name: \"Board\",\n        category: \"board\"\n    },\n    {\n        icon: \"fa-solid fa-mug-hot\",\n        name: \"Casual\",\n        category: \"casual\"\n    },\n    {\n        icon: \"fa-solid fa-puzzle-piece\",\n        name: \"Puzzle\",\n        category: \"puzzle\"\n    },\n    {\n        icon: \"fa-solid fa-clipboard-question\",\n        name: \"Simulation\",\n        category: \"simulation\"\n    },\n    {\n        icon: \"fa-regular fa-circle-question\",\n        name: \"Trivia\",\n        category: \"trivia\"\n    },\n    {\n        icon: \"fa-brands fa-space-awesome\",\n        name: \"Adventure\",\n        category: \"adventure\"\n    },\n    {\n        icon: \"fa-regular fa-heart\",\n        name: \"Card\",\n        category: \"card\"\n    },\n    {\n        icon: \"fa-solid fa-car\",\n        name: \"Racing\",\n        category: \"racing\"\n    },\n    {\n        icon: \"fa-regular fa-futbol\",\n        name: \"Sports\",\n        category: \"sports\"\n    },\n    {\n        icon: \"fa-solid fa-pen-to-square\",\n        name: \"Word\",\n        category: \"word\"\n    },\n    {\n        icon: \"fa-solid fa-trophy\",\n        name: \"Arcade\",\n        category: \"arcade\"\n    },\n    {\n        icon: \"fa-regular fa-square\",\n        name: \"Casino\",\n        category: \"casino\"\n    },\n    {\n        icon: \"fa-solid fa-music\",\n        name: \"Music\",\n        category: \"music\"\n    },\n    {\n        icon: \"fa-solid fa-users\",\n        name: \"Role Playing\",\n        category: \"role-playing\"\n    },\n    {\n        icon: \"fa-regular fa-lightbulb\",\n        name: \"Strategy\",\n        category: \"strategy\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Constant/staticData.js\n");

/***/ }),

/***/ "(ssr)/./src/app/Loading.jsx":
/*!*****************************!*\
  !*** ./src/app/Loading.jsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst LoadingComponent = ({ length, md = 2, lg })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `grid grid-cols-1 md:grid-cols-${md} lg:grid-cols-${lg} gap-4 p-4`,\n        children: Array.from({\n            length: length\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse hover:bg-gray-100 p-1 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md bg-slate-200 h-20 w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                            lineNumber: 10,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2.5 bg-gray-200 rounded-full  w-48 my-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2 bg-gray-200 rounded-full  w-48 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2 bg-gray-200 rounded-full  w-48 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2.5 bg-gray-200 rounded-full  w-32 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                            lineNumber: 11,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, undefined)\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingComponent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Loading.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/ReduxLayout/layout.js":
/*!***************************************!*\
  !*** ./src/app/ReduxLayout/layout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReduxProvider: () => (/* binding */ ReduxProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _redux_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../redux/store */ \"(ssr)/./src/app/redux/store.js\");\n/* __next_internal_client_entry_do_not_use__ ReduxProvider auto */ \n\n\nfunction ReduxProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_2__.Provider, {\n        store: _redux_store__WEBPACK_IMPORTED_MODULE_1__.store,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\layout.js\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1JlZHV4TGF5b3V0L2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDdUM7QUFDQTtBQUVoQyxTQUFTRSxjQUFjLEVBQUVDLFFBQVEsRUFBRTtJQUN4QyxxQkFBTyw4REFBQ0gsaURBQVFBO1FBQUNDLE9BQU9BLCtDQUFLQTtrQkFBR0U7Ozs7OztBQUNsQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Fway1taXJyb3IvLi9zcmMvYXBwL1JlZHV4TGF5b3V0L2xheW91dC5qcz8xYzE1Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHsgUHJvdmlkZXIgfSBmcm9tIFwicmVhY3QtcmVkdXhcIjtcbmltcG9ydCB7IHN0b3JlIH0gZnJvbSBcIi4uL3JlZHV4L3N0b3JlXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBSZWR1eFByb3ZpZGVyKHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gPFByb3ZpZGVyIHN0b3JlPXtzdG9yZX0+e2NoaWxkcmVufTwvUHJvdmlkZXI+O1xufVxuIl0sIm5hbWVzIjpbIlByb3ZpZGVyIiwic3RvcmUiLCJSZWR1eFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/ReduxLayout/layout.js\n");

/***/ }),

/***/ "(ssr)/./src/app/SideBar.jsx":
/*!*****************************!*\
  !*** ./src/app/SideBar.jsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Loading__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Loading */ \"(ssr)/./src/app/Loading.jsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst SideBar = ({ sideappDetails, header = \"RECENTLY UPDATED APPS\", isLoading })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-3.5 p-5 bg-white rounded-md shadow-md \",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"mb-2.5 font-normal text-slate-500 tracking-wider uppercase\",\n                children: header\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loading__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                length: 5,\n                md: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid sm:grid-cols-2 lg:grid-cols-1\",\n                children: sideappDetails?.map((appDetails)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: `/apps/appdetails/${appDetails.appId}`,\n                        target: \"_blank\",\n                        prefetch: false,\n                        className: \"mt-2.5 p-1.5 hover:bg-gray-100 p-1 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" grid grid-cols-4 lg:grid-cols-3 xl:grid-cols-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    width: 75,\n                                    height: 75,\n                                    className: \"rounded-2xl\",\n                                    src: appDetails.icon,\n                                    alt: `${appDetails.title} Icon`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 17\n                                }, undefined),\n                                header === \"RECENTLY UPDATED APPS\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-2 col-span-2 \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium truncate\",\n                                            title: appDetails.title,\n                                            children: appDetails.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-400 tracking-wider\",\n                                            children: [\n                                                \"VERSION \",\n                                                appDetails.latestVersion?.split(\" \")[0]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-400 uppercase tracking-wider\",\n                                            children: appDetails.updated\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-yellow-400 me-1\",\n                                                    \"aria-hidden\": \"true\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 22 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-slate-400 dark:text-slate-400\",\n                                                    children: appDetails.scoreText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-2 col-span-2 \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium truncate\",\n                                            title: appDetails.title,\n                                            children: appDetails.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-400 tracking-wider uppercase\",\n                                            children: appDetails.developer\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-yellow-400 me-1\",\n                                                    \"aria-hidden\": \"true\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 22 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-slate-400 dark:text-slate-400\",\n                                                    children: appDetails.scoreText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                            lineNumber: 28,\n                            columnNumber: 15\n                        }, undefined)\n                    }, appDetails.appId, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                        lineNumber: 21,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SideBar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1NpZGVCYXIuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUF5QztBQUNWO0FBQ0Y7QUFDSDtBQUUxQixNQUFNSSxVQUFVLENBQUMsRUFDZkMsY0FBYyxFQUNkQyxTQUFTLHVCQUF1QixFQUNoQ0MsU0FBUyxFQUNWO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBR0QsV0FBVTswQkFDWEg7Ozs7OztZQUVGQywwQkFDQyw4REFBQ1AsZ0RBQWdCQTtnQkFBQ1csUUFBUTtnQkFBR0MsSUFBSTs7Ozs7MENBRWpDLDhEQUFDSjtnQkFBSUMsV0FBVTswQkFDWkosZ0JBQWdCUSxJQUFJLENBQUNDLDJCQUNwQiw4REFBQ1osaURBQUlBO3dCQUNIYSxNQUFNLENBQUMsaUJBQWlCLEVBQUVELFdBQVdFLEtBQUssQ0FBQyxDQUFDO3dCQUM1Q0MsUUFBTzt3QkFDUEMsVUFBVTt3QkFFVlQsV0FBVTtrQ0FFViw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDUixrREFBS0E7b0NBQ0prQixPQUFPO29DQUNQQyxRQUFRO29DQUNSWCxXQUFVO29DQUNWWSxLQUFLUCxXQUFXUSxJQUFJO29DQUNwQkMsS0FBSyxDQUFDLEVBQUVULFdBQVdVLEtBQUssQ0FBQyxLQUFLLENBQUM7Ozs7OztnQ0FFaENsQixXQUFXLHdDQUNWLDhEQUFDRTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNnQjs0Q0FDQ2hCLFdBQVU7NENBQ1ZlLE9BQU9WLFdBQVdVLEtBQUs7c0RBRXRCVixXQUFXVSxLQUFLOzs7Ozs7c0RBRW5CLDhEQUFDRTs0Q0FBRWpCLFdBQVU7O2dEQUF3QztnREFDMUNLLFdBQVdhLGFBQWEsRUFBRUMsTUFBTSxJQUFJLENBQUMsRUFBRTs7Ozs7OztzREFFbEQsOERBQUNGOzRDQUFFakIsV0FBVTtzREFDVkssV0FBV2UsT0FBTzs7Ozs7O3NEQUVyQiw4REFBQ3JCOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ3FCO29EQUNDckIsV0FBVTtvREFDVnNCLGVBQVk7b0RBQ1pDLE9BQU07b0RBQ05DLE1BQUs7b0RBQ0xDLFNBQVE7OERBRVIsNEVBQUNDO3dEQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs4REFFViw4REFBQ1Y7b0RBQUVqQixXQUFVOzhEQUNWSyxXQUFXdUIsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSzNCLDhEQUFDN0I7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDZ0I7NENBQ0NoQixXQUFVOzRDQUNWZSxPQUFPVixXQUFXVSxLQUFLO3NEQUV0QlYsV0FBV1UsS0FBSzs7Ozs7O3NEQUVuQiw4REFBQ0U7NENBQUVqQixXQUFVO3NEQUNWSyxXQUFXd0IsU0FBUzs7Ozs7O3NEQUd2Qiw4REFBQzlCOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ3FCO29EQUNDckIsV0FBVTtvREFDVnNCLGVBQVk7b0RBQ1pDLE9BQU07b0RBQ05DLE1BQUs7b0RBQ0xDLFNBQVE7OERBRVIsNEVBQUNDO3dEQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs4REFFViw4REFBQ1Y7b0RBQUVqQixXQUFVOzhEQUNWSyxXQUFXdUIsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VCQS9EMUJ2QixXQUFXRSxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7O0FBMkVuQztBQUVBLGlFQUFlWixPQUFPQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL3NyYy9hcHAvU2lkZUJhci5qc3g/Y2MzZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTG9hZGluZ0NvbXBvbmVudCBmcm9tIFwiLi9Mb2FkaW5nXCI7XHJcbmltcG9ydCBJbWFnZSBmcm9tIFwibmV4dC9pbWFnZVwiO1xyXG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XHJcbmltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuXHJcbmNvbnN0IFNpZGVCYXIgPSAoe1xyXG4gIHNpZGVhcHBEZXRhaWxzLFxyXG4gIGhlYWRlciA9IFwiUkVDRU5UTFkgVVBEQVRFRCBBUFBTXCIsXHJcbiAgaXNMb2FkaW5nLFxyXG59KSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibXktMy41IHAtNSBiZy13aGl0ZSByb3VuZGVkLW1kIHNoYWRvdy1tZCBcIj5cclxuICAgICAgPGgyIGNsYXNzTmFtZT1cIm1iLTIuNSBmb250LW5vcm1hbCB0ZXh0LXNsYXRlLTUwMCB0cmFja2luZy13aWRlciB1cHBlcmNhc2VcIj5cclxuICAgICAgICB7aGVhZGVyfVxyXG4gICAgICA8L2gyPlxyXG4gICAgICB7aXNMb2FkaW5nID8gKFxyXG4gICAgICAgIDxMb2FkaW5nQ29tcG9uZW50IGxlbmd0aD17NX0gbWQ9ezF9IC8+XHJcbiAgICAgICkgOiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIHNtOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0xXCI+XHJcbiAgICAgICAgICB7c2lkZWFwcERldGFpbHM/Lm1hcCgoYXBwRGV0YWlscykgPT4gKFxyXG4gICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgIGhyZWY9e2AvYXBwcy9hcHBkZXRhaWxzLyR7YXBwRGV0YWlscy5hcHBJZH1gfVxyXG4gICAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiXHJcbiAgICAgICAgICAgICAgcHJlZmV0Y2g9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgIGtleT17YXBwRGV0YWlscy5hcHBJZH1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0yLjUgcC0xLjUgaG92ZXI6YmctZ3JheS0xMDAgcC0xIHJvdW5kZWQtbWRcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCIgZ3JpZCBncmlkLWNvbHMtNCBsZzpncmlkLWNvbHMtMyB4bDpncmlkLWNvbHMtNFwiPlxyXG4gICAgICAgICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgICAgICAgIHdpZHRoPXs3NX1cclxuICAgICAgICAgICAgICAgICAgaGVpZ2h0PXs3NX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC0yeGxcIlxyXG4gICAgICAgICAgICAgICAgICBzcmM9e2FwcERldGFpbHMuaWNvbn1cclxuICAgICAgICAgICAgICAgICAgYWx0PXtgJHthcHBEZXRhaWxzLnRpdGxlfSBJY29uYH1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICB7aGVhZGVyID09PSBcIlJFQ0VOVExZIFVQREFURUQgQVBQU1wiID8gKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTIgY29sLXNwYW4tMiBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8aDRcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdHJ1bmNhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgdGl0bGU9e2FwcERldGFpbHMudGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2FwcERldGFpbHMudGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9oND5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtNDAwIHRyYWNraW5nLXdpZGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICBWRVJTSU9OIHthcHBEZXRhaWxzLmxhdGVzdFZlcnNpb24/LnNwbGl0KFwiIFwiKVswXX1cclxuICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTQwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHthcHBEZXRhaWxzLnVwZGF0ZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzdmdcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXllbGxvdy00MDAgbWUtMVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtaGlkZGVuPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyMiAyMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMjAuOTI0IDcuNjI1YTEuNTIzIDEuNTIzIDAgMCAwLTEuMjM4LTEuMDQ0bC01LjA1MS0uNzM0LTIuMjU5LTQuNTc3YTEuNTM0IDEuNTM0IDAgMCAwLTIuNzUyIDBMNy4zNjUgNS44NDdsLTUuMDUxLjczNEExLjUzNSAxLjUzNSAwIDAgMCAxLjQ2MyA5LjJsMy42NTYgMy41NjMtLjg2MyA1LjAzMWExLjUzMiAxLjUzMiAwIDAgMCAyLjIyNiAxLjYxNkwxMSAxNy4wMzNsNC41MTggMi4zNzVhMS41MzQgMS41MzQgMCAwIDAgMi4yMjYtMS42MTdsLS44NjMtNS4wM0wyMC41MzcgOS4yYTEuNTIzIDEuNTIzIDAgMCAwIC4zODctMS41NzVaXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LWJvbGQgdGV4dC1zbGF0ZS00MDAgZGFyazp0ZXh0LXNsYXRlLTQwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7YXBwRGV0YWlscy5zY29yZVRleHR9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC0yIGNvbC1zcGFuLTIgXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGg0XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRydW5jYXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPXthcHBEZXRhaWxzLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHthcHBEZXRhaWxzLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvaDQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTQwMCB0cmFja2luZy13aWRlciB1cHBlcmNhc2VcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHthcHBEZXRhaWxzLmRldmVsb3Blcn1cclxuICAgICAgICAgICAgICAgICAgICA8L3A+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzdmdcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXllbGxvdy00MDAgbWUtMVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtaGlkZGVuPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyMiAyMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMjAuOTI0IDcuNjI1YTEuNTIzIDEuNTIzIDAgMCAwLTEuMjM4LTEuMDQ0bC01LjA1MS0uNzM0LTIuMjU5LTQuNTc3YTEuNTM0IDEuNTM0IDAgMCAwLTIuNzUyIDBMNy4zNjUgNS44NDdsLTUuMDUxLjczNEExLjUzNSAxLjUzNSAwIDAgMCAxLjQ2MyA5LjJsMy42NTYgMy41NjMtLjg2MyA1LjAzMWExLjUzMiAxLjUzMiAwIDAgMCAyLjIyNiAxLjYxNkwxMSAxNy4wMzNsNC41MTggMi4zNzVhMS41MzQgMS41MzQgMCAwIDAgMi4yMjYtMS42MTdsLS44NjMtNS4wM0wyMC41MzcgOS4yYTEuNTIzIDEuNTIzIDAgMCAwIC4zODctMS41NzVaXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LWJvbGQgdGV4dC1zbGF0ZS00MDAgZGFyazp0ZXh0LXNsYXRlLTQwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7YXBwRGV0YWlscy5zY29yZVRleHR9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgKSl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICl9XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgU2lkZUJhcjtcclxuIl0sIm5hbWVzIjpbIkxvYWRpbmdDb21wb25lbnQiLCJJbWFnZSIsIkxpbmsiLCJSZWFjdCIsIlNpZGVCYXIiLCJzaWRlYXBwRGV0YWlscyIsImhlYWRlciIsImlzTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwibGVuZ3RoIiwibWQiLCJtYXAiLCJhcHBEZXRhaWxzIiwiaHJlZiIsImFwcElkIiwidGFyZ2V0IiwicHJlZmV0Y2giLCJ3aWR0aCIsImhlaWdodCIsInNyYyIsImljb24iLCJhbHQiLCJ0aXRsZSIsImg0IiwicCIsImxhdGVzdFZlcnNpb24iLCJzcGxpdCIsInVwZGF0ZWQiLCJzdmciLCJhcmlhLWhpZGRlbiIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJwYXRoIiwiZCIsInNjb3JlVGV4dCIsImRldmVsb3BlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/SideBar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/apps/page.js":
/*!******************************!*\
  !*** ./src/app/apps/page.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _categoryWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../categoryWrapper */ \"(ssr)/./src/app/categoryWrapper.jsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _SideBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../SideBar */ \"(ssr)/./src/app/SideBar.jsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _Loading__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../Loading */ \"(ssr)/./src/app/Loading.jsx\");\n/* harmony import */ var _Ads__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Ads */ \"(ssr)/./src/app/Ads.jsx\");\n/* harmony import */ var _util_useBrowserFingerprint__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../util/useBrowserFingerprint */ \"(ssr)/./src/app/util/useBrowserFingerprint.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nconst Apps = ()=>{\n    (0,_util_useBrowserFingerprint__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [androidApps, setAndroidApps] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [recentlyUpdatedAppsAndGames, setRecentlyUpdatedAppsAndGames] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const isAdsServe = JSON.parse(\"true\");\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const getAndroidApps = async ()=>{\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(`/api/get_apps_games?type=app`);\n                if (response && response.status === 200) {\n                    setAndroidApps(response.data.AndroidData);\n                    setRecentlyUpdatedAppsAndGames(response.data.recentlyUpdatedAppsAndGames);\n                    setIsLoading(false);\n                }\n            } catch (errors) {\n                console.error(errors);\n            }\n        };\n        getAndroidApps();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n            className: \"lg:container flex flex-col items-center justify-between mt-0.5 mx-5 sm:mx-0 md:mx-20 lg:mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container max-w-screen-xl mx-auto\",\n                children: [\n                    isAdsServe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Ads__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        slot: 12,\n                        className: \"mb-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                        lineNumber: 42,\n                        columnNumber: 26\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full md:px-3.5 justify-center flex flex-col lg:flex-row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"lg:w-4/6 xl:w-4/6 relative mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-3.5 p-3 bg-white rounded-md shadow-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    href: \"/\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 20\n                                                }, undefined),\n                                                \" / \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-slate-500\",\n                                                    children: \"Android Apps\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 51\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                            lineNumber: 46,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_categoryWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        name: \"Apps\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isAdsServe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Ads__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        slot: 26,\n                                        className: \"mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                        lineNumber: 49,\n                                        columnNumber: 30\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5 bg-white rounded-md shadow-lg mb-5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"uppercase mb-2.5 text-lg font-normal tracking text-gray-600\",\n                                                children: \"Android Apps\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loading__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                length: 6\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                lineNumber: 55,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 lg:grid-cols-1 xl:grid-cols-2  gap-4 p-4\",\n                                                children: androidApps?.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"hover:bg-gray-100 p-1 rounded-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            href: `/apps/appdetails/${app.appId}`,\n                                                            target: \"_blank\",\n                                                            prefetch: false,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                        className: \"rounded-2xl\",\n                                                                        width: 75,\n                                                                        height: 75,\n                                                                        src: app.icon,\n                                                                        alt: `${app.title} Icon`\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                                        lineNumber: 69,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-sm font-medium\",\n                                                                                children: app.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                                                lineNumber: 77,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-slate-400 tracking-wider\",\n                                                                                children: [\n                                                                                    \"VERSION \",\n                                                                                    app.latestVersion\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                                                lineNumber: 78,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-slate-400 uppercase tracking-wider\",\n                                                                                children: app.updated\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                                                lineNumber: 81,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-4 h-4 text-yellow-400 me-1\",\n                                                                                        \"aria-hidden\": \"true\",\n                                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                        fill: \"currentColor\",\n                                                                                        viewBox: \"0 0 22 20\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                                                            lineNumber: 92,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                                                        lineNumber: 85,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm font-bold text-slate-400 dark:text-slate-400\",\n                                                                                        children: app.scoreText\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                                                        lineNumber: 94,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                                                lineNumber: 84,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                                        lineNumber: 76,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                                lineNumber: 68,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, app.appId, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                                className: \"sidebar-container sm:w-auto lg:w-2/6 lg:px-3.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sideappDetails: recentlyUpdatedAppsAndGames,\n                                        isLoading: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                        lineNumber: 108,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    isAdsServe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Ads__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        slot: 13,\n                                        className: \"mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                        lineNumber: 109,\n                                        columnNumber: 26\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\page.js\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Apps);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/apps/page.js\n");

/***/ }),

/***/ "(ssr)/./src/app/categoryWrapper.jsx":
/*!*************************************!*\
  !*** ./src/app/categoryWrapper.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _Constant_staticData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Constant/staticData */ \"(ssr)/./src/app/Constant/staticData.js\");\n\n\n\nconst Android = ({ name })=>{\n    const categories = name === \"Apps\" ? _Constant_staticData__WEBPACK_IMPORTED_MODULE_2__.androidApps : _Constant_staticData__WEBPACK_IMPORTED_MODULE_2__.androidGames;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-3.5 p-5 bg-white rounded-md shadow-md flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"mb-2.5 text-base font-normal text-slate-500 uppercase tracking-wider\",\n                children: [\n                    \"Android \",\n                    name,\n                    \" Categories\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\categoryWrapper.jsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 \",\n                    children: categories.map((data, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"max-w-52 max-h-12 p-1 text-sm font-normal relative float-left rounded-md hover:bg-gray-100\",\n                            href: `/${name.toLowerCase()}/${data.category}`,\n                            prefetch: false,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg m-1 w-7 h-7 inline-block leading-7 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: data.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\categoryWrapper.jsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\categoryWrapper.jsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"py-2.5 inline-block w-4/4\",\n                                        children: data.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\categoryWrapper.jsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\categoryWrapper.jsx\",\n                                lineNumber: 21,\n                                columnNumber: 15\n                            }, undefined)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\categoryWrapper.jsx\",\n                            lineNumber: 15,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\categoryWrapper.jsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\categoryWrapper.jsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\categoryWrapper.jsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Android);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/categoryWrapper.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/redux/features/appSlice.js":
/*!********************************************!*\
  !*** ./src/app/redux/features/appSlice.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAppDetails: () => (/* binding */ getAppDetails)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n\nconst getAppDetails = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)(\"app/getAppDetails\", async (appId, { rejectWithValue })=>{\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(`/api/app_by_name_id?appId=${appId}`);\n        if (response && response.status === 200) {\n            return response.data;\n        }\n    } catch (error) {\n        return rejectWithValue(\"App Version not available\");\n    }\n});\nconst initialState = {\n    loading: false,\n    appDetail: [],\n    error: null\n};\nconst appSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: \"app\",\n    initialState,\n    reducers: {},\n    extraReducers: (builder)=>{\n        builder.addCase(getAppDetails.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(getAppDetails.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.appDetail = action.payload;\n            state.error = null;\n        }).addCase(getAppDetails.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (appSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/redux/features/appSlice.js\n");

/***/ }),

/***/ "(ssr)/./src/app/redux/store.js":
/*!********************************!*\
  !*** ./src/app/redux/store.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: () => (/* binding */ store)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var _features_appSlice__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./features/appSlice */ \"(ssr)/./src/app/redux/features/appSlice.js\");\n\n\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.configureStore)({\n    reducer: {\n        app: _features_appSlice__WEBPACK_IMPORTED_MODULE_0__[\"default\"]\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3JlZHV4L3N0b3JlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpRDtBQUNQO0FBRW5DLE1BQU1FLFFBQVFGLGdFQUFjQSxDQUFDO0lBQ2xDRyxTQUFTO1FBQ1BDLEtBQU1ILDBEQUFRQTtJQUNoQjtBQUNGLEdBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vc3JjL2FwcC9yZWR1eC9zdG9yZS5qcz9jZjA2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbmZpZ3VyZVN0b3JlIH0gZnJvbSAnQHJlZHV4anMvdG9vbGtpdCdcclxuaW1wb3J0IGFwcFNsaWNlIGZyb20gJy4vZmVhdHVyZXMvYXBwU2xpY2UnXHJcblxyXG5leHBvcnQgY29uc3Qgc3RvcmUgPSBjb25maWd1cmVTdG9yZSh7XHJcbiAgcmVkdWNlcjoge1xyXG4gICAgYXBwIDogYXBwU2xpY2VcclxuICB9LFxyXG59KSJdLCJuYW1lcyI6WyJjb25maWd1cmVTdG9yZSIsImFwcFNsaWNlIiwic3RvcmUiLCJyZWR1Y2VyIiwiYXBwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/redux/store.js\n");

/***/ }),

/***/ "(ssr)/./src/app/util/useBrowserFingerprint.js":
/*!***********************************************!*\
  !*** ./src/app/util/useBrowserFingerprint.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Helper function to get cookies\nconst getCookies = ()=>{\n    const cookies = document.cookie.split(\";\").reduce((acc, cookie)=>{\n        const [key, value] = cookie.trim().split(\"=\");\n        acc[key] = value;\n        return acc;\n    }, {});\n    return cookies;\n};\n// Helper function to set a cookie\nconst setCookie = (name, value, days)=>{\n    const date = new Date();\n    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);\n    const expires = `expires=${date.toUTCString()}`;\n    document.cookie = `${name}=${value};${expires};path=/`;\n};\nconst useBrowserFingerprint = ()=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const cookies = getCookies();\n        // Check if the fingerprint has already been captured\n        if (!cookies.browserFingerprintFlag) {\n            if (typeof window.FingerprintJS !== \"undefined\") {\n                FingerprintJS.load().then((fp)=>{\n                    fp.get().then((result)=>{\n                        const browserFingerprint = result.visitorId;\n                        const browserInfo = {\n                            userAgent: navigator.userAgent,\n                            platform: navigator.platform,\n                            language: navigator.language,\n                            screenResolution: `${window.screen.width}x${window.screen.height}`,\n                            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n                            pageUrl: window.location.href,\n                            fingerprint: browserFingerprint,\n                            cookies: getCookies()\n                        };\n                        // Send data to your custom API\n                        const apiUrl = \"https://test.apk-mirror.com/collect\";\n                        fetch(apiUrl, {\n                            method: \"POST\",\n                            headers: {\n                                \"Content-Type\": \"application/json\"\n                            },\n                            body: JSON.stringify(browserInfo)\n                        }).then((response)=>response.json()).then((data)=>{\n                            console.log(\"Successfully sent browser info and fingerprint:\", data);\n                            // Set a flag in the cookie to avoid future calls\n                            setCookie(\"browserFingerprintFlag\", browserFingerprint, 30); // Cookie expires in 30 days\n                        }).catch((error)=>{\n                            console.error(\"Error sending browser info and fingerprint:\", error);\n                        });\n                    });\n                });\n            }\n        } else {\n            console.log(\"Fingerprint already captured, skipping API call.\");\n        }\n    }, []);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useBrowserFingerprint);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3V0aWwvdXNlQnJvd3NlckZpbmdlcnByaW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrQztBQUVsQyxpQ0FBaUM7QUFDakMsTUFBTUMsYUFBYTtJQUNqQixNQUFNQyxVQUFVQyxTQUFTQyxNQUFNLENBQUNDLEtBQUssQ0FBQyxLQUFLQyxNQUFNLENBQUMsQ0FBQ0MsS0FBS0g7UUFDdEQsTUFBTSxDQUFDSSxLQUFLQyxNQUFNLEdBQUdMLE9BQU9NLElBQUksR0FBR0wsS0FBSyxDQUFDO1FBQ3pDRSxHQUFHLENBQUNDLElBQUksR0FBR0M7UUFDWCxPQUFPRjtJQUNULEdBQUcsQ0FBQztJQUNKLE9BQU9MO0FBQ1Q7QUFFQSxrQ0FBa0M7QUFDbEMsTUFBTVMsWUFBWSxDQUFDQyxNQUFNSCxPQUFPSTtJQUM5QixNQUFNQyxPQUFPLElBQUlDO0lBQ2pCRCxLQUFLRSxPQUFPLENBQUNGLEtBQUtHLE9BQU8sS0FBS0osT0FBTyxLQUFLLEtBQUssS0FBSztJQUNwRCxNQUFNSyxVQUFVLENBQUMsUUFBUSxFQUFFSixLQUFLSyxXQUFXLEdBQUcsQ0FBQztJQUMvQ2hCLFNBQVNDLE1BQU0sR0FBRyxDQUFDLEVBQUVRLEtBQUssQ0FBQyxFQUFFSCxNQUFNLENBQUMsRUFBRVMsUUFBUSxPQUFPLENBQUM7QUFDeEQ7QUFFQSxNQUFNRSx3QkFBd0I7SUFDNUJwQixnREFBU0EsQ0FBQztRQUNSLE1BQU1FLFVBQVVEO1FBRWhCLHFEQUFxRDtRQUNyRCxJQUFJLENBQUNDLFFBQVFtQixzQkFBc0IsRUFBRTtZQUNuQyxJQUFJLE9BQU9DLE9BQU9DLGFBQWEsS0FBSyxhQUFhO2dCQUMvQ0EsY0FBY0MsSUFBSSxHQUFHQyxJQUFJLENBQUMsQ0FBQ0M7b0JBQ3pCQSxHQUFHQyxHQUFHLEdBQUdGLElBQUksQ0FBQyxDQUFDRzt3QkFDYixNQUFNQyxxQkFBcUJELE9BQU9FLFNBQVM7d0JBQzNDLE1BQU1DLGNBQWM7NEJBQ2xCQyxXQUFXQyxVQUFVRCxTQUFTOzRCQUM5QkUsVUFBVUQsVUFBVUMsUUFBUTs0QkFDNUJDLFVBQVVGLFVBQVVFLFFBQVE7NEJBQzVCQyxrQkFBa0IsQ0FBQyxFQUFFZCxPQUFPZSxNQUFNLENBQUNDLEtBQUssQ0FBQyxDQUFDLEVBQUVoQixPQUFPZSxNQUFNLENBQUNFLE1BQU0sQ0FBQyxDQUFDOzRCQUNsRUMsVUFBVUMsS0FBS0MsY0FBYyxHQUFHQyxlQUFlLEdBQUdILFFBQVE7NEJBQzFESSxTQUFTdEIsT0FBT3VCLFFBQVEsQ0FBQ0MsSUFBSTs0QkFDN0JDLGFBQWFsQjs0QkFDYjNCLFNBQVNEO3dCQUNYO3dCQUVBLCtCQUErQjt3QkFDL0IsTUFBTStDLFNBQVM7d0JBQ2ZDLE1BQU1ELFFBQVE7NEJBQ1pFLFFBQVE7NEJBQ1JDLFNBQVM7Z0NBQ1AsZ0JBQWdCOzRCQUNsQjs0QkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDdkI7d0JBQ3ZCLEdBQ0dOLElBQUksQ0FBQyxDQUFDOEIsV0FBYUEsU0FBU0MsSUFBSSxJQUNoQy9CLElBQUksQ0FBQyxDQUFDZ0M7NEJBQ0xDLFFBQVFDLEdBQUcsQ0FBQyxtREFBbURGOzRCQUUvRCxpREFBaUQ7NEJBQ2pEOUMsVUFBVSwwQkFBMEJrQixvQkFBb0IsS0FBSyw0QkFBNEI7d0JBQzNGLEdBQ0MrQixLQUFLLENBQUMsQ0FBQ0M7NEJBQ05ILFFBQVFHLEtBQUssQ0FBQywrQ0FBK0NBO3dCQUMvRDtvQkFDSjtnQkFDRjtZQUNGO1FBQ0YsT0FBTztZQUNMSCxRQUFRQyxHQUFHLENBQUM7UUFDZDtJQUNGLEdBQUcsRUFBRTtBQUNQO0FBRUEsaUVBQWV2QyxxQkFBcUJBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vc3JjL2FwcC91dGlsL3VzZUJyb3dzZXJGaW5nZXJwcmludC5qcz8xZGU3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGdldCBjb29raWVzXG5jb25zdCBnZXRDb29raWVzID0gKCkgPT4ge1xuICBjb25zdCBjb29raWVzID0gZG9jdW1lbnQuY29va2llLnNwbGl0KCc7JykucmVkdWNlKChhY2MsIGNvb2tpZSkgPT4ge1xuICAgIGNvbnN0IFtrZXksIHZhbHVlXSA9IGNvb2tpZS50cmltKCkuc3BsaXQoJz0nKTtcbiAgICBhY2Nba2V5XSA9IHZhbHVlO1xuICAgIHJldHVybiBhY2M7XG4gIH0sIHt9KTtcbiAgcmV0dXJuIGNvb2tpZXM7XG59O1xuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gc2V0IGEgY29va2llXG5jb25zdCBzZXRDb29raWUgPSAobmFtZSwgdmFsdWUsIGRheXMpID0+IHtcbiAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCk7XG4gIGRhdGUuc2V0VGltZShkYXRlLmdldFRpbWUoKSArIGRheXMgKiAyNCAqIDYwICogNjAgKiAxMDAwKTtcbiAgY29uc3QgZXhwaXJlcyA9IGBleHBpcmVzPSR7ZGF0ZS50b1VUQ1N0cmluZygpfWA7XG4gIGRvY3VtZW50LmNvb2tpZSA9IGAke25hbWV9PSR7dmFsdWV9OyR7ZXhwaXJlc307cGF0aD0vYDtcbn07XG5cbmNvbnN0IHVzZUJyb3dzZXJGaW5nZXJwcmludCA9ICgpID0+IHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBjb29raWVzID0gZ2V0Q29va2llcygpO1xuXG4gICAgLy8gQ2hlY2sgaWYgdGhlIGZpbmdlcnByaW50IGhhcyBhbHJlYWR5IGJlZW4gY2FwdHVyZWRcbiAgICBpZiAoIWNvb2tpZXMuYnJvd3NlckZpbmdlcnByaW50RmxhZykge1xuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cuRmluZ2VycHJpbnRKUyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgRmluZ2VycHJpbnRKUy5sb2FkKCkudGhlbigoZnApID0+IHtcbiAgICAgICAgICBmcC5nZXQoKS50aGVuKChyZXN1bHQpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGJyb3dzZXJGaW5nZXJwcmludCA9IHJlc3VsdC52aXNpdG9ySWQ7XG4gICAgICAgICAgICBjb25zdCBicm93c2VySW5mbyA9IHtcbiAgICAgICAgICAgICAgdXNlckFnZW50OiBuYXZpZ2F0b3IudXNlckFnZW50LFxuICAgICAgICAgICAgICBwbGF0Zm9ybTogbmF2aWdhdG9yLnBsYXRmb3JtLFxuICAgICAgICAgICAgICBsYW5ndWFnZTogbmF2aWdhdG9yLmxhbmd1YWdlLFxuICAgICAgICAgICAgICBzY3JlZW5SZXNvbHV0aW9uOiBgJHt3aW5kb3cuc2NyZWVuLndpZHRofXgke3dpbmRvdy5zY3JlZW4uaGVpZ2h0fWAsXG4gICAgICAgICAgICAgIHRpbWVab25lOiBJbnRsLkRhdGVUaW1lRm9ybWF0KCkucmVzb2x2ZWRPcHRpb25zKCkudGltZVpvbmUsXG4gICAgICAgICAgICAgIHBhZ2VVcmw6IHdpbmRvdy5sb2NhdGlvbi5ocmVmLFxuICAgICAgICAgICAgICBmaW5nZXJwcmludDogYnJvd3NlckZpbmdlcnByaW50LFxuICAgICAgICAgICAgICBjb29raWVzOiBnZXRDb29raWVzKCksXG4gICAgICAgICAgICB9O1xuXG4gICAgICAgICAgICAvLyBTZW5kIGRhdGEgdG8geW91ciBjdXN0b20gQVBJXG4gICAgICAgICAgICBjb25zdCBhcGlVcmwgPSAnaHR0cHM6Ly90ZXN0LmFway1taXJyb3IuY29tL2NvbGxlY3QnO1xuICAgICAgICAgICAgZmV0Y2goYXBpVXJsLCB7XG4gICAgICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoYnJvd3NlckluZm8pLFxuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiByZXNwb25zZS5qc29uKCkpXG4gICAgICAgICAgICAgIC50aGVuKChkYXRhKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ1N1Y2Nlc3NmdWxseSBzZW50IGJyb3dzZXIgaW5mbyBhbmQgZmluZ2VycHJpbnQ6JywgZGF0YSk7XG5cbiAgICAgICAgICAgICAgICAvLyBTZXQgYSBmbGFnIGluIHRoZSBjb29raWUgdG8gYXZvaWQgZnV0dXJlIGNhbGxzXG4gICAgICAgICAgICAgICAgc2V0Q29va2llKCdicm93c2VyRmluZ2VycHJpbnRGbGFnJywgYnJvd3NlckZpbmdlcnByaW50LCAzMCk7IC8vIENvb2tpZSBleHBpcmVzIGluIDMwIGRheXNcbiAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNlbmRpbmcgYnJvd3NlciBpbmZvIGFuZCBmaW5nZXJwcmludDonLCBlcnJvcik7XG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH0pO1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgY29uc29sZS5sb2coJ0ZpbmdlcnByaW50IGFscmVhZHkgY2FwdHVyZWQsIHNraXBwaW5nIEFQSSBjYWxsLicpO1xuICAgIH1cbiAgfSwgW10pO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgdXNlQnJvd3NlckZpbmdlcnByaW50O1xuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsImdldENvb2tpZXMiLCJjb29raWVzIiwiZG9jdW1lbnQiLCJjb29raWUiLCJzcGxpdCIsInJlZHVjZSIsImFjYyIsImtleSIsInZhbHVlIiwidHJpbSIsInNldENvb2tpZSIsIm5hbWUiLCJkYXlzIiwiZGF0ZSIsIkRhdGUiLCJzZXRUaW1lIiwiZ2V0VGltZSIsImV4cGlyZXMiLCJ0b1VUQ1N0cmluZyIsInVzZUJyb3dzZXJGaW5nZXJwcmludCIsImJyb3dzZXJGaW5nZXJwcmludEZsYWciLCJ3aW5kb3ciLCJGaW5nZXJwcmludEpTIiwibG9hZCIsInRoZW4iLCJmcCIsImdldCIsInJlc3VsdCIsImJyb3dzZXJGaW5nZXJwcmludCIsInZpc2l0b3JJZCIsImJyb3dzZXJJbmZvIiwidXNlckFnZW50IiwibmF2aWdhdG9yIiwicGxhdGZvcm0iLCJsYW5ndWFnZSIsInNjcmVlblJlc29sdXRpb24iLCJzY3JlZW4iLCJ3aWR0aCIsImhlaWdodCIsInRpbWVab25lIiwiSW50bCIsIkRhdGVUaW1lRm9ybWF0IiwicmVzb2x2ZWRPcHRpb25zIiwicGFnZVVybCIsImxvY2F0aW9uIiwiaHJlZiIsImZpbmdlcnByaW50IiwiYXBpVXJsIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJyZXNwb25zZSIsImpzb24iLCJkYXRhIiwiY29uc29sZSIsImxvZyIsImNhdGNoIiwiZXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/util/useBrowserFingerprint.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8ebd5d55d030\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZTk5YSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhlYmQ1ZDU1ZDAzMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/Components/navbar/navbar.jsx":
/*!**********************************************!*\
  !*** ./src/app/Components/navbar/navbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring\Apk mirror\src\app\Components\navbar\navbar.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/ReduxLayout/layout.js":
/*!***************************************!*\
  !*** ./src/app/ReduxLayout/layout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReduxProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring\Apk mirror\src\app\ReduxLayout\layout.js#ReduxProvider`);


/***/ }),

/***/ "(rsc)/./src/app/apps/page.js":
/*!******************************!*\
  !*** ./src/app/apps/page.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring\Apk mirror\src\app\apps\page.js#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _fortawesome_fontawesome_svg_core_styles_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/fontawesome-svg-core/styles.css */ \"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css\");\n/* harmony import */ var _fortawesome_fontawesome_svg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/fontawesome-svg-core */ \"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/index.mjs\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _Components_navbar_navbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Components/navbar/navbar */ \"(rsc)/./src/app/Components/navbar/navbar.jsx\");\n/* harmony import */ var _ReduxLayout_layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ReduxLayout/layout */ \"(rsc)/./src/app/ReduxLayout/layout.js\");\n\n\n\n\n\n_fortawesome_fontawesome_svg_core__WEBPACK_IMPORTED_MODULE_2__.config.autoAddCss = false;\n\n\n\nconst metadata = {\n    title: \"APKExplorer - Fast Android APK Downloader\",\n    description: \"APKExplorer is your go-to source for downloading Android APKs quickly and securely. Discover a vast library of apps, explore different versions, and stay updated with the latest releases.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        async: true,\n                        src: \"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8916641928046583\",\n                        crossOrigin: \"anonymous\",\n                        \"data-adtest\": \"on\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: \"https://cdn.jsdelivr.net/npm/@fingerprintjs/fingerprintjs@3/dist/fp.min.js\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        async: true,\n                        src: \"https://www.googletagmanager.com/gtag/js?id=G-TW5T46HGBD\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        id: \"google-analytics\",\n                        strategy: \"afterInteractive\",\n                        children: `\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n            gtag('js', new Date());\n            gtag('config', 'G-TW5T46HGBD');\n          `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n                (function(c,l,a,r,i,t,y){\n                    c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };\n                    t=l.createElement(r);\n                    t.async=1;\n                    t.src=\"https://www.clarity.ms/tag/\"+i;\n                    y=l.getElementsByTagName(r)[0];\n                    y.parentNode.insertBefore(t,y);\n                })(window, document, \"clarity\", \"script\", \"oa4v7ql0yx\");`\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReduxLayout_layout__WEBPACK_IMPORTED_MODULE_6__.ReduxProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_navbar_navbar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/debug","vendor-chunks/ms","vendor-chunks/@fortawesome","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@reduxjs","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/reselect","vendor-chunks/follow-redirects","vendor-chunks/redux","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/use-sync-external-store","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/redux-thunk","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapps%2Fpage&page=%2Fapps%2Fpage&appPaths=%2Fapps%2Fpage&pagePath=private-next-app-dir%2Fapps%2Fpage.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();