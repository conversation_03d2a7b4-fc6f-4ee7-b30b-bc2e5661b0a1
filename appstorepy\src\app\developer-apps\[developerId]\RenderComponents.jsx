"use client";
import { BeatLoader } from "react-spinners";
import { connect } from "react-redux";
import { Container } from "@mui/material";
import { Box } from "@mui/material";
import AppCard from "@/app/components/tableData/appTableView";
import DeveloperDetailsUI from './serverPage';

const RenderApps = (props) => {
  const {
    developerInfo,
    searchApps,
    searchResultsAvailable,
    searchAppsLoading,
  } = props;
  return (
    <>
    {searchAppsLoading ? (
      <Box
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "65vh"
        }}
      >
        <BeatLoader color={"#00A3FF"} loading={searchAppsLoading} size={25} />
      </Box>
    ) : (
      <>
        {searchResultsAvailable ? (
          <Container sx={{ marginTop: "6rem", marginBottom: "2rem" }} maxWidth="1150px">
             <AppCard apps={searchApps} />
        </Container>
        ) : (
          <DeveloperDetailsUI developerInfo={developerInfo} />
        )}
      </>
    )}
  </>
  );
};

const mapStateToProps = ({ topApps }) => ({
  searchApps: topApps?.searchApps,
  searchResultsAvailable: topApps?.searchResultsAvailable,
  searchAppsLoading: topApps?.searchAppsLoading,
});

export default connect(mapStateToProps)(RenderApps);
