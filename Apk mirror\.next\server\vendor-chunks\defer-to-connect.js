"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/defer-to-connect";
exports.ids = ["vendor-chunks/defer-to-connect"];
exports.modules = {

/***/ "(rsc)/./node_modules/defer-to-connect/dist/source/index.js":
/*!************************************************************!*\
  !*** ./node_modules/defer-to-connect/dist/source/index.js ***!
  \************************************************************/
/***/ ((module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nfunction isTLSSocket(socket) {\n    return socket.encrypted;\n}\nconst deferToConnect = (socket, fn) => {\n    let listeners;\n    if (typeof fn === 'function') {\n        const connect = fn;\n        listeners = { connect };\n    }\n    else {\n        listeners = fn;\n    }\n    const hasConnectListener = typeof listeners.connect === 'function';\n    const hasSecureConnectListener = typeof listeners.secureConnect === 'function';\n    const hasCloseListener = typeof listeners.close === 'function';\n    const onConnect = () => {\n        if (hasConnectListener) {\n            listeners.connect();\n        }\n        if (isTLSSocket(socket) && hasSecureConnectListener) {\n            if (socket.authorized) {\n                listeners.secureConnect();\n            }\n            else if (!socket.authorizationError) {\n                socket.once('secureConnect', listeners.secureConnect);\n            }\n        }\n        if (hasCloseListener) {\n            socket.once('close', listeners.close);\n        }\n    };\n    if (socket.writable && !socket.connecting) {\n        onConnect();\n    }\n    else if (socket.connecting) {\n        socket.once('connect', onConnect);\n    }\n    else if (socket.destroyed && hasCloseListener) {\n        listeners.close(socket._hadError);\n    }\n};\nexports[\"default\"] = deferToConnect;\n// For CommonJS default export support\nmodule.exports = deferToConnect;\nmodule.exports[\"default\"] = deferToConnect;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/defer-to-connect/dist/source/index.js\n");

/***/ })

};
;