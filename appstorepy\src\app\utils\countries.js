
//  export  const countries = [
//     { code: "AD", label: "Andorra" },
//     { code: "AE", label: "United Arab Emirates" },
//     { code: "AF", label: "Afghanistan" },
//     { code: "AG", label: "Antigua and Barbuda" },
//     { code: "AI", label: "Anguilla" },
//     { code: "AL", label: "Albania" },
//     { code: "AM", label: "Armenia" },
//     { code: "AO", label: "Angola" },
//     { code: "AQ", label: "Antarctica" },
//     { code: "AR", label: "Argentina" },
//     { code: "AS", label: "American Samoa" },
//     { code: "AT", label: "Austria" },
//     { code: "AU", label: "Australia", suggested: true },
//     { code: "AW", label: "Aruba" },
//     { code: "AX", label: "Alland Islands" },
//     { code: "AZ", label: "Azerbaijan" },
//     { code: "BA", label: "Bosnia and Herzegovina" },
//     { code: "BB", label: "Barbados" },
//     { code: "BD", label: "Bangladesh" },
//     { code: "BE", label: "Belgium" },
//     { code: "BF", label: "Burkina Faso" },
//     { code: "BG", label: "Bulgaria" },
//     { code: "BH", label: "Bahrain" },
//     { code: "BI", label: "Burundi" },
//     { code: "BJ", label: "Benin" },
//     { code: "BL", label: "Saint Barthelemy" },
//     { code: "BM", label: "Bermuda" },
//     { code: "BN", label: "Brunei Darussalam" },
//     { code: "BO", label: "Bolivia" },
//     { code: "BR", label: "Brazil" },
//     { code: "BS", label: "Bahamas" },
//     { code: "BT", label: "Bhutan" },
//     { code: "BV", label: "Bouvet Island" },
//     { code: "BW", label: "Botswana" },
//     { code: "BY", label: "Belarus" },
//     { code: "BZ", label: "Belize" },
//     { code: "CA", label: "Canada", suggested: true },
//     { code: "CC", label: "Cocos (Keeling) Islands" },
//     { code: "CD", label: "Congo, Democratic Republic of the" },
//     { code: "CF", label: "Central African Republic" },
//     { code: "CG", label: "Congo, Republic of the" },
//     { code: "CH", label: "Switzerland" },
//     { code: "CI", label: "Cote d'Ivoire" },
//     { code: "CK", label: "Cook Islands" },
//     { code: "CL", label: "Chile" },
//     { code: "CM", label: "Cameroon" },
//     { code: "CN", label: "China" },
//     { code: "CO", label: "Colombia" },
//     { code: "CR", label: "Costa Rica" },
//     { code: "CU", label: "Cuba" },
//     { code: "CV", label: "Cape Verde" },
//     { code: "CW", label: "Curacao" },
//     { code: "CX", label: "Christmas Island" },
//     { code: "CY", label: "Cyprus" },
//     { code: "CZ", label: "Czech Republic" },
//     { code: "DE", label: "Germany", suggested: true },
//     { code: "DJ", label: "Djibouti" },
//     { code: "DK", label: "Denmark" },
//     { code: "DM", label: "Dominica" },
//     { code: "DO", label: "Dominican Republic" },
//     { code: "DZ", label: "Algeria" },
//     { code: "EC", label: "Ecuador" },
//     { code: "EE", label: "Estonia" },
//     { code: "EG", label: "Egypt" },
//     { code: "EH", label: "Western Sahara" },
//     { code: "ER", label: "Eritrea" },
//     { code: "ES", label: "Spain" },
//     { code: "ET", label: "Ethiopia" },
//     { code: "FI", label: "Finland" },
//     { code: "FJ", label: "Fiji" },
//     { code: "FK", label: "Falkland Islands (Malvinas)" },
//     { code: "FM", label: "Micronesia, Federated States of" },
//     { code: "FO", label: "Faroe Islands" },
//     { code: "FR", label: "France", suggested: true },
//     { code: "GA", label: "Gabon" },
//     { code: "GB", label: "United Kingdom" },
//     { code: "GD", label: "Grenada" },
//     { code: "GE", label: "Georgia" },
//     { code: "GF", label: "French Guiana" },
//     { code: "GG", label: "Guernsey" },
//     { code: "GH", label: "Ghana" },
//     { code: "GI", label: "Gibraltar" },
//     { code: "GL", label: "Greenland" },
//     { code: "GM", label: "Gambia" },
//     { code: "GN", label: "Guinea" },
//     { code: "GP", label: "Guadeloupe" },
//     { code: "GQ", label: "Equatorial Guinea" },
//     { code: "GR", label: "Greece" },
//     { code: "GS", label: "South Georgia and the South Sandwich Islands" },
//     { code: "GT", label: "Guatemala" },
//     { code: "GU", label: "Guam" },
//     { code: "GW", label: "Guinea-Bissau" },
//     { code: "GY", label: "Guyana" },
//     { code: "HK", label: "Hong Kong" },
//     { code: "HM", label: "Heard Island and McDonald Islands" },
//     { code: "HN", label: "Honduras" },
//     { code: "HR", label: "Croatia" },
//     { code: "HT", label: "Haiti" },
//     { code: "HU", label: "Hungary" },
//     { code: "ID", label: "Indonesia" },
//     { code: "IE", label: "Ireland" },
//     { code: "IL", label: "Israel" },
//     { code: "IM", label: "Isle of Man" },
//     { code: "IN", label: "India" },
//     { code: "IO", label: "British Indian Ocean Territory" },
//     { code: "IQ", label: "Iraq" },
//     { code: "IR", label: "Iran, Islamic Republic of" },
//     { code: "IS", label: "Iceland" },
//     { code: "IT", label: "Italy" },
//     { code: "JE", label: "Jersey" },
//     { code: "JM", label: "Jamaica" },
//     { code: "JO", label: "Jordan" },
//     { code: "JP", label: "Japan", suggested: true },
//     { code: "KE", label: "Kenya" },
//     { code: "KG", label: "Kyrgyzstan" },
//     { code: "KH", label: "Cambodia" },
//     { code: "KI", label: "Kiribati" },
//     { code: "KM", label: "Comoros" },
//     { code: "KN", label: "Saint Kitts and Nevis" },
//     { code: "KP", label: "Korea, Democratic People's Republic of" },
//     { code: "KR", label: "Korea, Republic of" },
//     { code: "KW", label: "Kuwait" },
//     { code: "KY", label: "Cayman Islands" },
//     { code: "KZ", label: "Kazakhstan" },
//     { code: "LA", label: "Lao People's Democratic Republic" },
//     { code: "LB", label: "Lebanon" },
//     { code: "LC", label: "Saint Lucia" },
//     { code: "LI", label: "Liechtenstein" },
//     { code: "LK", label: "Sri Lanka" },
//     { code: "LR", label: "Liberia" },
//     { code: "LS", label: "Lesotho" },
//     { code: "LT", label: "Lithuania" },
//     { code: "LU", label: "Luxembourg" },
//     { code: "LV", label: "Latvia" },
//     { code: "LY", label: "Libya" },
//     { code: "MA", label: "Morocco" },
//     { code: "MC", label: "Monaco" },
//     { code: "MD", label: "Moldova, Republic of" },
//     { code: "ME", label: "Montenegro" },
//     { code: "MF", label: "Saint Martin (French part)" },
//     { code: "MG", label: "Madagascar" },
//     { code: "MH", label: "Marshall Islands" },
//     { code: "MK", label: "Macedonia, the Former Yugoslav Republic of" },
//     { code: "ML", label: "Mali" },
//     { code: "MM", label: "Myanmar" },
//     { code: "MN", label: "Mongolia" },
//     { code: "MO", label: "Macao" },
//     { code: "MP", label: "Northern Mariana Islands" },
//     { code: "MQ", label: "Martinique" },
//     { code: "MR", label: "Mauritania" },
//     { code: "MS", label: "Montserrat" },
//     { code: "MT", label: "Malta" },
//     { code: "MU", label: "Mauritius" },
//     { code: "MV", label: "Maldives" },
//     { code: "MW", label: "Malawi" },
//     { code: "MX", label: "Mexico" },
//     { code: "MY", label: "Malaysia" },
//     { code: "MZ", label: "Mozambique" },
//     { code: "NA", label: "Namibia" },
//     { code: "NC", label: "New Caledonia" },
//     { code: "NE", label: "Niger" },
//     { code: "NF", label: "Norfolk Island" },
//     { code: "NG", label: "Nigeria" },
//     { code: "NI", label: "Nicaragua" },
//     { code: "NL", label: "Netherlands" },
//     { code: "NO", label: "Norway" },
//     { code: "NP", label: "Nepal" },
//     { code: "NR", label: "Nauru" },
//     { code: "NU", label: "Niue" },
//     { code: "NZ", label: "New Zealand" },
//     { code: "OM", label: "Oman" },
//     { code: "PA", label: "Panama" },
//     { code: "PE", label: "Peru" },
//     { code: "PF", label: "French Polynesia" },
//     { code: "PG", label: "Papua New Guinea" },
//     { code: "PH", label: "Philippines" },
//     { code: "PK", label: "Pakistan" },
//     { code: "PL", label: "Poland" },
//     { code: "PM", label: "Saint Pierre and Miquelon" },
//     { code: "PN", label: "Pitcairn" },
//     { code: "PR", label: "Puerto Rico" },
//     { code: "PS", label: "Palestine, State of" },
//     { code: "PT", label: "Portugal" },
//     { code: "PW", label: "Palau" },
//     { code: "PY", label: "Paraguay" },
//     { code: "QA", label: "Qatar" },
//     { code: "RE", label: "Reunion" },
//     { code: "RO", label: "Romania" },
//     { code: "RS", label: "Serbia" },
//     { code: "RU", label: "Russian Federation" },
//     { code: "RW", label: "Rwanda" },
//     { code: "SA", label: "Saudi Arabia" },
//     { code: "SB", label: "Solomon Islands" },
//     { code: "SC", label: "Seychelles" },
//     { code: "SD", label: "Sudan" },
//     { code: "SE", label: "Sweden" },
//     { code: "SG", label: "Singapore" },
//     { code: "SH", label: "Saint Helena" },
//     { code: "SI", label: "Slovenia" },
//     { code: "SJ", label: "Svalbard and Jan Mayen" },
//     {
//       code: "ST",
//       label: "Sao Tome and Principe",
//     },
//     { code: "SK", label: "Slovakia" },
//     { code: "SL", label: "Sierra Leone" },
//     { code: "SM", label: "San Marino" },
//     { code: "SN", label: "Senegal" },
//     { code: "SO", label: "Somalia" },
//     { code: "SR", label: "Suriname" },
//     { code: "SS", label: "South Sudan" },
//     { code: "SV", label: "El Salvador" },
//     { code: "SX", label: "Sint Maarten (Dutch part)" },
//     { code: "SY", label: "Syrian Arab Republic" },
//     { code: "SZ", label: "Swaziland" },
//     { code: "TC", label: "Turks and Caicos Islands" },
//     { code: "TD", label: "Chad" },
//     { code: "TF", label: "French Southern Territories" },
//     { code: "TG", label: "Togo" },
//     { code: "TH", label: "Thailand" },
//     { code: "TJ", label: "Tajikistan" },
//     { code: "TK", label: "Tokelau" },
//     { code: "TL", label: "Timor-Leste" },
//     { code: "TM", label: "Turkmenistan" },
//     { code: "TN", label: "Tunisia" },
//     { code: "TO", label: "Tonga" },
//     { code: "TR", label: "Turkey" },
//     { code: "TT", label: "Trinidad and Tobago" },
//     { code: "TV", label: "Tuvalu" },
//     { code: "TW", label: "Taiwan" },
//     { code: "TZ", label: "United Republic of Tanzania" },
//     { code: "UA", label: "Ukraine" },
//     { code: "UG", label: "Uganda" },
//     { code: "US", label: "United States", suggested: true },
//     { code: "UY", label: "Uruguay" },
//     { code: "UZ", label: "Uzbekistan" },
//     { code: "VA", label: "Holy See (Vatican City State)" },
//     { code: "VC", label: "Saint Vincent and the Grenadines" },
//     { code: "VE", label: "Venezuela" },
//     { code: "VG", label: "British Virgin Islands" },
//     { code: "VI", label: "US Virgin Islands" },
//     { code: "VN", label: "Vietnam" },
//     { code: "VU", label: "Vanuatu" },
//     { code: "WF", label: "Wallis and Futuna" },
//     { code: "WS", label: "Samoa" },
//     { code: "XK", label: "Kosovo" },
//     { code: "YE", label: "Yemen" },
//     { code: "YT", label: "Mayotte" },
//     { code: "ZA", label: "South Africa" },
//     { code: "ZM", label: "Zambia" },
//     { code: "ZW", label: "Zimbabwe" },
//   ];
  
  export const countries = [
    { code: "IN", label: "India" },
    { code: "US", label: "United States" },
    { code: "AE", label: "United Arab Emirates" },
    { code: "AU", label: "Australia" },
    { code: "AR", label: "Argentina" },
    { code: "MX", label: "Mexico" },
    { code: "NL", label: "Netherlands" },
    { code: "AT", label: "Austria" },
    { code: "BE", label: "Belgium" },
    { code: "NO", label: "Norway" },
    { code: "NG", label: "Nigeria" },
    { code: "BR", label: "Brazil" },
    { code: "KW", label: "Kuwait" },
    { code: "PL", label: "Poland" },
    { code: "CA", label: "Canada" },
    { code: "PT", label: "Portugal" },
    { code: "CZ", label: "Czech Republic" },
    { code: "RU", label: "Russian Federation" },
    { code: "DK", label: "Denmark" },
    { code: "SA", label: "Saudi Arabia" },
    { code: "OM", label: "Oman" },
    { code: "SK", label: "Slovakia" },
    { code: "FI", label: "Finland" },
    { code: "KR", label: "Korea" },
    { code: "KZ", label: "Kazakhstan" },
    { code: "GH", label: "Ghana" },
    { code: "ES", label: "Spain" },
    { code: "DE", label: "Germany" },
    { code: "FR", label: "France" },
    { code: "RO", label: "Romania" },
    { code: "SE", label: "Sweden" },
    { code: "PE", label: "Peru" },
    { code: "ID", label: "Indonesia" },
    { code: "CH", label: "Switzerland" },
    { code: "ZA", label: "South Africa" },
    { code: "IR", label: "Iran" },
    { code: "TR", label: "Turkey" },
    { code: "IT", label: "Italy" },
    { code: "GB", label: "United Kingdom" },
    { code: "JP", label: "Japan" },
  ]


  
  export const timezoneToCountryName = {
  "Asia/Kolkata": "India",
  "Asia/Calcutta": "India",
  "America/New_York": "United States",
  "America/Detroit": "United States",
  "America/Kentucky/Louisville": "United States",
  "America/Kentucky/Monticello": "United States",
  "America/Indiana/Indianapolis": "United States",
  "America/Indiana/Vincennes": "United States",
  "America/Indiana/Winamac": "United States",
  "America/Indiana/Marengo": "United States",
  "America/Indiana/Petersburg": "United States",
  "America/Indiana/Vevay": "United States",
  "America/Chicago": "United States",
  "America/Indiana/Tell_City": "United States",
  "America/Indiana/Knox": "United States",
  "America/Menominee": "United States",
  "America/North_Dakota/Center": "United States",
  "America/North_Dakota/New_Salem": "United States",
  "America/North_Dakota/Beulah": "United States",
  "America/Denver": "United States",
  "America/Boise": "United States",
  "America/Phoenix": "United States",
  "America/Los_Angeles": "United States",
  "America/Anchorage": "United States",
  "America/Juneau": "United States",
  "America/Sitka": "United States",
  "America/Metlakatla": "United States",
  "America/Yakutat": "United States",
  "America/Nome": "United States",
  "America/Adak": "United States",
  "Pacific/Honolulu": "United States",
  "Australia/Lord_Howe": "Australia",
  "Antarctica/Macquarie": "Australia",
  "Australia/Hobart": "Australia",
  "Australia/Melbourne": "Australia",
  "Australia/Sydney": "Australia",
  "Australia/Broken_Hill": "Australia",
  "Australia/Brisbane": "Australia",
  "Australia/Lindeman": "Australia",
  "Australia/Adelaide": "Australia",
  "Australia/Darwin": "Australia",
  "Australia/Perth": "Australia",
  "Australia/Eucla": "Australia",
  "America/Mexico_City": "Mexico",
  "America/Cancun": "Mexico",
  "America/Merida": "Mexico",
  "America/Monterrey": "Mexico",
  "America/Matamoros": "Mexico",
  "America/Mazatlan": "Mexico",
  "America/Chihuahua": "Mexico",
  "America/Ojinaga": "Mexico",
  "America/Hermosillo": "Mexico",
  "America/Tijuana": "Mexico",
  "America/Bahia_Banderas": "Mexico",
  "Europe/Amsterdam": "Netherlands",
  "Europe/Vienna": "Austria",
  "Europe/Brussels": "Belgium",
  "Europe/Oslo": "Norway",
  "America/Noronha": "Brazil",
  "America/Belem": "Brazil",
  "America/Fortaleza": "Brazil",
  "America/Recife": "Brazil",
  "America/Araguaina": "Brazil",
  "America/Maceio": "Brazil",
  "America/Bahia": "Brazil",
  "America/Sao_Paulo": "Brazil",
  "America/Campo_Grande": "Brazil",
  "America/Cuiaba": "Brazil",
  "America/Santarem": "Brazil",
  "America/Porto_Velho": "Brazil",
  "America/Boa_Vista": "Brazil",
  "America/Manaus": "Brazil",
  "America/Eirunepe": "Brazil",
  "America/Rio_Branco": "Brazil",
  "Europe/Warsaw": "Poland",
  "America/St_Johns": "Canada",
  "America/Halifax": "Canada",
  "America/Glace_Bay": "Canada",
  "America/Moncton": "Canada",
  "America/Goose_Bay": "Canada",
  "America/Toronto": "Canada",
  "America/Nipigon": "Canada",
  "America/Thunder_Bay": "Canada",
  "America/Iqaluit": "Canada",
  "America/Pangnirtung": "Canada",
  "America/Winnipeg": "Canada",
  "America/Rainy_River": "Canada",
  "America/Resolute": "Canada",
  "America/Rankin_Inlet": "Canada",
  "America/Regina": "Canada",
  "America/Swift_Current": "Canada",
  "America/Edmonton": "Canada",
  "America/Cambridge_Bay": "Canada",
  "America/Yellowknife": "Canada",
  "America/Inuvik": "Canada",
  "America/Dawson_Creek": "Canada",
  "America/Fort_Nelson": "Canada",
  "America/Whitehorse": "Canada",
  "America/Dawson": "Canada",
  "America/Vancouver": "Canada",
  "America/Blanc-Sablon": "Canada",
  "America/Atikokan": "Canada",
  "America/Creston": "Canada",
  "Europe/Lisbon": "Portugal",
  "Atlantic/Madeira": "Portugal",
  "Atlantic/Azores": "Portugal",
  "Europe/Prague": "Czech Republic",
  "Europe/Kaliningrad": "Russian Federation",
  "Europe/Moscow": "Russian Federation",
  "Europe/Simferopol": "Russian Federation",
  "Europe/Kirov": "Russian Federation",
  "Europe/Volgograd": "Russian Federation",
  "Europe/Astrakhan": "Russian Federation",
  "Europe/Saratov": "Russian Federation",
  "Europe/Ulyanovsk": "Russian Federation",
  "Europe/Samara": "Russian Federation",
  "Asia/Yekaterinburg": "Russian Federation",
  "Asia/Omsk": "Russian Federation",
  "Asia/Novosibirsk": "Russian Federation",
  "Asia/Barnaul": "Russian Federation",
  "Asia/Tomsk": "Russian Federation",
  "Asia/Novokuznetsk": "Russian Federation",
  "Asia/Krasnoyarsk": "Russian Federation",
  "Asia/Irkutsk": "Russian Federation",
  "Asia/Chita": "Russian Federation",
  "Asia/Yakutsk": "Russian Federation",
  "Asia/Khandyga": "Russian Federation",
  "Asia/Vladivostok": "Russian Federation",
  "Asia/Ust-Nera": "Russian Federation",
  "Asia/Magadan": "Russian Federation",
  "Asia/Sakhalin": "Russian Federation",
  "Asia/Srednekolymsk": "Russian Federation",
  "Asia/Kamchatka": "Russian Federation",
  "Asia/Anadyr": "Russian Federation",
  "Europe/Copenhagen": "Denmark",
  "Asia/Riyadh": "Saudi Arabia",
  "Europe/Bratislava": "Slovakia",
  "Europe/Helsinki": "Finland",
  "Asia/Pyongyang": "Korea",
  "Asia/Seoul": "Korea",
  "Europe/Madrid": "Spain",
  "Africa/Ceuta": "Spain",
  "Atlantic/Canary": "Spain",
  "Europe/Berlin": "Germany",
  "Europe/Busingen": "Germany",
  "Europe/Paris": "France",
  "Europe/Stockholm": "Sweden",
  "Asia/Jakarta": "Indonesia",
  "Asia/Pontianak": "Indonesia",
  "Asia/Makassar": "Indonesia",
  "Asia/Jayapura": "Indonesia",
  "Europe/Zurich": "Switzerland",
  "Asia/Tehran": "Iran",
  "Europe/Istanbul": "Turkey",
  "Europe/Rome": "Italy",
  "Europe/London": "United Kingdom",
  "America/Tortola": "United Kingdom",
  "Asia/Tokyo": "Japan",
  }