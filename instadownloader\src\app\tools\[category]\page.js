'use client'
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { categories } from "@/app/util/constants";
import LoadingComponent from "@/app/Loading";
import SideBar from "@/app/components/Sidebar";
import Card from "@/app/components/reuseable/Card";
import { getToolByCategory } from "@/app/util/dataFetch";

const Category = ({ params: { category } }) => {
    const getNameFromCategory = (category) => {
        const foundCategory = categories.find(item => item.category === category);
        return foundCategory ? foundCategory.name : null;
    };
    const categoryName = getNameFromCategory(category);
    const [categoryApps, setCategoryApps] = useState([]);
    const [similarApps, setSimilarApps] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [toolsPerPage] = useState(10);
    const [totalPages, setTotalPages] = useState(0);


    useEffect(() => {
        const fetchAppDetails = async () => {
            try {
                const getTools = await getToolByCategory(categoryName);
                setCategoryApps(getTools.tools);
                setSimilarApps(getTools.similarTools);
                setTotalPages(Math.ceil(getTools.tools.length / toolsPerPage));
            } catch (error) {
                console.error("Error fetching category details:", error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchAppDetails();
    }, [category]);
    const handlePageChange = (page) => {
        if (page < 1 || page > totalPages) return;
        setCurrentPage(page);
    };
    const indexOfLastTool = currentPage * toolsPerPage;
    const indexOfFirstTool = indexOfLastTool - toolsPerPage;
    const currentTools = categoryApps.slice(indexOfFirstTool, indexOfLastTool);

    return (
        <div className="lg:container flex flex-col items-center justify-between mt-0.5 mx-5 sm:mx-0 md:mx-20 lg:mx-auto">
            <div className="container mx-auto max-w-screen-xl">
                <div className="w-full justify-center flex flex-wrap">
                    <main className="w-full xl:w-4/6 mt-4 relative px-0 xl:px-2">
                        <div className="mb-3.5 p-3 bg-white rounded-md shadow-md">
                            <p className="text-[10px] sm:text-sm">
                                <Link href={"/"} prefetch={false}>Home</Link>&nbsp;/&nbsp;
                                <Link href={"/tool"} prefetch={false}> AI Tools </Link>&nbsp;/&nbsp;
                                <span className="text-slate-500">{categoryName}</span>
                            </p>

                        </div>
                        <div className="mb-3.5 p-5 bg-white rounded-lg shadow-md flex flex-col">
                            <h2 className="mb-2.5 text-base font-normal text-slate-500 uppercase tracking-wider">
                                AI {categoryName}
                            </h2>
                            {isLoading ? <LoadingComponent length={6} md={1} lg={1} /> : <div>
                                <Card tools={currentTools} grid={1} errorMsg="No tools for this Category"/>
                                <div className="flex justify-between items-center mt-4">
                                    <button
                                        className="px-4 py-2 text-white bg-slate-900 rounded-md hover:bg-slate-700 disabled:opacity-50"
                                        onClick={() => handlePageChange(currentPage - 1)}
                                        disabled={currentPage === 1}
                                    >
                                        Previous
                                    </button>
                                    <span className="text-lg">
                                        Page {currentPage} of {totalPages}
                                    </span>
                                    <button
                                        className="px-4 py-2 text-white bg-slate-900 rounded-md hover:bg-slate-700 disabled:opacity-50"
                                        onClick={() => handlePageChange(currentPage + 1)}
                                        disabled={currentPage === totalPages}
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>}

                        </div>
                    </main>
                    <aside className=" sidebar-container w-full xl:w-2/6 xl:px-2">
                        <SideBar sideToolsDetails={similarApps} isLoading={isLoading} header="SIMILAR TOOLS" />
                    </aside>
                </div>
            </div>
        </div>
    );
};

export default Category;

