"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/material-ui-popup-state";
exports.ids = ["vendor-chunks/material-ui-popup-state"];
exports.modules = {

/***/ "(ssr)/./node_modules/material-ui-popup-state/hooks.mjs":
/*!********************************************************!*\
  !*** ./node_modules/material-ui-popup-state/hooks.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   anchorRef: () => (/* binding */ anchorRef),\n/* harmony export */   bindContextMenu: () => (/* binding */ bindContextMenu),\n/* harmony export */   bindDialog: () => (/* binding */ bindDialog),\n/* harmony export */   bindDoubleClick: () => (/* binding */ bindDoubleClick),\n/* harmony export */   bindFocus: () => (/* binding */ bindFocus),\n/* harmony export */   bindHover: () => (/* binding */ bindHover),\n/* harmony export */   bindMenu: () => (/* binding */ bindMenu),\n/* harmony export */   bindPopover: () => (/* binding */ bindPopover),\n/* harmony export */   bindPopper: () => (/* binding */ bindPopper),\n/* harmony export */   bindToggle: () => (/* binding */ bindToggle),\n/* harmony export */   bindTrigger: () => (/* binding */ bindTrigger),\n/* harmony export */   initCoreState: () => (/* binding */ initCoreState),\n/* harmony export */   usePopupState: () => (/* binding */ usePopupState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useEvent_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useEvent.mjs */ \"(ssr)/./node_modules/material-ui-popup-state/useEvent.mjs\");\n/* eslint-env browser */ \n\n\nconst printedWarnings = {};\nfunction warn(key, message) {\n    if (printedWarnings[key]) return;\n    printedWarnings[key] = true;\n    // eslint-disable-next-line no-console\n    console.error(\"[material-ui-popup-state] WARNING\", message);\n}\nconst initCoreState = {\n    isOpen: false,\n    setAnchorElUsed: false,\n    anchorEl: undefined,\n    anchorPosition: undefined,\n    hovered: false,\n    focused: false,\n    _openEventType: null,\n    _childPopupState: null,\n    _deferNextOpen: false,\n    _deferNextClose: false\n};\n// https://github.com/jcoreio/material-ui-popup-state/issues/138\n// Webpack prod build doesn't like it if we refer to React.useId conditionally,\n// but aliasing to a variable like this works\nconst _react = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)));\nconst defaultPopupId = \"useId\" in _react ? ()=>_react.useId() : ()=>undefined;\nfunction usePopupState({ parentPopupState, popupId = defaultPopupId(), variant, disableAutoFocus }) {\n    const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        isMounted.current = true;\n        return ()=>{\n            isMounted.current = false;\n        };\n    }, []);\n    const [state, _setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initCoreState);\n    const setState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((state)=>{\n        if (isMounted.current) _setState(state);\n    }, []);\n    const setAnchorEl = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((anchorEl)=>setState((state)=>({\n                ...state,\n                setAnchorElUsed: true,\n                anchorEl: anchorEl ?? undefined\n            })), []);\n    const toggle = (0,_useEvent_mjs__WEBPACK_IMPORTED_MODULE_1__.useEvent)((eventOrAnchorEl)=>{\n        if (state.isOpen) close(eventOrAnchorEl);\n        else open(eventOrAnchorEl);\n        return state;\n    });\n    const open = (0,_useEvent_mjs__WEBPACK_IMPORTED_MODULE_1__.useEvent)((eventOrAnchorEl)=>{\n        const event = eventOrAnchorEl instanceof Element ? undefined : eventOrAnchorEl;\n        const element = eventOrAnchorEl instanceof Element ? eventOrAnchorEl : (eventOrAnchorEl === null || eventOrAnchorEl === void 0 ? void 0 : eventOrAnchorEl.currentTarget) instanceof Element ? eventOrAnchorEl.currentTarget : undefined;\n        if ((event === null || event === void 0 ? void 0 : event.type) === \"touchstart\") {\n            setState((state)=>({\n                    ...state,\n                    _deferNextOpen: true\n                }));\n            return;\n        }\n        const clientX = event === null || event === void 0 ? void 0 : event.clientX;\n        const clientY = event === null || event === void 0 ? void 0 : event.clientY;\n        const anchorPosition = typeof clientX === \"number\" && typeof clientY === \"number\" ? {\n            left: clientX,\n            top: clientY\n        } : undefined;\n        const doOpen = (state)=>{\n            if (!eventOrAnchorEl && !state.setAnchorElUsed && variant !== \"dialog\") {\n                warn(\"missingEventOrAnchorEl\", \"eventOrAnchorEl should be defined if setAnchorEl is not used\");\n            }\n            if (parentPopupState) {\n                if (!parentPopupState.isOpen) return state;\n                setTimeout(()=>parentPopupState._setChildPopupState(popupState));\n            }\n            const newState = {\n                ...state,\n                isOpen: true,\n                anchorPosition,\n                hovered: (event === null || event === void 0 ? void 0 : event.type) === \"mouseover\" || state.hovered,\n                focused: (event === null || event === void 0 ? void 0 : event.type) === \"focus\" || state.focused,\n                _openEventType: event === null || event === void 0 ? void 0 : event.type\n            };\n            if (!state.setAnchorElUsed) {\n                if (event !== null && event !== void 0 && event.currentTarget) {\n                    newState.anchorEl = event.currentTarget;\n                } else if (element) {\n                    newState.anchorEl = element;\n                }\n            }\n            return newState;\n        };\n        setState((state)=>{\n            if (state._deferNextOpen) {\n                setTimeout(()=>setState(doOpen), 0);\n                return {\n                    ...state,\n                    _deferNextOpen: false\n                };\n            } else {\n                return doOpen(state);\n            }\n        });\n    });\n    const doClose = (state)=>{\n        const { _childPopupState } = state;\n        setTimeout(()=>{\n            _childPopupState === null || _childPopupState === void 0 || _childPopupState.close();\n            parentPopupState === null || parentPopupState === void 0 || parentPopupState._setChildPopupState(null);\n        });\n        return {\n            ...state,\n            isOpen: false,\n            hovered: false,\n            focused: false\n        };\n    };\n    const close = (0,_useEvent_mjs__WEBPACK_IMPORTED_MODULE_1__.useEvent)((eventOrAnchorEl)=>{\n        const event = eventOrAnchorEl instanceof Element ? undefined : eventOrAnchorEl;\n        if ((event === null || event === void 0 ? void 0 : event.type) === \"touchstart\") {\n            setState((state)=>({\n                    ...state,\n                    _deferNextClose: true\n                }));\n            return;\n        }\n        setState((state)=>{\n            if (state._deferNextClose) {\n                setTimeout(()=>setState(doClose), 0);\n                return {\n                    ...state,\n                    _deferNextClose: false\n                };\n            } else {\n                return doClose(state);\n            }\n        });\n    });\n    const setOpen = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((nextOpen, eventOrAnchorEl)=>{\n        if (nextOpen) {\n            open(eventOrAnchorEl);\n        } else {\n            close(eventOrAnchorEl);\n        }\n    }, []);\n    const onMouseLeave = (0,_useEvent_mjs__WEBPACK_IMPORTED_MODULE_1__.useEvent)((event)=>{\n        const { relatedTarget } = event;\n        setState((state)=>{\n            if (state.hovered && !(relatedTarget instanceof Element && isElementInPopup(relatedTarget, popupState))) {\n                if (state.focused) {\n                    return {\n                        ...state,\n                        hovered: false\n                    };\n                } else {\n                    return doClose(state);\n                }\n            }\n            return state;\n        });\n    });\n    const onBlur = (0,_useEvent_mjs__WEBPACK_IMPORTED_MODULE_1__.useEvent)((event)=>{\n        if (!event) return;\n        const { relatedTarget } = event;\n        setState((state)=>{\n            if (state.focused && !(relatedTarget instanceof Element && isElementInPopup(relatedTarget, popupState))) {\n                if (state.hovered) {\n                    return {\n                        ...state,\n                        focused: false\n                    };\n                } else {\n                    return doClose(state);\n                }\n            }\n            return state;\n        });\n    });\n    const _setChildPopupState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((_childPopupState)=>setState((state)=>({\n                ...state,\n                _childPopupState\n            })), []);\n    const popupState = {\n        ...state,\n        setAnchorEl,\n        popupId: popupId ?? undefined,\n        variant,\n        open,\n        close,\n        toggle,\n        setOpen,\n        onBlur,\n        onMouseLeave,\n        disableAutoFocus: disableAutoFocus ?? Boolean(state.hovered || state.focused),\n        _setChildPopupState\n    };\n    return popupState;\n}\n/**\n * Creates a ref that sets the anchorEl for the popup.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */ function anchorRef({ setAnchorEl }) {\n    return setAnchorEl;\n}\nfunction controlAriaProps({ isOpen, popupId, variant }) {\n    return {\n        ...variant === \"popover\" ? {\n            \"aria-haspopup\": true,\n            \"aria-controls\": isOpen ? popupId : undefined\n        } : variant === \"popper\" ? {\n            \"aria-describedby\": isOpen ? popupId : undefined\n        } : undefined\n    };\n}\n/**\n * Creates props for a component that opens the popup when clicked.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */ function bindTrigger(popupState) {\n    return {\n        ...controlAriaProps(popupState),\n        onClick: popupState.open,\n        onTouchStart: popupState.open\n    };\n}\n/**\n * Creates props for a component that opens the popup on its contextmenu event (right click).\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */ function bindContextMenu(popupState) {\n    return {\n        ...controlAriaProps(popupState),\n        onContextMenu: (e)=>{\n            e.preventDefault();\n            popupState.open(e);\n        }\n    };\n}\n/**\n * Creates props for a component that toggles the popup when clicked.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */ function bindToggle(popupState) {\n    return {\n        ...controlAriaProps(popupState),\n        onClick: popupState.toggle,\n        onTouchStart: popupState.toggle\n    };\n}\n/**\n * Creates props for a component that opens the popup while hovered.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */ function bindHover(popupState) {\n    const { open, onMouseLeave } = popupState;\n    return {\n        ...controlAriaProps(popupState),\n        onTouchStart: open,\n        onMouseOver: open,\n        onMouseLeave\n    };\n}\n/**\n * Creates props for a component that opens the popup while focused.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */ function bindFocus(popupState) {\n    const { open, onBlur } = popupState;\n    return {\n        ...controlAriaProps(popupState),\n        onFocus: open,\n        onBlur\n    };\n}\n/**\n * Creates props for a component that opens the popup while double click.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */ function bindDoubleClick({ isOpen, open, popupId, variant }) {\n    return {\n        // $FlowFixMe\n        [variant === \"popover\" ? \"aria-controls\" : \"aria-describedby\"]: isOpen ? popupId : null,\n        \"aria-haspopup\": variant === \"popover\" ? true : undefined,\n        onDoubleClick: open\n    };\n}\n/**\n * Creates props for a `Popover` component.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */ function bindPopover({ isOpen, anchorEl, anchorPosition, close, popupId, onMouseLeave, disableAutoFocus, _openEventType }) {\n    const usePopoverPosition = _openEventType === \"contextmenu\";\n    return {\n        id: popupId,\n        anchorEl,\n        anchorPosition,\n        anchorReference: usePopoverPosition ? \"anchorPosition\" : \"anchorEl\",\n        open: isOpen,\n        onClose: close,\n        onMouseLeave,\n        ...disableAutoFocus && {\n            disableAutoFocus: true,\n            disableEnforceFocus: true,\n            disableRestoreFocus: true\n        }\n    };\n}\n/**\n * Creates props for a `Menu` component.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */ /**\n * Creates props for a `Popover` component.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */ function bindMenu({ isOpen, anchorEl, anchorPosition, close, popupId, onMouseLeave, disableAutoFocus, _openEventType }) {\n    const usePopoverPosition = _openEventType === \"contextmenu\";\n    return {\n        id: popupId,\n        anchorEl,\n        anchorPosition,\n        anchorReference: usePopoverPosition ? \"anchorPosition\" : \"anchorEl\",\n        open: isOpen,\n        onClose: close,\n        onMouseLeave,\n        ...disableAutoFocus && {\n            autoFocus: false,\n            disableAutoFocusItem: true,\n            disableAutoFocus: true,\n            disableEnforceFocus: true,\n            disableRestoreFocus: true\n        }\n    };\n}\n/**\n * Creates props for a `Popper` component.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */ function bindPopper({ isOpen, anchorEl, popupId, onMouseLeave }) {\n    return {\n        id: popupId,\n        anchorEl,\n        open: isOpen,\n        onMouseLeave\n    };\n}\n/**\n * Creates props for a `Dialog` component.\n *\n * @param {object} popupState the argument passed to the child function of\n * `PopupState`\n */ function bindDialog({ isOpen, close }) {\n    return {\n        open: isOpen,\n        onClose: close\n    };\n}\nfunction getPopup(element, { popupId }) {\n    if (!popupId) return null;\n    const rootNode = typeof element.getRootNode === \"function\" ? element.getRootNode() : document;\n    if (typeof rootNode.getElementById === \"function\") {\n        return rootNode.getElementById(popupId);\n    }\n    return null;\n}\nfunction isElementInPopup(element, popupState) {\n    const { anchorEl, _childPopupState } = popupState;\n    return isAncestor(anchorEl, element) || isAncestor(getPopup(element, popupState), element) || _childPopupState != null && isElementInPopup(element, _childPopupState);\n}\nfunction isAncestor(parent, child) {\n    if (!parent) return false;\n    while(child){\n        if (child === parent) return true;\n        child = child.parentElement;\n    }\n    return false;\n} //# sourceMappingURL=hooks.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/material-ui-popup-state/hooks.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/material-ui-popup-state/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/material-ui-popup-state/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   anchorRef: () => (/* reexport safe */ _hooks_mjs__WEBPACK_IMPORTED_MODULE_0__.anchorRef),\n/* harmony export */   bindContextMenu: () => (/* reexport safe */ _hooks_mjs__WEBPACK_IMPORTED_MODULE_0__.bindContextMenu),\n/* harmony export */   bindDialog: () => (/* reexport safe */ _hooks_mjs__WEBPACK_IMPORTED_MODULE_0__.bindDialog),\n/* harmony export */   bindDoubleClick: () => (/* reexport safe */ _hooks_mjs__WEBPACK_IMPORTED_MODULE_0__.bindDoubleClick),\n/* harmony export */   bindFocus: () => (/* reexport safe */ _hooks_mjs__WEBPACK_IMPORTED_MODULE_0__.bindFocus),\n/* harmony export */   bindHover: () => (/* reexport safe */ _hooks_mjs__WEBPACK_IMPORTED_MODULE_0__.bindHover),\n/* harmony export */   bindMenu: () => (/* reexport safe */ _hooks_mjs__WEBPACK_IMPORTED_MODULE_0__.bindMenu),\n/* harmony export */   bindPopover: () => (/* reexport safe */ _hooks_mjs__WEBPACK_IMPORTED_MODULE_0__.bindPopover),\n/* harmony export */   bindPopper: () => (/* reexport safe */ _hooks_mjs__WEBPACK_IMPORTED_MODULE_0__.bindPopper),\n/* harmony export */   bindToggle: () => (/* reexport safe */ _hooks_mjs__WEBPACK_IMPORTED_MODULE_0__.bindToggle),\n/* harmony export */   bindTrigger: () => (/* reexport safe */ _hooks_mjs__WEBPACK_IMPORTED_MODULE_0__.bindTrigger),\n/* harmony export */   \"default\": () => (/* binding */ PopupState)\n/* harmony export */ });\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var _hooks_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hooks.mjs */ \"(ssr)/./node_modules/material-ui-popup-state/hooks.mjs\");\n\n\n\nfunction PopupState({ children, popupId, variant, parentPopupState, disableAutoFocus }) {\n    const popupState = (0,_hooks_mjs__WEBPACK_IMPORTED_MODULE_0__.usePopupState)({\n        popupId,\n        variant,\n        parentPopupState,\n        disableAutoFocus\n    });\n    const result = children(popupState);\n    return result != null ? result : null;\n}\nPopupState.propTypes = {\n    /**\n   * The render function.\n   *\n   * @param {object} props the properties injected by `PopupState`:\n   * <ul>\n   *   <li>`open(eventOrAnchorEl)`: opens the popup</li>\n   *   <li>`close()`: closes the popup</li>\n   *   <li>`toggle(eventOrAnchorEl)`: opens the popup if it is closed, or\n   *     closes the popup if it is open.\n   *   </li>\n   *   <li>`setOpen(open, [eventOrAnchorEl])`: sets whether the popup is open.\n   *     `eventOrAnchorEl` is required if `open` is truthy.\n   *   </li>\n   *   <li>`isOpen`: `true`/`false` if the popup is open/closed</li>\n   *   <li>`anchorEl`: the current anchor element (`null` the popup is closed)</li>\n   *   <li>`popupId`: the `popupId` prop you passed</li>\n   * </ul>\n   *\n   * @returns {React.Node} the content to display\n   */ children: prop_types__WEBPACK_IMPORTED_MODULE_1__.func.isRequired,\n    /**\n   * The `id` property to use for the popup.  Will be passed to the render\n   * function as `bindPopup.id`, and also used for the `aria-controls` property\n   * passed to the trigger component via `bindTrigger`.\n   */ popupId: prop_types__WEBPACK_IMPORTED_MODULE_1__.string,\n    /**\n   * Which type of popup you are controlling.  Use `'popover'` for `Popover`\n   * and `Menu`; use `'popper'` for `Popper`s.  Right now this only affects\n   * whether `aria-controls` or `aria-describedby` is used on the trigger\n   * component.\n   */ variant: prop_types__WEBPACK_IMPORTED_MODULE_1__.oneOf([\n        \"popover\",\n        \"popper\"\n    ]).isRequired,\n    /**\n   * The popupState of the parent menu (for cascading menus)\n   */ parentPopupState: prop_types__WEBPACK_IMPORTED_MODULE_1__.object,\n    /**\n   * Will focus the popup when it opens unless disableAutoFocus is explicitly false.\n   */ disableAutoFocus: prop_types__WEBPACK_IMPORTED_MODULE_1__.bool\n}; //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/material-ui-popup-state/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/material-ui-popup-state/useEvent.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/material-ui-popup-state/useEvent.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEvent: () => (/* binding */ useEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction useEvent(handler) {\n    // istanbul ignore next\n    if (true) {\n        // useLayoutEffect doesn't work on the server side, don't bother\n        // trying to make callback functions stable\n        return handler;\n    }\n    const handlerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(()=>{\n        handlerRef.current = handler;\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback((...args)=>{\n        var _handlerRef$current;\n        (_handlerRef$current = handlerRef.current) === null || _handlerRef$current === void 0 || _handlerRef$current.call(handlerRef, ...args);\n    }, []);\n} //# sourceMappingURL=useEvent.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWF0ZXJpYWwtdWktcG9wdXAtc3RhdGUvdXNlRXZlbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStCO0FBQ3hCLFNBQVNDLFNBQVNDLE9BQU87SUFDOUIsdUJBQXVCO0lBQ3ZCLElBQUksSUFBa0IsRUFBYTtRQUNqQyxnRUFBZ0U7UUFDaEUsMkNBQTJDO1FBQzNDLE9BQU9BO0lBQ1Q7SUFDQSxNQUFNQyxhQUFhSCx5Q0FBWSxDQUFDO0lBQ2hDQSxrREFBcUIsQ0FBQztRQUNwQkcsV0FBV0csT0FBTyxHQUFHSjtJQUN2QjtJQUNBLE9BQU9GLDhDQUFpQixDQUFDLENBQUMsR0FBR1E7UUFDM0IsSUFBSUM7UUFDSEEsQ0FBQUEsc0JBQXNCTixXQUFXRyxPQUFPLE1BQU0sUUFBUUcsd0JBQXdCLEtBQUssS0FBS0Esb0JBQW9CQyxJQUFJLENBQUNQLGVBQWVLO0lBQ25JLEdBQUcsRUFBRTtBQUNQLEVBQ0EscUNBQXFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvbWF0ZXJpYWwtdWktcG9wdXAtc3RhdGUvdXNlRXZlbnQubWpzP2I1NmQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUV2ZW50KGhhbmRsZXIpIHtcbiAgLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgLy8gdXNlTGF5b3V0RWZmZWN0IGRvZXNuJ3Qgd29yayBvbiB0aGUgc2VydmVyIHNpZGUsIGRvbid0IGJvdGhlclxuICAgIC8vIHRyeWluZyB0byBtYWtlIGNhbGxiYWNrIGZ1bmN0aW9ucyBzdGFibGVcbiAgICByZXR1cm4gaGFuZGxlcjtcbiAgfVxuICBjb25zdCBoYW5kbGVyUmVmID0gUmVhY3QudXNlUmVmKG51bGwpO1xuICBSZWFjdC51c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIGhhbmRsZXJSZWYuY3VycmVudCA9IGhhbmRsZXI7XG4gIH0pO1xuICByZXR1cm4gUmVhY3QudXNlQ2FsbGJhY2soKC4uLmFyZ3MpID0+IHtcbiAgICB2YXIgX2hhbmRsZXJSZWYkY3VycmVudDtcbiAgICAoX2hhbmRsZXJSZWYkY3VycmVudCA9IGhhbmRsZXJSZWYuY3VycmVudCkgPT09IG51bGwgfHwgX2hhbmRsZXJSZWYkY3VycmVudCA9PT0gdm9pZCAwIHx8IF9oYW5kbGVyUmVmJGN1cnJlbnQuY2FsbChoYW5kbGVyUmVmLCAuLi5hcmdzKTtcbiAgfSwgW10pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXNlRXZlbnQubWpzLm1hcCJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUV2ZW50IiwiaGFuZGxlciIsImhhbmRsZXJSZWYiLCJ1c2VSZWYiLCJ1c2VMYXlvdXRFZmZlY3QiLCJjdXJyZW50IiwidXNlQ2FsbGJhY2siLCJhcmdzIiwiX2hhbmRsZXJSZWYkY3VycmVudCIsImNhbGwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/material-ui-popup-state/useEvent.mjs\n");

/***/ })

};
;