"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/http2-wrapper";
exports.ids = ["vendor-chunks/http2-wrapper"];
exports.modules = {

/***/ "(rsc)/./node_modules/http2-wrapper/source/agent.js":
/*!****************************************************!*\
  !*** ./node_modules/http2-wrapper/source/agent.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst EventEmitter = __webpack_require__(/*! events */ \"events\");\nconst tls = __webpack_require__(/*! tls */ \"tls\");\nconst http2 = __webpack_require__(/*! http2 */ \"http2\");\nconst QuickLRU = __webpack_require__(/*! quick-lru */ \"(rsc)/./node_modules/quick-lru/index.js\");\n\nconst kCurrentStreamsCount = Symbol('currentStreamsCount');\nconst kRequest = Symbol('request');\nconst kOriginSet = Symbol('cachedOriginSet');\nconst kGracefullyClosing = Symbol('gracefullyClosing');\n\nconst nameKeys = [\n\t// `http2.connect()` options\n\t'maxDeflateDynamicTableSize',\n\t'maxSessionMemory',\n\t'maxHeaderListPairs',\n\t'maxOutstandingPings',\n\t'maxReservedRemoteStreams',\n\t'maxSendHeaderBlockLength',\n\t'paddingStrategy',\n\n\t// `tls.connect()` options\n\t'localAddress',\n\t'path',\n\t'rejectUnauthorized',\n\t'minDHSize',\n\n\t// `tls.createSecureContext()` options\n\t'ca',\n\t'cert',\n\t'clientCertEngine',\n\t'ciphers',\n\t'key',\n\t'pfx',\n\t'servername',\n\t'minVersion',\n\t'maxVersion',\n\t'secureProtocol',\n\t'crl',\n\t'honorCipherOrder',\n\t'ecdhCurve',\n\t'dhparam',\n\t'secureOptions',\n\t'sessionIdContext'\n];\n\nconst getSortedIndex = (array, value, compare) => {\n\tlet low = 0;\n\tlet high = array.length;\n\n\twhile (low < high) {\n\t\tconst mid = (low + high) >>> 1;\n\n\t\t/* istanbul ignore next */\n\t\tif (compare(array[mid], value)) {\n\t\t\t// This never gets called because we use descending sort. Better to have this anyway.\n\t\t\tlow = mid + 1;\n\t\t} else {\n\t\t\thigh = mid;\n\t\t}\n\t}\n\n\treturn low;\n};\n\nconst compareSessions = (a, b) => {\n\treturn a.remoteSettings.maxConcurrentStreams > b.remoteSettings.maxConcurrentStreams;\n};\n\n// See https://tools.ietf.org/html/rfc8336\nconst closeCoveredSessions = (where, session) => {\n\t// Clients SHOULD NOT emit new requests on any connection whose Origin\n\t// Set is a proper subset of another connection's Origin Set, and they\n\t// SHOULD close it once all outstanding requests are satisfied.\n\tfor (const coveredSession of where) {\n\t\tif (\n\t\t\t// The set is a proper subset when its length is less than the other set.\n\t\t\tcoveredSession[kOriginSet].length < session[kOriginSet].length &&\n\n\t\t\t// And the other set includes all elements of the subset.\n\t\t\tcoveredSession[kOriginSet].every(origin => session[kOriginSet].includes(origin)) &&\n\n\t\t\t// Makes sure that the session can handle all requests from the covered session.\n\t\t\tcoveredSession[kCurrentStreamsCount] + session[kCurrentStreamsCount] <= session.remoteSettings.maxConcurrentStreams\n\t\t) {\n\t\t\t// This allows pending requests to finish and prevents making new requests.\n\t\t\tgracefullyClose(coveredSession);\n\t\t}\n\t}\n};\n\n// This is basically inverted `closeCoveredSessions(...)`.\nconst closeSessionIfCovered = (where, coveredSession) => {\n\tfor (const session of where) {\n\t\tif (\n\t\t\tcoveredSession[kOriginSet].length < session[kOriginSet].length &&\n\t\t\tcoveredSession[kOriginSet].every(origin => session[kOriginSet].includes(origin)) &&\n\t\t\tcoveredSession[kCurrentStreamsCount] + session[kCurrentStreamsCount] <= session.remoteSettings.maxConcurrentStreams\n\t\t) {\n\t\t\tgracefullyClose(coveredSession);\n\t\t}\n\t}\n};\n\nconst getSessions = ({agent, isFree}) => {\n\tconst result = {};\n\n\t// eslint-disable-next-line guard-for-in\n\tfor (const normalizedOptions in agent.sessions) {\n\t\tconst sessions = agent.sessions[normalizedOptions];\n\n\t\tconst filtered = sessions.filter(session => {\n\t\t\tconst result = session[Agent.kCurrentStreamsCount] < session.remoteSettings.maxConcurrentStreams;\n\n\t\t\treturn isFree ? result : !result;\n\t\t});\n\n\t\tif (filtered.length !== 0) {\n\t\t\tresult[normalizedOptions] = filtered;\n\t\t}\n\t}\n\n\treturn result;\n};\n\nconst gracefullyClose = session => {\n\tsession[kGracefullyClosing] = true;\n\n\tif (session[kCurrentStreamsCount] === 0) {\n\t\tsession.close();\n\t}\n};\n\nclass Agent extends EventEmitter {\n\tconstructor({timeout = 60000, maxSessions = Infinity, maxFreeSessions = 10, maxCachedTlsSessions = 100} = {}) {\n\t\tsuper();\n\n\t\t// A session is considered busy when its current streams count\n\t\t// is equal to or greater than the `maxConcurrentStreams` value.\n\n\t\t// A session is considered free when its current streams count\n\t\t// is less than the `maxConcurrentStreams` value.\n\n\t\t// SESSIONS[NORMALIZED_OPTIONS] = [];\n\t\tthis.sessions = {};\n\n\t\t// The queue for creating new sessions. It looks like this:\n\t\t// QUEUE[NORMALIZED_OPTIONS][NORMALIZED_ORIGIN] = ENTRY_FUNCTION\n\t\t//\n\t\t// The entry function has `listeners`, `completed` and `destroyed` properties.\n\t\t// `listeners` is an array of objects containing `resolve` and `reject` functions.\n\t\t// `completed` is a boolean. It's set to true after ENTRY_FUNCTION is executed.\n\t\t// `destroyed` is a boolean. If it's set to true, the session will be destroyed if hasn't connected yet.\n\t\tthis.queue = {};\n\n\t\t// Each session will use this timeout value.\n\t\tthis.timeout = timeout;\n\n\t\t// Max sessions in total\n\t\tthis.maxSessions = maxSessions;\n\n\t\t// Max free sessions in total\n\t\t// TODO: decreasing `maxFreeSessions` should close some sessions\n\t\tthis.maxFreeSessions = maxFreeSessions;\n\n\t\tthis._freeSessionsCount = 0;\n\t\tthis._sessionsCount = 0;\n\n\t\t// We don't support push streams by default.\n\t\tthis.settings = {\n\t\t\tenablePush: false\n\t\t};\n\n\t\t// Reusing TLS sessions increases performance.\n\t\tthis.tlsSessionCache = new QuickLRU({maxSize: maxCachedTlsSessions});\n\t}\n\n\tstatic normalizeOrigin(url, servername) {\n\t\tif (typeof url === 'string') {\n\t\t\turl = new URL(url);\n\t\t}\n\n\t\tif (servername && url.hostname !== servername) {\n\t\t\turl.hostname = servername;\n\t\t}\n\n\t\treturn url.origin;\n\t}\n\n\tnormalizeOptions(options) {\n\t\tlet normalized = '';\n\n\t\tif (options) {\n\t\t\tfor (const key of nameKeys) {\n\t\t\t\tif (options[key]) {\n\t\t\t\t\tnormalized += `:${options[key]}`;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn normalized;\n\t}\n\n\t_tryToCreateNewSession(normalizedOptions, normalizedOrigin) {\n\t\tif (!(normalizedOptions in this.queue) || !(normalizedOrigin in this.queue[normalizedOptions])) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst item = this.queue[normalizedOptions][normalizedOrigin];\n\n\t\t// The entry function can be run only once.\n\t\t// BUG: The session may be never created when:\n\t\t// - the first condition is false AND\n\t\t// - this function is never called with the same arguments in the future.\n\t\tif (this._sessionsCount < this.maxSessions && !item.completed) {\n\t\t\titem.completed = true;\n\n\t\t\titem();\n\t\t}\n\t}\n\n\tgetSession(origin, options, listeners) {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tif (Array.isArray(listeners)) {\n\t\t\t\tlisteners = [...listeners];\n\n\t\t\t\t// Resolve the current promise ASAP, we're just moving the listeners.\n\t\t\t\t// They will be executed at a different time.\n\t\t\t\tresolve();\n\t\t\t} else {\n\t\t\t\tlisteners = [{resolve, reject}];\n\t\t\t}\n\n\t\t\tconst normalizedOptions = this.normalizeOptions(options);\n\t\t\tconst normalizedOrigin = Agent.normalizeOrigin(origin, options && options.servername);\n\n\t\t\tif (normalizedOrigin === undefined) {\n\t\t\t\tfor (const {reject} of listeners) {\n\t\t\t\t\treject(new TypeError('The `origin` argument needs to be a string or an URL object'));\n\t\t\t\t}\n\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (normalizedOptions in this.sessions) {\n\t\t\t\tconst sessions = this.sessions[normalizedOptions];\n\n\t\t\t\tlet maxConcurrentStreams = -1;\n\t\t\t\tlet currentStreamsCount = -1;\n\t\t\t\tlet optimalSession;\n\n\t\t\t\t// We could just do this.sessions[normalizedOptions].find(...) but that isn't optimal.\n\t\t\t\t// Additionally, we are looking for session which has biggest current pending streams count.\n\t\t\t\tfor (const session of sessions) {\n\t\t\t\t\tconst sessionMaxConcurrentStreams = session.remoteSettings.maxConcurrentStreams;\n\n\t\t\t\t\tif (sessionMaxConcurrentStreams < maxConcurrentStreams) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (session[kOriginSet].includes(normalizedOrigin)) {\n\t\t\t\t\t\tconst sessionCurrentStreamsCount = session[kCurrentStreamsCount];\n\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\tsessionCurrentStreamsCount >= sessionMaxConcurrentStreams ||\n\t\t\t\t\t\t\tsession[kGracefullyClosing] ||\n\t\t\t\t\t\t\t// Unfortunately the `close` event isn't called immediately,\n\t\t\t\t\t\t\t// so `session.destroyed` is `true`, but `session.closed` is `false`.\n\t\t\t\t\t\t\tsession.destroyed\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// We only need set this once.\n\t\t\t\t\t\tif (!optimalSession) {\n\t\t\t\t\t\t\tmaxConcurrentStreams = sessionMaxConcurrentStreams;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// We're looking for the session which has biggest current pending stream count,\n\t\t\t\t\t\t// in order to minimalize the amount of active sessions.\n\t\t\t\t\t\tif (sessionCurrentStreamsCount > currentStreamsCount) {\n\t\t\t\t\t\t\toptimalSession = session;\n\t\t\t\t\t\t\tcurrentStreamsCount = sessionCurrentStreamsCount;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (optimalSession) {\n\t\t\t\t\t/* istanbul ignore next: safety check */\n\t\t\t\t\tif (listeners.length !== 1) {\n\t\t\t\t\t\tfor (const {reject} of listeners) {\n\t\t\t\t\t\t\tconst error = new Error(\n\t\t\t\t\t\t\t\t`Expected the length of listeners to be 1, got ${listeners.length}.\\n` +\n\t\t\t\t\t\t\t\t'Please report this to https://github.com/szmarczak/http2-wrapper/'\n\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\treject(error);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tlisteners[0].resolve(optimalSession);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (normalizedOptions in this.queue) {\n\t\t\t\tif (normalizedOrigin in this.queue[normalizedOptions]) {\n\t\t\t\t\t// There's already an item in the queue, just attach ourselves to it.\n\t\t\t\t\tthis.queue[normalizedOptions][normalizedOrigin].listeners.push(...listeners);\n\n\t\t\t\t\t// This shouldn't be executed here.\n\t\t\t\t\t// See the comment inside _tryToCreateNewSession.\n\t\t\t\t\tthis._tryToCreateNewSession(normalizedOptions, normalizedOrigin);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthis.queue[normalizedOptions] = {};\n\t\t\t}\n\n\t\t\t// The entry must be removed from the queue IMMEDIATELY when:\n\t\t\t// 1. the session connects successfully,\n\t\t\t// 2. an error occurs.\n\t\t\tconst removeFromQueue = () => {\n\t\t\t\t// Our entry can be replaced. We cannot remove the new one.\n\t\t\t\tif (normalizedOptions in this.queue && this.queue[normalizedOptions][normalizedOrigin] === entry) {\n\t\t\t\t\tdelete this.queue[normalizedOptions][normalizedOrigin];\n\n\t\t\t\t\tif (Object.keys(this.queue[normalizedOptions]).length === 0) {\n\t\t\t\t\t\tdelete this.queue[normalizedOptions];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\n\t\t\t// The main logic is here\n\t\t\tconst entry = () => {\n\t\t\t\tconst name = `${normalizedOrigin}:${normalizedOptions}`;\n\t\t\t\tlet receivedSettings = false;\n\n\t\t\t\ttry {\n\t\t\t\t\tconst session = http2.connect(origin, {\n\t\t\t\t\t\tcreateConnection: this.createConnection,\n\t\t\t\t\t\tsettings: this.settings,\n\t\t\t\t\t\tsession: this.tlsSessionCache.get(name),\n\t\t\t\t\t\t...options\n\t\t\t\t\t});\n\t\t\t\t\tsession[kCurrentStreamsCount] = 0;\n\t\t\t\t\tsession[kGracefullyClosing] = false;\n\n\t\t\t\t\tconst isFree = () => session[kCurrentStreamsCount] < session.remoteSettings.maxConcurrentStreams;\n\t\t\t\t\tlet wasFree = true;\n\n\t\t\t\t\tsession.socket.once('session', tlsSession => {\n\t\t\t\t\t\tthis.tlsSessionCache.set(name, tlsSession);\n\t\t\t\t\t});\n\n\t\t\t\t\tsession.once('error', error => {\n\t\t\t\t\t\t// Listeners are empty when the session successfully connected.\n\t\t\t\t\t\tfor (const {reject} of listeners) {\n\t\t\t\t\t\t\treject(error);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// The connection got broken, purge the cache.\n\t\t\t\t\t\tthis.tlsSessionCache.delete(name);\n\t\t\t\t\t});\n\n\t\t\t\t\tsession.setTimeout(this.timeout, () => {\n\t\t\t\t\t\t// Terminates all streams owned by this session.\n\t\t\t\t\t\t// TODO: Maybe the streams should have a \"Session timed out\" error?\n\t\t\t\t\t\tsession.destroy();\n\t\t\t\t\t});\n\n\t\t\t\t\tsession.once('close', () => {\n\t\t\t\t\t\tif (receivedSettings) {\n\t\t\t\t\t\t\t// 1. If it wasn't free then no need to decrease because\n\t\t\t\t\t\t\t//    it has been decreased already in session.request().\n\t\t\t\t\t\t\t// 2. `stream.once('close')` won't increment the count\n\t\t\t\t\t\t\t//    because the session is already closed.\n\t\t\t\t\t\t\tif (wasFree) {\n\t\t\t\t\t\t\t\tthis._freeSessionsCount--;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tthis._sessionsCount--;\n\n\t\t\t\t\t\t\t// This cannot be moved to the stream logic,\n\t\t\t\t\t\t\t// because there may be a session that hadn't made a single request.\n\t\t\t\t\t\t\tconst where = this.sessions[normalizedOptions];\n\t\t\t\t\t\t\twhere.splice(where.indexOf(session), 1);\n\n\t\t\t\t\t\t\tif (where.length === 0) {\n\t\t\t\t\t\t\t\tdelete this.sessions[normalizedOptions];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Broken connection\n\t\t\t\t\t\t\tconst error = new Error('Session closed without receiving a SETTINGS frame');\n\t\t\t\t\t\t\terror.code = 'HTTP2WRAPPER_NOSETTINGS';\n\n\t\t\t\t\t\t\tfor (const {reject} of listeners) {\n\t\t\t\t\t\t\t\treject(error);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tremoveFromQueue();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// There may be another session awaiting.\n\t\t\t\t\t\tthis._tryToCreateNewSession(normalizedOptions, normalizedOrigin);\n\t\t\t\t\t});\n\n\t\t\t\t\t// Iterates over the queue and processes listeners.\n\t\t\t\t\tconst processListeners = () => {\n\t\t\t\t\t\tif (!(normalizedOptions in this.queue) || !isFree()) {\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tfor (const origin of session[kOriginSet]) {\n\t\t\t\t\t\t\tif (origin in this.queue[normalizedOptions]) {\n\t\t\t\t\t\t\t\tconst {listeners} = this.queue[normalizedOptions][origin];\n\n\t\t\t\t\t\t\t\t// Prevents session overloading.\n\t\t\t\t\t\t\t\twhile (listeners.length !== 0 && isFree()) {\n\t\t\t\t\t\t\t\t\t// We assume `resolve(...)` calls `request(...)` *directly*,\n\t\t\t\t\t\t\t\t\t// otherwise the session will get overloaded.\n\t\t\t\t\t\t\t\t\tlisteners.shift().resolve(session);\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tconst where = this.queue[normalizedOptions];\n\t\t\t\t\t\t\t\tif (where[origin].listeners.length === 0) {\n\t\t\t\t\t\t\t\t\tdelete where[origin];\n\n\t\t\t\t\t\t\t\t\tif (Object.keys(where).length === 0) {\n\t\t\t\t\t\t\t\t\t\tdelete this.queue[normalizedOptions];\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// We're no longer free, no point in continuing.\n\t\t\t\t\t\t\t\tif (!isFree()) {\n\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\n\t\t\t\t\t// The Origin Set cannot shrink. No need to check if it suddenly became covered by another one.\n\t\t\t\t\tsession.on('origin', () => {\n\t\t\t\t\t\tsession[kOriginSet] = session.originSet;\n\n\t\t\t\t\t\tif (!isFree()) {\n\t\t\t\t\t\t\t// The session is full.\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tprocessListeners();\n\n\t\t\t\t\t\t// Close covered sessions (if possible).\n\t\t\t\t\t\tcloseCoveredSessions(this.sessions[normalizedOptions], session);\n\t\t\t\t\t});\n\n\t\t\t\t\tsession.once('remoteSettings', () => {\n\t\t\t\t\t\t// Fix Node.js bug preventing the process from exiting\n\t\t\t\t\t\tsession.ref();\n\t\t\t\t\t\tsession.unref();\n\n\t\t\t\t\t\tthis._sessionsCount++;\n\n\t\t\t\t\t\t// The Agent could have been destroyed already.\n\t\t\t\t\t\tif (entry.destroyed) {\n\t\t\t\t\t\t\tconst error = new Error('Agent has been destroyed');\n\n\t\t\t\t\t\t\tfor (const listener of listeners) {\n\t\t\t\t\t\t\t\tlistener.reject(error);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tsession.destroy();\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tsession[kOriginSet] = session.originSet;\n\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tconst where = this.sessions;\n\n\t\t\t\t\t\t\tif (normalizedOptions in where) {\n\t\t\t\t\t\t\t\tconst sessions = where[normalizedOptions];\n\t\t\t\t\t\t\t\tsessions.splice(getSortedIndex(sessions, session, compareSessions), 0, session);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\twhere[normalizedOptions] = [session];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tthis._freeSessionsCount += 1;\n\t\t\t\t\t\treceivedSettings = true;\n\n\t\t\t\t\t\tthis.emit('session', session);\n\n\t\t\t\t\t\tprocessListeners();\n\t\t\t\t\t\tremoveFromQueue();\n\n\t\t\t\t\t\t// TODO: Close last recently used (or least used?) session\n\t\t\t\t\t\tif (session[kCurrentStreamsCount] === 0 && this._freeSessionsCount > this.maxFreeSessions) {\n\t\t\t\t\t\t\tsession.close();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Check if we haven't managed to execute all listeners.\n\t\t\t\t\t\tif (listeners.length !== 0) {\n\t\t\t\t\t\t\t// Request for a new session with predefined listeners.\n\t\t\t\t\t\t\tthis.getSession(normalizedOrigin, options, listeners);\n\t\t\t\t\t\t\tlisteners.length = 0;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// `session.remoteSettings.maxConcurrentStreams` might get increased\n\t\t\t\t\t\tsession.on('remoteSettings', () => {\n\t\t\t\t\t\t\tprocessListeners();\n\n\t\t\t\t\t\t\t// In case the Origin Set changes\n\t\t\t\t\t\t\tcloseCoveredSessions(this.sessions[normalizedOptions], session);\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\n\t\t\t\t\t// Shim `session.request()` in order to catch all streams\n\t\t\t\t\tsession[kRequest] = session.request;\n\t\t\t\t\tsession.request = (headers, streamOptions) => {\n\t\t\t\t\t\tif (session[kGracefullyClosing]) {\n\t\t\t\t\t\t\tthrow new Error('The session is gracefully closing. No new streams are allowed.');\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst stream = session[kRequest](headers, streamOptions);\n\n\t\t\t\t\t\t// The process won't exit until the session is closed or all requests are gone.\n\t\t\t\t\t\tsession.ref();\n\n\t\t\t\t\t\t++session[kCurrentStreamsCount];\n\n\t\t\t\t\t\tif (session[kCurrentStreamsCount] === session.remoteSettings.maxConcurrentStreams) {\n\t\t\t\t\t\t\tthis._freeSessionsCount--;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tstream.once('close', () => {\n\t\t\t\t\t\t\twasFree = isFree();\n\n\t\t\t\t\t\t\t--session[kCurrentStreamsCount];\n\n\t\t\t\t\t\t\tif (!session.destroyed && !session.closed) {\n\t\t\t\t\t\t\t\tcloseSessionIfCovered(this.sessions[normalizedOptions], session);\n\n\t\t\t\t\t\t\t\tif (isFree() && !session.closed) {\n\t\t\t\t\t\t\t\t\tif (!wasFree) {\n\t\t\t\t\t\t\t\t\t\tthis._freeSessionsCount++;\n\n\t\t\t\t\t\t\t\t\t\twasFree = true;\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tconst isEmpty = session[kCurrentStreamsCount] === 0;\n\n\t\t\t\t\t\t\t\t\tif (isEmpty) {\n\t\t\t\t\t\t\t\t\t\tsession.unref();\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t\t\tisEmpty &&\n\t\t\t\t\t\t\t\t\t\t(\n\t\t\t\t\t\t\t\t\t\t\tthis._freeSessionsCount > this.maxFreeSessions ||\n\t\t\t\t\t\t\t\t\t\t\tsession[kGracefullyClosing]\n\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\t\t\tsession.close();\n\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\tcloseCoveredSessions(this.sessions[normalizedOptions], session);\n\t\t\t\t\t\t\t\t\t\tprocessListeners();\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn stream;\n\t\t\t\t\t};\n\t\t\t\t} catch (error) {\n\t\t\t\t\tfor (const listener of listeners) {\n\t\t\t\t\t\tlistener.reject(error);\n\t\t\t\t\t}\n\n\t\t\t\t\tremoveFromQueue();\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tentry.listeners = listeners;\n\t\t\tentry.completed = false;\n\t\t\tentry.destroyed = false;\n\n\t\t\tthis.queue[normalizedOptions][normalizedOrigin] = entry;\n\t\t\tthis._tryToCreateNewSession(normalizedOptions, normalizedOrigin);\n\t\t});\n\t}\n\n\trequest(origin, options, headers, streamOptions) {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tthis.getSession(origin, options, [{\n\t\t\t\treject,\n\t\t\t\tresolve: session => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tresolve(session.request(headers, streamOptions));\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\treject(error);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}]);\n\t\t});\n\t}\n\n\tcreateConnection(origin, options) {\n\t\treturn Agent.connect(origin, options);\n\t}\n\n\tstatic connect(origin, options) {\n\t\toptions.ALPNProtocols = ['h2'];\n\n\t\tconst port = origin.port || 443;\n\t\tconst host = origin.hostname || origin.host;\n\n\t\tif (typeof options.servername === 'undefined') {\n\t\t\toptions.servername = host;\n\t\t}\n\n\t\treturn tls.connect(port, host, options);\n\t}\n\n\tcloseFreeSessions() {\n\t\tfor (const sessions of Object.values(this.sessions)) {\n\t\t\tfor (const session of sessions) {\n\t\t\t\tif (session[kCurrentStreamsCount] === 0) {\n\t\t\t\t\tsession.close();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tdestroy(reason) {\n\t\tfor (const sessions of Object.values(this.sessions)) {\n\t\t\tfor (const session of sessions) {\n\t\t\t\tsession.destroy(reason);\n\t\t\t}\n\t\t}\n\n\t\tfor (const entriesOfAuthority of Object.values(this.queue)) {\n\t\t\tfor (const entry of Object.values(entriesOfAuthority)) {\n\t\t\t\tentry.destroyed = true;\n\t\t\t}\n\t\t}\n\n\t\t// New requests should NOT attach to destroyed sessions\n\t\tthis.queue = {};\n\t}\n\n\tget freeSessions() {\n\t\treturn getSessions({agent: this, isFree: true});\n\t}\n\n\tget busySessions() {\n\t\treturn getSessions({agent: this, isFree: false});\n\t}\n}\n\nAgent.kCurrentStreamsCount = kCurrentStreamsCount;\nAgent.kGracefullyClosing = kGracefullyClosing;\n\nmodule.exports = {\n\tAgent,\n\tglobalAgent: new Agent()\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http2-wrapper/source/agent.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http2-wrapper/source/auto.js":
/*!***************************************************!*\
  !*** ./node_modules/http2-wrapper/source/auto.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst resolveALPN = __webpack_require__(/*! resolve-alpn */ \"(rsc)/./node_modules/resolve-alpn/index.js\");\nconst QuickLRU = __webpack_require__(/*! quick-lru */ \"(rsc)/./node_modules/quick-lru/index.js\");\nconst Http2ClientRequest = __webpack_require__(/*! ./client-request */ \"(rsc)/./node_modules/http2-wrapper/source/client-request.js\");\nconst calculateServerName = __webpack_require__(/*! ./utils/calculate-server-name */ \"(rsc)/./node_modules/http2-wrapper/source/utils/calculate-server-name.js\");\nconst urlToOptions = __webpack_require__(/*! ./utils/url-to-options */ \"(rsc)/./node_modules/http2-wrapper/source/utils/url-to-options.js\");\n\nconst cache = new QuickLRU({maxSize: 100});\nconst queue = new Map();\n\nconst installSocket = (agent, socket, options) => {\n\tsocket._httpMessage = {shouldKeepAlive: true};\n\n\tconst onFree = () => {\n\t\tagent.emit('free', socket, options);\n\t};\n\n\tsocket.on('free', onFree);\n\n\tconst onClose = () => {\n\t\tagent.removeSocket(socket, options);\n\t};\n\n\tsocket.on('close', onClose);\n\n\tconst onRemove = () => {\n\t\tagent.removeSocket(socket, options);\n\t\tsocket.off('close', onClose);\n\t\tsocket.off('free', onFree);\n\t\tsocket.off('agentRemove', onRemove);\n\t};\n\n\tsocket.on('agentRemove', onRemove);\n\n\tagent.emit('free', socket, options);\n};\n\nconst resolveProtocol = async options => {\n\tconst name = `${options.host}:${options.port}:${options.ALPNProtocols.sort()}`;\n\n\tif (!cache.has(name)) {\n\t\tif (queue.has(name)) {\n\t\t\tconst result = await queue.get(name);\n\t\t\treturn result.alpnProtocol;\n\t\t}\n\n\t\tconst {path, agent} = options;\n\t\toptions.path = options.socketPath;\n\n\t\tconst resultPromise = resolveALPN(options);\n\t\tqueue.set(name, resultPromise);\n\n\t\ttry {\n\t\t\tconst {socket, alpnProtocol} = await resultPromise;\n\t\t\tcache.set(name, alpnProtocol);\n\n\t\t\toptions.path = path;\n\n\t\t\tif (alpnProtocol === 'h2') {\n\t\t\t\t// https://github.com/nodejs/node/issues/33343\n\t\t\t\tsocket.destroy();\n\t\t\t} else {\n\t\t\t\tconst {globalAgent} = https;\n\t\t\t\tconst defaultCreateConnection = https.Agent.prototype.createConnection;\n\n\t\t\t\tif (agent) {\n\t\t\t\t\tif (agent.createConnection === defaultCreateConnection) {\n\t\t\t\t\t\tinstallSocket(agent, socket, options);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tsocket.destroy();\n\t\t\t\t\t}\n\t\t\t\t} else if (globalAgent.createConnection === defaultCreateConnection) {\n\t\t\t\t\tinstallSocket(globalAgent, socket, options);\n\t\t\t\t} else {\n\t\t\t\t\tsocket.destroy();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tqueue.delete(name);\n\n\t\t\treturn alpnProtocol;\n\t\t} catch (error) {\n\t\t\tqueue.delete(name);\n\n\t\t\tthrow error;\n\t\t}\n\t}\n\n\treturn cache.get(name);\n};\n\nmodule.exports = async (input, options, callback) => {\n\tif (typeof input === 'string' || input instanceof URL) {\n\t\tinput = urlToOptions(new URL(input));\n\t}\n\n\tif (typeof options === 'function') {\n\t\tcallback = options;\n\t\toptions = undefined;\n\t}\n\n\toptions = {\n\t\tALPNProtocols: ['h2', 'http/1.1'],\n\t\t...input,\n\t\t...options,\n\t\tresolveSocket: true\n\t};\n\n\tif (!Array.isArray(options.ALPNProtocols) || options.ALPNProtocols.length === 0) {\n\t\tthrow new Error('The `ALPNProtocols` option must be an Array with at least one entry');\n\t}\n\n\toptions.protocol = options.protocol || 'https:';\n\tconst isHttps = options.protocol === 'https:';\n\n\toptions.host = options.hostname || options.host || 'localhost';\n\toptions.session = options.tlsSession;\n\toptions.servername = options.servername || calculateServerName(options);\n\toptions.port = options.port || (isHttps ? 443 : 80);\n\toptions._defaultAgent = isHttps ? https.globalAgent : http.globalAgent;\n\n\tconst agents = options.agent;\n\n\tif (agents) {\n\t\tif (agents.addRequest) {\n\t\t\tthrow new Error('The `options.agent` object can contain only `http`, `https` or `http2` properties');\n\t\t}\n\n\t\toptions.agent = agents[isHttps ? 'https' : 'http'];\n\t}\n\n\tif (isHttps) {\n\t\tconst protocol = await resolveProtocol(options);\n\n\t\tif (protocol === 'h2') {\n\t\t\tif (agents) {\n\t\t\t\toptions.agent = agents.http2;\n\t\t\t}\n\n\t\t\treturn new Http2ClientRequest(options, callback);\n\t\t}\n\t}\n\n\treturn http.request(options, callback);\n};\n\nmodule.exports.protocolCache = cache;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http2-wrapper/source/auto.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http2-wrapper/source/client-request.js":
/*!*************************************************************!*\
  !*** ./node_modules/http2-wrapper/source/client-request.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst http2 = __webpack_require__(/*! http2 */ \"http2\");\nconst {Writable} = __webpack_require__(/*! stream */ \"stream\");\nconst {Agent, globalAgent} = __webpack_require__(/*! ./agent */ \"(rsc)/./node_modules/http2-wrapper/source/agent.js\");\nconst IncomingMessage = __webpack_require__(/*! ./incoming-message */ \"(rsc)/./node_modules/http2-wrapper/source/incoming-message.js\");\nconst urlToOptions = __webpack_require__(/*! ./utils/url-to-options */ \"(rsc)/./node_modules/http2-wrapper/source/utils/url-to-options.js\");\nconst proxyEvents = __webpack_require__(/*! ./utils/proxy-events */ \"(rsc)/./node_modules/http2-wrapper/source/utils/proxy-events.js\");\nconst isRequestPseudoHeader = __webpack_require__(/*! ./utils/is-request-pseudo-header */ \"(rsc)/./node_modules/http2-wrapper/source/utils/is-request-pseudo-header.js\");\nconst {\n\tERR_INVALID_ARG_TYPE,\n\tERR_INVALID_PROTOCOL,\n\tERR_HTTP_HEADERS_SENT,\n\tERR_INVALID_HTTP_TOKEN,\n\tERR_HTTP_INVALID_HEADER_VALUE,\n\tERR_INVALID_CHAR\n} = __webpack_require__(/*! ./utils/errors */ \"(rsc)/./node_modules/http2-wrapper/source/utils/errors.js\");\n\nconst {\n\tHTTP2_HEADER_STATUS,\n\tHTTP2_HEADER_METHOD,\n\tHTTP2_HEADER_PATH,\n\tHTTP2_METHOD_CONNECT\n} = http2.constants;\n\nconst kHeaders = Symbol('headers');\nconst kOrigin = Symbol('origin');\nconst kSession = Symbol('session');\nconst kOptions = Symbol('options');\nconst kFlushedHeaders = Symbol('flushedHeaders');\nconst kJobs = Symbol('jobs');\n\nconst isValidHttpToken = /^[\\^`\\-\\w!#$%&*+.|~]+$/;\nconst isInvalidHeaderValue = /[^\\t\\u0020-\\u007E\\u0080-\\u00FF]/;\n\nclass ClientRequest extends Writable {\n\tconstructor(input, options, callback) {\n\t\tsuper({\n\t\t\tautoDestroy: false\n\t\t});\n\n\t\tconst hasInput = typeof input === 'string' || input instanceof URL;\n\t\tif (hasInput) {\n\t\t\tinput = urlToOptions(input instanceof URL ? input : new URL(input));\n\t\t}\n\n\t\tif (typeof options === 'function' || options === undefined) {\n\t\t\t// (options, callback)\n\t\t\tcallback = options;\n\t\t\toptions = hasInput ? input : {...input};\n\t\t} else {\n\t\t\t// (input, options, callback)\n\t\t\toptions = {...input, ...options};\n\t\t}\n\n\t\tif (options.h2session) {\n\t\t\tthis[kSession] = options.h2session;\n\t\t} else if (options.agent === false) {\n\t\t\tthis.agent = new Agent({maxFreeSessions: 0});\n\t\t} else if (typeof options.agent === 'undefined' || options.agent === null) {\n\t\t\tif (typeof options.createConnection === 'function') {\n\t\t\t\t// This is a workaround - we don't have to create the session on our own.\n\t\t\t\tthis.agent = new Agent({maxFreeSessions: 0});\n\t\t\t\tthis.agent.createConnection = options.createConnection;\n\t\t\t} else {\n\t\t\t\tthis.agent = globalAgent;\n\t\t\t}\n\t\t} else if (typeof options.agent.request === 'function') {\n\t\t\tthis.agent = options.agent;\n\t\t} else {\n\t\t\tthrow new ERR_INVALID_ARG_TYPE('options.agent', ['Agent-like Object', 'undefined', 'false'], options.agent);\n\t\t}\n\n\t\tif (options.protocol && options.protocol !== 'https:') {\n\t\t\tthrow new ERR_INVALID_PROTOCOL(options.protocol, 'https:');\n\t\t}\n\n\t\tconst port = options.port || options.defaultPort || (this.agent && this.agent.defaultPort) || 443;\n\t\tconst host = options.hostname || options.host || 'localhost';\n\n\t\t// Don't enforce the origin via options. It may be changed in an Agent.\n\t\tdelete options.hostname;\n\t\tdelete options.host;\n\t\tdelete options.port;\n\n\t\tconst {timeout} = options;\n\t\toptions.timeout = undefined;\n\n\t\tthis[kHeaders] = Object.create(null);\n\t\tthis[kJobs] = [];\n\n\t\tthis.socket = null;\n\t\tthis.connection = null;\n\n\t\tthis.method = options.method || 'GET';\n\t\tthis.path = options.path;\n\n\t\tthis.res = null;\n\t\tthis.aborted = false;\n\t\tthis.reusedSocket = false;\n\n\t\tif (options.headers) {\n\t\t\tfor (const [header, value] of Object.entries(options.headers)) {\n\t\t\t\tthis.setHeader(header, value);\n\t\t\t}\n\t\t}\n\n\t\tif (options.auth && !('authorization' in this[kHeaders])) {\n\t\t\tthis[kHeaders].authorization = 'Basic ' + Buffer.from(options.auth).toString('base64');\n\t\t}\n\n\t\toptions.session = options.tlsSession;\n\t\toptions.path = options.socketPath;\n\n\t\tthis[kOptions] = options;\n\n\t\t// Clients that generate HTTP/2 requests directly SHOULD use the :authority pseudo-header field instead of the Host header field.\n\t\tif (port === 443) {\n\t\t\tthis[kOrigin] = `https://${host}`;\n\n\t\t\tif (!(':authority' in this[kHeaders])) {\n\t\t\t\tthis[kHeaders][':authority'] = host;\n\t\t\t}\n\t\t} else {\n\t\t\tthis[kOrigin] = `https://${host}:${port}`;\n\n\t\t\tif (!(':authority' in this[kHeaders])) {\n\t\t\t\tthis[kHeaders][':authority'] = `${host}:${port}`;\n\t\t\t}\n\t\t}\n\n\t\tif (timeout) {\n\t\t\tthis.setTimeout(timeout);\n\t\t}\n\n\t\tif (callback) {\n\t\t\tthis.once('response', callback);\n\t\t}\n\n\t\tthis[kFlushedHeaders] = false;\n\t}\n\n\tget method() {\n\t\treturn this[kHeaders][HTTP2_HEADER_METHOD];\n\t}\n\n\tset method(value) {\n\t\tif (value) {\n\t\t\tthis[kHeaders][HTTP2_HEADER_METHOD] = value.toUpperCase();\n\t\t}\n\t}\n\n\tget path() {\n\t\treturn this[kHeaders][HTTP2_HEADER_PATH];\n\t}\n\n\tset path(value) {\n\t\tif (value) {\n\t\t\tthis[kHeaders][HTTP2_HEADER_PATH] = value;\n\t\t}\n\t}\n\n\tget _mustNotHaveABody() {\n\t\treturn this.method === 'GET' || this.method === 'HEAD' || this.method === 'DELETE';\n\t}\n\n\t_write(chunk, encoding, callback) {\n\t\t// https://github.com/nodejs/node/blob/654df09ae0c5e17d1b52a900a545f0664d8c7627/lib/internal/http2/util.js#L148-L156\n\t\tif (this._mustNotHaveABody) {\n\t\t\tcallback(new Error('The GET, HEAD and DELETE methods must NOT have a body'));\n\t\t\t/* istanbul ignore next: Node.js 12 throws directly */\n\t\t\treturn;\n\t\t}\n\n\t\tthis.flushHeaders();\n\n\t\tconst callWrite = () => this._request.write(chunk, encoding, callback);\n\t\tif (this._request) {\n\t\t\tcallWrite();\n\t\t} else {\n\t\t\tthis[kJobs].push(callWrite);\n\t\t}\n\t}\n\n\t_final(callback) {\n\t\tif (this.destroyed) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.flushHeaders();\n\n\t\tconst callEnd = () => {\n\t\t\t// For GET, HEAD and DELETE\n\t\t\tif (this._mustNotHaveABody) {\n\t\t\t\tcallback();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis._request.end(callback);\n\t\t};\n\n\t\tif (this._request) {\n\t\t\tcallEnd();\n\t\t} else {\n\t\t\tthis[kJobs].push(callEnd);\n\t\t}\n\t}\n\n\tabort() {\n\t\tif (this.res && this.res.complete) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!this.aborted) {\n\t\t\tprocess.nextTick(() => this.emit('abort'));\n\t\t}\n\n\t\tthis.aborted = true;\n\n\t\tthis.destroy();\n\t}\n\n\t_destroy(error, callback) {\n\t\tif (this.res) {\n\t\t\tthis.res._dump();\n\t\t}\n\n\t\tif (this._request) {\n\t\t\tthis._request.destroy();\n\t\t}\n\n\t\tcallback(error);\n\t}\n\n\tasync flushHeaders() {\n\t\tif (this[kFlushedHeaders] || this.destroyed) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis[kFlushedHeaders] = true;\n\n\t\tconst isConnectMethod = this.method === HTTP2_METHOD_CONNECT;\n\n\t\t// The real magic is here\n\t\tconst onStream = stream => {\n\t\t\tthis._request = stream;\n\n\t\t\tif (this.destroyed) {\n\t\t\t\tstream.destroy();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Forwards `timeout`, `continue`, `close` and `error` events to this instance.\n\t\t\tif (!isConnectMethod) {\n\t\t\t\tproxyEvents(stream, this, ['timeout', 'continue', 'close', 'error']);\n\t\t\t}\n\n\t\t\t// Wait for the `finish` event. We don't want to emit the `response` event\n\t\t\t// before `request.end()` is called.\n\t\t\tconst waitForEnd = fn => {\n\t\t\t\treturn (...args) => {\n\t\t\t\t\tif (!this.writable && !this.destroyed) {\n\t\t\t\t\t\tfn(...args);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.once('finish', () => {\n\t\t\t\t\t\t\tfn(...args);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t};\n\n\t\t\t// This event tells we are ready to listen for the data.\n\t\t\tstream.once('response', waitForEnd((headers, flags, rawHeaders) => {\n\t\t\t\t// If we were to emit raw request stream, it would be as fast as the native approach.\n\t\t\t\t// Note that wrapping the raw stream in a Proxy instance won't improve the performance (already tested it).\n\t\t\t\tconst response = new IncomingMessage(this.socket, stream.readableHighWaterMark);\n\t\t\t\tthis.res = response;\n\n\t\t\t\tresponse.req = this;\n\t\t\t\tresponse.statusCode = headers[HTTP2_HEADER_STATUS];\n\t\t\t\tresponse.headers = headers;\n\t\t\t\tresponse.rawHeaders = rawHeaders;\n\n\t\t\t\tresponse.once('end', () => {\n\t\t\t\t\tif (this.aborted) {\n\t\t\t\t\t\tresponse.aborted = true;\n\t\t\t\t\t\tresponse.emit('aborted');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresponse.complete = true;\n\n\t\t\t\t\t\t// Has no effect, just be consistent with the Node.js behavior\n\t\t\t\t\t\tresponse.socket = null;\n\t\t\t\t\t\tresponse.connection = null;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tif (isConnectMethod) {\n\t\t\t\t\tresponse.upgrade = true;\n\n\t\t\t\t\t// The HTTP1 API says the socket is detached here,\n\t\t\t\t\t// but we can't do that so we pass the original HTTP2 request.\n\t\t\t\t\tif (this.emit('connect', response, stream, Buffer.alloc(0))) {\n\t\t\t\t\t\tthis.emit('close');\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// No listeners attached, destroy the original request.\n\t\t\t\t\t\tstream.destroy();\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// Forwards data\n\t\t\t\t\tstream.on('data', chunk => {\n\t\t\t\t\t\tif (!response._dumped && !response.push(chunk)) {\n\t\t\t\t\t\t\tstream.pause();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tstream.once('end', () => {\n\t\t\t\t\t\tresponse.push(null);\n\t\t\t\t\t});\n\n\t\t\t\t\tif (!this.emit('response', response)) {\n\t\t\t\t\t\t// No listeners attached, dump the response.\n\t\t\t\t\t\tresponse._dump();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}));\n\n\t\t\t// Emits `information` event\n\t\t\tstream.once('headers', waitForEnd(\n\t\t\t\theaders => this.emit('information', {statusCode: headers[HTTP2_HEADER_STATUS]})\n\t\t\t));\n\n\t\t\tstream.once('trailers', waitForEnd((trailers, flags, rawTrailers) => {\n\t\t\t\tconst {res} = this;\n\n\t\t\t\t// Assigns trailers to the response object.\n\t\t\t\tres.trailers = trailers;\n\t\t\t\tres.rawTrailers = rawTrailers;\n\t\t\t}));\n\n\t\t\tconst {socket} = stream.session;\n\t\t\tthis.socket = socket;\n\t\t\tthis.connection = socket;\n\n\t\t\tfor (const job of this[kJobs]) {\n\t\t\t\tjob();\n\t\t\t}\n\n\t\t\tthis.emit('socket', this.socket);\n\t\t};\n\n\t\t// Makes a HTTP2 request\n\t\tif (this[kSession]) {\n\t\t\ttry {\n\t\t\t\tonStream(this[kSession].request(this[kHeaders]));\n\t\t\t} catch (error) {\n\t\t\t\tthis.emit('error', error);\n\t\t\t}\n\t\t} else {\n\t\t\tthis.reusedSocket = true;\n\n\t\t\ttry {\n\t\t\t\tonStream(await this.agent.request(this[kOrigin], this[kOptions], this[kHeaders]));\n\t\t\t} catch (error) {\n\t\t\t\tthis.emit('error', error);\n\t\t\t}\n\t\t}\n\t}\n\n\tgetHeader(name) {\n\t\tif (typeof name !== 'string') {\n\t\t\tthrow new ERR_INVALID_ARG_TYPE('name', 'string', name);\n\t\t}\n\n\t\treturn this[kHeaders][name.toLowerCase()];\n\t}\n\n\tget headersSent() {\n\t\treturn this[kFlushedHeaders];\n\t}\n\n\tremoveHeader(name) {\n\t\tif (typeof name !== 'string') {\n\t\t\tthrow new ERR_INVALID_ARG_TYPE('name', 'string', name);\n\t\t}\n\n\t\tif (this.headersSent) {\n\t\t\tthrow new ERR_HTTP_HEADERS_SENT('remove');\n\t\t}\n\n\t\tdelete this[kHeaders][name.toLowerCase()];\n\t}\n\n\tsetHeader(name, value) {\n\t\tif (this.headersSent) {\n\t\t\tthrow new ERR_HTTP_HEADERS_SENT('set');\n\t\t}\n\n\t\tif (typeof name !== 'string' || (!isValidHttpToken.test(name) && !isRequestPseudoHeader(name))) {\n\t\t\tthrow new ERR_INVALID_HTTP_TOKEN('Header name', name);\n\t\t}\n\n\t\tif (typeof value === 'undefined') {\n\t\t\tthrow new ERR_HTTP_INVALID_HEADER_VALUE(value, name);\n\t\t}\n\n\t\tif (isInvalidHeaderValue.test(value)) {\n\t\t\tthrow new ERR_INVALID_CHAR('header content', name);\n\t\t}\n\n\t\tthis[kHeaders][name.toLowerCase()] = value;\n\t}\n\n\tsetNoDelay() {\n\t\t// HTTP2 sockets cannot be malformed, do nothing.\n\t}\n\n\tsetSocketKeepAlive() {\n\t\t// HTTP2 sockets cannot be malformed, do nothing.\n\t}\n\n\tsetTimeout(ms, callback) {\n\t\tconst applyTimeout = () => this._request.setTimeout(ms, callback);\n\n\t\tif (this._request) {\n\t\t\tapplyTimeout();\n\t\t} else {\n\t\t\tthis[kJobs].push(applyTimeout);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tget maxHeadersCount() {\n\t\tif (!this.destroyed && this._request) {\n\t\t\treturn this._request.session.localSettings.maxHeaderListSize;\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\tset maxHeadersCount(_value) {\n\t\t// Updating HTTP2 settings would affect all requests, do nothing.\n\t}\n}\n\nmodule.exports = ClientRequest;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http2-wrapper/source/client-request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http2-wrapper/source/incoming-message.js":
/*!***************************************************************!*\
  !*** ./node_modules/http2-wrapper/source/incoming-message.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst {Readable} = __webpack_require__(/*! stream */ \"stream\");\n\nclass IncomingMessage extends Readable {\n\tconstructor(socket, highWaterMark) {\n\t\tsuper({\n\t\t\thighWaterMark,\n\t\t\tautoDestroy: false\n\t\t});\n\n\t\tthis.statusCode = null;\n\t\tthis.statusMessage = '';\n\t\tthis.httpVersion = '2.0';\n\t\tthis.httpVersionMajor = 2;\n\t\tthis.httpVersionMinor = 0;\n\t\tthis.headers = {};\n\t\tthis.trailers = {};\n\t\tthis.req = null;\n\n\t\tthis.aborted = false;\n\t\tthis.complete = false;\n\t\tthis.upgrade = null;\n\n\t\tthis.rawHeaders = [];\n\t\tthis.rawTrailers = [];\n\n\t\tthis.socket = socket;\n\t\tthis.connection = socket;\n\n\t\tthis._dumped = false;\n\t}\n\n\t_destroy(error) {\n\t\tthis.req._request.destroy(error);\n\t}\n\n\tsetTimeout(ms, callback) {\n\t\tthis.req.setTimeout(ms, callback);\n\t\treturn this;\n\t}\n\n\t_dump() {\n\t\tif (!this._dumped) {\n\t\t\tthis._dumped = true;\n\n\t\t\tthis.removeAllListeners('data');\n\t\t\tthis.resume();\n\t\t}\n\t}\n\n\t_read() {\n\t\tif (this.req) {\n\t\t\tthis.req._request.resume();\n\t\t}\n\t}\n}\n\nmodule.exports = IncomingMessage;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http2-wrapper/source/incoming-message.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http2-wrapper/source/index.js":
/*!****************************************************!*\
  !*** ./node_modules/http2-wrapper/source/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst http2 = __webpack_require__(/*! http2 */ \"http2\");\nconst agent = __webpack_require__(/*! ./agent */ \"(rsc)/./node_modules/http2-wrapper/source/agent.js\");\nconst ClientRequest = __webpack_require__(/*! ./client-request */ \"(rsc)/./node_modules/http2-wrapper/source/client-request.js\");\nconst IncomingMessage = __webpack_require__(/*! ./incoming-message */ \"(rsc)/./node_modules/http2-wrapper/source/incoming-message.js\");\nconst auto = __webpack_require__(/*! ./auto */ \"(rsc)/./node_modules/http2-wrapper/source/auto.js\");\n\nconst request = (url, options, callback) => {\n\treturn new ClientRequest(url, options, callback);\n};\n\nconst get = (url, options, callback) => {\n\t// eslint-disable-next-line unicorn/prevent-abbreviations\n\tconst req = new ClientRequest(url, options, callback);\n\treq.end();\n\n\treturn req;\n};\n\nmodule.exports = {\n\t...http2,\n\tClientRequest,\n\tIncomingMessage,\n\t...agent,\n\trequest,\n\tget,\n\tauto\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaHR0cDItd3JhcHBlci9zb3VyY2UvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYixjQUFjLG1CQUFPLENBQUMsb0JBQU87QUFDN0IsY0FBYyxtQkFBTyxDQUFDLG1FQUFTO0FBQy9CLHNCQUFzQixtQkFBTyxDQUFDLHFGQUFrQjtBQUNoRCx3QkFBd0IsbUJBQU8sQ0FBQyx5RkFBb0I7QUFDcEQsYUFBYSxtQkFBTyxDQUFDLGlFQUFROztBQUU3QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vbm9kZV9tb2R1bGVzL2h0dHAyLXdyYXBwZXIvc291cmNlL2luZGV4LmpzPzlmMjYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuY29uc3QgaHR0cDIgPSByZXF1aXJlKCdodHRwMicpO1xuY29uc3QgYWdlbnQgPSByZXF1aXJlKCcuL2FnZW50Jyk7XG5jb25zdCBDbGllbnRSZXF1ZXN0ID0gcmVxdWlyZSgnLi9jbGllbnQtcmVxdWVzdCcpO1xuY29uc3QgSW5jb21pbmdNZXNzYWdlID0gcmVxdWlyZSgnLi9pbmNvbWluZy1tZXNzYWdlJyk7XG5jb25zdCBhdXRvID0gcmVxdWlyZSgnLi9hdXRvJyk7XG5cbmNvbnN0IHJlcXVlc3QgPSAodXJsLCBvcHRpb25zLCBjYWxsYmFjaykgPT4ge1xuXHRyZXR1cm4gbmV3IENsaWVudFJlcXVlc3QodXJsLCBvcHRpb25zLCBjYWxsYmFjayk7XG59O1xuXG5jb25zdCBnZXQgPSAodXJsLCBvcHRpb25zLCBjYWxsYmFjaykgPT4ge1xuXHQvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgdW5pY29ybi9wcmV2ZW50LWFiYnJldmlhdGlvbnNcblx0Y29uc3QgcmVxID0gbmV3IENsaWVudFJlcXVlc3QodXJsLCBvcHRpb25zLCBjYWxsYmFjayk7XG5cdHJlcS5lbmQoKTtcblxuXHRyZXR1cm4gcmVxO1xufTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG5cdC4uLmh0dHAyLFxuXHRDbGllbnRSZXF1ZXN0LFxuXHRJbmNvbWluZ01lc3NhZ2UsXG5cdC4uLmFnZW50LFxuXHRyZXF1ZXN0LFxuXHRnZXQsXG5cdGF1dG9cbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http2-wrapper/source/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http2-wrapper/source/utils/calculate-server-name.js":
/*!**************************************************************************!*\
  !*** ./node_modules/http2-wrapper/source/utils/calculate-server-name.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst net = __webpack_require__(/*! net */ \"net\");\n/* istanbul ignore file: https://github.com/nodejs/node/blob/v13.0.1/lib/_http_agent.js */\n\nmodule.exports = options => {\n\tlet servername = options.host;\n\tconst hostHeader = options.headers && options.headers.host;\n\n\tif (hostHeader) {\n\t\tif (hostHeader.startsWith('[')) {\n\t\t\tconst index = hostHeader.indexOf(']');\n\t\t\tif (index === -1) {\n\t\t\t\tservername = hostHeader;\n\t\t\t} else {\n\t\t\t\tservername = hostHeader.slice(1, -1);\n\t\t\t}\n\t\t} else {\n\t\t\tservername = hostHeader.split(':', 1)[0];\n\t\t}\n\t}\n\n\tif (net.isIP(servername)) {\n\t\treturn '';\n\t}\n\n\treturn servername;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaHR0cDItd3JhcHBlci9zb3VyY2UvdXRpbHMvY2FsY3VsYXRlLXNlcnZlci1uYW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsWUFBWSxtQkFBTyxDQUFDLGdCQUFLO0FBQ3pCOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL25vZGVfbW9kdWxlcy9odHRwMi13cmFwcGVyL3NvdXJjZS91dGlscy9jYWxjdWxhdGUtc2VydmVyLW5hbWUuanM/MDRiNyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5jb25zdCBuZXQgPSByZXF1aXJlKCduZXQnKTtcbi8qIGlzdGFuYnVsIGlnbm9yZSBmaWxlOiBodHRwczovL2dpdGh1Yi5jb20vbm9kZWpzL25vZGUvYmxvYi92MTMuMC4xL2xpYi9faHR0cF9hZ2VudC5qcyAqL1xuXG5tb2R1bGUuZXhwb3J0cyA9IG9wdGlvbnMgPT4ge1xuXHRsZXQgc2VydmVybmFtZSA9IG9wdGlvbnMuaG9zdDtcblx0Y29uc3QgaG9zdEhlYWRlciA9IG9wdGlvbnMuaGVhZGVycyAmJiBvcHRpb25zLmhlYWRlcnMuaG9zdDtcblxuXHRpZiAoaG9zdEhlYWRlcikge1xuXHRcdGlmIChob3N0SGVhZGVyLnN0YXJ0c1dpdGgoJ1snKSkge1xuXHRcdFx0Y29uc3QgaW5kZXggPSBob3N0SGVhZGVyLmluZGV4T2YoJ10nKTtcblx0XHRcdGlmIChpbmRleCA9PT0gLTEpIHtcblx0XHRcdFx0c2VydmVybmFtZSA9IGhvc3RIZWFkZXI7XG5cdFx0XHR9IGVsc2Uge1xuXHRcdFx0XHRzZXJ2ZXJuYW1lID0gaG9zdEhlYWRlci5zbGljZSgxLCAtMSk7XG5cdFx0XHR9XG5cdFx0fSBlbHNlIHtcblx0XHRcdHNlcnZlcm5hbWUgPSBob3N0SGVhZGVyLnNwbGl0KCc6JywgMSlbMF07XG5cdFx0fVxuXHR9XG5cblx0aWYgKG5ldC5pc0lQKHNlcnZlcm5hbWUpKSB7XG5cdFx0cmV0dXJuICcnO1xuXHR9XG5cblx0cmV0dXJuIHNlcnZlcm5hbWU7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http2-wrapper/source/utils/calculate-server-name.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http2-wrapper/source/utils/errors.js":
/*!***********************************************************!*\
  !*** ./node_modules/http2-wrapper/source/utils/errors.js ***!
  \***********************************************************/
/***/ ((module) => {

eval("\n/* istanbul ignore file: https://github.com/nodejs/node/blob/master/lib/internal/errors.js */\n\nconst makeError = (Base, key, getMessage) => {\n\tmodule.exports[key] = class NodeError extends Base {\n\t\tconstructor(...args) {\n\t\t\tsuper(typeof getMessage === 'string' ? getMessage : getMessage(args));\n\t\t\tthis.name = `${super.name} [${key}]`;\n\t\t\tthis.code = key;\n\t\t}\n\t};\n};\n\nmakeError(TypeError, 'ERR_INVALID_ARG_TYPE', args => {\n\tconst type = args[0].includes('.') ? 'property' : 'argument';\n\n\tlet valid = args[1];\n\tconst isManyTypes = Array.isArray(valid);\n\n\tif (isManyTypes) {\n\t\tvalid = `${valid.slice(0, -1).join(', ')} or ${valid.slice(-1)}`;\n\t}\n\n\treturn `The \"${args[0]}\" ${type} must be ${isManyTypes ? 'one of' : 'of'} type ${valid}. Received ${typeof args[2]}`;\n});\n\nmakeError(TypeError, 'ERR_INVALID_PROTOCOL', args => {\n\treturn `Protocol \"${args[0]}\" not supported. Expected \"${args[1]}\"`;\n});\n\nmakeError(Error, 'ERR_HTTP_HEADERS_SENT', args => {\n\treturn `Cannot ${args[0]} headers after they are sent to the client`;\n});\n\nmakeError(TypeError, 'ERR_INVALID_HTTP_TOKEN', args => {\n\treturn `${args[0]} must be a valid HTTP token [${args[1]}]`;\n});\n\nmakeError(TypeError, 'ERR_HTTP_INVALID_HEADER_VALUE', args => {\n\treturn `Invalid value \"${args[0]} for header \"${args[1]}\"`;\n});\n\nmakeError(TypeError, 'ERR_INVALID_CHAR', args => {\n\treturn `Invalid character in ${args[0]} [${args[1]}]`;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http2-wrapper/source/utils/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http2-wrapper/source/utils/is-request-pseudo-header.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/http2-wrapper/source/utils/is-request-pseudo-header.js ***!
  \*****************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = header => {\n\tswitch (header) {\n\t\tcase ':method':\n\t\tcase ':scheme':\n\t\tcase ':authority':\n\t\tcase ':path':\n\t\t\treturn true;\n\t\tdefault:\n\t\t\treturn false;\n\t}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaHR0cDItd3JhcHBlci9zb3VyY2UvdXRpbHMvaXMtcmVxdWVzdC1wc2V1ZG8taGVhZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vbm9kZV9tb2R1bGVzL2h0dHAyLXdyYXBwZXIvc291cmNlL3V0aWxzL2lzLXJlcXVlc3QtcHNldWRvLWhlYWRlci5qcz8xNGUzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxubW9kdWxlLmV4cG9ydHMgPSBoZWFkZXIgPT4ge1xuXHRzd2l0Y2ggKGhlYWRlcikge1xuXHRcdGNhc2UgJzptZXRob2QnOlxuXHRcdGNhc2UgJzpzY2hlbWUnOlxuXHRcdGNhc2UgJzphdXRob3JpdHknOlxuXHRcdGNhc2UgJzpwYXRoJzpcblx0XHRcdHJldHVybiB0cnVlO1xuXHRcdGRlZmF1bHQ6XG5cdFx0XHRyZXR1cm4gZmFsc2U7XG5cdH1cbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http2-wrapper/source/utils/is-request-pseudo-header.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http2-wrapper/source/utils/proxy-events.js":
/*!*****************************************************************!*\
  !*** ./node_modules/http2-wrapper/source/utils/proxy-events.js ***!
  \*****************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = (from, to, events) => {\n\tfor (const event of events) {\n\t\tfrom.on(event, (...args) => to.emit(event, ...args));\n\t}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaHR0cDItd3JhcHBlci9zb3VyY2UvdXRpbHMvcHJveHktZXZlbnRzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vbm9kZV9tb2R1bGVzL2h0dHAyLXdyYXBwZXIvc291cmNlL3V0aWxzL3Byb3h5LWV2ZW50cy5qcz8xYTUzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxubW9kdWxlLmV4cG9ydHMgPSAoZnJvbSwgdG8sIGV2ZW50cykgPT4ge1xuXHRmb3IgKGNvbnN0IGV2ZW50IG9mIGV2ZW50cykge1xuXHRcdGZyb20ub24oZXZlbnQsICguLi5hcmdzKSA9PiB0by5lbWl0KGV2ZW50LCAuLi5hcmdzKSk7XG5cdH1cbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http2-wrapper/source/utils/proxy-events.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/http2-wrapper/source/utils/url-to-options.js":
/*!*******************************************************************!*\
  !*** ./node_modules/http2-wrapper/source/utils/url-to-options.js ***!
  \*******************************************************************/
/***/ ((module) => {

eval("\n/* istanbul ignore file: https://github.com/nodejs/node/blob/a91293d4d9ab403046ab5eb022332e4e3d249bd3/lib/internal/url.js#L1257 */\n\nmodule.exports = url => {\n\tconst options = {\n\t\tprotocol: url.protocol,\n\t\thostname: typeof url.hostname === 'string' && url.hostname.startsWith('[') ? url.hostname.slice(1, -1) : url.hostname,\n\t\thost: url.host,\n\t\thash: url.hash,\n\t\tsearch: url.search,\n\t\tpathname: url.pathname,\n\t\thref: url.href,\n\t\tpath: `${url.pathname || ''}${url.search || ''}`\n\t};\n\n\tif (typeof url.port === 'string' && url.port.length !== 0) {\n\t\toptions.port = Number(url.port);\n\t}\n\n\tif (url.username || url.password) {\n\t\toptions.auth = `${url.username || ''}:${url.password || ''}`;\n\t}\n\n\treturn options;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaHR0cDItd3JhcHBlci9zb3VyY2UvdXRpbHMvdXJsLXRvLW9wdGlvbnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLG1CQUFtQixFQUFFLGlCQUFpQjtBQUNqRDs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0IsbUJBQW1CLEdBQUcsbUJBQW1CO0FBQzdEOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vbm9kZV9tb2R1bGVzL2h0dHAyLXdyYXBwZXIvc291cmNlL3V0aWxzL3VybC10by1vcHRpb25zLmpzPzI2NzUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuLyogaXN0YW5idWwgaWdub3JlIGZpbGU6IGh0dHBzOi8vZ2l0aHViLmNvbS9ub2RlanMvbm9kZS9ibG9iL2E5MTI5M2Q0ZDlhYjQwMzA0NmFiNWViMDIyMzMyZTRlM2QyNDliZDMvbGliL2ludGVybmFsL3VybC5qcyNMMTI1NyAqL1xuXG5tb2R1bGUuZXhwb3J0cyA9IHVybCA9PiB7XG5cdGNvbnN0IG9wdGlvbnMgPSB7XG5cdFx0cHJvdG9jb2w6IHVybC5wcm90b2NvbCxcblx0XHRob3N0bmFtZTogdHlwZW9mIHVybC5ob3N0bmFtZSA9PT0gJ3N0cmluZycgJiYgdXJsLmhvc3RuYW1lLnN0YXJ0c1dpdGgoJ1snKSA/IHVybC5ob3N0bmFtZS5zbGljZSgxLCAtMSkgOiB1cmwuaG9zdG5hbWUsXG5cdFx0aG9zdDogdXJsLmhvc3QsXG5cdFx0aGFzaDogdXJsLmhhc2gsXG5cdFx0c2VhcmNoOiB1cmwuc2VhcmNoLFxuXHRcdHBhdGhuYW1lOiB1cmwucGF0aG5hbWUsXG5cdFx0aHJlZjogdXJsLmhyZWYsXG5cdFx0cGF0aDogYCR7dXJsLnBhdGhuYW1lIHx8ICcnfSR7dXJsLnNlYXJjaCB8fCAnJ31gXG5cdH07XG5cblx0aWYgKHR5cGVvZiB1cmwucG9ydCA9PT0gJ3N0cmluZycgJiYgdXJsLnBvcnQubGVuZ3RoICE9PSAwKSB7XG5cdFx0b3B0aW9ucy5wb3J0ID0gTnVtYmVyKHVybC5wb3J0KTtcblx0fVxuXG5cdGlmICh1cmwudXNlcm5hbWUgfHwgdXJsLnBhc3N3b3JkKSB7XG5cdFx0b3B0aW9ucy5hdXRoID0gYCR7dXJsLnVzZXJuYW1lIHx8ICcnfToke3VybC5wYXNzd29yZCB8fCAnJ31gO1xuXHR9XG5cblx0cmV0dXJuIG9wdGlvbnM7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http2-wrapper/source/utils/url-to-options.js\n");

/***/ })

};
;