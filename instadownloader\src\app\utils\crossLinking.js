/**
 * Cross-linking utilities for SEO-friendly navigation between APK and AI Tools sites
 */

import { generateSmartAPKURL } from './toolMappingService';

// Domain configuration
export const DOMAINS = {
  APK_SITE: process.env.NEXT_PUBLIC_APK_DOMAIN || 'https://apkdemo1.com',
  AI_TOOLS_SITE: process.env.NEXT_PUBLIC_AI_TOOLS_DOMAIN || 'https://apk3.demo1.com'
};

// Check if cross-linking is enabled
export const isCrossLinkingEnabled = () => {
  return process.env.NEXT_PUBLIC_ENABLE_CROSS_LINKING === 'true';
};

/**
 * Category mapping between AI tool categories and APK categories
 */
export const AI_TO_APK_MAPPING = {
  // AI tool categories to APK categories
  'productivity': ['productivity', 'business', 'tools'],
  'social-media': ['communication', 'social', 'dating'],
  'email-generator': ['communication', 'business'],
  'writing-generator': ['productivity', 'education', 'news-magazines'],
  'blog-generator': ['news-magazines', 'productivity'],
  'business': ['business', 'productivity', 'finance'],
  'automation': ['tools', 'productivity'],
  'art': ['art-design', 'photography'],
  'graphic-design': ['art-design', 'photography'],
  'image-generation': ['art-design', 'photography'],
  'video-generation': ['video-players', 'entertainment'],
  'music': ['music-audio', 'entertainment'],
  'education': ['education', 'books-reference'],
  'research': ['education', 'books-reference'],
  'health': ['health-fitness', 'medical'],
  'lifestyle': ['lifestyle', 'health-fitness'],
  'finace': ['finance', 'business'],
  'startup': ['business', 'productivity'],
  'marketing': ['business', 'social'],
  'developer-tools': ['tools', 'libraries-demo'],
  'communication': ['communication', 'social'],
  'translation': ['education', 'tools'],
  'avatar': ['personalization', 'art-design'],
  'anime': ['entertainment', 'art-design']
};

/**
 * Get relevant APK categories for an AI tool category
 */
export const getRelevantAPKCategories = (aiCategory) => {
  if (!aiCategory) return ['productivity']; // Default fallback
  
  const normalizedCategory = aiCategory.toLowerCase().replace(/[^a-z0-9]/g, '-');
  return AI_TO_APK_MAPPING[normalizedCategory] || ['productivity'];
};

/**
 * Generate APK download URL for an AI tool (Enhanced with smart mapping)
 */
export const generateAPKURL = (toolDetails) => {
  if (!isCrossLinkingEnabled()) return null;

  const baseURL = DOMAINS.APK_SITE;

  // Use smart mapping service for better matching
  return generateSmartAPKURL(toolDetails, baseURL);
};

/**
 * Generate AI tools discovery URL for an app
 */
export const generateAIToolsURL = (appDetails) => {
  if (!isCrossLinkingEnabled()) return null;
  
  const baseURL = DOMAINS.AI_TOOLS_SITE;
  
  // If we have app category, link to relevant AI tools category
  if (appDetails?.category) {
    // Reverse mapping from APK to AI categories
    const apkCategory = appDetails.category.toLowerCase();
    let relevantCategory = 'productivity'; // default
    
    // Find matching AI category
    for (const [aiCat, apkCats] of Object.entries(AI_TO_APK_MAPPING)) {
      if (apkCats.includes(apkCategory)) {
        relevantCategory = aiCat;
        break;
      }
    }
    
    return `${baseURL}/tools/${relevantCategory}?ref=apk&app=${encodeURIComponent(appDetails.appId || '')}`;
  }
  
  // Fallback to main AI tools page
  return `${baseURL}/tool?ref=apk&app=${encodeURIComponent(appDetails?.appId || '')}`;
};

/**
 * Generate SEO-friendly anchor text for cross-links
 */
export const generateAnchorText = (type, details) => {
  if (type === 'ai-tools') {
    const category = details?.category || 'productivity';
    return `Discover AI ${category.charAt(0).toUpperCase() + category.slice(1)} Tools`;
  } else if (type === 'apk-download') {
    const toolName = details?.title || 'Related Apps';
    return `Download ${toolName} APK & Similar Apps`;
  }
  return 'Explore More';
};

/**
 * Track cross-link clicks for analytics
 */
export const trackCrossLinkClick = (source, target, details) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'cross_link_click', {
      event_category: 'Cross Linking',
      event_label: `${source} to ${target}`,
      custom_parameter_1: details?.appId || details?.toolId || 'unknown'
    });
  }
};
