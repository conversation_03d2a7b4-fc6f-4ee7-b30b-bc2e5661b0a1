@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
} 

 /* body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
} */

body{
  background: #eee;
}

/* @layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}  */
.container {
  max-width: 1280px;
}
ins.adsbygoogle.adsbygoogle-noablate:nth-child(12) {
    left: 137px !important;
}
.sidebar-container {
  display: flex;
  flex-direction: column; /* Default to column for mobile view */
}

@media (min-width: 1024px) { /* Tailwind's lg breakpoint */
  .sidebar-container {
    flex-direction: column; /* Maintain column direction for large screens */
  }
}

@media (max-width: 1023px) { /* Tailwind's sm breakpoint */
  .sidebar-container {
    flex-direction: column-reverse; /* Reverse order on mobile screens */
  }
}