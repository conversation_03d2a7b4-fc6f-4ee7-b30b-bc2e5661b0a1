"use client";
import React, { useState,useEffect } from "react";
import {
  Select as MuiSelect,
  MenuItem,
  Button,
  Box,
  InputLabel,
  Grid,
  Autocomplete,
  TextField,
  FormControl,
} from "@mui/material";
import axios from "axios";
import { connect } from "react-redux";
import CountrySelect from "./country";
import SearchIcon from '@mui/icons-material/Search';
import { searchApps, selectedUserCountry } from "@/app/redux/slice/topAppSlice";
const SearchApps = ({ searchAppsLoading, dispatch, userCountry }) => {
  
  const [searchTerm, setSearchTerm] = useState("");
  const [storeType, setStoreType] = useState("playStore");
  const [searchError, setSearchError] = useState("");
  const [urlCountryCode, setUrlCountryCode] = useState("");
  const [price, setPrice] = useState("all");
  const [country, setCountry] = useState(userCountry.country);
  const [countryCode, setCountryCode] = useState(userCountry.countryCode);
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchTerm || searchTerm === "") {
      setSearchError("Please enter app name*");
      return;
    }
    setSearchError("");
    try {
      dispatch(searchApps({ searchTerm, storeType, price, countryCode: countryCode || urlCountryCode }));
    } catch (error) {
      console.error("Error:", error);
    }
    setSearchTerm("");
    setSuggestions([]);
  };

  const getAppIdAndCountryCode = (url) => {
    try {
      const parsedUrl = new URL(url);
      const searchParams = parsedUrl.searchParams;

      const appId = searchParams.get('id');

      const countryCode = searchParams.get('gl');

      return { appId, countryCode };
    } catch (error) {
      return { appId: null, countryCode: null };
    }
  };
  const handleInputChange = async (event, newInputValue) => {
    if (newInputValue && newInputValue.includes('http')) {
      const { appId, countryCode } = getAppIdAndCountryCode(newInputValue);
      setSearchTerm(appId);
      setUrlCountryCode(countryCode);
    } else {
      setSearchTerm(newInputValue);
    }
    if (newInputValue === "") {
      setSuggestions([]);
      setLoading(false);
      return;
    }
    const delayDebounceFn = setTimeout(async () => {
      setLoading(true);
      try {
        const response = await axios.post("/api/suggestion", {
          searchTerm: newInputValue,
          storeType,
        });

        setSuggestions(response.data.suggestions);
      } catch (error) {
        console.error("Error fetching suggestions:", error);
      } finally {
        setLoading(false);
      }
    }, 1000);

    return () => clearTimeout(delayDebounceFn);
  };
  return (
    <>
      <Grid item xs={12} md={8} lg={8}>
        <form
          onSubmit={handleSearch}
          style={{
            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
            borderRadius: "8px",
            padding: "1rem",
            background: "white",
            border: "1px solid #e4e4e4",
            width: "100%",
          }}
        >
          <Box
            sx={{
              display: { xs: "block", md: "flex" },
              justifyContent: "space-between",
              flexWrap: "wrap",
            }}
          >
            <Grid container spacing={2}>
              <Grid item xs={12} sm={2} md={2}>
                <FormControl fullWidth>
                  <InputLabel id="price">Price</InputLabel>
                  <MuiSelect
                    labelId="price"
                    value={price}
                    label="price"
                    onChange={(e) => setPrice(e.target.value)}
                  >
                    <MenuItem value="all">All</MenuItem>
                    <MenuItem value="free">Free</MenuItem>
                    <MenuItem value="paid">Paid</MenuItem>
                  </MuiSelect>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3} md={3}>
              <CountrySelect setCountryCode={setCountryCode} setCountry={setCountry} countryCode={countryCode} country={country} />
              </Grid>
              <Grid item xs={12} sm={6} md={5}>
                <Autocomplete
                  value={searchTerm}
                  onInputChange={handleInputChange}
                  getOptionLabel={(option) => {
                    if (typeof option === "string") {
                      return option;
                    }
                    if (option.inputValue) {
                      return option.inputValue;
                    }
                    return option;
                  }}
                  filterOptions={(options, params) => {
                    const filtered = options.filter((option) => {
                      const inputValue = params.inputValue.toLowerCase();
                      return (
                        option.toLowerCase().includes(inputValue) &&
                        option.toLowerCase() !== inputValue
                      );
                    });
                    return filtered;
                  }}
                  id="free-solo-with-text-demo"
                  selectOnFocus
                  clearOnBlur
                  loading={loading}
                  freeSolo
                  handleHomeEndKeys
                  options={suggestions}
                  renderOption={(props, option) => (
                    <li {...props}>{option}</li>
                  )}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Search for Apps or App ID"
                      error={!!searchError}
                      helperText={searchError || ""}
                      sx={{ width: "100%" }}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} sm={2} md={2} sx={{ display: "flex", justifyContent: "center" }}>
                <Button
                  type="submit"
                  sx={{
                    background: "#00A3FF", "&:hover": {
                      backgroundColor: "#00A3FF",
                      color: "#fff",
                    }, textTransform: "none", letterSpacing: "2px", borderRadius: "12px"
                  }}
                  disabled={searchAppsLoading ? true : false}
                  variant="contained"
                >
                  {searchAppsLoading ? "Searching.." : <><SearchIcon />&nbsp;Search</>}
                </Button>
              </Grid>
            </Grid>
          </Box>
        </form>
      </Grid>
    </>
  );
};

const mapStateToProps = ({ topApps }) => ({
  searchAppsLoading: topApps?.searchAppsLoading,
  userCountry: topApps.userCountry
});

export default connect(mapStateToProps)(SearchApps);