"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-tick";
exports.ids = ["vendor-chunks/next-tick"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-tick/index.js":
/*!*****************************************!*\
  !*** ./node_modules/next-tick/index.js ***!
  \*****************************************/
/***/ ((module) => {

eval("\n\nvar ensureCallable = function (fn) {\n\tif (typeof fn !== 'function') throw new TypeError(fn + \" is not a function\");\n\treturn fn;\n};\n\nvar byObserver = function (Observer) {\n\tvar node = document.createTextNode(''), queue, currentQueue, i = 0;\n\tnew Observer(function () {\n\t\tvar callback;\n\t\tif (!queue) {\n\t\t\tif (!currentQueue) return;\n\t\t\tqueue = currentQueue;\n\t\t} else if (currentQueue) {\n\t\t\tqueue = currentQueue.concat(queue);\n\t\t}\n\t\tcurrentQueue = queue;\n\t\tqueue = null;\n\t\tif (typeof currentQueue === 'function') {\n\t\t\tcallback = currentQueue;\n\t\t\tcurrentQueue = null;\n\t\t\tcallback();\n\t\t\treturn;\n\t\t}\n\t\tnode.data = (i = ++i % 2); // Invoke other batch, to handle leftover callbacks in case of crash\n\t\twhile (currentQueue) {\n\t\t\tcallback = currentQueue.shift();\n\t\t\tif (!currentQueue.length) currentQueue = null;\n\t\t\tcallback();\n\t\t}\n\t}).observe(node, { characterData: true });\n\treturn function (fn) {\n\t\tensureCallable(fn);\n\t\tif (queue) {\n\t\t\tif (typeof queue === 'function') queue = [queue, fn];\n\t\t\telse queue.push(fn);\n\t\t\treturn;\n\t\t}\n\t\tqueue = fn;\n\t\tnode.data = (i = ++i % 2);\n\t};\n};\n\nmodule.exports = (function () {\n\t// Node.js\n\tif ((typeof process === 'object') && process && (typeof process.nextTick === 'function')) {\n\t\treturn process.nextTick;\n\t}\n\n\t// queueMicrotask\n\tif (typeof queueMicrotask === \"function\") {\n\t\treturn function (cb) { queueMicrotask(ensureCallable(cb)); };\n\t}\n\n\t// MutationObserver\n\tif ((typeof document === 'object') && document) {\n\t\tif (typeof MutationObserver === 'function') return byObserver(MutationObserver);\n\t\tif (typeof WebKitMutationObserver === 'function') return byObserver(WebKitMutationObserver);\n\t}\n\n\t// W3C Draft\n\t// http://dvcs.w3.org/hg/webperf/raw-file/tip/specs/setImmediate/Overview.html\n\tif (typeof setImmediate === 'function') {\n\t\treturn function (cb) { setImmediate(ensureCallable(cb)); };\n\t}\n\n\t// Wide available standard\n\tif ((typeof setTimeout === 'function') || (typeof setTimeout === 'object')) {\n\t\treturn function (cb) { setTimeout(ensureCallable(cb), 0); };\n\t}\n\n\treturn null;\n}());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-tick/index.js\n");

/***/ })

};
;