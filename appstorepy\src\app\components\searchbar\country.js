import { Box, TextField, Autocomplete } from "@mui/material";
import {countries} from "../../utils/countries.js";

export default function CountrySelect({ setCountryCode, setCountry = () => {}, countryCode, country}) {
  return (
    <Autocomplete
      id="country-select-demo"
      sx={ {width: { xs: '100%' }}}
      options={countries}
      defaultValue={ { code: countryCode, label: country }}
      isOptionEqualToValue={(option, value) => option.code === value?.code}
      onChange={(e, newValue) => {
        const countryCode = newValue ? newValue.code : "";
        const country = newValue ? newValue.label : "";
        setCountryCode(countryCode);
        setCountry(country);
      }}
      autoHighlight
      getOptionLabel={(option) => option.label}
      renderOption={(props, option) => {
        const { key, ...rest } = props;
        return (
          <Box
            key={key}
            component="li"
            sx={{ '& > img': { mr: 2, flexShrink: 0 } }}
            {...rest}
          >
            <img
              loading="lazy"
              width="20"
              srcSet={`https://flagcdn.com/w40/${option.code.toLowerCase()}.png 2x`}
              src={`https://flagcdn.com/w20/${option.code.toLowerCase()}.png`}
              alt={option.label}
              title={option.code}
            />
            {option.label} ({option.code})
          </Box>
        );
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label="Choose a country"
          inputProps={{
            ...params.inputProps,
            autoComplete: "new-password",
          }}
        />
      )}
    />
  );
}
