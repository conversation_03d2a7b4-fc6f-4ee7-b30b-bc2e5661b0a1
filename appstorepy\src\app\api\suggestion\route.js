import { NextResponse } from "next/server";
import gplay from "google-play-scraper";
import connectDB from "../../database/mongoose";

export const POST = async (req, res) => {

  try {
    await connectDB();
    const { searchTerm, storeType } = await req.json();
    if (!searchTerm) {
      return NextResponse.json(
        { error: "Search term is required" },
        { status: 400 }
      );
    }

    let suggestions = [];

    if (storeType === "playStore" || storeType === "appStore") {
      suggestions = await getPlayStoreSuggestions(searchTerm);
    } else {
      return NextResponse.json({ error: "Invalid store type" }, { status: 400 });
    }

    return NextResponse.json({ suggestions }, { status: 200 });
  } catch (error) {
    console.error(error);
      return NextResponse.json({ error: `Error fetching suggestions: ${error.message || error}`}, { status: 500 });
  }
}

//for playStore suggestions
async function getPlayStoreSuggestions(searchTerm) {
  const playStoreSuggestions = await gplay.suggest({
    term: searchTerm,
  });
  if(!playStoreSuggestions || playStoreSuggestions.length === 0) {
    return [];
  }
  return playStoreSuggestions.map((term) => term);
}


