import React, { useState } from "react";
import { Line } from "react-chartjs-2";
import Image from 'next/image';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import {
  FormControl,
  Box,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Button,
} from "@mui/material";
import { useRouter } from "next/navigation";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const options = {
    responsive: true,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'App Analytics Installs Chart'
      }
    }
  }

export function AppAnalyticsInstallsChart({status, analyticsData}) {
  const [selectedRecord, setSelectedRecord] = useState(0);
  const router = useRouter();

  const handleDropdownChange = (event) => {
    setSelectedRecord(parseInt(event.target.value, 10));
  };
  const selectedDataObject = analyticsData[selectedRecord];
  const monthOptions = selectedDataObject?.analytics
    ? Object.keys(selectedDataObject.analytics)
    : [];
  const installsData = selectedDataObject?.analytics
    ? Object.values(selectedDataObject.analytics).map((data) => data.installs)
    : [];

  const data = {
    labels: monthOptions,
    datasets: [
      {
        label: "Installs",
        data: installsData,
        borderColor: '#00A3FF',
        backgroundColor: '#00A3FF',
        tension: 0.1
      },
    ],
  };
const monthNames = ["January", "February", "March", "April", "May", "June",
                   "July", "August", "September", "October", "November", "December"];
  return (
    <Box sx={{ position: "relative", width: "100%" }}>
      {status !== "authenticated" ? (
        <>
        <Box sx={{ maxWidth: "1200px" , filter: "blur(4px)" }}>
          <Image
            src="/ChartsDownload.png"
            alt="chart"
            layout="responsive"
            width={800}
            height={500}
          />
        </Box>
        <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          backgroundColor: "#fff",
          padding: "20px",
          borderRadius: "5px",
          textAlign: "center",
          zIndex: 1,
          border: "1px solid #a2abb5",
          filter: "none" 
        }}
      >
        <Box sx={{ fontSize: "1.2rem",fontWeight:600 }}>
          Unlock the Growth data
        </Box>
        <Box sx={{ fontSize: "1.2rem",fontWeight:600 }}>
          revenue and downloads dyanamics
        </Box>
        <Button onClick={() => router.push("/register")} sx={{
                  mt:3, width:"90%",borderRadius:"10px", fontWeight:600,background: "#00A3FF", "&:hover": {
                    backgroundColor: "#00A3FF",
                    color: "#fff",
                   
                  }, textTransform: "none", letterSpacing: "2px"
                }} variant="contained">
         Login Now
        </Button>
      </Box>
        </>
      ) : (
        <>
          <Box
            sx={{
              border: "1px solid",
              borderRadius: "5px",
              filter: status !== "authenticated" ? "blur(4px)" : "none",
            }}
          >
            <Grid
              container
              spacing={2}
              sx={{ display: "flex", justifyContent: "end" }}
            >
              <Grid
                item
                xs={12}
                sm={6}
                md={4}
                lg={3}
                xl={2}
                sx={{ textAlign: "right" }}
              >
                <FormControl sx={{ m: 1, minWidth: 120 }} size="small">
                  <InputLabel id="demo-select-small-label">Month</InputLabel>
                  <Select
                    labelId="demo-select-small-label"
                    id="demo-select-small"
                    value={selectedRecord}
                    onChange={handleDropdownChange}
                    label="Month"
                  >
                    {analyticsData.map((record, index) => {
                      if (record === null) {
                        return null;
                      }
                      const monthKey = Object.keys(record.analytics)[0];
                      const month = monthNames[new Date(monthKey + " 01, 2000").getMonth()]; 
                      return (
                        <MenuItem key={index} value={index}>
                          {month}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
            <Line options={options} data={data}  />
          </Box>
        </>
      )}
    </Box>
  );
 }