"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/get_apps_games/route";
exports.ids = ["app/api/get_apps_games/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fget_apps_games%2Froute&page=%2Fapi%2Fget_apps_games%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fget_apps_games%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fget_apps_games%2Froute&page=%2Fapi%2Fget_apps_games%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fget_apps_games%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Bhushan_patil_OneDrive_Desktop_Anchoring_Apk_mirror_src_app_api_get_apps_games_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/get_apps_games/route.js */ \"(rsc)/./src/app/api/get_apps_games/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/get_apps_games/route\",\n        pathname: \"/api/get_apps_games\",\n        filename: \"route\",\n        bundlePath: \"app/api/get_apps_games/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\api\\\\get_apps_games\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_Bhushan_patil_OneDrive_Desktop_Anchoring_Apk_mirror_src_app_api_get_apps_games_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/get_apps_games/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fget_apps_games%2Froute&page=%2Fapi%2Fget_apps_games%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fget_apps_games%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/get_apps_games/route.js":
/*!*********************************************!*\
  !*** ./src/app/api/get_apps_games/route.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _app_database_appData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/database/appData */ \"(rsc)/./src/app/database/appData.js\");\n/* harmony import */ var _app_database_appApk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/database/appApk */ \"(rsc)/./src/app/database/appApk.js\");\n/* harmony import */ var _app_database_mongoose__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/database/mongoose */ \"(rsc)/./src/app/database/mongoose.js\");\n\n\n\n\nconst GET = async (request, res)=>{\n    const url = new URL(request.url);\n    const type = url.searchParams.get(\"type\");\n    if (!type || type !== \"app\" && type !== \"game\") {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Invalid or missing type parameter\"\n        }, {\n            status: 400\n        });\n    }\n    try {\n        await (0,_app_database_mongoose__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n        const getAndroidAppsAndGames = await _app_database_appApk__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({\n            type,\n            isPopular: {\n                $ne: true\n            }\n        });\n        const recentlyUpdated = await _app_database_appApk__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({\n            type,\n            recentlyUpdated: {\n                $ne: true\n            }\n        }).limit(6).select(\"appId versions type\");\n        const fetchAppDetailsWithVersion = async (appApk)=>{\n            const app = await _app_database_appData__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findOne({\n                appId: appApk.appId\n            }).select(\"title icon developer scoreText\");\n            if (app) {\n                const latestVersion = appApk.versions.find((version)=>version.latestVersion) || appApk.versions[0];\n                return {\n                    title: app.title,\n                    appId: appApk.appId,\n                    icon: app.icon,\n                    developer: app.developer,\n                    scoreText: app.scoreText,\n                    latestVersion: latestVersion ? latestVersion.versionNumber : null,\n                    updated: latestVersion ? latestVersion.updated : null\n                };\n            }\n            return null;\n        };\n        const appDetailsWithVersion = await Promise.all(getAndroidAppsAndGames.map(fetchAppDetailsWithVersion));\n        const recentlyUpdatedApps = await Promise.all(recentlyUpdated.map(fetchAppDetailsWithVersion));\n        const filteredAppDetails = appDetailsWithVersion.filter((app)=>app !== null);\n        const recentlyUpdatedAppsAndGames = recentlyUpdatedApps.filter((app)=>app !== null);\n        const responseData = {\n            AndroidData: filteredAppDetails,\n            recentlyUpdatedAppsAndGames\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(responseData, {\n            status: 200\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Error fetching get apps details\",\n            error\n        }, {\n            status: 500\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9nZXRfYXBwc19nYW1lcy9yb3V0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEyQztBQUNGO0FBQ0U7QUFDSztBQUV6QyxNQUFNSSxNQUFNLE9BQU9DLFNBQVNDO0lBQ2pDLE1BQU1DLE1BQU0sSUFBSUMsSUFBSUgsUUFBUUUsR0FBRztJQUMvQixNQUFNRSxPQUFPRixJQUFJRyxZQUFZLENBQUNDLEdBQUcsQ0FBQztJQUNsQyxJQUFJLENBQUNGLFFBQVNBLFNBQVMsU0FBU0EsU0FBUyxRQUFTO1FBQ2hELE9BQU9ULHFEQUFZQSxDQUFDWSxJQUFJLENBQUM7WUFBRUMsT0FBTztRQUFvQyxHQUFHO1lBQUVDLFFBQVE7UUFBSTtJQUN6RjtJQUVBLElBQUk7UUFDRixNQUFNWCxrRUFBU0E7UUFFZixNQUFNWSx5QkFBeUIsTUFBTWIsNERBQU1BLENBQUNjLElBQUksQ0FBQztZQUFFUDtZQUFNUSxXQUFXO2dCQUFFQyxLQUFLO1lBQUs7UUFBRTtRQUVsRixNQUFNQyxrQkFBa0IsTUFBTWpCLDREQUFNQSxDQUFDYyxJQUFJLENBQUM7WUFBRVA7WUFBTVUsaUJBQWlCO2dCQUFFRCxLQUFLO1lBQUs7UUFBRSxHQUFHRSxLQUFLLENBQUMsR0FBR0MsTUFBTSxDQUFDO1FBRXBHLE1BQU1DLDZCQUE2QixPQUFPQztZQUN4QyxNQUFNQyxNQUFNLE1BQU12Qiw2REFBR0EsQ0FBQ3dCLE9BQU8sQ0FBQztnQkFBRUMsT0FBT0gsT0FBT0csS0FBSztZQUFDLEdBQUdMLE1BQU0sQ0FBQztZQUM5RCxJQUFJRyxLQUFLO2dCQUNQLE1BQU1HLGdCQUFnQkosT0FBT0ssUUFBUSxDQUFDWixJQUFJLENBQUNhLENBQUFBLFVBQVdBLFFBQVFGLGFBQWEsS0FBS0osT0FBT0ssUUFBUSxDQUFDLEVBQUU7Z0JBQ2xHLE9BQU87b0JBQ0xFLE9BQU9OLElBQUlNLEtBQUs7b0JBQ2hCSixPQUFPSCxPQUFPRyxLQUFLO29CQUNuQkssTUFBTVAsSUFBSU8sSUFBSTtvQkFDZEMsV0FBV1IsSUFBSVEsU0FBUztvQkFDeEJDLFdBQVdULElBQUlTLFNBQVM7b0JBQ3hCTixlQUFlQSxnQkFBZ0JBLGNBQWNPLGFBQWEsR0FBRztvQkFDN0RDLFNBQVNSLGdCQUFnQkEsY0FBY1EsT0FBTyxHQUFHO2dCQUNuRDtZQUNGO1lBQ0EsT0FBTztRQUNUO1FBRUEsTUFBTUMsd0JBQXdCLE1BQU1DLFFBQVFDLEdBQUcsQ0FDN0N2Qix1QkFBdUJ3QixHQUFHLENBQUNqQjtRQUc3QixNQUFNa0Isc0JBQXNCLE1BQU1ILFFBQVFDLEdBQUcsQ0FDM0NuQixnQkFBZ0JvQixHQUFHLENBQUNqQjtRQUd0QixNQUFNbUIscUJBQXFCTCxzQkFBc0JNLE1BQU0sQ0FBQ2xCLENBQUFBLE1BQU9BLFFBQVE7UUFDdkUsTUFBTW1CLDhCQUE4Qkgsb0JBQW9CRSxNQUFNLENBQUNsQixDQUFBQSxNQUFPQSxRQUFRO1FBRTlFLE1BQU1vQixlQUFlO1lBQ25CQyxhQUFhSjtZQUNiRTtRQUNGO1FBRUEsT0FBTzNDLHFEQUFZQSxDQUFDWSxJQUFJLENBQUNnQyxjQUFjO1lBQUU5QixRQUFRO1FBQUk7SUFDdkQsRUFBRSxPQUFPRCxPQUFPO1FBQ2QsT0FBT2IscURBQVlBLENBQUNZLElBQUksQ0FDdEI7WUFBRWtDLFNBQVM7WUFBbUNqQztRQUFNLEdBQ3BEO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vc3JjL2FwcC9hcGkvZ2V0X2FwcHNfZ2FtZXMvcm91dGUuanM/ZjUzNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVzcG9uc2UgfSBmcm9tIFwibmV4dC9zZXJ2ZXJcIjtcbmltcG9ydCBBcHAgZnJvbSBcIkAvYXBwL2RhdGFiYXNlL2FwcERhdGFcIjtcbmltcG9ydCBBcHBBcGsgZnJvbSBcIkAvYXBwL2RhdGFiYXNlL2FwcEFwa1wiO1xuaW1wb3J0IGNvbm5lY3REQiBmcm9tIFwiQC9hcHAvZGF0YWJhc2UvbW9uZ29vc2VcIjtcblxuZXhwb3J0IGNvbnN0IEdFVCA9IGFzeW5jIChyZXF1ZXN0LCByZXMpID0+IHtcbiAgY29uc3QgdXJsID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gIGNvbnN0IHR5cGUgPSB1cmwuc2VhcmNoUGFyYW1zLmdldChcInR5cGVcIik7XG4gIGlmICghdHlwZSB8fCAodHlwZSAhPT0gJ2FwcCcgJiYgdHlwZSAhPT0gJ2dhbWUnKSkge1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBcIkludmFsaWQgb3IgbWlzc2luZyB0eXBlIHBhcmFtZXRlclwiIH0sIHsgc3RhdHVzOiA0MDAgfSk7XG4gIH1cblxuICB0cnkge1xuICAgIGF3YWl0IGNvbm5lY3REQigpO1xuXG4gICAgY29uc3QgZ2V0QW5kcm9pZEFwcHNBbmRHYW1lcyA9IGF3YWl0IEFwcEFway5maW5kKHsgdHlwZSwgaXNQb3B1bGFyOiB7ICRuZTogdHJ1ZSB9IH0pO1xuXG4gICAgY29uc3QgcmVjZW50bHlVcGRhdGVkID0gYXdhaXQgQXBwQXBrLmZpbmQoeyB0eXBlLCByZWNlbnRseVVwZGF0ZWQ6IHsgJG5lOiB0cnVlIH0gfSkubGltaXQoNikuc2VsZWN0KCdhcHBJZCB2ZXJzaW9ucyB0eXBlJyk7XG5cbiAgICBjb25zdCBmZXRjaEFwcERldGFpbHNXaXRoVmVyc2lvbiA9IGFzeW5jIChhcHBBcGspID0+IHtcbiAgICAgIGNvbnN0IGFwcCA9IGF3YWl0IEFwcC5maW5kT25lKHsgYXBwSWQ6IGFwcEFway5hcHBJZCB9KS5zZWxlY3QoJ3RpdGxlIGljb24gZGV2ZWxvcGVyIHNjb3JlVGV4dCcpO1xuICAgICAgaWYgKGFwcCkge1xuICAgICAgICBjb25zdCBsYXRlc3RWZXJzaW9uID0gYXBwQXBrLnZlcnNpb25zLmZpbmQodmVyc2lvbiA9PiB2ZXJzaW9uLmxhdGVzdFZlcnNpb24pIHx8IGFwcEFway52ZXJzaW9uc1swXTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICB0aXRsZTogYXBwLnRpdGxlLFxuICAgICAgICAgIGFwcElkOiBhcHBBcGsuYXBwSWQsXG4gICAgICAgICAgaWNvbjogYXBwLmljb24sXG4gICAgICAgICAgZGV2ZWxvcGVyOiBhcHAuZGV2ZWxvcGVyLFxuICAgICAgICAgIHNjb3JlVGV4dDogYXBwLnNjb3JlVGV4dCxcbiAgICAgICAgICBsYXRlc3RWZXJzaW9uOiBsYXRlc3RWZXJzaW9uID8gbGF0ZXN0VmVyc2lvbi52ZXJzaW9uTnVtYmVyIDogbnVsbCxcbiAgICAgICAgICB1cGRhdGVkOiBsYXRlc3RWZXJzaW9uID8gbGF0ZXN0VmVyc2lvbi51cGRhdGVkIDogbnVsbFxuICAgICAgICB9O1xuICAgICAgfVxuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfTtcblxuICAgIGNvbnN0IGFwcERldGFpbHNXaXRoVmVyc2lvbiA9IGF3YWl0IFByb21pc2UuYWxsKFxuICAgICAgZ2V0QW5kcm9pZEFwcHNBbmRHYW1lcy5tYXAoZmV0Y2hBcHBEZXRhaWxzV2l0aFZlcnNpb24pXG4gICAgKTtcblxuICAgIGNvbnN0IHJlY2VudGx5VXBkYXRlZEFwcHMgPSBhd2FpdCBQcm9taXNlLmFsbChcbiAgICAgIHJlY2VudGx5VXBkYXRlZC5tYXAoZmV0Y2hBcHBEZXRhaWxzV2l0aFZlcnNpb24pXG4gICAgKTtcblxuICAgIGNvbnN0IGZpbHRlcmVkQXBwRGV0YWlscyA9IGFwcERldGFpbHNXaXRoVmVyc2lvbi5maWx0ZXIoYXBwID0+IGFwcCAhPT0gbnVsbCk7XG4gICAgY29uc3QgcmVjZW50bHlVcGRhdGVkQXBwc0FuZEdhbWVzID0gcmVjZW50bHlVcGRhdGVkQXBwcy5maWx0ZXIoYXBwID0+IGFwcCAhPT0gbnVsbCk7XG5cbiAgICBjb25zdCByZXNwb25zZURhdGEgPSB7XG4gICAgICBBbmRyb2lkRGF0YTogZmlsdGVyZWRBcHBEZXRhaWxzLFxuICAgICAgcmVjZW50bHlVcGRhdGVkQXBwc0FuZEdhbWVzXG4gICAgfTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihyZXNwb25zZURhdGEsIHsgc3RhdHVzOiAyMDAgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBtZXNzYWdlOiBcIkVycm9yIGZldGNoaW5nIGdldCBhcHBzIGRldGFpbHNcIiwgZXJyb3IgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn07Il0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsIkFwcCIsIkFwcEFwayIsImNvbm5lY3REQiIsIkdFVCIsInJlcXVlc3QiLCJyZXMiLCJ1cmwiLCJVUkwiLCJ0eXBlIiwic2VhcmNoUGFyYW1zIiwiZ2V0IiwianNvbiIsImVycm9yIiwic3RhdHVzIiwiZ2V0QW5kcm9pZEFwcHNBbmRHYW1lcyIsImZpbmQiLCJpc1BvcHVsYXIiLCIkbmUiLCJyZWNlbnRseVVwZGF0ZWQiLCJsaW1pdCIsInNlbGVjdCIsImZldGNoQXBwRGV0YWlsc1dpdGhWZXJzaW9uIiwiYXBwQXBrIiwiYXBwIiwiZmluZE9uZSIsImFwcElkIiwibGF0ZXN0VmVyc2lvbiIsInZlcnNpb25zIiwidmVyc2lvbiIsInRpdGxlIiwiaWNvbiIsImRldmVsb3BlciIsInNjb3JlVGV4dCIsInZlcnNpb25OdW1iZXIiLCJ1cGRhdGVkIiwiYXBwRGV0YWlsc1dpdGhWZXJzaW9uIiwiUHJvbWlzZSIsImFsbCIsIm1hcCIsInJlY2VudGx5VXBkYXRlZEFwcHMiLCJmaWx0ZXJlZEFwcERldGFpbHMiLCJmaWx0ZXIiLCJyZWNlbnRseVVwZGF0ZWRBcHBzQW5kR2FtZXMiLCJyZXNwb25zZURhdGEiLCJBbmRyb2lkRGF0YSIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/get_apps_games/route.js\n");

/***/ }),

/***/ "(rsc)/./src/app/database/appApk.js":
/*!************************************!*\
  !*** ./src/app/database/appApk.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Schema = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema);\nconst versionSchema = new Schema({\n    versionLink: {\n        type: String,\n        required: true\n    },\n    actualLink: {\n        type: String,\n        require: true\n    },\n    versionNumber: {\n        type: String,\n        required: true\n    },\n    latestVersion: {\n        type: Boolean,\n        default: false\n    },\n    size: {\n        type: String\n    },\n    minimum: {\n        type: String\n    },\n    updated: {\n        type: String\n    }\n});\nconst categorySchema = new Schema({\n    name: {\n        type: String,\n        required: true\n    },\n    value: {\n        type: String,\n        required: true\n    }\n});\nconst appApkSchema = new Schema({\n    appId: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    type: {\n        type: String,\n        required: true\n    },\n    // category: { type: categorySchema, required: true },\n    category: {\n        type: String,\n        required: true\n    },\n    isPopular: {\n        type: Boolean,\n        default: false\n    },\n    recentlyUpdated: {\n        type: Boolean,\n        default: false\n    },\n    isSimilar: {\n        type: Boolean,\n        default: false\n    },\n    versions: [\n        versionSchema\n    ]\n});\n(mongoose__WEBPACK_IMPORTED_MODULE_0___default().models) = {};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"AppApk\", appApkSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/database/appApk.js\n");

/***/ }),

/***/ "(rsc)/./src/app/database/appData.js":
/*!*************************************!*\
  !*** ./src/app/database/appData.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n\nconst mongoose = __webpack_require__(/*! mongoose */ \"mongoose\");\nconst Schema = mongoose.Schema;\nconst appSchema = new Schema({\n    title: {\n        type: String,\n        required: true\n    },\n    appId: {\n        type: String,\n        required: true,\n        unique: true\n    },\n    description: String,\n    descriptionHTML: String,\n    summary: String,\n    installs: String,\n    minInstalls: Number,\n    maxInstalls: Number,\n    score: Number,\n    scoreText: String,\n    ratings: Number,\n    reviews: Number,\n    histogram: {\n        type: Schema.Types.Mixed,\n        default: {}\n    },\n    price: Number,\n    free: Boolean,\n    currency: String,\n    priceText: String,\n    available: Boolean,\n    offersIAP: Boolean,\n    androidVersion: String,\n    androidVersionText: String,\n    androidMaxVersion: String,\n    developer: String,\n    developerId: String,\n    developerEmail: String,\n    developerWebsite: String,\n    developerAddress: String,\n    privacyPolicy: String,\n    developerInternalID: String,\n    genre: String,\n    genreId: String,\n    categories: [\n        {\n            name: String,\n            id: String\n        }\n    ],\n    icon: String,\n    headerImage: String,\n    screenshots: [\n        String\n    ],\n    bannerImage: String,\n    video: String,\n    VideoImage: String,\n    previewVideo: String,\n    contentRating: String,\n    adSupported: Boolean,\n    released: String,\n    recentChanges: String,\n    updated: String,\n    version: String,\n    recentChanges: String,\n    comments: [\n        String\n    ],\n    preregister: Boolean,\n    earlyAccessEnabled: Boolean,\n    isAvailableInPlayPass: Boolean,\n    editorsChoice: Boolean,\n    url: String\n});\nmongoose.models = {};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (mongoose.model(\"App\", appSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2RhdGFiYXNlL2FwcERhdGEuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFhO0FBRWIsTUFBTUEsV0FBV0MsbUJBQU9BLENBQUM7QUFFekIsTUFBTUMsU0FBU0YsU0FBU0UsTUFBTTtBQUU5QixNQUFNQyxZQUFZLElBQUlELE9BQU87SUFDekJFLE9BQU87UUFBRUMsTUFBTUM7UUFBUUMsVUFBVTtJQUFLO0lBQ3RDQyxPQUFPO1FBQUVILE1BQU1DO1FBQVFDLFVBQVU7UUFBTUUsUUFBUTtJQUFLO0lBQ3BEQyxhQUFhSjtJQUNiSyxpQkFBaUJMO0lBQ2pCTSxTQUFTTjtJQUNUTyxVQUFVUDtJQUNWUSxhQUFhQztJQUNiQyxhQUFhRDtJQUNiRSxPQUFPRjtJQUNQRyxXQUFXWjtJQUNYYSxTQUFTSjtJQUNUSyxTQUFTTDtJQUNUTSxXQUFXO1FBQ1RoQixNQUFNSCxPQUFPb0IsS0FBSyxDQUFDQyxLQUFLO1FBQ3hCQyxTQUFTLENBQUM7SUFDWjtJQUNBQyxPQUFPVjtJQUNQVyxNQUFNQztJQUNOQyxVQUFVdEI7SUFDVnVCLFdBQVd2QjtJQUNYd0IsV0FBV0g7SUFDWEksV0FBV0o7SUFDWEssZ0JBQWdCMUI7SUFDaEIyQixvQkFBb0IzQjtJQUNwQjRCLG1CQUFtQjVCO0lBQ25CNkIsV0FBVzdCO0lBQ1g4QixhQUFhOUI7SUFDYitCLGdCQUFnQi9CO0lBQ2hCZ0Msa0JBQWtCaEM7SUFDbEJpQyxrQkFBa0JqQztJQUNsQmtDLGVBQWVsQztJQUNmbUMscUJBQXFCbkM7SUFDckJvQyxPQUFPcEM7SUFDUHFDLFNBQVNyQztJQUNUc0MsWUFBWTtRQUFDO1lBQUVDLE1BQU12QztZQUFRd0MsSUFBSXhDO1FBQU87S0FBRTtJQUMxQ3lDLE1BQU16QztJQUNOMEMsYUFBYTFDO0lBQ2IyQyxhQUFhO1FBQUMzQztLQUFPO0lBQ3JCNEMsYUFBYTVDO0lBQ2I2QyxPQUFPN0M7SUFDUDhDLFlBQVk5QztJQUNaK0MsY0FBYy9DO0lBQ2RnRCxlQUFlaEQ7SUFDZmlELGFBQWE1QjtJQUNiNkIsVUFBVWxEO0lBQ1ZtRCxlQUFlbkQ7SUFDZm9ELFNBQVNwRDtJQUNUcUQsU0FBU3JEO0lBQ1RtRCxlQUFlbkQ7SUFDZnNELFVBQVU7UUFBQ3REO0tBQU87SUFDbEJ1RCxhQUFhbEM7SUFDYm1DLG9CQUFvQm5DO0lBQ3BCb0MsdUJBQXVCcEM7SUFDdkJxQyxlQUFlckM7SUFDZnNDLEtBQUszRDtBQUNQO0FBQ0ROLFNBQVNrRSxNQUFNLEdBQUcsQ0FBQztBQUNsQixpRUFBZ0JsRSxTQUFTbUUsS0FBSyxDQUFDLE9BQU9oRSxVQUFVQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL3NyYy9hcHAvZGF0YWJhc2UvYXBwRGF0YS5qcz9lNjA2Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xyXG5cclxuY29uc3QgbW9uZ29vc2UgPSByZXF1aXJlKCdtb25nb29zZScpO1xyXG5cclxuY29uc3QgU2NoZW1hID0gbW9uZ29vc2UuU2NoZW1hO1xyXG5cclxuY29uc3QgYXBwU2NoZW1hID0gbmV3IFNjaGVtYSh7XHJcbiAgICB0aXRsZTogeyB0eXBlOiBTdHJpbmcsIHJlcXVpcmVkOiB0cnVlIH0sXHJcbiAgICBhcHBJZDogeyB0eXBlOiBTdHJpbmcsIHJlcXVpcmVkOiB0cnVlLCB1bmlxdWU6IHRydWUgfSxcclxuICAgIGRlc2NyaXB0aW9uOiBTdHJpbmcsXHJcbiAgICBkZXNjcmlwdGlvbkhUTUw6IFN0cmluZyxcclxuICAgIHN1bW1hcnk6IFN0cmluZyxcclxuICAgIGluc3RhbGxzOiBTdHJpbmcsXHJcbiAgICBtaW5JbnN0YWxsczogTnVtYmVyLFxyXG4gICAgbWF4SW5zdGFsbHM6IE51bWJlcixcclxuICAgIHNjb3JlOiBOdW1iZXIsXHJcbiAgICBzY29yZVRleHQ6IFN0cmluZyxcclxuICAgIHJhdGluZ3M6IE51bWJlcixcclxuICAgIHJldmlld3M6IE51bWJlcixcclxuICAgIGhpc3RvZ3JhbToge1xyXG4gICAgICB0eXBlOiBTY2hlbWEuVHlwZXMuTWl4ZWQsIFxyXG4gICAgICBkZWZhdWx0OiB7fSBcclxuICAgIH0sXHJcbiAgICBwcmljZTogTnVtYmVyLFxyXG4gICAgZnJlZTogQm9vbGVhbixcclxuICAgIGN1cnJlbmN5OiBTdHJpbmcsXHJcbiAgICBwcmljZVRleHQ6IFN0cmluZyxcclxuICAgIGF2YWlsYWJsZTogQm9vbGVhbixcclxuICAgIG9mZmVyc0lBUDogQm9vbGVhbixcclxuICAgIGFuZHJvaWRWZXJzaW9uOiBTdHJpbmcsXHJcbiAgICBhbmRyb2lkVmVyc2lvblRleHQ6IFN0cmluZyxcclxuICAgIGFuZHJvaWRNYXhWZXJzaW9uOiBTdHJpbmcsXHJcbiAgICBkZXZlbG9wZXI6IFN0cmluZyxcclxuICAgIGRldmVsb3BlcklkOiBTdHJpbmcsXHJcbiAgICBkZXZlbG9wZXJFbWFpbDogU3RyaW5nLFxyXG4gICAgZGV2ZWxvcGVyV2Vic2l0ZTogU3RyaW5nLFxyXG4gICAgZGV2ZWxvcGVyQWRkcmVzczogU3RyaW5nLFxyXG4gICAgcHJpdmFjeVBvbGljeTogU3RyaW5nLFxyXG4gICAgZGV2ZWxvcGVySW50ZXJuYWxJRDogU3RyaW5nLFxyXG4gICAgZ2VucmU6IFN0cmluZyxcclxuICAgIGdlbnJlSWQ6IFN0cmluZyxcclxuICAgIGNhdGVnb3JpZXM6IFt7IG5hbWU6IFN0cmluZywgaWQ6IFN0cmluZyB9XSxcclxuICAgIGljb246IFN0cmluZyxcclxuICAgIGhlYWRlckltYWdlOiBTdHJpbmcsXHJcbiAgICBzY3JlZW5zaG90czogW1N0cmluZ10sXHJcbiAgICBiYW5uZXJJbWFnZTogU3RyaW5nLFxyXG4gICAgdmlkZW86IFN0cmluZyxcclxuICAgIFZpZGVvSW1hZ2U6IFN0cmluZyxcclxuICAgIHByZXZpZXdWaWRlbzogU3RyaW5nLFxyXG4gICAgY29udGVudFJhdGluZzogU3RyaW5nLFxyXG4gICAgYWRTdXBwb3J0ZWQ6IEJvb2xlYW4sXHJcbiAgICByZWxlYXNlZDogU3RyaW5nLFxyXG4gICAgcmVjZW50Q2hhbmdlczogU3RyaW5nLFxyXG4gICAgdXBkYXRlZDogU3RyaW5nLFxyXG4gICAgdmVyc2lvbjogU3RyaW5nLFxyXG4gICAgcmVjZW50Q2hhbmdlczogU3RyaW5nLFxyXG4gICAgY29tbWVudHM6IFtTdHJpbmddLFxyXG4gICAgcHJlcmVnaXN0ZXI6IEJvb2xlYW4sXHJcbiAgICBlYXJseUFjY2Vzc0VuYWJsZWQ6IEJvb2xlYW4sXHJcbiAgICBpc0F2YWlsYWJsZUluUGxheVBhc3M6IEJvb2xlYW4sXHJcbiAgICBlZGl0b3JzQ2hvaWNlOiBCb29sZWFuLFxyXG4gICAgdXJsOiBTdHJpbmcsXHJcbiAgfSk7XHJcbiBtb25nb29zZS5tb2RlbHMgPSB7fTtcclxuICBleHBvcnQgZGVmYXVsdCAgbW9uZ29vc2UubW9kZWwoJ0FwcCcsIGFwcFNjaGVtYSk7XHJcblxyXG4iXSwibmFtZXMiOlsibW9uZ29vc2UiLCJyZXF1aXJlIiwiU2NoZW1hIiwiYXBwU2NoZW1hIiwidGl0bGUiLCJ0eXBlIiwiU3RyaW5nIiwicmVxdWlyZWQiLCJhcHBJZCIsInVuaXF1ZSIsImRlc2NyaXB0aW9uIiwiZGVzY3JpcHRpb25IVE1MIiwic3VtbWFyeSIsImluc3RhbGxzIiwibWluSW5zdGFsbHMiLCJOdW1iZXIiLCJtYXhJbnN0YWxscyIsInNjb3JlIiwic2NvcmVUZXh0IiwicmF0aW5ncyIsInJldmlld3MiLCJoaXN0b2dyYW0iLCJUeXBlcyIsIk1peGVkIiwiZGVmYXVsdCIsInByaWNlIiwiZnJlZSIsIkJvb2xlYW4iLCJjdXJyZW5jeSIsInByaWNlVGV4dCIsImF2YWlsYWJsZSIsIm9mZmVyc0lBUCIsImFuZHJvaWRWZXJzaW9uIiwiYW5kcm9pZFZlcnNpb25UZXh0IiwiYW5kcm9pZE1heFZlcnNpb24iLCJkZXZlbG9wZXIiLCJkZXZlbG9wZXJJZCIsImRldmVsb3BlckVtYWlsIiwiZGV2ZWxvcGVyV2Vic2l0ZSIsImRldmVsb3BlckFkZHJlc3MiLCJwcml2YWN5UG9saWN5IiwiZGV2ZWxvcGVySW50ZXJuYWxJRCIsImdlbnJlIiwiZ2VucmVJZCIsImNhdGVnb3JpZXMiLCJuYW1lIiwiaWQiLCJpY29uIiwiaGVhZGVySW1hZ2UiLCJzY3JlZW5zaG90cyIsImJhbm5lckltYWdlIiwidmlkZW8iLCJWaWRlb0ltYWdlIiwicHJldmlld1ZpZGVvIiwiY29udGVudFJhdGluZyIsImFkU3VwcG9ydGVkIiwicmVsZWFzZWQiLCJyZWNlbnRDaGFuZ2VzIiwidXBkYXRlZCIsInZlcnNpb24iLCJjb21tZW50cyIsInByZXJlZ2lzdGVyIiwiZWFybHlBY2Nlc3NFbmFibGVkIiwiaXNBdmFpbGFibGVJblBsYXlQYXNzIiwiZWRpdG9yc0Nob2ljZSIsInVybCIsIm1vZGVscyIsIm1vZGVsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/database/appData.js\n");

/***/ }),

/***/ "(rsc)/./src/app/database/mongoose.js":
/*!**************************************!*\
  !*** ./src/app/database/mongoose.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst connectDB = async ()=>{\n    try {\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0]?.readyState) {\n            console.log(\"Using existing database connection\");\n            return;\n        }\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(process.env.MONGODB_URI, {\n            // useNewUrlParser: true,\n            useUnifiedTopology: true\n        });\n        console.log(\"MongoDB connection established successfully\");\n    } catch (error) {\n        console.error(\"Database connection error:\", error);\n        throw error;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2RhdGFiYXNlL21vbmdvb3NlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnQztBQUVoQyxNQUFNQyxZQUFZO0lBQ2hCLElBQUk7UUFDRixJQUFJRCw2REFBb0IsQ0FBQyxFQUFFLEVBQUVHLFlBQVk7WUFDdkNDLFFBQVFDLEdBQUcsQ0FBQztZQUNaO1FBQ0Y7UUFFQSxNQUFNTCx1REFBZ0IsQ0FBQ08sUUFBUUMsR0FBRyxDQUFDQyxXQUFXLEVBQUU7WUFDOUMseUJBQXlCO1lBQ3pCQyxvQkFBb0I7UUFDdEI7UUFFQU4sUUFBUUMsR0FBRyxDQUFDO0lBQ2QsRUFBRSxPQUFPTSxPQUFPO1FBQ2RQLFFBQVFPLEtBQUssQ0FBQyw4QkFBOEJBO1FBQzVDLE1BQU1BO0lBQ1I7QUFDRjtBQUVBLGlFQUFlVixTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL3NyYy9hcHAvZGF0YWJhc2UvbW9uZ29vc2UuanM/OTk3YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbW9uZ29vc2UgZnJvbSBcIm1vbmdvb3NlXCI7XHJcblxyXG5jb25zdCBjb25uZWN0REIgPSBhc3luYyAoKSA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGlmIChtb25nb29zZS5jb25uZWN0aW9uc1swXT8ucmVhZHlTdGF0ZSkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIlVzaW5nIGV4aXN0aW5nIGRhdGFiYXNlIGNvbm5lY3Rpb25cIik7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBhd2FpdCBtb25nb29zZS5jb25uZWN0KHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJLCB7XHJcbiAgICAgIC8vIHVzZU5ld1VybFBhcnNlcjogdHJ1ZSxcclxuICAgICAgdXNlVW5pZmllZFRvcG9sb2d5OiB0cnVlLFxyXG4gICAgfSk7XHJcblxyXG4gICAgY29uc29sZS5sb2coXCJNb25nb0RCIGNvbm5lY3Rpb24gZXN0YWJsaXNoZWQgc3VjY2Vzc2Z1bGx5XCIpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiRGF0YWJhc2UgY29ubmVjdGlvbiBlcnJvcjpcIiwgZXJyb3IpO1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgY29ubmVjdERCO1xyXG4iXSwibmFtZXMiOlsibW9uZ29vc2UiLCJjb25uZWN0REIiLCJjb25uZWN0aW9ucyIsInJlYWR5U3RhdGUiLCJjb25zb2xlIiwibG9nIiwiY29ubmVjdCIsInByb2Nlc3MiLCJlbnYiLCJNT05HT0RCX1VSSSIsInVzZVVuaWZpZWRUb3BvbG9neSIsImVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/database/mongoose.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fget_apps_games%2Froute&page=%2Fapi%2Fget_apps_games%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fget_apps_games%2Froute.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();