import "@fortawesome/fontawesome-svg-core/styles.css";
import { config } from "@fortawesome/fontawesome-svg-core";
import Script from "next/script";
config.autoAddCss = false;
import Link from "next/link";
import localFont from "next/font/local";
import "./globals.css";
import Navbar from './components/navbar/Navbar';
import AdComponent from "./Ads";

const isAdsServe = JSON.parse(process.env.NEXT_PUBLIC_SERVE_ADS);

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});


export const metadata = {
  title: "SmartTools | Discover Top AI Tools & Reviews | Your AI Resource",
  description: "Generated by create next app",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
        />
        <link rel="icon" href="/st_logo.png" />
        <Script
          strategy="afterInteractive"
          src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2219975169694529"
          crossOrigin="anonymous"
        />
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <Navbar />
        {children}
        {
          isAdsServe &&  <div className="mt-8 mb-4">
          <AdComponent adSlot="5991868361" />
        </div>
        }
       
        <footer className="bg-white dark:bg-gray-900">
          <div className="mx-auto w-full max-w-screen-xl p-4 py-6 lg:py-8">
            <div className="md:flex md:justify-between">
              <div className="w-full md:w-6/12 lg:w-6/12 mb-6 md:mb-0">
                <Link href="https://smart-tools.com/" className="flex items-center">
                  <img src="/st_logo.png" className="h-8 me-3" alt="SmartTool Logo" />
                  <span className="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">SmartTools</span>
                </Link>
                <p className="lg:me-6 p-1 dark:text-gray-400">
                  SmartTools is an AI tools platform featuring 1000+ tool reviews and value-packed blogs targeted for professionals to increase everyone's productivity and efficiency.
                </p>
              </div>
              <div className="grid grid-cols-2 gap-8 sm:gap-6 sm:grid-cols-3">
                <div>
                  <h2 className="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Pages</h2>
                  <ul className="text-gray-500 dark:text-gray-400 font-medium">
                    <li className="mb-4">
                      <Link href="/" className="hover:underline">Home</Link>
                    </li>
                    <li>
                      <Link href="/tool" className="hover:underline">Tools</Link>
                    </li>
                  </ul>
                </div>
                <div>
                  <h2 className="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Legal</h2>
                  <ul className="text-gray-500 dark:text-gray-400 font-medium">
                    <li className="mb-4">
                      <Link href="/privacy-policy" className="hover:underline">Privacy Policy</Link>
                    </li>
                    <li>
                      <Link href="terms-conditions" className="hover:underline">Terms &amp; Conditions</Link>
                    </li>
                  </ul>
                </div>
                <div>
                  <h2 className="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Categories</h2>
                  <ul className="text-gray-500 dark:text-gray-400 font-medium">
                    <li className="mb-4">
                      <Link href="/" className="hover:underline">AI Audio Tools</Link>
                    </li>
                    <li className="mb-4">
                      <Link href="/" className="hover:underline">AI Image Tools</Link>
                    </li>
                    <li className="mb-4">
                      <Link href="/" className="hover:underline">AI Video Tools</Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <hr className="my-6 border-gray-200 sm:mx-auto dark:border-gray-700 lg:my-8" />
            <div className="sm:flex sm:items-center sm:justify-between">
              <span className="text-sm text-gray-500 sm:text-center dark:text-gray-400">
                © 2024 <Link href="https://smart-tools.com/" className="hover:underline">SmartTools</Link>. All Rights Reserved.
              </span>
              <div className="flex mt-4 sm:justify-center sm:mt-0">
                <Link href="#" className="text-gray-500 hover:text-gray-900 dark:hover:text-white">
                  <i className="fa-brands fa-facebook" />
                  <span className="sr-only">Facebook page</span>
                </Link>
                <Link href="#" className="text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5">
                  <i className="fa-brands fa-discord" />
                  <span className="sr-only">Discord community</span>
                </Link>
                <Link href="#" className="text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5">
                  <i className="fa-brands fa-twitter" />
                  <span className="sr-only">Twitter page</span>
                </Link>
                <Link href="#" className="text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5">
                  <i className="fa-brands fa-github" />
                  <span className="sr-only">GitHub account</span>
                </Link>
                <Link href="#" className="text-gray-500 hover:text-gray-900 dark:hover:text-white ms-5">
                  <i className="fa-brands fa-dribbble" />
                  <span className="sr-only">Dribbble account</span>
                </Link>
              </div>
            </div>
          </div>
        </footer>
      </body>
    </html>
  );
}
