"use client";
import React, { useEffect, useState } from "react";
import { Box, Typography, Button, Grid, FormControl, InputLabel, MenuItem, Select as MuiSelect, } from "@mui/material";
import { connect } from "react-redux";
import { BeatLoader } from "react-spinners";
import { useSession } from "next-auth/react";
import Tooltip from '@mui/material/Tooltip';
import HelpOutlineOutlinedIcon from '@mui/icons-material/HelpOutlineOutlined';
import Link from 'next/link';
import {
  fetchTopAppDetails,
  resetTopAppDetails,
  updateCategory,
} from "../../redux/slice/topAppSlice";
import CheckUserTypeModal from "../checkUserType/checkUserType";
import AppCard from "../tableData/appTableView";
import CountrySelect from "../searchbar/country";
import ArrowDropDownRoundedIcon from '@mui/icons-material/ArrowDropDownRounded';
import ArrowDropUpRoundedIcon from '@mui/icons-material/ArrowDropUpRounded';
import { countries, timezoneToCountryName } from "@/app/utils/countries";


const ITEM_HEIGHT = 78;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 100,
      overflow: 'auto',
    },
  },
  disableScrollLock: true,
  transitionDuration: 0,
};

const categories = {
  APPLICATION: "APPLICATION",
  GAME: "GAME"
}

const initialPagesState = {
  freePage: 0,
  paidPage: 0,
  grossingPage: 0,
  topNewFreePage: 0,
  topNewPaidPage: 0
};
const TopRankApps = React.memo((props) => {
  const {
    topFreeApps,
    topGrossingApps,
    topNewFreeApps,
    topNewPaidApps,
    topPaidApps,
    topAppsLoading,
    dispatch,
    category,
    type
  } = props;
  const [pages, setPages] = useState(initialPagesState);
  const [openModal, setOpenModal] = useState(false);
  const [countryCode, setCountryCode] = useState("");
  const [country, setCountry] = useState("");
  const [selectedCategory, setSelectedCategory] = useState(category);
  const [viewAllApps, setViewAllApps] = useState(false);
  const [topAppsData, setTopAppData] = useState([]);
  const [collectionType, setCollectionType] = useState("");
  const { status } = useSession();
  useEffect(() => {
    if (Intl) {
      let userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      let userCountry = timezoneToCountryName[userTimeZone];
      const cc = countries.find((x) => x.label == userCountry);
      setCountryCode(cc?.code || 'US');
      setCountry(userCountry || 'United States');
    }
  }, [])

  useEffect(() => {
    if (countryCode) {
      setPages(initialPagesState);
      dispatch(resetTopAppDetails());
      dispatch(fetchTopAppDetails({ countryCode, category: selectedCategory }));
    }
  }, [countryCode]);

  const handleViewAll = (collection) => {
    if (status === "authenticated") {
      const fetchAction = fetchTopAppDetails({ collection, countryCode, category: selectedCategory });
      dispatch(fetchAction).then(() => {
        setViewAllApps(true);
        setCollectionType(collection);
      });
    } else {
      setOpenModal(true);
    }
  };

  const renderApps = (apps, page, color) => {
    const startIndex = page * 10;
    const endIndex = startIndex + 10;
    return apps.slice(startIndex, endIndex).map((app, index) => renderAppBox(app, color, index));
  };

  useEffect(() => {
    if (collectionType === "TOP_FREE") {
      setTopAppData(topFreeApps);
    } else if (collectionType === "TOP_PAID") {
      setTopAppData(topPaidApps);
    } else if (collectionType === "GROSSING") {
      setTopAppData(topGrossingApps);
    } else if (collectionType === "TOP_NEW_PAID") {
      setTopAppData(topNewPaidApps);
    } else if (collectionType === "TOP_NEW_FREE") {
      setTopAppData(topNewFreeApps);
    }
  }, [topFreeApps, topPaidApps, topGrossingApps, topNewFreeApps, topNewPaidApps, collectionType]);

  const handleChange = (e) => {
    const selected = e.target.value;
    setSelectedCategory(selected);
    dispatch(updateCategory(selected));

    dispatch(fetchTopAppDetails({ countryCode: countryCode, collection: collectionType, category: selected }));
  };

  const renderAppBox = (app, color, index) => {
    const tooltipText = `
      ${app.ranking && app.ranking.rank === '+' ? `Increased by  ${app.ranking.position}` : ''} 
      ${app.ranking && app.ranking.rank === '-' ? `Decreased by  ${app.ranking.position}` : ''}
      ${app.ranking && app.ranking.position === '=' ? 'Same position' : ''}
      ${app.ranking && app.ranking.position === 'new' ? 'New app' : ''}
    `;
    return (
      <Box
        key={app.appId}
        display="flex"
        alignItems="center"
        sx={{
          "&:hover": {
            backgroundColor: `${color}`,
            borderRadius: "5px",
          },
        }}
      >
        <Box sx={{ width: "5%", textAlign: "center" }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 700, fontSize: "0.8rem" }}>
            {index + 1}
          </Typography>
        </Box>
        <img
          src={app.icon}
          alt={app.title}
          style={{
            width: "45px",
            height: "45px",
            margin: "0.5rem",
            borderRadius: "10px",
            aspectRatio: "1/1",
          }}
        />
        <Box sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          width: "100%",
          overflow: "hidden",
        }}>
          <Box sx={{
            display: "flex",
            flexDirection: "column",
            flexGrow: 1,
            overflow: "hidden",
          }}>
            <Typography variant="h3" title={app.title} sx={{
              fontSize: "15px",
              fontWeight: 600,
              fontFamily: "system-ui",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}>
              <Link href={`/app-details/${app.appId}`}>
                
                  {app.title}
               
              </Link>
            </Typography>
            <Typography variant="subtitle1" title={app.developer} sx={{
              color: "#818a91",
              fontSize: "0.8rem",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}>
              <Box
                component="span"
                sx={{
                  display: 'inline-block',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  width: '100%',
                }}
              >
                <Link
                  href={{
                    pathname: `/developer-apps/${app.developer}`,
                    query: { countryCode: countryCode }
                  }}
                 
                >
                
                    {app.developer}
                
                </Link>
              </Box>
            </Typography>
          </Box>
          {app.ranking && (
            <Box sx={{
              display: "flex",
              alignItems: "center",
              cursor: "pointer",
              width: "20%",
              justifyContent: "flex-end",
              color: app.ranking.rank === "+" ? "#80af3f" : app.ranking.rank === "-" ? "#ef4723" : "#00A3FF",
              overflow: "hidden",
            }}>
              <Tooltip title={tooltipText} arrow placement="top">
                <Typography variant="h3" sx={{
                  fontSize: "1rem",
                  fontWeight: 600,
                  display: "flex",
                  alignItems: "center",
                  fontFamily: "system-ui",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}>
                  {app.ranking.rank === "+" && <ArrowDropUpRoundedIcon />}
                  {app.ranking.rank === "-" && <ArrowDropDownRoundedIcon />}
                  {app.ranking.position}
                </Typography>
              </Tooltip>
            </Box>
          )}
        </Box>
      </Box>
    )
  };

  const renderTopApps = (backGroundColor, typeValue, apps, page, hoverColor, collection) => {
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          padding: "0 0.6rem",
          borderRight: { md: "2px solid #efefef", sm: "2px solid #efefef", xs: "none" },
          height: "100%",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "1rem",
            padding: "0.5rem 0",
            borderBottom: `4px solid ${backGroundColor}`,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 0 }}>
            <Typography variant="h6" fontWeight="bold" sx={{ fontSize: "1rem", whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis" }}>
              {typeValue}&nbsp;
              <Tooltip title={`${typeValue} apps of ${country}`} arrow placement="top">
                <HelpOutlineOutlinedIcon
                  fontSize="small"
                  sx={{
                    color: "#c0c4c9",
                    fontSize: "1.2rem",
                    cursor: "pointer"
                  }}
                />
              </Tooltip>
            </Typography>
          </Box>
          {apps.length > 0 && (
            <Typography variant="subtitle1" sx={{
              color: "#b5b5b5",
              fontSize: "0.8rem",
              whiteSpace: "nowrap",
              marginLeft: "auto",
            }}>
              {apps[0].updatedAt.substring(0, 10)} updated
            </Typography>
          )}
          <Button
            variant="text"
            size="small"
            sx={{
              fontWeight: 700,
              color: `${backGroundColor}`,
              fontSize: "0.7rem",
              whiteSpace: "nowrap",
              backgroundColor: "#eef9ff",
              borderRadius: "5px",
              padding: "5px",
              "&:hover": {
                backgroundColor: `${backGroundColor}`,
                color: "#fff",
              },
              visibility: apps.length > 0 ? "visible" : "hidden",
              marginLeft: '1rem',
            }}
            onClick={() => {
              handleViewAll(collection);
            }}
          >
            View All {'>'}
          </Button>
        </Box>
        {apps.length > 0 ? (
          <>{renderApps(apps, page, hoverColor)}</>
        ) : (
          <Box
            sx={{
              alignItems: "center",
              display: "flex",
              justifyContent: "center",
              height: "100%",
              fontSize: "0.9rem",
              fontWeight: 600,
            }}
          >
            No {typeValue} Apps for this country
          </Box>
        )}
      </Box>
    )
  }
  const topAppsDetails = [
    { backgroundColor: "#00A3FF", typeValue: "Free", apps: topFreeApps, page: pages.freePage, hoverColor: "#e9f7ff", collection: "TOP_FREE" },
    { backgroundColor: "#41BB4D", typeValue: "New Free", apps: topNewFreeApps, page: pages.topNewFreePage, hoverColor: "#e6ffee", collection: "TOP_NEW_FREE" },
    { backgroundColor: "#ffad02", typeValue: "Paid", apps: topPaidApps, page: pages.paidPage, hoverColor: "#fff8eb", collection: "TOP_PAID" },
    { backgroundColor: "#4f47cd", typeValue: "New Paid", apps: topNewPaidApps, page: pages.topNewPaidPage, hoverColor: "#f0efff", collection: "TOP_NEW_PAID" },
    { backgroundColor: "#e14040", typeValue: "Grossing", apps: topGrossingApps, page: pages.grossingPage, hoverColor: "#fdefef", collection: "GROSSING" }
  ];
  const filteredAppsDetails = topAppsDetails.filter(data => data.apps && data.apps.length > 0);
  const gridItemWidth = filteredAppsDetails.length > 0 ? 12 / filteredAppsDetails.length : 12;
  return (
    <>
      {topAppsLoading ? (
        <Box
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "80vh"
          }}
        >
          <BeatLoader color={"#00A3FF"} loading={topAppsLoading} size={25} />
        </Box>
      ) : !viewAllApps ? (
        <Box
          sx={{
            borderRadius: "5px",
            background: "white",
            padding: "1rem 0",
            width: "100%",
          }}
        >
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: { xs: 'flex-start', md: 'center' },
            justifyContent: 'space-between',
            mb: 3,
            gap: 2,
            width: '100%'
          }}>
            {/* Left Section - Title and Country */}
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              flexWrap: 'wrap',
              flex: 1,
              width:"50%"
            }}>
              {/* Play Store Logo and Title */}
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1.5,
                minWidth: 'fit-content'
              }}>
                <Box sx={{
                  borderRadius: '12px',
                  padding: '0.5rem',
                  background: 'linear-gradient(135deg, #4285f4 0%, #34a853 100%)',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}>
                  <img
                    src="https://cdn-icons-png.flaticon.com/128/300/300218.png"
                    loading="lazy"
                    alt="PlayStore"
                    width="32"
                    height="32"
                  />
                </Box>

                <Typography variant="h6" sx={{
                  fontSize: { xs: '1rem', sm: '1.25rem' },
                  fontWeight: 700,
                  background: 'linear-gradient(135deg, #4285f4 0%, #34a853 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  lineHeight: 1.2,
                  whiteSpace: 'nowrap'
                }}>
                 {`Top Ranking | Google Play ${type}`}
                </Typography>
              </Box>

              {/* Country Flag */}
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: '20px',
                padding: '4px 12px',
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                transition: 'all 0.2s ease',
                '&:hover': {
                  backgroundColor: '#eeeeee'
                }
              }}>
                <Box sx={{
                  width: 28,
                  height: 20,
                  borderRadius: '2px',
                  overflow: 'hidden',
                  boxShadow: '0 0 2px rgba(0,0,0,0.2)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <img
                    loading="lazy"
                    width="28"
                    src={`https://flagcdn.com/w40/${countryCode?.toLowerCase()}.png`}
                    srcSet={`https://flagcdn.com/w80/${countryCode?.toLowerCase()}.png 2x`}
                    alt={country}
                    style={{
                      objectFit: 'cover',
                      width: '100%',
                      height: '100%'
                    }}
                  />
                </Box>
                <Typography variant="body1" sx={{
                  color: '#5f6368',
                  ml: 1.5,
                  fontWeight: 500,
                  fontSize: '0.9rem'
                }}>
                  {country}
                </Typography>
              </Box>
            </Box>

            <Box sx={{
              display: 'flex',
              gap: 2,
              justifyContent: 'flex-end',
              width: { xs: '100%', md: '50%' } 
            }}>
              {/* <Box sx={{ width: '50%' }}>  <FormControl fullWidth>
                <InputLabel id="category-label" sx={{ color: '#5f6368' }}>Category</InputLabel>
                <MuiSelect
                  labelId="category-label"
                  value={selectedCategory}
                  label="Category"
                  onChange={handleChange}
                  
                 MenuProps={MenuProps}
                >
                  {Object.keys(categories).map((key) => (
                    <MenuItem
                      key={key}
                      value={categories[key]}
                      sx={{
                        padding: '8px 16px',
                        fontSize: '0.875rem',
                        '&:hover': {
                          backgroundColor: '#f1f3f4',
                        }
                      }}
                    >
                      {categories[key]}
                    </MenuItem>
                  ))}
                </MuiSelect>
              </FormControl></Box> */}


              <Box sx={{ width: '50%' }}>
                <CountrySelect
                  setCountryCode={setCountryCode}
                  setCountry={setCountry}
                  countryCode={countryCode}
                  country={country}
                />
              </Box>
            </Box>
          </Box>
          {filteredAppsDetails.length > 0 ? (
            <Grid container spacing={0} sx={{ borderRadius: "1rem", padding: "1rem 0", boxShadow: "rgba(100, 100, 111, 0.2) 0px 7px 29px 0px" }}>
              {filteredAppsDetails.map((data, index) => (
                <Grid item xs={12} sm={6} md={gridItemWidth} key={index}>
                  {renderTopApps(data.backgroundColor, data.typeValue, data.apps, data.page, data.hoverColor, data.collection)}
                </Grid>
              ))}
            </Grid>
          ) : <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "100px",
              width: "100%",
              fontSize: "1.2rem",
              fontWeight: 500,
              color: "#999",
              fontFamily: "Poppins",
              textAlign: "center",
            }}
          >
            No data found for this country
          </Box>}
          <CheckUserTypeModal
            open={openModal}
            onClose={() => setOpenModal(false)}
          />
        </Box>
      ) : (
        <AppCard
          apps={topAppsData}
          collection={collectionType}
          setViewAllApps={setViewAllApps}
          topAppsByCountry
          country={country}
          countryCode={countryCode}
        />
      )}
    </>
  );
});

const mapStateToProps = ({ topApps }) => ({
  topFreeApps: topApps.topFreeApps,
  topPaidApps: topApps.topPaidApps,
  topNewPaidApps: topApps.topNewPaidApps,
  topNewFreeApps: topApps.topNewFreeApps,
  topGrossingApps: topApps.topGrossingApps,
  topAppsLoading: topApps.topAppsLoading,
  error: topApps.error,
});

export default connect(mapStateToProps)(TopRankApps);