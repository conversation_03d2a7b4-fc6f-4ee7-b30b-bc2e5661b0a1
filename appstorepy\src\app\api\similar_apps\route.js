import { NextResponse } from "next/server";
import gplay from "google-play-scraper";
import AppId from "@/app/database/appId";
import connectDB from "@/app/database/mongoose";
import App from "@/app/database/appData";
const formatApps = (apps) =>
  apps.map(({ appId, title, developer, icon, scoreText, genre }) => ({
    appId,
    title: title,
    developer: developer,
    icon,
    scoreText: scoreText,
    genre: genre
  }));

export const GET = async (request,res) => {
    const url = new URL(request.url);
  const appId = url.searchParams.get("appId");
  if (!appId) {
    return NextResponse.json({ error: "App ID is required" }, { status: 400 });
  }
  try {
    await connectDB();    
    const [similarApps] = await Promise.all([
      gplay.similar({ appId }).catch(() => []),
    ]);
    const processedSimilarApps = await Promise.all(
      similarApps.map(async (similarApp) => {
          await AppId.findOneAndUpdate(
            { appId:similarApp.appId },
            { appId:similarApp.appId },
            { upsert: true }
          );
          const existingSimilarApp = await App.findOne({ appId: similarApp.appId });
        let newApp;
        if (existingSimilarApp) {
          existingSimilarApp.set(similarApp);
          newApp = await existingSimilarApp.save();
        } else {
          newApp = await App.create(similarApp);
        }
        return newApp;
      })
    );
    return NextResponse.json({ app: formatApps(processedSimilarApps) }, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      { message: "Error fetching app", error },
      { status: 500 }
    );
  }
};