/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
      config.module.rules.push({
          test: /\.(woff|woff2|eot|ttf|otf)$/i,
          type: 'asset/resource',
          generator: {
              filename: 'static/fonts/[name][ext]',
              publicPath: '/_next/',
          },
      });

      return config;
  },
  images: {
      domains: ['play-lh.googleusercontent.com', 'img.freepik.com'],
      remotePatterns: [
          {
              protocol: 'https',
              hostname: 'assets.example.com',
              port: '',
              pathname: '/account123/**',
          },
      ],
  },
};

export default nextConfig;
