"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/timers-ext";
exports.ids = ["vendor-chunks/timers-ext"];
exports.modules = {

/***/ "(rsc)/./node_modules/timers-ext/max-timeout.js":
/*!************************************************!*\
  !*** ./node_modules/timers-ext/max-timeout.js ***!
  \************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = 2147483647;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdGltZXJzLWV4dC9tYXgtdGltZW91dC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiIsInNvdXJjZXMiOlsid2VicGFjazovL2Fway1taXJyb3IvLi9ub2RlX21vZHVsZXMvdGltZXJzLWV4dC9tYXgtdGltZW91dC5qcz80M2I0Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5tb2R1bGUuZXhwb3J0cyA9IDIxNDc0ODM2NDc7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/timers-ext/max-timeout.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/timers-ext/valid-timeout.js":
/*!**************************************************!*\
  !*** ./node_modules/timers-ext/valid-timeout.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar toPosInt   = __webpack_require__(/*! es5-ext/number/to-pos-integer */ \"(rsc)/./node_modules/es5-ext/number/to-pos-integer.js\")\n  , maxTimeout = __webpack_require__(/*! ./max-timeout */ \"(rsc)/./node_modules/timers-ext/max-timeout.js\");\n\nmodule.exports = function (value) {\n\tvalue = toPosInt(value);\n\tif (value > maxTimeout) throw new TypeError(value + \" exceeds maximum possible timeout\");\n\treturn value;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdGltZXJzLWV4dC92YWxpZC10aW1lb3V0LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGlCQUFpQixtQkFBTyxDQUFDLDRGQUErQjtBQUN4RCxpQkFBaUIsbUJBQU8sQ0FBQyxxRUFBZTs7QUFFeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Fway1taXJyb3IvLi9ub2RlX21vZHVsZXMvdGltZXJzLWV4dC92YWxpZC10aW1lb3V0LmpzPzk1MWIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciB0b1Bvc0ludCAgID0gcmVxdWlyZShcImVzNS1leHQvbnVtYmVyL3RvLXBvcy1pbnRlZ2VyXCIpXG4gICwgbWF4VGltZW91dCA9IHJlcXVpcmUoXCIuL21heC10aW1lb3V0XCIpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uICh2YWx1ZSkge1xuXHR2YWx1ZSA9IHRvUG9zSW50KHZhbHVlKTtcblx0aWYgKHZhbHVlID4gbWF4VGltZW91dCkgdGhyb3cgbmV3IFR5cGVFcnJvcih2YWx1ZSArIFwiIGV4Y2VlZHMgbWF4aW11bSBwb3NzaWJsZSB0aW1lb3V0XCIpO1xuXHRyZXR1cm4gdmFsdWU7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/timers-ext/valid-timeout.js\n");

/***/ })

};
;