"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse5-parser-stream";
exports.ids = ["vendor-chunks/parse5-parser-stream"];
exports.modules = {

/***/ "(rsc)/./node_modules/parse5-parser-stream/dist/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/parse5-parser-stream/dist/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParserStream: () => (/* binding */ ParserStream)\n/* harmony export */ });\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! parse5 */ \"(rsc)/./node_modules/parse5/dist/index.js\");\n\n\n/* eslint-disable unicorn/consistent-function-scoping -- The rule seems to be broken here. */\n/**\n * Streaming HTML parser with scripting support.\n * A [writable stream](https://nodejs.org/api/stream.html#stream_class_stream_writable).\n *\n * @example\n *\n * ```js\n * const ParserStream = require('parse5-parser-stream');\n * const http = require('http');\n * const { finished } = require('node:stream');\n *\n * // Fetch the page content and obtain it's <head> node\n * http.get('http://inikulin.github.io/parse5/', res => {\n *     const parser = new ParserStream();\n *\n *     finished(parser, () => {\n *         console.log(parser.document.childNodes[1].childNodes[0].tagName); //> 'head'\n *     });\n *\n *     res.pipe(parser);\n * });\n * ```\n *\n */\nclass ParserStream extends node_stream__WEBPACK_IMPORTED_MODULE_0__.Writable {\n    static getFragmentStream(fragmentContext, options) {\n        const parser = parse5__WEBPACK_IMPORTED_MODULE_1__.Parser.getFragmentParser(fragmentContext, options);\n        const stream = new ParserStream(options, parser);\n        return stream;\n    }\n    /** The resulting document node. */\n    get document() {\n        return this.parser.document;\n    }\n    getFragment() {\n        return this.parser.getFragment();\n    }\n    /**\n     * @param options Parsing options.\n     */\n    constructor(options, parser = new parse5__WEBPACK_IMPORTED_MODULE_1__.Parser(options)) {\n        super({ decodeStrings: false });\n        this.parser = parser;\n        this.lastChunkWritten = false;\n        this.writeCallback = undefined;\n        this.pendingHtmlInsertions = [];\n        const resume = () => {\n            for (let i = this.pendingHtmlInsertions.length - 1; i >= 0; i--) {\n                this.parser.tokenizer.insertHtmlAtCurrentPos(this.pendingHtmlInsertions[i]);\n            }\n            this.pendingHtmlInsertions.length = 0;\n            //NOTE: keep parsing if we don't wait for the next input chunk\n            this.parser.tokenizer.resume(this.writeCallback);\n        };\n        const documentWrite = (html) => {\n            if (!this.parser.stopped) {\n                this.pendingHtmlInsertions.push(html);\n            }\n        };\n        const scriptHandler = (scriptElement) => {\n            if (this.listenerCount('script') > 0) {\n                this.parser.tokenizer.pause();\n                this.emit('script', scriptElement, documentWrite, resume);\n            }\n        };\n        this.parser.scriptHandler = scriptHandler;\n    }\n    //WritableStream implementation\n    _write(chunk, _encoding, callback) {\n        if (typeof chunk !== 'string') {\n            throw new TypeError('Parser can work only with string streams.');\n        }\n        this.writeCallback = callback;\n        this.parser.tokenizer.write(chunk, this.lastChunkWritten, this.writeCallback);\n    }\n    // TODO [engine:node@>=16]: Due to issues with Node < 16, we are overriding `end` instead of `_final`.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    end(chunk, encoding, callback) {\n        this.lastChunkWritten = true;\n        super.end(chunk || '', encoding, callback);\n    }\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/parse5-parser-stream/dist/index.js\n");

/***/ })

};
;