"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jss-plugin-global";
exports.ids = ["vendor-chunks/jss-plugin-global"];
exports.modules = {

/***/ "(ssr)/./node_modules/jss-plugin-global/dist/jss-plugin-global.esm.js":
/*!**********************************************************************!*\
  !*** ./node_modules/jss-plugin-global/dist/jss-plugin-global.esm.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var jss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jss */ \"(ssr)/./node_modules/jss/dist/jss.esm.js\");\n\n\nvar at = \"@global\";\nvar atPrefix = \"@global \";\nvar GlobalContainerRule = /*#__PURE__*/ function() {\n    function GlobalContainerRule(key, styles, options) {\n        this.type = \"global\";\n        this.at = at;\n        this.isProcessed = false;\n        this.key = key;\n        this.options = options;\n        this.rules = new jss__WEBPACK_IMPORTED_MODULE_1__.RuleList((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n            parent: this\n        }));\n        for(var selector in styles){\n            this.rules.add(selector, styles[selector]);\n        }\n        this.rules.process();\n    }\n    /**\n   * Get a rule.\n   */ var _proto = GlobalContainerRule.prototype;\n    _proto.getRule = function getRule(name) {\n        return this.rules.get(name);\n    } /**\n   * Create and register rule, run plugins.\n   */ ;\n    _proto.addRule = function addRule(name, style, options) {\n        var rule = this.rules.add(name, style, options);\n        if (rule) this.options.jss.plugins.onProcessRule(rule);\n        return rule;\n    } /**\n   * Replace rule, run plugins.\n   */ ;\n    _proto.replaceRule = function replaceRule(name, style, options) {\n        var newRule = this.rules.replace(name, style, options);\n        if (newRule) this.options.jss.plugins.onProcessRule(newRule);\n        return newRule;\n    } /**\n   * Get index of a rule.\n   */ ;\n    _proto.indexOf = function indexOf(rule) {\n        return this.rules.indexOf(rule);\n    } /**\n   * Generates a CSS string.\n   */ ;\n    _proto.toString = function toString(options) {\n        return this.rules.toString(options);\n    };\n    return GlobalContainerRule;\n}();\nvar GlobalPrefixedRule = /*#__PURE__*/ function() {\n    function GlobalPrefixedRule(key, style, options) {\n        this.type = \"global\";\n        this.at = at;\n        this.isProcessed = false;\n        this.key = key;\n        this.options = options;\n        var selector = key.substr(atPrefix.length);\n        this.rule = options.jss.createRule(selector, style, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n            parent: this\n        }));\n    }\n    var _proto2 = GlobalPrefixedRule.prototype;\n    _proto2.toString = function toString(options) {\n        return this.rule ? this.rule.toString(options) : \"\";\n    };\n    return GlobalPrefixedRule;\n}();\nvar separatorRegExp = /\\s*,\\s*/g;\nfunction addScope(selector, scope) {\n    var parts = selector.split(separatorRegExp);\n    var scoped = \"\";\n    for(var i = 0; i < parts.length; i++){\n        scoped += scope + \" \" + parts[i].trim();\n        if (parts[i + 1]) scoped += \", \";\n    }\n    return scoped;\n}\nfunction handleNestedGlobalContainerRule(rule, sheet) {\n    var options = rule.options, style = rule.style;\n    var rules = style ? style[at] : null;\n    if (!rules) return;\n    for(var name in rules){\n        sheet.addRule(name, rules[name], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n            selector: addScope(name, rule.selector)\n        }));\n    }\n    delete style[at];\n}\nfunction handlePrefixedGlobalRule(rule, sheet) {\n    var options = rule.options, style = rule.style;\n    for(var prop in style){\n        if (prop[0] !== \"@\" || prop.substr(0, at.length) !== at) continue;\n        var selector = addScope(prop.substr(at.length), rule.selector);\n        sheet.addRule(selector, style[prop], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n            selector: selector\n        }));\n        delete style[prop];\n    }\n}\n/**\n * Convert nested rules to separate, remove them from original styles.\n */ function jssGlobal() {\n    function onCreateRule(name, styles, options) {\n        if (!name) return null;\n        if (name === at) {\n            return new GlobalContainerRule(name, styles, options);\n        }\n        if (name[0] === \"@\" && name.substr(0, atPrefix.length) === atPrefix) {\n            return new GlobalPrefixedRule(name, styles, options);\n        }\n        var parent = options.parent;\n        if (parent) {\n            if (parent.type === \"global\" || parent.options.parent && parent.options.parent.type === \"global\") {\n                options.scoped = false;\n            }\n        }\n        if (!options.selector && options.scoped === false) {\n            options.selector = name;\n        }\n        return null;\n    }\n    function onProcessRule(rule, sheet) {\n        if (rule.type !== \"style\" || !sheet) return;\n        handleNestedGlobalContainerRule(rule, sheet);\n        handlePrefixedGlobalRule(rule, sheet);\n    }\n    return {\n        onCreateRule: onCreateRule,\n        onProcessRule: onProcessRule\n    };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (jssGlobal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jss-plugin-global/dist/jss-plugin-global.esm.js\n");

/***/ })

};
;