"use client";
import {
    Typography,
    Card,
    CardMedia,
    CardContent,
    Grid,
    Box,
    Pagination,
    Chip,
    Skeleton
} from "@mui/material";
import { useRouter } from "next/navigation";
import StarIcon from "@mui/icons-material/Star";
import Link from 'next/link';
import React, { useState } from "react";
import Tooltip from '@mui/material/Tooltip';
import ArrowDropDownRoundedIcon from '@mui/icons-material/ArrowDropDownRounded';
import ArrowDropUpRoundedIcon from '@mui/icons-material/ArrowDropUpRounded';

const GridViewApp = (props) => {
    const router = useRouter();
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 12;
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const displayedApps = props.apps?.slice(startIndex, endIndex) || [];

    const handleAppClick = (appId) => {
        router.push(`/app-details/${appId}`);
    };

    const getRankingColor = (ranking) => {
        if (!ranking) return 'default';
        return ranking.rank === "+" ? "success" : 
               ranking.rank === "-" ? "error" : "primary";
    };

    const getRankingIcon = (ranking) => {
        if (!ranking) return null;
        return ranking.rank === "+" ? <ArrowDropUpRoundedIcon /> : 
               ranking.rank === "-" ? <ArrowDropDownRoundedIcon /> : null;
    };

    const getTooltipText = (app) => {
        if (!props.isForCountry || !app.ranking) return '';
        
        const { rank, position } = app.ranking;
        if (rank === '+') return `Increased by ${position}`;
        if (rank === '-') return `Decreased by ${position}`;
        if (position === '=') return 'Same position';
        if (position === 'new') return 'New app';
        return '';
    };

    return (
        <Box sx={{ p: 3 }}>
            {props.loading ? (
                <Grid container spacing={3}>
                    {[...Array(itemsPerPage)].map((_, index) => (
                        <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
                            <Skeleton variant="rectangular" width={220} height={350} sx={{ borderRadius: 2 }} />
                        </Grid>
                    ))}
                </Grid>
            ) : (
                <>
                    <Grid container spacing={3}>
                        {displayedApps.length > 0 ? (
                            displayedApps.map((app) => (
                                <Grid item xs={12} sm={6} md={4} lg={2} key={app.appId}>
                                    <Card sx={{
                                        height: '100%',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        transition: 'transform 0.3s ease',
                                        '&:hover': {
                                            transform: 'translateY(-5px)',
                                            boxShadow: 3
                                        }
                                    }}>
                                        <CardMedia
                                            component="img"
                                            sx={{
                                                p: 2,
                                                aspectRatio: '1/1',
                                                objectFit: 'contain',
                                                cursor: 'pointer'
                                            }}
                                            image={app.icon}
                                            alt={app.title}
                                            onError={(e) => {
                                                e.currentTarget.onerror = null;
                                                e.currentTarget.src = "https://cdn-icons-png.flaticon.com/128/300/300218.png";
                                            }}
                                            onClick={() => handleAppClick(app.appId)}
                                        />
                                        <CardContent sx={{ flexGrow: 1 }}>
                                            <Tooltip title={app.title} arrow>
                                                <Typography 
                                                    variant="subtitle1" 
                                                    sx={{
                                                        fontWeight: 600,
                                                        whiteSpace: 'nowrap',
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        mb: 1
                                                    }}
                                                >
                                                    <Link href={`/app-details/${app.appId}`}>
                                                        {app.title}
                                                    </Link>
                                                </Typography>
                                            </Tooltip>
                                            
                                            <Typography 
                                                variant="body2" 
                                                color="text.secondary" 
                                                sx={{ 
                                                    mb: 1,
                                                    whiteSpace: 'nowrap',
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis'
                                                }}
                                            >
                                                <Link href={`/developer-apps/${app.developer}`}>
                                                    {app.developer}
                                                </Link>
                                            </Typography>

                                            <Chip 
                                                label={app.genre} 
                                                size="small" 
                                                sx={{ 
                                                    mb: 1.5,
                                                    maxWidth: '100%',
                                                    '& .MuiChip-label': {
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis'
                                                    }
                                                }} 
                                            />

                                            <Box sx={{ 
                                                display: 'flex', 
                                                justifyContent: 'space-between',
                                                alignItems: 'center'
                                            }}>
                                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                    <StarIcon sx={{ color: '#FFB400', mr: 0.5 }} />
                                                    <Typography variant="body2">
                                                        {app.scoreText}
                                                    </Typography>
                                                </Box>

                                                {props.isForCountry && app.ranking && (
                                                    <Tooltip title={getTooltipText(app)} arrow>
                                                        <Chip
                                                            icon={getRankingIcon(app.ranking)}
                                                            label={app.ranking.position}
                                                            color={getRankingColor(app.ranking)}
                                                            size="small"
                                                            sx={{ fontWeight: 600 }}
                                                        />
                                                    </Tooltip>
                                                )}
                                            </Box>
                                        </CardContent>
                                    </Card>
                                </Grid>
                            ))
                        ) : (
                            <Grid item xs={12} sx={{ 
                                display: 'flex', 
                                justifyContent: 'center', 
                                alignItems: 'center', 
                                minHeight: '40vh'
                            }}>
                                <Typography variant="h6" color="text.secondary">
                                    {`No apps found for this ${props.isForCountry ? 'country' : 'search'}`}
                                </Typography>
                            </Grid>
                        )}
                    </Grid>

                    {props.apps?.length > itemsPerPage && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                            <Pagination
                                count={Math.ceil(props.apps.length / itemsPerPage)}
                                page={currentPage}
                                onChange={(e, value) => setCurrentPage(value)}
                                color="primary"
                                shape="rounded"
                                size="large"
                            />
                        </Box>
                    )}
                </>
            )}
        </Box>
    );
};

export default GridViewApp;