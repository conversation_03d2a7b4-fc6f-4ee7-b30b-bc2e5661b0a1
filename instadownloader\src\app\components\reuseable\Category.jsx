import Link from "next/link";
import { useState } from "react";
import { categories } from "@/app/util/constants";

const Category = ({ isDetailsPage = false }) => {
  const initialCount = 24;
  const itemsPerPage = 18;
  const [showAll, setShowAll] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  const displayedCategories = isDetailsPage
    ? categories.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
    : showAll
      ? categories
      : categories.slice(0, initialCount);

  const totalPages = Math.ceil(categories.length / itemsPerPage);
  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleShowMore = () => {
    setShowAll(!showAll);
  };

  return (
    <div className="mb-3.5 p-5 bg-white rounded-md shadow-md flex flex-col">
      <h2 className="mb-2.5 text-base font-normal text-slate-500 uppercase tracking-wider">
        AI Categories
      </h2>
      <div className="block">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {displayedCategories.map((data, index) => (
            <Link
              key={index}
              className="max-w-52 max-h-12 p-1 text-sm font-normal relative float-left rounded-md hover:bg-gray-100"
              href={`/tools/${data.category}`}
              prefetch={false}
            >
              <div className="flex items-center">
                <span className="text-lg m-1 w-7 h-7 inline-block leading-7 text-center">
                  <i className={data.icon} />
                </span>
                <span className="py-2.5 inline-block w-4/4">{data.name}</span>
              </div>
            </Link>
          ))}
        </div>

        {/* Pagination controls for details page */}
        {isDetailsPage && (
          <div className="mt-4 text-right flex justify-between items-center">
            <button
              onClick={handlePreviousPage}
              className={`text-blue-500 hover:text-blue-700 font-medium text-sm ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}`}
              disabled={currentPage === 1}
            >
              <span className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-slate-900  group-hover:bg-slate-700  group-focus:ring-4 group-focus:ring-white  group-focus:outline-none">

                <i className="fa-solid  text-white fa-chevron-left" />
              </span>
            </button>
            <span className="text-sm text-gray-600">
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={handleNextPage}
              className={`text-blue-500 hover:text-blue-700 font-medium text-sm ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}`}
              disabled={currentPage === totalPages}
            >
              <span className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-slate-900  group-hover:bg-slate-700  group-focus:ring-4 group-focus:ring-white  group-focus:outline-none">

                <i className="fa-solid  text-white fa-chevron-right" />
              </span>
            </button>
          </div>
        )}

        {/* Show More/Show Less button for non-detail pages */}
        {!isDetailsPage && categories.length > initialCount && (
          <div className="mt-4 text-right">
            <button
              onClick={handleShowMore}
              className="text-blue-500 hover:text-blue-700 font-medium text-sm"
            >
              {showAll ? "Show Less" : "Show More"}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Category;

