/**
 * Intelligent App-to-AI-Tools Mapping Service
 * Provides smart matching between APK apps and AI tools
 */

/**
 * Enhanced app-to-AI-tool mappings with keyword matching
 */
export const APP_AI_MAPPINGS = {
  // Direct app name mappings
  direct: {
    'whatsapp': ['social-media', 'communication', 'chatbot'],
    'telegram': ['social-media', 'communication', 'chatbot'],
    'instagram': ['social-media', 'image-generation', 'content-creation'],
    'tiktok': ['video-generation', 'social-media', 'content-creation'],
    'youtube': ['video-generation', 'content-creation', 'transcription'],
    'spotify': ['music', 'audio', 'podcast'],
    'netflix': ['video-generation', 'entertainment', 'content-creation'],
    'canva': ['graphic-design', 'image-generation', 'presentation'],
    'photoshop': ['image-generation', 'art', 'graphic-design'],
    'lightroom': ['image-generation', 'photography', 'art'],
    'figma': ['graphic-design', 'design', 'prototyping'],
    'notion': ['productivity', 'note-taking', 'organization'],
    'slack': ['communication', 'productivity', 'team-collaboration'],
    'discord': ['communication', 'social-media', 'gaming'],
    'zoom': ['communication', 'video-conferencing', 'productivity'],
    'teams': ['communication', 'productivity', 'collaboration'],
    'gmail': ['email-generator', 'communication', 'productivity'],
    'outlook': ['email-generator', 'communication', 'productivity'],
    'chrome': ['productivity', 'web-browsing', 'research'],
    'firefox': ['productivity', 'web-browsing', 'research'],
    'office': ['productivity', 'document-generation', 'presentation'],
    'word': ['writing-generator', 'document-generation', 'productivity'],
    'excel': ['data-analysis', 'productivity', 'automation'],
    'powerpoint': ['presentation', 'graphic-design', 'productivity'],
    'adobe': ['graphic-design', 'image-generation', 'video-generation'],
    'camera': ['image-generation', 'photography', 'art'],
    'gallery': ['image-generation', 'photography', 'organization'],
    'music': ['music', 'audio', 'entertainment'],
    'video': ['video-generation', 'entertainment', 'editing'],
    'calculator': ['productivity', 'math', 'automation'],
    'calendar': ['productivity', 'scheduling', 'organization'],
    'notes': ['note-taking', 'productivity', 'writing-generator'],
    'weather': ['lifestyle', 'productivity', 'information'],
    'maps': ['navigation', 'travel', 'productivity'],
    'translator': ['translation', 'language', 'communication'],
    'keyboard': ['productivity', 'typing', 'communication'],
    'launcher': ['productivity', 'customization', 'automation'],
    'vpn': ['security', 'privacy', 'productivity'],
    'antivirus': ['security', 'privacy', 'productivity'],
    'cleaner': ['productivity', 'optimization', 'automation'],
    'battery': ['productivity', 'optimization', 'monitoring'],
    'file': ['productivity', 'organization', 'automation'],
    'download': ['productivity', 'file-management', 'automation']
  },

  // Category-based mappings
  category: {
    'communication': ['social-media', 'email-generator', 'chatbot', 'translation'],
    'social': ['social-media', 'content-creation', 'image-generation'],
    'productivity': ['productivity', 'automation', 'organization', 'note-taking'],
    'business': ['business', 'productivity', 'automation', 'finace'],
    'education': ['education', 'research', 'writing-generator', 'translation'],
    'entertainment': ['video-generation', 'music', 'content-creation', 'gaming'],
    'art-design': ['art', 'graphic-design', 'image-generation', 'avatar'],
    'photography': ['image-generation', 'art', 'graphic-design', 'avatar'],
    'music-audio': ['music', 'audio', 'podcast', 'transcription'],
    'video-players': ['video-generation', 'entertainment', 'transcription'],
    'news-magazines': ['blog-generator', 'writing-generator', 'research'],
    'books-reference': ['education', 'research', 'writing-generator', 'translation'],
    'health-fitness': ['health', 'lifestyle', 'monitoring', 'coaching'],
    'lifestyle': ['lifestyle', 'health', 'organization', 'coaching'],
    'travel-local': ['travel', 'lifestyle', 'translation', 'navigation'],
    'shopping': ['e-commerce', 'recommendation', 'price-comparison'],
    'finance': ['finace', 'business', 'automation', 'analysis'],
    'tools': ['productivity', 'automation', 'developer-tools', 'optimization'],
    'personalization': ['avatar', 'customization', 'art', 'graphic-design'],
    'weather': ['lifestyle', 'information', 'prediction'],
    'maps-navigation': ['navigation', 'travel', 'productivity'],
    'auto-vehicles': ['automation', 'monitoring', 'productivity'],
    'dating': ['social-media', 'communication', 'lifestyle'],
    'food-drink': ['lifestyle', 'recommendation', 'health'],
    'house-home': ['lifestyle', 'automation', 'monitoring'],
    'libraries-demo': ['developer-tools', 'education', 'productivity'],
    'medical': ['health', 'monitoring', 'analysis', 'research'],
    'parenting': ['education', 'lifestyle', 'monitoring'],
    'sports': ['lifestyle', 'health', 'monitoring', 'analysis'],
    'beauty': ['lifestyle', 'image-generation', 'recommendation']
  },

  // Keyword-based mappings for app descriptions/titles
  keywords: {
    'chat': ['chatbot', 'communication', 'social-media'],
    'message': ['communication', 'social-media', 'email-generator'],
    'photo': ['image-generation', 'photography', 'art'],
    'camera': ['image-generation', 'photography', 'art'],
    'video': ['video-generation', 'entertainment', 'content-creation'],
    'music': ['music', 'audio', 'entertainment'],
    'edit': ['image-generation', 'video-generation', 'content-creation'],
    'design': ['graphic-design', 'art', 'image-generation'],
    'write': ['writing-generator', 'blog-generator', 'productivity'],
    'note': ['note-taking', 'productivity', 'organization'],
    'task': ['productivity', 'organization', 'automation'],
    'calendar': ['productivity', 'scheduling', 'organization'],
    'email': ['email-generator', 'communication', 'productivity'],
    'translate': ['translation', 'language', 'communication'],
    'learn': ['education', 'research', 'skill-development'],
    'fitness': ['health', 'lifestyle', 'monitoring'],
    'health': ['health', 'monitoring', 'analysis'],
    'game': ['gaming', 'entertainment', 'content-creation'],
    'social': ['social-media', 'communication', 'content-creation'],
    'business': ['business', 'productivity', 'finace'],
    'finance': ['finace', 'business', 'analysis'],
    'shop': ['e-commerce', 'recommendation', 'price-comparison'],
    'travel': ['travel', 'lifestyle', 'navigation'],
    'news': ['blog-generator', 'research', 'information'],
    'weather': ['lifestyle', 'information', 'prediction'],
    'security': ['security', 'privacy', 'monitoring'],
    'clean': ['productivity', 'optimization', 'automation'],
    'optimize': ['productivity', 'optimization', 'automation'],
    'backup': ['productivity', 'automation', 'security'],
    'scan': ['productivity', 'security', 'analysis'],
    'voice': ['audio', 'transcription', 'communication'],
    'speech': ['audio', 'transcription', 'communication'],
    'ai': ['artificial-intelligence', 'automation', 'productivity'],
    'smart': ['automation', 'productivity', 'artificial-intelligence'],
    'auto': ['automation', 'productivity', 'optimization']
  }
};

/**
 * Get relevant AI tool categories for an app
 */
export const getRelevantAICategories = (appDetails) => {
  const categories = new Set();
  
  if (!appDetails) return ['productivity'];
  
  const appId = (appDetails.appId || '').toLowerCase();
  const title = (appDetails.title || '').toLowerCase();
  const category = (appDetails.category || '').toLowerCase();
  const description = (appDetails.description || '').toLowerCase();
  
  // Check direct app mappings
  for (const [appName, aiCategories] of Object.entries(APP_AI_MAPPINGS.direct)) {
    if (appId.includes(appName) || title.includes(appName)) {
      aiCategories.forEach(cat => categories.add(cat));
    }
  }
  
  // Check category mappings
  const normalizedCategory = category.replace(/[^a-z0-9]/g, '-');
  if (APP_AI_MAPPINGS.category[normalizedCategory]) {
    APP_AI_MAPPINGS.category[normalizedCategory].forEach(cat => categories.add(cat));
  }
  
  // Check keyword mappings
  const searchText = `${title} ${description}`.toLowerCase();
  for (const [keyword, aiCategories] of Object.entries(APP_AI_MAPPINGS.keywords)) {
    if (searchText.includes(keyword)) {
      aiCategories.forEach(cat => categories.add(cat));
    }
  }
  
  // Return array with most relevant first, fallback to productivity
  const result = Array.from(categories);
  return result.length > 0 ? result : ['productivity'];
};

/**
 * Get confidence score for app-to-AI-tool matching
 */
export const getMatchingConfidence = (appDetails, aiCategory) => {
  const relevantCategories = getRelevantAICategories(appDetails);
  const index = relevantCategories.indexOf(aiCategory);
  
  if (index === -1) return 0;
  if (index === 0) return 1.0;
  if (index === 1) return 0.8;
  if (index === 2) return 0.6;
  return 0.4;
};

/**
 * Get the best AI tool category for an app
 */
export const getBestAICategory = (appDetails) => {
  const categories = getRelevantAICategories(appDetails);
  return categories[0] || 'productivity';
};

/**
 * Generate smart AI tools URL with best matching category
 */
export const generateSmartAIToolsURL = (appDetails, baseURL) => {
  const bestCategory = getBestAICategory(appDetails);
  const appId = appDetails?.appId || '';
  const appTitle = appDetails?.title || '';
  
  return `${baseURL}/tools/${bestCategory}?ref=apk&app=${encodeURIComponent(appId)}&title=${encodeURIComponent(appTitle)}`;
};
