"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/whatwg-mimetype";
exports.ids = ["vendor-chunks/whatwg-mimetype"];
exports.modules = {

/***/ "(rsc)/./node_modules/whatwg-mimetype/lib/mime-type-parameters.js":
/*!******************************************************************!*\
  !*** ./node_modules/whatwg-mimetype/lib/mime-type-parameters.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst {\n  asciiLowercase,\n  solelyContainsHTTPTokenCodePoints,\n  soleyContainsHTTPQuotedStringTokenCodePoints\n} = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/whatwg-mimetype/lib/utils.js\");\n\nmodule.exports = class MIMETypeParameters {\n  constructor(map) {\n    this._map = map;\n  }\n\n  get size() {\n    return this._map.size;\n  }\n\n  get(name) {\n    name = asciiLowercase(String(name));\n    return this._map.get(name);\n  }\n\n  has(name) {\n    name = asciiLowercase(String(name));\n    return this._map.has(name);\n  }\n\n  set(name, value) {\n    name = asciiLowercase(String(name));\n    value = String(value);\n\n    if (!solelyContainsHTTPTokenCodePoints(name)) {\n      throw new Error(`Invalid MIME type parameter name \"${name}\": only HTTP token code points are valid.`);\n    }\n    if (!soleyContainsHTTPQuotedStringTokenCodePoints(value)) {\n      throw new Error(`Invalid MIME type parameter value \"${value}\": only HTTP quoted-string token code points are ` +\n                      `valid.`);\n    }\n\n    return this._map.set(name, value);\n  }\n\n  clear() {\n    this._map.clear();\n  }\n\n  delete(name) {\n    name = asciiLowercase(String(name));\n    return this._map.delete(name);\n  }\n\n  forEach(callbackFn, thisArg) {\n    this._map.forEach(callbackFn, thisArg);\n  }\n\n  keys() {\n    return this._map.keys();\n  }\n\n  values() {\n    return this._map.values();\n  }\n\n  entries() {\n    return this._map.entries();\n  }\n\n  [Symbol.iterator]() {\n    return this._map[Symbol.iterator]();\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-mimetype/lib/mime-type-parameters.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-mimetype/lib/mime-type.js":
/*!*******************************************************!*\
  !*** ./node_modules/whatwg-mimetype/lib/mime-type.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst MIMETypeParameters = __webpack_require__(/*! ./mime-type-parameters.js */ \"(rsc)/./node_modules/whatwg-mimetype/lib/mime-type-parameters.js\");\nconst parse = __webpack_require__(/*! ./parser.js */ \"(rsc)/./node_modules/whatwg-mimetype/lib/parser.js\");\nconst serialize = __webpack_require__(/*! ./serializer.js */ \"(rsc)/./node_modules/whatwg-mimetype/lib/serializer.js\");\nconst {\n  asciiLowercase,\n  solelyContainsHTTPTokenCodePoints\n} = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/whatwg-mimetype/lib/utils.js\");\n\nmodule.exports = class MIMEType {\n  constructor(string) {\n    string = String(string);\n    const result = parse(string);\n    if (result === null) {\n      throw new Error(`Could not parse MIME type string \"${string}\"`);\n    }\n\n    this._type = result.type;\n    this._subtype = result.subtype;\n    this._parameters = new MIMETypeParameters(result.parameters);\n  }\n\n  static parse(string) {\n    try {\n      return new this(string);\n    } catch (e) {\n      return null;\n    }\n  }\n\n  get essence() {\n    return `${this.type}/${this.subtype}`;\n  }\n\n  get type() {\n    return this._type;\n  }\n\n  set type(value) {\n    value = asciiLowercase(String(value));\n\n    if (value.length === 0) {\n      throw new Error(\"Invalid type: must be a non-empty string\");\n    }\n    if (!solelyContainsHTTPTokenCodePoints(value)) {\n      throw new Error(`Invalid type ${value}: must contain only HTTP token code points`);\n    }\n\n    this._type = value;\n  }\n\n  get subtype() {\n    return this._subtype;\n  }\n\n  set subtype(value) {\n    value = asciiLowercase(String(value));\n\n    if (value.length === 0) {\n      throw new Error(\"Invalid subtype: must be a non-empty string\");\n    }\n    if (!solelyContainsHTTPTokenCodePoints(value)) {\n      throw new Error(`Invalid subtype ${value}: must contain only HTTP token code points`);\n    }\n\n    this._subtype = value;\n  }\n\n  get parameters() {\n    return this._parameters;\n  }\n\n  toString() {\n    // The serialize function works on both \"MIME type records\" (i.e. the results of parse) and on this class, since\n    // this class's interface is identical.\n    return serialize(this);\n  }\n\n  isJavaScript({ prohibitParameters = false } = {}) {\n    switch (this._type) {\n      case \"text\": {\n        switch (this._subtype) {\n          case \"ecmascript\":\n          case \"javascript\":\n          case \"javascript1.0\":\n          case \"javascript1.1\":\n          case \"javascript1.2\":\n          case \"javascript1.3\":\n          case \"javascript1.4\":\n          case \"javascript1.5\":\n          case \"jscript\":\n          case \"livescript\":\n          case \"x-ecmascript\":\n          case \"x-javascript\": {\n            return !prohibitParameters || this._parameters.size === 0;\n          }\n          default: {\n            return false;\n          }\n        }\n      }\n      case \"application\": {\n        switch (this._subtype) {\n          case \"ecmascript\":\n          case \"javascript\":\n          case \"x-ecmascript\":\n          case \"x-javascript\": {\n            return !prohibitParameters || this._parameters.size === 0;\n          }\n          default: {\n            return false;\n          }\n        }\n      }\n      default: {\n        return false;\n      }\n    }\n  }\n  isXML() {\n    return (this._subtype === \"xml\" && (this._type === \"text\" || this._type === \"application\")) ||\n           this._subtype.endsWith(\"+xml\");\n  }\n  isHTML() {\n    return this._subtype === \"html\" && this._type === \"text\";\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-mimetype/lib/mime-type.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-mimetype/lib/parser.js":
/*!****************************************************!*\
  !*** ./node_modules/whatwg-mimetype/lib/parser.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst {\n  removeLeadingAndTrailingHTTPWhitespace,\n  removeTrailingHTTPWhitespace,\n  isHTTPWhitespaceChar,\n  solelyContainsHTTPTokenCodePoints,\n  soleyContainsHTTPQuotedStringTokenCodePoints,\n  asciiLowercase,\n  collectAnHTTPQuotedString\n} = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/whatwg-mimetype/lib/utils.js\");\n\nmodule.exports = input => {\n  input = removeLeadingAndTrailingHTTPWhitespace(input);\n\n  let position = 0;\n  let type = \"\";\n  while (position < input.length && input[position] !== \"/\") {\n    type += input[position];\n    ++position;\n  }\n\n  if (type.length === 0 || !solelyContainsHTTPTokenCodePoints(type)) {\n    return null;\n  }\n\n  if (position >= input.length) {\n    return null;\n  }\n\n  // Skips past \"/\"\n  ++position;\n\n  let subtype = \"\";\n  while (position < input.length && input[position] !== \";\") {\n    subtype += input[position];\n    ++position;\n  }\n\n  subtype = removeTrailingHTTPWhitespace(subtype);\n\n  if (subtype.length === 0 || !solelyContainsHTTPTokenCodePoints(subtype)) {\n    return null;\n  }\n\n  const mimeType = {\n    type: asciiLowercase(type),\n    subtype: asciiLowercase(subtype),\n    parameters: new Map()\n  };\n\n  while (position < input.length) {\n    // Skip past \";\"\n    ++position;\n\n    while (isHTTPWhitespaceChar(input[position])) {\n      ++position;\n    }\n\n    let parameterName = \"\";\n    while (position < input.length && input[position] !== \";\" && input[position] !== \"=\") {\n      parameterName += input[position];\n      ++position;\n    }\n    parameterName = asciiLowercase(parameterName);\n\n    if (position < input.length) {\n      if (input[position] === \";\") {\n        continue;\n      }\n\n      // Skip past \"=\"\n      ++position;\n    }\n\n    let parameterValue = null;\n    if (input[position] === \"\\\"\") {\n      [parameterValue, position] = collectAnHTTPQuotedString(input, position);\n\n      while (position < input.length && input[position] !== \";\") {\n        ++position;\n      }\n    } else {\n      parameterValue = \"\";\n      while (position < input.length && input[position] !== \";\") {\n        parameterValue += input[position];\n        ++position;\n      }\n\n      parameterValue = removeTrailingHTTPWhitespace(parameterValue);\n\n      if (parameterValue === \"\") {\n        continue;\n      }\n    }\n\n    if (parameterName.length > 0 &&\n        solelyContainsHTTPTokenCodePoints(parameterName) &&\n        soleyContainsHTTPQuotedStringTokenCodePoints(parameterValue) &&\n        !mimeType.parameters.has(parameterName)) {\n      mimeType.parameters.set(parameterName, parameterValue);\n    }\n  }\n\n  return mimeType;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-mimetype/lib/parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-mimetype/lib/serializer.js":
/*!********************************************************!*\
  !*** ./node_modules/whatwg-mimetype/lib/serializer.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst { solelyContainsHTTPTokenCodePoints } = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/whatwg-mimetype/lib/utils.js\");\n\nmodule.exports = mimeType => {\n  let serialization = `${mimeType.type}/${mimeType.subtype}`;\n\n  if (mimeType.parameters.size === 0) {\n    return serialization;\n  }\n\n  for (let [name, value] of mimeType.parameters) {\n    serialization += \";\";\n    serialization += name;\n    serialization += \"=\";\n\n    if (!solelyContainsHTTPTokenCodePoints(value) || value.length === 0) {\n      value = value.replace(/([\"\\\\])/ug, \"\\\\$1\");\n      value = `\"${value}\"`;\n    }\n\n    serialization += value;\n  }\n\n  return serialization;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvd2hhdHdnLW1pbWV0eXBlL2xpYi9zZXJpYWxpemVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsUUFBUSxvQ0FBb0MsRUFBRSxtQkFBTyxDQUFDLHFFQUFZOztBQUVsRTtBQUNBLHlCQUF5QixjQUFjLEdBQUcsaUJBQWlCOztBQUUzRDtBQUNBO0FBQ0E7O0FBRUE7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esa0JBQWtCLE1BQU07QUFDeEI7O0FBRUE7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL25vZGVfbW9kdWxlcy93aGF0d2ctbWltZXR5cGUvbGliL3NlcmlhbGl6ZXIuanM/MjFkMCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbmNvbnN0IHsgc29sZWx5Q29udGFpbnNIVFRQVG9rZW5Db2RlUG9pbnRzIH0gPSByZXF1aXJlKFwiLi91dGlscy5qc1wiKTtcblxubW9kdWxlLmV4cG9ydHMgPSBtaW1lVHlwZSA9PiB7XG4gIGxldCBzZXJpYWxpemF0aW9uID0gYCR7bWltZVR5cGUudHlwZX0vJHttaW1lVHlwZS5zdWJ0eXBlfWA7XG5cbiAgaWYgKG1pbWVUeXBlLnBhcmFtZXRlcnMuc2l6ZSA9PT0gMCkge1xuICAgIHJldHVybiBzZXJpYWxpemF0aW9uO1xuICB9XG5cbiAgZm9yIChsZXQgW25hbWUsIHZhbHVlXSBvZiBtaW1lVHlwZS5wYXJhbWV0ZXJzKSB7XG4gICAgc2VyaWFsaXphdGlvbiArPSBcIjtcIjtcbiAgICBzZXJpYWxpemF0aW9uICs9IG5hbWU7XG4gICAgc2VyaWFsaXphdGlvbiArPSBcIj1cIjtcblxuICAgIGlmICghc29sZWx5Q29udGFpbnNIVFRQVG9rZW5Db2RlUG9pbnRzKHZhbHVlKSB8fCB2YWx1ZS5sZW5ndGggPT09IDApIHtcbiAgICAgIHZhbHVlID0gdmFsdWUucmVwbGFjZSgvKFtcIlxcXFxdKS91ZywgXCJcXFxcJDFcIik7XG4gICAgICB2YWx1ZSA9IGBcIiR7dmFsdWV9XCJgO1xuICAgIH1cblxuICAgIHNlcmlhbGl6YXRpb24gKz0gdmFsdWU7XG4gIH1cblxuICByZXR1cm4gc2VyaWFsaXphdGlvbjtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-mimetype/lib/serializer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-mimetype/lib/utils.js":
/*!***************************************************!*\
  !*** ./node_modules/whatwg-mimetype/lib/utils.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nexports.removeLeadingAndTrailingHTTPWhitespace = string => {\n  return string.replace(/^[ \\t\\n\\r]+/u, \"\").replace(/[ \\t\\n\\r]+$/u, \"\");\n};\n\nexports.removeTrailingHTTPWhitespace = string => {\n  return string.replace(/[ \\t\\n\\r]+$/u, \"\");\n};\n\nexports.isHTTPWhitespaceChar = char => {\n  return char === \" \" || char === \"\\t\" || char === \"\\n\" || char === \"\\r\";\n};\n\nexports.solelyContainsHTTPTokenCodePoints = string => {\n  return /^[-!#$%&'*+.^_`|~A-Za-z0-9]*$/u.test(string);\n};\n\nexports.soleyContainsHTTPQuotedStringTokenCodePoints = string => {\n  return /^[\\t\\u0020-\\u007E\\u0080-\\u00FF]*$/u.test(string);\n};\n\nexports.asciiLowercase = string => {\n  return string.replace(/[A-Z]/ug, l => l.toLowerCase());\n};\n\n// This variant only implements it with the extract-value flag set.\nexports.collectAnHTTPQuotedString = (input, position) => {\n  let value = \"\";\n\n  position++;\n\n  while (true) {\n    while (position < input.length && input[position] !== \"\\\"\" && input[position] !== \"\\\\\") {\n      value += input[position];\n      ++position;\n    }\n\n    if (position >= input.length) {\n      break;\n    }\n\n    const quoteOrBackslash = input[position];\n    ++position;\n\n    if (quoteOrBackslash === \"\\\\\") {\n      if (position >= input.length) {\n        value += \"\\\\\";\n        break;\n      }\n\n      value += input[position];\n      ++position;\n    } else {\n      break;\n    }\n  }\n\n  return [value, position];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-mimetype/lib/utils.js\n");

/***/ })

};
;