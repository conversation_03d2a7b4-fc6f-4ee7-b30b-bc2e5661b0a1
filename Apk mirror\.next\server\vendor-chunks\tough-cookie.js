/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tough-cookie";
exports.ids = ["vendor-chunks/tough-cookie"];
exports.modules = {

/***/ "(rsc)/./node_modules/tough-cookie/lib/cookie.js":
/*!*************************************************!*\
  !*** ./node_modules/tough-cookie/lib/cookie.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("/*!\n * Copyright (c) 2015-2020, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\nconst punycode = __webpack_require__(/*! punycode/ */ \"(rsc)/./node_modules/punycode/punycode.es6.js\");\nconst urlParse = __webpack_require__(/*! url-parse */ \"(rsc)/./node_modules/url-parse/index.js\");\nconst pubsuffix = __webpack_require__(/*! ./pubsuffix-psl */ \"(rsc)/./node_modules/tough-cookie/lib/pubsuffix-psl.js\");\nconst Store = (__webpack_require__(/*! ./store */ \"(rsc)/./node_modules/tough-cookie/lib/store.js\").Store);\nconst MemoryCookieStore = (__webpack_require__(/*! ./memstore */ \"(rsc)/./node_modules/tough-cookie/lib/memstore.js\").MemoryCookieStore);\nconst pathMatch = (__webpack_require__(/*! ./pathMatch */ \"(rsc)/./node_modules/tough-cookie/lib/pathMatch.js\").pathMatch);\nconst validators = __webpack_require__(/*! ./validators.js */ \"(rsc)/./node_modules/tough-cookie/lib/validators.js\");\nconst VERSION = __webpack_require__(/*! ./version */ \"(rsc)/./node_modules/tough-cookie/lib/version.js\");\nconst { fromCallback } = __webpack_require__(/*! universalify */ \"(rsc)/./node_modules/universalify/index.js\");\nconst { getCustomInspectSymbol } = __webpack_require__(/*! ./utilHelper */ \"(rsc)/./node_modules/tough-cookie/lib/utilHelper.js\");\n\n// From RFC6265 S4.1.1\n// note that it excludes \\x3B \";\"\nconst COOKIE_OCTETS = /^[\\x21\\x23-\\x2B\\x2D-\\x3A\\x3C-\\x5B\\x5D-\\x7E]+$/;\n\nconst CONTROL_CHARS = /[\\x00-\\x1F]/;\n\n// From Chromium // '\\r', '\\n' and '\\0' should be treated as a terminator in\n// the \"relaxed\" mode, see:\n// https://github.com/ChromiumWebApps/chromium/blob/b3d3b4da8bb94c1b2e061600df106d590fda3620/net/cookies/parsed_cookie.cc#L60\nconst TERMINATORS = [\"\\n\", \"\\r\", \"\\0\"];\n\n// RFC6265 S4.1.1 defines path value as 'any CHAR except CTLs or \";\"'\n// Note ';' is \\x3B\nconst PATH_VALUE = /[\\x20-\\x3A\\x3C-\\x7E]+/;\n\n// date-time parsing constants (RFC6265 S5.1.1)\n\nconst DATE_DELIM = /[\\x09\\x20-\\x2F\\x3B-\\x40\\x5B-\\x60\\x7B-\\x7E]/;\n\nconst MONTH_TO_NUM = {\n  jan: 0,\n  feb: 1,\n  mar: 2,\n  apr: 3,\n  may: 4,\n  jun: 5,\n  jul: 6,\n  aug: 7,\n  sep: 8,\n  oct: 9,\n  nov: 10,\n  dec: 11\n};\n\nconst MAX_TIME = 2147483647000; // 31-bit max\nconst MIN_TIME = 0; // 31-bit min\nconst SAME_SITE_CONTEXT_VAL_ERR =\n  'Invalid sameSiteContext option for getCookies(); expected one of \"strict\", \"lax\", or \"none\"';\n\nfunction checkSameSiteContext(value) {\n  validators.validate(validators.isNonEmptyString(value), value);\n  const context = String(value).toLowerCase();\n  if (context === \"none\" || context === \"lax\" || context === \"strict\") {\n    return context;\n  } else {\n    return null;\n  }\n}\n\nconst PrefixSecurityEnum = Object.freeze({\n  SILENT: \"silent\",\n  STRICT: \"strict\",\n  DISABLED: \"unsafe-disabled\"\n});\n\n// Dumped from ip-regex@4.0.0, with the following changes:\n// * all capturing groups converted to non-capturing -- \"(?:)\"\n// * support for IPv6 Scoped Literal (\"%eth1\") removed\n// * lowercase hexadecimal only\nconst IP_REGEX_LOWERCASE = /(?:^(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}$)|(?:^(?:(?:[a-f\\d]{1,4}:){7}(?:[a-f\\d]{1,4}|:)|(?:[a-f\\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|:[a-f\\d]{1,4}|:)|(?:[a-f\\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-f\\d]{1,4}){1,2}|:)|(?:[a-f\\d]{1,4}:){4}(?:(?::[a-f\\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-f\\d]{1,4}){1,3}|:)|(?:[a-f\\d]{1,4}:){3}(?:(?::[a-f\\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-f\\d]{1,4}){1,4}|:)|(?:[a-f\\d]{1,4}:){2}(?:(?::[a-f\\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-f\\d]{1,4}){1,5}|:)|(?:[a-f\\d]{1,4}:){1}(?:(?::[a-f\\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-f\\d]{1,4}){1,6}|:)|(?::(?:(?::[a-f\\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-f\\d]{1,4}){1,7}|:)))$)/;\nconst IP_V6_REGEX = `\n\\\\[?(?:\n(?:[a-fA-F\\\\d]{1,4}:){7}(?:[a-fA-F\\\\d]{1,4}|:)|\n(?:[a-fA-F\\\\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|:[a-fA-F\\\\d]{1,4}|:)|\n(?:[a-fA-F\\\\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|(?::[a-fA-F\\\\d]{1,4}){1,2}|:)|\n(?:[a-fA-F\\\\d]{1,4}:){4}(?:(?::[a-fA-F\\\\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|(?::[a-fA-F\\\\d]{1,4}){1,3}|:)|\n(?:[a-fA-F\\\\d]{1,4}:){3}(?:(?::[a-fA-F\\\\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|(?::[a-fA-F\\\\d]{1,4}){1,4}|:)|\n(?:[a-fA-F\\\\d]{1,4}:){2}(?:(?::[a-fA-F\\\\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|(?::[a-fA-F\\\\d]{1,4}){1,5}|:)|\n(?:[a-fA-F\\\\d]{1,4}:){1}(?:(?::[a-fA-F\\\\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|(?::[a-fA-F\\\\d]{1,4}){1,6}|:)|\n(?::(?:(?::[a-fA-F\\\\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|(?::[a-fA-F\\\\d]{1,4}){1,7}|:))\n)(?:%[0-9a-zA-Z]{1,})?\\\\]?\n`\n  .replace(/\\s*\\/\\/.*$/gm, \"\")\n  .replace(/\\n/g, \"\")\n  .trim();\nconst IP_V6_REGEX_OBJECT = new RegExp(`^${IP_V6_REGEX}$`);\n\n/*\n * Parses a Natural number (i.e., non-negative integer) with either the\n *    <min>*<max>DIGIT ( non-digit *OCTET )\n * or\n *    <min>*<max>DIGIT\n * grammar (RFC6265 S5.1.1).\n *\n * The \"trailingOK\" boolean controls if the grammar accepts a\n * \"( non-digit *OCTET )\" trailer.\n */\nfunction parseDigits(token, minDigits, maxDigits, trailingOK) {\n  let count = 0;\n  while (count < token.length) {\n    const c = token.charCodeAt(count);\n    // \"non-digit = %x00-2F / %x3A-FF\"\n    if (c <= 0x2f || c >= 0x3a) {\n      break;\n    }\n    count++;\n  }\n\n  // constrain to a minimum and maximum number of digits.\n  if (count < minDigits || count > maxDigits) {\n    return null;\n  }\n\n  if (!trailingOK && count != token.length) {\n    return null;\n  }\n\n  return parseInt(token.substr(0, count), 10);\n}\n\nfunction parseTime(token) {\n  const parts = token.split(\":\");\n  const result = [0, 0, 0];\n\n  /* RF6256 S5.1.1:\n   *      time            = hms-time ( non-digit *OCTET )\n   *      hms-time        = time-field \":\" time-field \":\" time-field\n   *      time-field      = 1*2DIGIT\n   */\n\n  if (parts.length !== 3) {\n    return null;\n  }\n\n  for (let i = 0; i < 3; i++) {\n    // \"time-field\" must be strictly \"1*2DIGIT\", HOWEVER, \"hms-time\" can be\n    // followed by \"( non-digit *OCTET )\" so therefore the last time-field can\n    // have a trailer\n    const trailingOK = i == 2;\n    const num = parseDigits(parts[i], 1, 2, trailingOK);\n    if (num === null) {\n      return null;\n    }\n    result[i] = num;\n  }\n\n  return result;\n}\n\nfunction parseMonth(token) {\n  token = String(token)\n    .substr(0, 3)\n    .toLowerCase();\n  const num = MONTH_TO_NUM[token];\n  return num >= 0 ? num : null;\n}\n\n/*\n * RFC6265 S5.1.1 date parser (see RFC for full grammar)\n */\nfunction parseDate(str) {\n  if (!str) {\n    return;\n  }\n\n  /* RFC6265 S5.1.1:\n   * 2. Process each date-token sequentially in the order the date-tokens\n   * appear in the cookie-date\n   */\n  const tokens = str.split(DATE_DELIM);\n  if (!tokens) {\n    return;\n  }\n\n  let hour = null;\n  let minute = null;\n  let second = null;\n  let dayOfMonth = null;\n  let month = null;\n  let year = null;\n\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i].trim();\n    if (!token.length) {\n      continue;\n    }\n\n    let result;\n\n    /* 2.1. If the found-time flag is not set and the token matches the time\n     * production, set the found-time flag and set the hour- value,\n     * minute-value, and second-value to the numbers denoted by the digits in\n     * the date-token, respectively.  Skip the remaining sub-steps and continue\n     * to the next date-token.\n     */\n    if (second === null) {\n      result = parseTime(token);\n      if (result) {\n        hour = result[0];\n        minute = result[1];\n        second = result[2];\n        continue;\n      }\n    }\n\n    /* 2.2. If the found-day-of-month flag is not set and the date-token matches\n     * the day-of-month production, set the found-day-of- month flag and set\n     * the day-of-month-value to the number denoted by the date-token.  Skip\n     * the remaining sub-steps and continue to the next date-token.\n     */\n    if (dayOfMonth === null) {\n      // \"day-of-month = 1*2DIGIT ( non-digit *OCTET )\"\n      result = parseDigits(token, 1, 2, true);\n      if (result !== null) {\n        dayOfMonth = result;\n        continue;\n      }\n    }\n\n    /* 2.3. If the found-month flag is not set and the date-token matches the\n     * month production, set the found-month flag and set the month-value to\n     * the month denoted by the date-token.  Skip the remaining sub-steps and\n     * continue to the next date-token.\n     */\n    if (month === null) {\n      result = parseMonth(token);\n      if (result !== null) {\n        month = result;\n        continue;\n      }\n    }\n\n    /* 2.4. If the found-year flag is not set and the date-token matches the\n     * year production, set the found-year flag and set the year-value to the\n     * number denoted by the date-token.  Skip the remaining sub-steps and\n     * continue to the next date-token.\n     */\n    if (year === null) {\n      // \"year = 2*4DIGIT ( non-digit *OCTET )\"\n      result = parseDigits(token, 2, 4, true);\n      if (result !== null) {\n        year = result;\n        /* From S5.1.1:\n         * 3.  If the year-value is greater than or equal to 70 and less\n         * than or equal to 99, increment the year-value by 1900.\n         * 4.  If the year-value is greater than or equal to 0 and less\n         * than or equal to 69, increment the year-value by 2000.\n         */\n        if (year >= 70 && year <= 99) {\n          year += 1900;\n        } else if (year >= 0 && year <= 69) {\n          year += 2000;\n        }\n      }\n    }\n  }\n\n  /* RFC 6265 S5.1.1\n   * \"5. Abort these steps and fail to parse the cookie-date if:\n   *     *  at least one of the found-day-of-month, found-month, found-\n   *        year, or found-time flags is not set,\n   *     *  the day-of-month-value is less than 1 or greater than 31,\n   *     *  the year-value is less than 1601,\n   *     *  the hour-value is greater than 23,\n   *     *  the minute-value is greater than 59, or\n   *     *  the second-value is greater than 59.\n   *     (Note that leap seconds cannot be represented in this syntax.)\"\n   *\n   * So, in order as above:\n   */\n  if (\n    dayOfMonth === null ||\n    month === null ||\n    year === null ||\n    second === null ||\n    dayOfMonth < 1 ||\n    dayOfMonth > 31 ||\n    year < 1601 ||\n    hour > 23 ||\n    minute > 59 ||\n    second > 59\n  ) {\n    return;\n  }\n\n  return new Date(Date.UTC(year, month, dayOfMonth, hour, minute, second));\n}\n\nfunction formatDate(date) {\n  validators.validate(validators.isDate(date), date);\n  return date.toUTCString();\n}\n\n// S5.1.2 Canonicalized Host Names\nfunction canonicalDomain(str) {\n  if (str == null) {\n    return null;\n  }\n  str = str.trim().replace(/^\\./, \"\"); // S4.1.2.3 & S5.2.3: ignore leading .\n\n  if (IP_V6_REGEX_OBJECT.test(str)) {\n    str = str.replace(\"[\", \"\").replace(\"]\", \"\");\n  }\n\n  // convert to IDN if any non-ASCII characters\n  if (punycode && /[^\\u0001-\\u007f]/.test(str)) {\n    str = punycode.toASCII(str);\n  }\n\n  return str.toLowerCase();\n}\n\n// S5.1.3 Domain Matching\nfunction domainMatch(str, domStr, canonicalize) {\n  if (str == null || domStr == null) {\n    return null;\n  }\n  if (canonicalize !== false) {\n    str = canonicalDomain(str);\n    domStr = canonicalDomain(domStr);\n  }\n\n  /*\n   * S5.1.3:\n   * \"A string domain-matches a given domain string if at least one of the\n   * following conditions hold:\"\n   *\n   * \" o The domain string and the string are identical. (Note that both the\n   * domain string and the string will have been canonicalized to lower case at\n   * this point)\"\n   */\n  if (str == domStr) {\n    return true;\n  }\n\n  /* \" o All of the following [three] conditions hold:\" */\n\n  /* \"* The domain string is a suffix of the string\" */\n  const idx = str.lastIndexOf(domStr);\n  if (idx <= 0) {\n    return false; // it's a non-match (-1) or prefix (0)\n  }\n\n  // next, check it's a proper suffix\n  // e.g., \"a.b.c\".indexOf(\"b.c\") === 2\n  // 5 === 3+2\n  if (str.length !== domStr.length + idx) {\n    return false; // it's not a suffix\n  }\n\n  /* \"  * The last character of the string that is not included in the\n   * domain string is a %x2E (\".\") character.\" */\n  if (str.substr(idx - 1, 1) !== \".\") {\n    return false; // doesn't align on \".\"\n  }\n\n  /* \"  * The string is a host name (i.e., not an IP address).\" */\n  if (IP_REGEX_LOWERCASE.test(str)) {\n    return false; // it's an IP address\n  }\n\n  return true;\n}\n\n// RFC6265 S5.1.4 Paths and Path-Match\n\n/*\n * \"The user agent MUST use an algorithm equivalent to the following algorithm\n * to compute the default-path of a cookie:\"\n *\n * Assumption: the path (and not query part or absolute uri) is passed in.\n */\nfunction defaultPath(path) {\n  // \"2. If the uri-path is empty or if the first character of the uri-path is not\n  // a %x2F (\"/\") character, output %x2F (\"/\") and skip the remaining steps.\n  if (!path || path.substr(0, 1) !== \"/\") {\n    return \"/\";\n  }\n\n  // \"3. If the uri-path contains no more than one %x2F (\"/\") character, output\n  // %x2F (\"/\") and skip the remaining step.\"\n  if (path === \"/\") {\n    return path;\n  }\n\n  const rightSlash = path.lastIndexOf(\"/\");\n  if (rightSlash === 0) {\n    return \"/\";\n  }\n\n  // \"4. Output the characters of the uri-path from the first character up to,\n  // but not including, the right-most %x2F (\"/\").\"\n  return path.slice(0, rightSlash);\n}\n\nfunction trimTerminator(str) {\n  if (validators.isEmptyString(str)) return str;\n  for (let t = 0; t < TERMINATORS.length; t++) {\n    const terminatorIdx = str.indexOf(TERMINATORS[t]);\n    if (terminatorIdx !== -1) {\n      str = str.substr(0, terminatorIdx);\n    }\n  }\n\n  return str;\n}\n\nfunction parseCookiePair(cookiePair, looseMode) {\n  cookiePair = trimTerminator(cookiePair);\n  validators.validate(validators.isString(cookiePair), cookiePair);\n\n  let firstEq = cookiePair.indexOf(\"=\");\n  if (looseMode) {\n    if (firstEq === 0) {\n      // '=' is immediately at start\n      cookiePair = cookiePair.substr(1);\n      firstEq = cookiePair.indexOf(\"=\"); // might still need to split on '='\n    }\n  } else {\n    // non-loose mode\n    if (firstEq <= 0) {\n      // no '=' or is at start\n      return; // needs to have non-empty \"cookie-name\"\n    }\n  }\n\n  let cookieName, cookieValue;\n  if (firstEq <= 0) {\n    cookieName = \"\";\n    cookieValue = cookiePair.trim();\n  } else {\n    cookieName = cookiePair.substr(0, firstEq).trim();\n    cookieValue = cookiePair.substr(firstEq + 1).trim();\n  }\n\n  if (CONTROL_CHARS.test(cookieName) || CONTROL_CHARS.test(cookieValue)) {\n    return;\n  }\n\n  const c = new Cookie();\n  c.key = cookieName;\n  c.value = cookieValue;\n  return c;\n}\n\nfunction parse(str, options) {\n  if (!options || typeof options !== \"object\") {\n    options = {};\n  }\n\n  if (validators.isEmptyString(str) || !validators.isString(str)) {\n    return null;\n  }\n\n  str = str.trim();\n\n  // We use a regex to parse the \"name-value-pair\" part of S5.2\n  const firstSemi = str.indexOf(\";\"); // S5.2 step 1\n  const cookiePair = firstSemi === -1 ? str : str.substr(0, firstSemi);\n  const c = parseCookiePair(cookiePair, !!options.loose);\n  if (!c) {\n    return;\n  }\n\n  if (firstSemi === -1) {\n    return c;\n  }\n\n  // S5.2.3 \"unparsed-attributes consist of the remainder of the set-cookie-string\n  // (including the %x3B (\";\") in question).\" plus later on in the same section\n  // \"discard the first \";\" and trim\".\n  const unparsed = str.slice(firstSemi + 1).trim();\n\n  // \"If the unparsed-attributes string is empty, skip the rest of these\n  // steps.\"\n  if (unparsed.length === 0) {\n    return c;\n  }\n\n  /*\n   * S5.2 says that when looping over the items \"[p]rocess the attribute-name\n   * and attribute-value according to the requirements in the following\n   * subsections\" for every item.  Plus, for many of the individual attributes\n   * in S5.3 it says to use the \"attribute-value of the last attribute in the\n   * cookie-attribute-list\".  Therefore, in this implementation, we overwrite\n   * the previous value.\n   */\n  const cookie_avs = unparsed.split(\";\");\n  while (cookie_avs.length) {\n    const av = cookie_avs.shift().trim();\n    if (av.length === 0) {\n      // happens if \";;\" appears\n      continue;\n    }\n    const av_sep = av.indexOf(\"=\");\n    let av_key, av_value;\n\n    if (av_sep === -1) {\n      av_key = av;\n      av_value = null;\n    } else {\n      av_key = av.substr(0, av_sep);\n      av_value = av.substr(av_sep + 1);\n    }\n\n    av_key = av_key.trim().toLowerCase();\n\n    if (av_value) {\n      av_value = av_value.trim();\n    }\n\n    switch (av_key) {\n      case \"expires\": // S5.2.1\n        if (av_value) {\n          const exp = parseDate(av_value);\n          // \"If the attribute-value failed to parse as a cookie date, ignore the\n          // cookie-av.\"\n          if (exp) {\n            // over and underflow not realistically a concern: V8's getTime() seems to\n            // store something larger than a 32-bit time_t (even with 32-bit node)\n            c.expires = exp;\n          }\n        }\n        break;\n\n      case \"max-age\": // S5.2.2\n        if (av_value) {\n          // \"If the first character of the attribute-value is not a DIGIT or a \"-\"\n          // character ...[or]... If the remainder of attribute-value contains a\n          // non-DIGIT character, ignore the cookie-av.\"\n          if (/^-?[0-9]+$/.test(av_value)) {\n            const delta = parseInt(av_value, 10);\n            // \"If delta-seconds is less than or equal to zero (0), let expiry-time\n            // be the earliest representable date and time.\"\n            c.setMaxAge(delta);\n          }\n        }\n        break;\n\n      case \"domain\": // S5.2.3\n        // \"If the attribute-value is empty, the behavior is undefined.  However,\n        // the user agent SHOULD ignore the cookie-av entirely.\"\n        if (av_value) {\n          // S5.2.3 \"Let cookie-domain be the attribute-value without the leading %x2E\n          // (\".\") character.\"\n          const domain = av_value.trim().replace(/^\\./, \"\");\n          if (domain) {\n            // \"Convert the cookie-domain to lower case.\"\n            c.domain = domain.toLowerCase();\n          }\n        }\n        break;\n\n      case \"path\": // S5.2.4\n        /*\n         * \"If the attribute-value is empty or if the first character of the\n         * attribute-value is not %x2F (\"/\"):\n         *   Let cookie-path be the default-path.\n         * Otherwise:\n         *   Let cookie-path be the attribute-value.\"\n         *\n         * We'll represent the default-path as null since it depends on the\n         * context of the parsing.\n         */\n        c.path = av_value && av_value[0] === \"/\" ? av_value : null;\n        break;\n\n      case \"secure\": // S5.2.5\n        /*\n         * \"If the attribute-name case-insensitively matches the string \"Secure\",\n         * the user agent MUST append an attribute to the cookie-attribute-list\n         * with an attribute-name of Secure and an empty attribute-value.\"\n         */\n        c.secure = true;\n        break;\n\n      case \"httponly\": // S5.2.6 -- effectively the same as 'secure'\n        c.httpOnly = true;\n        break;\n\n      case \"samesite\": // RFC6265bis-02 S5.3.7\n        const enforcement = av_value ? av_value.toLowerCase() : \"\";\n        switch (enforcement) {\n          case \"strict\":\n            c.sameSite = \"strict\";\n            break;\n          case \"lax\":\n            c.sameSite = \"lax\";\n            break;\n          case \"none\":\n            c.sameSite = \"none\";\n            break;\n          default:\n            c.sameSite = undefined;\n            break;\n        }\n        break;\n\n      default:\n        c.extensions = c.extensions || [];\n        c.extensions.push(av);\n        break;\n    }\n  }\n\n  return c;\n}\n\n/**\n *  If the cookie-name begins with a case-sensitive match for the\n *  string \"__Secure-\", abort these steps and ignore the cookie\n *  entirely unless the cookie's secure-only-flag is true.\n * @param cookie\n * @returns boolean\n */\nfunction isSecurePrefixConditionMet(cookie) {\n  validators.validate(validators.isObject(cookie), cookie);\n  return !cookie.key.startsWith(\"__Secure-\") || cookie.secure;\n}\n\n/**\n *  If the cookie-name begins with a case-sensitive match for the\n *  string \"__Host-\", abort these steps and ignore the cookie\n *  entirely unless the cookie meets all the following criteria:\n *    1.  The cookie's secure-only-flag is true.\n *    2.  The cookie's host-only-flag is true.\n *    3.  The cookie-attribute-list contains an attribute with an\n *        attribute-name of \"Path\", and the cookie's path is \"/\".\n * @param cookie\n * @returns boolean\n */\nfunction isHostPrefixConditionMet(cookie) {\n  validators.validate(validators.isObject(cookie));\n  return (\n    !cookie.key.startsWith(\"__Host-\") ||\n    (cookie.secure &&\n      cookie.hostOnly &&\n      cookie.path != null &&\n      cookie.path === \"/\")\n  );\n}\n\n// avoid the V8 deoptimization monster!\nfunction jsonParse(str) {\n  let obj;\n  try {\n    obj = JSON.parse(str);\n  } catch (e) {\n    return e;\n  }\n  return obj;\n}\n\nfunction fromJSON(str) {\n  if (!str || validators.isEmptyString(str)) {\n    return null;\n  }\n\n  let obj;\n  if (typeof str === \"string\") {\n    obj = jsonParse(str);\n    if (obj instanceof Error) {\n      return null;\n    }\n  } else {\n    // assume it's an Object\n    obj = str;\n  }\n\n  const c = new Cookie();\n  for (let i = 0; i < Cookie.serializableProperties.length; i++) {\n    const prop = Cookie.serializableProperties[i];\n    if (obj[prop] === undefined || obj[prop] === cookieDefaults[prop]) {\n      continue; // leave as prototype default\n    }\n\n    if (prop === \"expires\" || prop === \"creation\" || prop === \"lastAccessed\") {\n      if (obj[prop] === null) {\n        c[prop] = null;\n      } else {\n        c[prop] = obj[prop] == \"Infinity\" ? \"Infinity\" : new Date(obj[prop]);\n      }\n    } else {\n      c[prop] = obj[prop];\n    }\n  }\n\n  return c;\n}\n\n/* Section 5.4 part 2:\n * \"*  Cookies with longer paths are listed before cookies with\n *     shorter paths.\n *\n *  *  Among cookies that have equal-length path fields, cookies with\n *     earlier creation-times are listed before cookies with later\n *     creation-times.\"\n */\n\nfunction cookieCompare(a, b) {\n  validators.validate(validators.isObject(a), a);\n  validators.validate(validators.isObject(b), b);\n  let cmp = 0;\n\n  // descending for length: b CMP a\n  const aPathLen = a.path ? a.path.length : 0;\n  const bPathLen = b.path ? b.path.length : 0;\n  cmp = bPathLen - aPathLen;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  // ascending for time: a CMP b\n  const aTime = a.creation ? a.creation.getTime() : MAX_TIME;\n  const bTime = b.creation ? b.creation.getTime() : MAX_TIME;\n  cmp = aTime - bTime;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  // break ties for the same millisecond (precision of JavaScript's clock)\n  cmp = a.creationIndex - b.creationIndex;\n\n  return cmp;\n}\n\n// Gives the permutation of all possible pathMatch()es of a given path. The\n// array is in longest-to-shortest order.  Handy for indexing.\nfunction permutePath(path) {\n  validators.validate(validators.isString(path));\n  if (path === \"/\") {\n    return [\"/\"];\n  }\n  const permutations = [path];\n  while (path.length > 1) {\n    const lindex = path.lastIndexOf(\"/\");\n    if (lindex === 0) {\n      break;\n    }\n    path = path.substr(0, lindex);\n    permutations.push(path);\n  }\n  permutations.push(\"/\");\n  return permutations;\n}\n\nfunction getCookieContext(url) {\n  if (url instanceof Object) {\n    return url;\n  }\n  // NOTE: decodeURI will throw on malformed URIs (see GH-32).\n  // Therefore, we will just skip decoding for such URIs.\n  try {\n    url = decodeURI(url);\n  } catch (err) {\n    // Silently swallow error\n  }\n\n  return urlParse(url);\n}\n\nconst cookieDefaults = {\n  // the order in which the RFC has them:\n  key: \"\",\n  value: \"\",\n  expires: \"Infinity\",\n  maxAge: null,\n  domain: null,\n  path: null,\n  secure: false,\n  httpOnly: false,\n  extensions: null,\n  // set by the CookieJar:\n  hostOnly: null,\n  pathIsDefault: null,\n  creation: null,\n  lastAccessed: null,\n  sameSite: undefined\n};\n\nclass Cookie {\n  constructor(options = {}) {\n    const customInspectSymbol = getCustomInspectSymbol();\n    if (customInspectSymbol) {\n      this[customInspectSymbol] = this.inspect;\n    }\n\n    Object.assign(this, cookieDefaults, options);\n    this.creation = this.creation || new Date();\n\n    // used to break creation ties in cookieCompare():\n    Object.defineProperty(this, \"creationIndex\", {\n      configurable: false,\n      enumerable: false, // important for assert.deepEqual checks\n      writable: true,\n      value: ++Cookie.cookiesCreated\n    });\n  }\n\n  inspect() {\n    const now = Date.now();\n    const hostOnly = this.hostOnly != null ? this.hostOnly : \"?\";\n    const createAge = this.creation\n      ? `${now - this.creation.getTime()}ms`\n      : \"?\";\n    const accessAge = this.lastAccessed\n      ? `${now - this.lastAccessed.getTime()}ms`\n      : \"?\";\n    return `Cookie=\"${this.toString()}; hostOnly=${hostOnly}; aAge=${accessAge}; cAge=${createAge}\"`;\n  }\n\n  toJSON() {\n    const obj = {};\n\n    for (const prop of Cookie.serializableProperties) {\n      if (this[prop] === cookieDefaults[prop]) {\n        continue; // leave as prototype default\n      }\n\n      if (\n        prop === \"expires\" ||\n        prop === \"creation\" ||\n        prop === \"lastAccessed\"\n      ) {\n        if (this[prop] === null) {\n          obj[prop] = null;\n        } else {\n          obj[prop] =\n            this[prop] == \"Infinity\" // intentionally not ===\n              ? \"Infinity\"\n              : this[prop].toISOString();\n        }\n      } else if (prop === \"maxAge\") {\n        if (this[prop] !== null) {\n          // again, intentionally not ===\n          obj[prop] =\n            this[prop] == Infinity || this[prop] == -Infinity\n              ? this[prop].toString()\n              : this[prop];\n        }\n      } else {\n        if (this[prop] !== cookieDefaults[prop]) {\n          obj[prop] = this[prop];\n        }\n      }\n    }\n\n    return obj;\n  }\n\n  clone() {\n    return fromJSON(this.toJSON());\n  }\n\n  validate() {\n    if (!COOKIE_OCTETS.test(this.value)) {\n      return false;\n    }\n    if (\n      this.expires != Infinity &&\n      !(this.expires instanceof Date) &&\n      !parseDate(this.expires)\n    ) {\n      return false;\n    }\n    if (this.maxAge != null && this.maxAge <= 0) {\n      return false; // \"Max-Age=\" non-zero-digit *DIGIT\n    }\n    if (this.path != null && !PATH_VALUE.test(this.path)) {\n      return false;\n    }\n\n    const cdomain = this.cdomain();\n    if (cdomain) {\n      if (cdomain.match(/\\.$/)) {\n        return false; // S4.1.2.3 suggests that this is bad. domainMatch() tests confirm this\n      }\n      const suffix = pubsuffix.getPublicSuffix(cdomain);\n      if (suffix == null) {\n        // it's a public suffix\n        return false;\n      }\n    }\n    return true;\n  }\n\n  setExpires(exp) {\n    if (exp instanceof Date) {\n      this.expires = exp;\n    } else {\n      this.expires = parseDate(exp) || \"Infinity\";\n    }\n  }\n\n  setMaxAge(age) {\n    if (age === Infinity || age === -Infinity) {\n      this.maxAge = age.toString(); // so JSON.stringify() works\n    } else {\n      this.maxAge = age;\n    }\n  }\n\n  cookieString() {\n    let val = this.value;\n    if (val == null) {\n      val = \"\";\n    }\n    if (this.key === \"\") {\n      return val;\n    }\n    return `${this.key}=${val}`;\n  }\n\n  // gives Set-Cookie header format\n  toString() {\n    let str = this.cookieString();\n\n    if (this.expires != Infinity) {\n      if (this.expires instanceof Date) {\n        str += `; Expires=${formatDate(this.expires)}`;\n      } else {\n        str += `; Expires=${this.expires}`;\n      }\n    }\n\n    if (this.maxAge != null && this.maxAge != Infinity) {\n      str += `; Max-Age=${this.maxAge}`;\n    }\n\n    if (this.domain && !this.hostOnly) {\n      str += `; Domain=${this.domain}`;\n    }\n    if (this.path) {\n      str += `; Path=${this.path}`;\n    }\n\n    if (this.secure) {\n      str += \"; Secure\";\n    }\n    if (this.httpOnly) {\n      str += \"; HttpOnly\";\n    }\n    if (this.sameSite && this.sameSite !== \"none\") {\n      const ssCanon = Cookie.sameSiteCanonical[this.sameSite.toLowerCase()];\n      str += `; SameSite=${ssCanon ? ssCanon : this.sameSite}`;\n    }\n    if (this.extensions) {\n      this.extensions.forEach(ext => {\n        str += `; ${ext}`;\n      });\n    }\n\n    return str;\n  }\n\n  // TTL() partially replaces the \"expiry-time\" parts of S5.3 step 3 (setCookie()\n  // elsewhere)\n  // S5.3 says to give the \"latest representable date\" for which we use Infinity\n  // For \"expired\" we use 0\n  TTL(now) {\n    /* RFC6265 S4.1.2.2 If a cookie has both the Max-Age and the Expires\n     * attribute, the Max-Age attribute has precedence and controls the\n     * expiration date of the cookie.\n     * (Concurs with S5.3 step 3)\n     */\n    if (this.maxAge != null) {\n      return this.maxAge <= 0 ? 0 : this.maxAge * 1000;\n    }\n\n    let expires = this.expires;\n    if (expires != Infinity) {\n      if (!(expires instanceof Date)) {\n        expires = parseDate(expires) || Infinity;\n      }\n\n      if (expires == Infinity) {\n        return Infinity;\n      }\n\n      return expires.getTime() - (now || Date.now());\n    }\n\n    return Infinity;\n  }\n\n  // expiryTime() replaces the \"expiry-time\" parts of S5.3 step 3 (setCookie()\n  // elsewhere)\n  expiryTime(now) {\n    if (this.maxAge != null) {\n      const relativeTo = now || this.creation || new Date();\n      const age = this.maxAge <= 0 ? -Infinity : this.maxAge * 1000;\n      return relativeTo.getTime() + age;\n    }\n\n    if (this.expires == Infinity) {\n      return Infinity;\n    }\n    return this.expires.getTime();\n  }\n\n  // expiryDate() replaces the \"expiry-time\" parts of S5.3 step 3 (setCookie()\n  // elsewhere), except it returns a Date\n  expiryDate(now) {\n    const millisec = this.expiryTime(now);\n    if (millisec == Infinity) {\n      return new Date(MAX_TIME);\n    } else if (millisec == -Infinity) {\n      return new Date(MIN_TIME);\n    } else {\n      return new Date(millisec);\n    }\n  }\n\n  // This replaces the \"persistent-flag\" parts of S5.3 step 3\n  isPersistent() {\n    return this.maxAge != null || this.expires != Infinity;\n  }\n\n  // Mostly S5.1.2 and S5.2.3:\n  canonicalizedDomain() {\n    if (this.domain == null) {\n      return null;\n    }\n    return canonicalDomain(this.domain);\n  }\n\n  cdomain() {\n    return this.canonicalizedDomain();\n  }\n}\n\nCookie.cookiesCreated = 0;\nCookie.parse = parse;\nCookie.fromJSON = fromJSON;\nCookie.serializableProperties = Object.keys(cookieDefaults);\nCookie.sameSiteLevel = {\n  strict: 3,\n  lax: 2,\n  none: 1\n};\n\nCookie.sameSiteCanonical = {\n  strict: \"Strict\",\n  lax: \"Lax\"\n};\n\nfunction getNormalizedPrefixSecurity(prefixSecurity) {\n  if (prefixSecurity != null) {\n    const normalizedPrefixSecurity = prefixSecurity.toLowerCase();\n    /* The three supported options */\n    switch (normalizedPrefixSecurity) {\n      case PrefixSecurityEnum.STRICT:\n      case PrefixSecurityEnum.SILENT:\n      case PrefixSecurityEnum.DISABLED:\n        return normalizedPrefixSecurity;\n    }\n  }\n  /* Default is SILENT */\n  return PrefixSecurityEnum.SILENT;\n}\n\nclass CookieJar {\n  constructor(store, options = { rejectPublicSuffixes: true }) {\n    if (typeof options === \"boolean\") {\n      options = { rejectPublicSuffixes: options };\n    }\n    validators.validate(validators.isObject(options), options);\n    this.rejectPublicSuffixes = options.rejectPublicSuffixes;\n    this.enableLooseMode = !!options.looseMode;\n    this.allowSpecialUseDomain =\n      typeof options.allowSpecialUseDomain === \"boolean\"\n        ? options.allowSpecialUseDomain\n        : true;\n    this.store = store || new MemoryCookieStore();\n    this.prefixSecurity = getNormalizedPrefixSecurity(options.prefixSecurity);\n    this._cloneSync = syncWrap(\"clone\");\n    this._importCookiesSync = syncWrap(\"_importCookies\");\n    this.getCookiesSync = syncWrap(\"getCookies\");\n    this.getCookieStringSync = syncWrap(\"getCookieString\");\n    this.getSetCookieStringsSync = syncWrap(\"getSetCookieStrings\");\n    this.removeAllCookiesSync = syncWrap(\"removeAllCookies\");\n    this.setCookieSync = syncWrap(\"setCookie\");\n    this.serializeSync = syncWrap(\"serialize\");\n  }\n\n  setCookie(cookie, url, options, cb) {\n    validators.validate(validators.isUrlStringOrObject(url), cb, options);\n\n    let err;\n\n    if (validators.isFunction(url)) {\n      cb = url;\n      return cb(new Error(\"No URL was specified\"));\n    }\n\n    const context = getCookieContext(url);\n    if (validators.isFunction(options)) {\n      cb = options;\n      options = {};\n    }\n\n    validators.validate(validators.isFunction(cb), cb);\n\n    if (\n      !validators.isNonEmptyString(cookie) &&\n      !validators.isObject(cookie) &&\n      cookie instanceof String &&\n      cookie.length == 0\n    ) {\n      return cb(null);\n    }\n\n    const host = canonicalDomain(context.hostname);\n    const loose = options.loose || this.enableLooseMode;\n\n    let sameSiteContext = null;\n    if (options.sameSiteContext) {\n      sameSiteContext = checkSameSiteContext(options.sameSiteContext);\n      if (!sameSiteContext) {\n        return cb(new Error(SAME_SITE_CONTEXT_VAL_ERR));\n      }\n    }\n\n    // S5.3 step 1\n    if (typeof cookie === \"string\" || cookie instanceof String) {\n      cookie = Cookie.parse(cookie, { loose: loose });\n      if (!cookie) {\n        err = new Error(\"Cookie failed to parse\");\n        return cb(options.ignoreError ? null : err);\n      }\n    } else if (!(cookie instanceof Cookie)) {\n      // If you're seeing this error, and are passing in a Cookie object,\n      // it *might* be a Cookie object from another loaded version of tough-cookie.\n      err = new Error(\n        \"First argument to setCookie must be a Cookie object or string\"\n      );\n      return cb(options.ignoreError ? null : err);\n    }\n\n    // S5.3 step 2\n    const now = options.now || new Date(); // will assign later to save effort in the face of errors\n\n    // S5.3 step 3: NOOP; persistent-flag and expiry-time is handled by getCookie()\n\n    // S5.3 step 4: NOOP; domain is null by default\n\n    // S5.3 step 5: public suffixes\n    if (this.rejectPublicSuffixes && cookie.domain) {\n      const suffix = pubsuffix.getPublicSuffix(cookie.cdomain(), {\n        allowSpecialUseDomain: this.allowSpecialUseDomain,\n        ignoreError: options.ignoreError\n      });\n      if (suffix == null && !IP_V6_REGEX_OBJECT.test(cookie.domain)) {\n        // e.g. \"com\"\n        err = new Error(\"Cookie has domain set to a public suffix\");\n        return cb(options.ignoreError ? null : err);\n      }\n    }\n\n    // S5.3 step 6:\n    if (cookie.domain) {\n      if (!domainMatch(host, cookie.cdomain(), false)) {\n        err = new Error(\n          `Cookie not in this host's domain. Cookie:${cookie.cdomain()} Request:${host}`\n        );\n        return cb(options.ignoreError ? null : err);\n      }\n\n      if (cookie.hostOnly == null) {\n        // don't reset if already set\n        cookie.hostOnly = false;\n      }\n    } else {\n      cookie.hostOnly = true;\n      cookie.domain = host;\n    }\n\n    //S5.2.4 If the attribute-value is empty or if the first character of the\n    //attribute-value is not %x2F (\"/\"):\n    //Let cookie-path be the default-path.\n    if (!cookie.path || cookie.path[0] !== \"/\") {\n      cookie.path = defaultPath(context.pathname);\n      cookie.pathIsDefault = true;\n    }\n\n    // S5.3 step 8: NOOP; secure attribute\n    // S5.3 step 9: NOOP; httpOnly attribute\n\n    // S5.3 step 10\n    if (options.http === false && cookie.httpOnly) {\n      err = new Error(\"Cookie is HttpOnly and this isn't an HTTP API\");\n      return cb(options.ignoreError ? null : err);\n    }\n\n    // 6252bis-02 S5.4 Step 13 & 14:\n    if (\n      cookie.sameSite !== \"none\" &&\n      cookie.sameSite !== undefined &&\n      sameSiteContext\n    ) {\n      // \"If the cookie's \"same-site-flag\" is not \"None\", and the cookie\n      //  is being set from a context whose \"site for cookies\" is not an\n      //  exact match for request-uri's host's registered domain, then\n      //  abort these steps and ignore the newly created cookie entirely.\"\n      if (sameSiteContext === \"none\") {\n        err = new Error(\n          \"Cookie is SameSite but this is a cross-origin request\"\n        );\n        return cb(options.ignoreError ? null : err);\n      }\n    }\n\n    /* 6265bis-02 S5.4 Steps 15 & 16 */\n    const ignoreErrorForPrefixSecurity =\n      this.prefixSecurity === PrefixSecurityEnum.SILENT;\n    const prefixSecurityDisabled =\n      this.prefixSecurity === PrefixSecurityEnum.DISABLED;\n    /* If prefix checking is not disabled ...*/\n    if (!prefixSecurityDisabled) {\n      let errorFound = false;\n      let errorMsg;\n      /* Check secure prefix condition */\n      if (!isSecurePrefixConditionMet(cookie)) {\n        errorFound = true;\n        errorMsg = \"Cookie has __Secure prefix but Secure attribute is not set\";\n      } else if (!isHostPrefixConditionMet(cookie)) {\n        /* Check host prefix condition */\n        errorFound = true;\n        errorMsg =\n          \"Cookie has __Host prefix but either Secure or HostOnly attribute is not set or Path is not '/'\";\n      }\n      if (errorFound) {\n        return cb(\n          options.ignoreError || ignoreErrorForPrefixSecurity\n            ? null\n            : new Error(errorMsg)\n        );\n      }\n    }\n\n    const store = this.store;\n\n    if (!store.updateCookie) {\n      store.updateCookie = function(oldCookie, newCookie, cb) {\n        this.putCookie(newCookie, cb);\n      };\n    }\n\n    function withCookie(err, oldCookie) {\n      if (err) {\n        return cb(err);\n      }\n\n      const next = function(err) {\n        if (err) {\n          return cb(err);\n        } else {\n          cb(null, cookie);\n        }\n      };\n\n      if (oldCookie) {\n        // S5.3 step 11 - \"If the cookie store contains a cookie with the same name,\n        // domain, and path as the newly created cookie:\"\n        if (options.http === false && oldCookie.httpOnly) {\n          // step 11.2\n          err = new Error(\"old Cookie is HttpOnly and this isn't an HTTP API\");\n          return cb(options.ignoreError ? null : err);\n        }\n        cookie.creation = oldCookie.creation; // step 11.3\n        cookie.creationIndex = oldCookie.creationIndex; // preserve tie-breaker\n        cookie.lastAccessed = now;\n        // Step 11.4 (delete cookie) is implied by just setting the new one:\n        store.updateCookie(oldCookie, cookie, next); // step 12\n      } else {\n        cookie.creation = cookie.lastAccessed = now;\n        store.putCookie(cookie, next); // step 12\n      }\n    }\n\n    store.findCookie(cookie.domain, cookie.path, cookie.key, withCookie);\n  }\n\n  // RFC6365 S5.4\n  getCookies(url, options, cb) {\n    validators.validate(validators.isUrlStringOrObject(url), cb, url);\n\n    const context = getCookieContext(url);\n    if (validators.isFunction(options)) {\n      cb = options;\n      options = {};\n    }\n    validators.validate(validators.isObject(options), cb, options);\n    validators.validate(validators.isFunction(cb), cb);\n\n    const host = canonicalDomain(context.hostname);\n    const path = context.pathname || \"/\";\n\n    let secure = options.secure;\n    if (\n      secure == null &&\n      context.protocol &&\n      (context.protocol == \"https:\" || context.protocol == \"wss:\")\n    ) {\n      secure = true;\n    }\n\n    let sameSiteLevel = 0;\n    if (options.sameSiteContext) {\n      const sameSiteContext = checkSameSiteContext(options.sameSiteContext);\n      sameSiteLevel = Cookie.sameSiteLevel[sameSiteContext];\n      if (!sameSiteLevel) {\n        return cb(new Error(SAME_SITE_CONTEXT_VAL_ERR));\n      }\n    }\n\n    let http = options.http;\n    if (http == null) {\n      http = true;\n    }\n\n    const now = options.now || Date.now();\n    const expireCheck = options.expire !== false;\n    const allPaths = !!options.allPaths;\n    const store = this.store;\n\n    function matchingCookie(c) {\n      // \"Either:\n      //   The cookie's host-only-flag is true and the canonicalized\n      //   request-host is identical to the cookie's domain.\n      // Or:\n      //   The cookie's host-only-flag is false and the canonicalized\n      //   request-host domain-matches the cookie's domain.\"\n      if (c.hostOnly) {\n        if (c.domain != host) {\n          return false;\n        }\n      } else {\n        if (!domainMatch(host, c.domain, false)) {\n          return false;\n        }\n      }\n\n      // \"The request-uri's path path-matches the cookie's path.\"\n      if (!allPaths && !pathMatch(path, c.path)) {\n        return false;\n      }\n\n      // \"If the cookie's secure-only-flag is true, then the request-uri's\n      // scheme must denote a \"secure\" protocol\"\n      if (c.secure && !secure) {\n        return false;\n      }\n\n      // \"If the cookie's http-only-flag is true, then exclude the cookie if the\n      // cookie-string is being generated for a \"non-HTTP\" API\"\n      if (c.httpOnly && !http) {\n        return false;\n      }\n\n      // RFC6265bis-02 S5.3.7\n      if (sameSiteLevel) {\n        const cookieLevel = Cookie.sameSiteLevel[c.sameSite || \"none\"];\n        if (cookieLevel > sameSiteLevel) {\n          // only allow cookies at or below the request level\n          return false;\n        }\n      }\n\n      // deferred from S5.3\n      // non-RFC: allow retention of expired cookies by choice\n      if (expireCheck && c.expiryTime() <= now) {\n        store.removeCookie(c.domain, c.path, c.key, () => {}); // result ignored\n        return false;\n      }\n\n      return true;\n    }\n\n    store.findCookies(\n      host,\n      allPaths ? null : path,\n      this.allowSpecialUseDomain,\n      (err, cookies) => {\n        if (err) {\n          return cb(err);\n        }\n\n        cookies = cookies.filter(matchingCookie);\n\n        // sorting of S5.4 part 2\n        if (options.sort !== false) {\n          cookies = cookies.sort(cookieCompare);\n        }\n\n        // S5.4 part 3\n        const now = new Date();\n        for (const cookie of cookies) {\n          cookie.lastAccessed = now;\n        }\n        // TODO persist lastAccessed\n\n        cb(null, cookies);\n      }\n    );\n  }\n\n  getCookieString(...args) {\n    const cb = args.pop();\n    validators.validate(validators.isFunction(cb), cb);\n    const next = function(err, cookies) {\n      if (err) {\n        cb(err);\n      } else {\n        cb(\n          null,\n          cookies\n            .sort(cookieCompare)\n            .map(c => c.cookieString())\n            .join(\"; \")\n        );\n      }\n    };\n    args.push(next);\n    this.getCookies.apply(this, args);\n  }\n\n  getSetCookieStrings(...args) {\n    const cb = args.pop();\n    validators.validate(validators.isFunction(cb), cb);\n    const next = function(err, cookies) {\n      if (err) {\n        cb(err);\n      } else {\n        cb(\n          null,\n          cookies.map(c => {\n            return c.toString();\n          })\n        );\n      }\n    };\n    args.push(next);\n    this.getCookies.apply(this, args);\n  }\n\n  serialize(cb) {\n    validators.validate(validators.isFunction(cb), cb);\n    let type = this.store.constructor.name;\n    if (validators.isObject(type)) {\n      type = null;\n    }\n\n    // update README.md \"Serialization Format\" if you change this, please!\n    const serialized = {\n      // The version of tough-cookie that serialized this jar. Generally a good\n      // practice since future versions can make data import decisions based on\n      // known past behavior. When/if this matters, use `semver`.\n      version: `tough-cookie@${VERSION}`,\n\n      // add the store type, to make humans happy:\n      storeType: type,\n\n      // CookieJar configuration:\n      rejectPublicSuffixes: !!this.rejectPublicSuffixes,\n      enableLooseMode: !!this.enableLooseMode,\n      allowSpecialUseDomain: !!this.allowSpecialUseDomain,\n      prefixSecurity: getNormalizedPrefixSecurity(this.prefixSecurity),\n\n      // this gets filled from getAllCookies:\n      cookies: []\n    };\n\n    if (\n      !(\n        this.store.getAllCookies &&\n        typeof this.store.getAllCookies === \"function\"\n      )\n    ) {\n      return cb(\n        new Error(\n          \"store does not support getAllCookies and cannot be serialized\"\n        )\n      );\n    }\n\n    this.store.getAllCookies((err, cookies) => {\n      if (err) {\n        return cb(err);\n      }\n\n      serialized.cookies = cookies.map(cookie => {\n        // convert to serialized 'raw' cookies\n        cookie = cookie instanceof Cookie ? cookie.toJSON() : cookie;\n\n        // Remove the index so new ones get assigned during deserialization\n        delete cookie.creationIndex;\n\n        return cookie;\n      });\n\n      return cb(null, serialized);\n    });\n  }\n\n  toJSON() {\n    return this.serializeSync();\n  }\n\n  // use the class method CookieJar.deserialize instead of calling this directly\n  _importCookies(serialized, cb) {\n    let cookies = serialized.cookies;\n    if (!cookies || !Array.isArray(cookies)) {\n      return cb(new Error(\"serialized jar has no cookies array\"));\n    }\n    cookies = cookies.slice(); // do not modify the original\n\n    const putNext = err => {\n      if (err) {\n        return cb(err);\n      }\n\n      if (!cookies.length) {\n        return cb(err, this);\n      }\n\n      let cookie;\n      try {\n        cookie = fromJSON(cookies.shift());\n      } catch (e) {\n        return cb(e);\n      }\n\n      if (cookie === null) {\n        return putNext(null); // skip this cookie\n      }\n\n      this.store.putCookie(cookie, putNext);\n    };\n\n    putNext();\n  }\n\n  clone(newStore, cb) {\n    if (arguments.length === 1) {\n      cb = newStore;\n      newStore = null;\n    }\n\n    this.serialize((err, serialized) => {\n      if (err) {\n        return cb(err);\n      }\n      CookieJar.deserialize(serialized, newStore, cb);\n    });\n  }\n\n  cloneSync(newStore) {\n    if (arguments.length === 0) {\n      return this._cloneSync();\n    }\n    if (!newStore.synchronous) {\n      throw new Error(\n        \"CookieJar clone destination store is not synchronous; use async API instead.\"\n      );\n    }\n    return this._cloneSync(newStore);\n  }\n\n  removeAllCookies(cb) {\n    validators.validate(validators.isFunction(cb), cb);\n    const store = this.store;\n\n    // Check that the store implements its own removeAllCookies(). The default\n    // implementation in Store will immediately call the callback with a \"not\n    // implemented\" Error.\n    if (\n      typeof store.removeAllCookies === \"function\" &&\n      store.removeAllCookies !== Store.prototype.removeAllCookies\n    ) {\n      return store.removeAllCookies(cb);\n    }\n\n    store.getAllCookies((err, cookies) => {\n      if (err) {\n        return cb(err);\n      }\n\n      if (cookies.length === 0) {\n        return cb(null);\n      }\n\n      let completedCount = 0;\n      const removeErrors = [];\n\n      function removeCookieCb(removeErr) {\n        if (removeErr) {\n          removeErrors.push(removeErr);\n        }\n\n        completedCount++;\n\n        if (completedCount === cookies.length) {\n          return cb(removeErrors.length ? removeErrors[0] : null);\n        }\n      }\n\n      cookies.forEach(cookie => {\n        store.removeCookie(\n          cookie.domain,\n          cookie.path,\n          cookie.key,\n          removeCookieCb\n        );\n      });\n    });\n  }\n\n  static deserialize(strOrObj, store, cb) {\n    if (arguments.length !== 3) {\n      // store is optional\n      cb = store;\n      store = null;\n    }\n    validators.validate(validators.isFunction(cb), cb);\n\n    let serialized;\n    if (typeof strOrObj === \"string\") {\n      serialized = jsonParse(strOrObj);\n      if (serialized instanceof Error) {\n        return cb(serialized);\n      }\n    } else {\n      serialized = strOrObj;\n    }\n\n    const jar = new CookieJar(store, {\n      rejectPublicSuffixes: serialized.rejectPublicSuffixes,\n      looseMode: serialized.enableLooseMode,\n      allowSpecialUseDomain: serialized.allowSpecialUseDomain,\n      prefixSecurity: serialized.prefixSecurity\n    });\n    jar._importCookies(serialized, err => {\n      if (err) {\n        return cb(err);\n      }\n      cb(null, jar);\n    });\n  }\n\n  static deserializeSync(strOrObj, store) {\n    const serialized =\n      typeof strOrObj === \"string\" ? JSON.parse(strOrObj) : strOrObj;\n    const jar = new CookieJar(store, {\n      rejectPublicSuffixes: serialized.rejectPublicSuffixes,\n      looseMode: serialized.enableLooseMode\n    });\n\n    // catch this mistake early:\n    if (!jar.store.synchronous) {\n      throw new Error(\n        \"CookieJar store is not synchronous; use async API instead.\"\n      );\n    }\n\n    jar._importCookiesSync(serialized);\n    return jar;\n  }\n}\nCookieJar.fromJSON = CookieJar.deserializeSync;\n\n[\n  \"_importCookies\",\n  \"clone\",\n  \"getCookies\",\n  \"getCookieString\",\n  \"getSetCookieStrings\",\n  \"removeAllCookies\",\n  \"serialize\",\n  \"setCookie\"\n].forEach(name => {\n  CookieJar.prototype[name] = fromCallback(CookieJar.prototype[name]);\n});\nCookieJar.deserialize = fromCallback(CookieJar.deserialize);\n\n// Use a closure to provide a true imperative API for synchronous stores.\nfunction syncWrap(method) {\n  return function(...args) {\n    if (!this.store.synchronous) {\n      throw new Error(\n        \"CookieJar store is not synchronous; use async API instead.\"\n      );\n    }\n\n    let syncErr, syncResult;\n    this[method](...args, (err, result) => {\n      syncErr = err;\n      syncResult = result;\n    });\n\n    if (syncErr) {\n      throw syncErr;\n    }\n    return syncResult;\n  };\n}\n\nexports.version = VERSION;\nexports.CookieJar = CookieJar;\nexports.Cookie = Cookie;\nexports.Store = Store;\nexports.MemoryCookieStore = MemoryCookieStore;\nexports.parseDate = parseDate;\nexports.formatDate = formatDate;\nexports.parse = parse;\nexports.fromJSON = fromJSON;\nexports.domainMatch = domainMatch;\nexports.defaultPath = defaultPath;\nexports.pathMatch = pathMatch;\nexports.getPublicSuffix = pubsuffix.getPublicSuffix;\nexports.cookieCompare = cookieCompare;\nexports.permuteDomain = __webpack_require__(/*! ./permuteDomain */ \"(rsc)/./node_modules/tough-cookie/lib/permuteDomain.js\").permuteDomain;\nexports.permutePath = permutePath;\nexports.canonicalDomain = canonicalDomain;\nexports.PrefixSecurityEnum = PrefixSecurityEnum;\nexports.ParameterError = validators.ParameterError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/cookie.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/memstore.js":
/*!***************************************************!*\
  !*** ./node_modules/tough-cookie/lib/memstore.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\nconst { fromCallback } = __webpack_require__(/*! universalify */ \"(rsc)/./node_modules/universalify/index.js\");\nconst Store = (__webpack_require__(/*! ./store */ \"(rsc)/./node_modules/tough-cookie/lib/store.js\").Store);\nconst permuteDomain = (__webpack_require__(/*! ./permuteDomain */ \"(rsc)/./node_modules/tough-cookie/lib/permuteDomain.js\").permuteDomain);\nconst pathMatch = (__webpack_require__(/*! ./pathMatch */ \"(rsc)/./node_modules/tough-cookie/lib/pathMatch.js\").pathMatch);\nconst { getCustomInspectSymbol, getUtilInspect } = __webpack_require__(/*! ./utilHelper */ \"(rsc)/./node_modules/tough-cookie/lib/utilHelper.js\");\n\nclass MemoryCookieStore extends Store {\n  constructor() {\n    super();\n    this.synchronous = true;\n    this.idx = Object.create(null);\n    const customInspectSymbol = getCustomInspectSymbol();\n    if (customInspectSymbol) {\n      this[customInspectSymbol] = this.inspect;\n    }\n  }\n\n  inspect() {\n    const util = { inspect: getUtilInspect(inspectFallback) };\n    return `{ idx: ${util.inspect(this.idx, false, 2)} }`;\n  }\n\n  findCookie(domain, path, key, cb) {\n    if (!this.idx[domain]) {\n      return cb(null, undefined);\n    }\n    if (!this.idx[domain][path]) {\n      return cb(null, undefined);\n    }\n    return cb(null, this.idx[domain][path][key] || null);\n  }\n  findCookies(domain, path, allowSpecialUseDomain, cb) {\n    const results = [];\n    if (typeof allowSpecialUseDomain === \"function\") {\n      cb = allowSpecialUseDomain;\n      allowSpecialUseDomain = true;\n    }\n    if (!domain) {\n      return cb(null, []);\n    }\n\n    let pathMatcher;\n    if (!path) {\n      // null means \"all paths\"\n      pathMatcher = function matchAll(domainIndex) {\n        for (const curPath in domainIndex) {\n          const pathIndex = domainIndex[curPath];\n          for (const key in pathIndex) {\n            results.push(pathIndex[key]);\n          }\n        }\n      };\n    } else {\n      pathMatcher = function matchRFC(domainIndex) {\n        //NOTE: we should use path-match algorithm from S5.1.4 here\n        //(see : https://github.com/ChromiumWebApps/chromium/blob/b3d3b4da8bb94c1b2e061600df106d590fda3620/net/cookies/canonical_cookie.cc#L299)\n        Object.keys(domainIndex).forEach(cookiePath => {\n          if (pathMatch(path, cookiePath)) {\n            const pathIndex = domainIndex[cookiePath];\n            for (const key in pathIndex) {\n              results.push(pathIndex[key]);\n            }\n          }\n        });\n      };\n    }\n\n    const domains = permuteDomain(domain, allowSpecialUseDomain) || [domain];\n    const idx = this.idx;\n    domains.forEach(curDomain => {\n      const domainIndex = idx[curDomain];\n      if (!domainIndex) {\n        return;\n      }\n      pathMatcher(domainIndex);\n    });\n\n    cb(null, results);\n  }\n\n  putCookie(cookie, cb) {\n    if (!this.idx[cookie.domain]) {\n      this.idx[cookie.domain] = Object.create(null);\n    }\n    if (!this.idx[cookie.domain][cookie.path]) {\n      this.idx[cookie.domain][cookie.path] = Object.create(null);\n    }\n    this.idx[cookie.domain][cookie.path][cookie.key] = cookie;\n    cb(null);\n  }\n  updateCookie(oldCookie, newCookie, cb) {\n    // updateCookie() may avoid updating cookies that are identical.  For example,\n    // lastAccessed may not be important to some stores and an equality\n    // comparison could exclude that field.\n    this.putCookie(newCookie, cb);\n  }\n  removeCookie(domain, path, key, cb) {\n    if (\n      this.idx[domain] &&\n      this.idx[domain][path] &&\n      this.idx[domain][path][key]\n    ) {\n      delete this.idx[domain][path][key];\n    }\n    cb(null);\n  }\n  removeCookies(domain, path, cb) {\n    if (this.idx[domain]) {\n      if (path) {\n        delete this.idx[domain][path];\n      } else {\n        delete this.idx[domain];\n      }\n    }\n    return cb(null);\n  }\n  removeAllCookies(cb) {\n    this.idx = Object.create(null);\n    return cb(null);\n  }\n  getAllCookies(cb) {\n    const cookies = [];\n    const idx = this.idx;\n\n    const domains = Object.keys(idx);\n    domains.forEach(domain => {\n      const paths = Object.keys(idx[domain]);\n      paths.forEach(path => {\n        const keys = Object.keys(idx[domain][path]);\n        keys.forEach(key => {\n          if (key !== null) {\n            cookies.push(idx[domain][path][key]);\n          }\n        });\n      });\n    });\n\n    // Sort by creationIndex so deserializing retains the creation order.\n    // When implementing your own store, this SHOULD retain the order too\n    cookies.sort((a, b) => {\n      return (a.creationIndex || 0) - (b.creationIndex || 0);\n    });\n\n    cb(null, cookies);\n  }\n}\n\n[\n  \"findCookie\",\n  \"findCookies\",\n  \"putCookie\",\n  \"updateCookie\",\n  \"removeCookie\",\n  \"removeCookies\",\n  \"removeAllCookies\",\n  \"getAllCookies\"\n].forEach(name => {\n  MemoryCookieStore.prototype[name] = fromCallback(\n    MemoryCookieStore.prototype[name]\n  );\n});\n\nexports.MemoryCookieStore = MemoryCookieStore;\n\nfunction inspectFallback(val) {\n  const domains = Object.keys(val);\n  if (domains.length === 0) {\n    return \"[Object: null prototype] {}\";\n  }\n  let result = \"[Object: null prototype] {\\n\";\n  Object.keys(val).forEach((domain, i) => {\n    result += formatDomain(domain, val[domain]);\n    if (i < domains.length - 1) {\n      result += \",\";\n    }\n    result += \"\\n\";\n  });\n  result += \"}\";\n  return result;\n}\n\nfunction formatDomain(domainName, domainValue) {\n  const indent = \"  \";\n  let result = `${indent}'${domainName}': [Object: null prototype] {\\n`;\n  Object.keys(domainValue).forEach((path, i, paths) => {\n    result += formatPath(path, domainValue[path]);\n    if (i < paths.length - 1) {\n      result += \",\";\n    }\n    result += \"\\n\";\n  });\n  result += `${indent}}`;\n  return result;\n}\n\nfunction formatPath(pathName, pathValue) {\n  const indent = \"    \";\n  let result = `${indent}'${pathName}': [Object: null prototype] {\\n`;\n  Object.keys(pathValue).forEach((cookieName, i, cookieNames) => {\n    const cookie = pathValue[cookieName];\n    result += `      ${cookieName}: ${cookie.inspect()}`;\n    if (i < cookieNames.length - 1) {\n      result += \",\";\n    }\n    result += \"\\n\";\n  });\n  result += `${indent}}`;\n  return result;\n}\n\nexports.inspectFallback = inspectFallback;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/memstore.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/pathMatch.js":
/*!****************************************************!*\
  !*** ./node_modules/tough-cookie/lib/pathMatch.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\n/*\n * \"A request-path path-matches a given cookie-path if at least one of the\n * following conditions holds:\"\n */\nfunction pathMatch(reqPath, cookiePath) {\n  // \"o  The cookie-path and the request-path are identical.\"\n  if (cookiePath === reqPath) {\n    return true;\n  }\n\n  const idx = reqPath.indexOf(cookiePath);\n  if (idx === 0) {\n    // \"o  The cookie-path is a prefix of the request-path, and the last\n    // character of the cookie-path is %x2F (\"/\").\"\n    if (cookiePath.substr(-1) === \"/\") {\n      return true;\n    }\n\n    // \" o  The cookie-path is a prefix of the request-path, and the first\n    // character of the request-path that is not included in the cookie- path\n    // is a %x2F (\"/\") character.\"\n    if (reqPath.substr(cookiePath.length, 1) === \"/\") {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nexports.pathMatch = pathMatch;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/pathMatch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/permuteDomain.js":
/*!********************************************************!*\
  !*** ./node_modules/tough-cookie/lib/permuteDomain.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\nconst pubsuffix = __webpack_require__(/*! ./pubsuffix-psl */ \"(rsc)/./node_modules/tough-cookie/lib/pubsuffix-psl.js\");\n\n// Gives the permutation of all possible domainMatch()es of a given domain. The\n// array is in shortest-to-longest order.  Handy for indexing.\n\nfunction permuteDomain(domain, allowSpecialUseDomain) {\n  const pubSuf = pubsuffix.getPublicSuffix(domain, {\n    allowSpecialUseDomain: allowSpecialUseDomain\n  });\n\n  if (!pubSuf) {\n    return null;\n  }\n  if (pubSuf == domain) {\n    return [domain];\n  }\n\n  // Nuke trailing dot\n  if (domain.slice(-1) == \".\") {\n    domain = domain.slice(0, -1);\n  }\n\n  const prefix = domain.slice(0, -(pubSuf.length + 1)); // \".example.com\"\n  const parts = prefix.split(\".\").reverse();\n  let cur = pubSuf;\n  const permutations = [cur];\n  while (parts.length) {\n    cur = `${parts.shift()}.${cur}`;\n    permutations.push(cur);\n  }\n  return permutations;\n}\n\nexports.permuteDomain = permuteDomain;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/permuteDomain.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/pubsuffix-psl.js":
/*!********************************************************!*\
  !*** ./node_modules/tough-cookie/lib/pubsuffix-psl.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("/*!\n * Copyright (c) 2018, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\nconst psl = __webpack_require__(/*! psl */ \"(rsc)/./node_modules/psl/dist/psl.cjs\");\n\n// RFC 6761\nconst SPECIAL_USE_DOMAINS = [\n  \"local\",\n  \"example\",\n  \"invalid\",\n  \"localhost\",\n  \"test\"\n];\n\nconst SPECIAL_TREATMENT_DOMAINS = [\"localhost\", \"invalid\"];\n\nfunction getPublicSuffix(domain, options = {}) {\n  const domainParts = domain.split(\".\");\n  const topLevelDomain = domainParts[domainParts.length - 1];\n  const allowSpecialUseDomain = !!options.allowSpecialUseDomain;\n  const ignoreError = !!options.ignoreError;\n\n  if (allowSpecialUseDomain && SPECIAL_USE_DOMAINS.includes(topLevelDomain)) {\n    if (domainParts.length > 1) {\n      const secondLevelDomain = domainParts[domainParts.length - 2];\n      // In aforementioned example, the eTLD/pubSuf will be apple.localhost\n      return `${secondLevelDomain}.${topLevelDomain}`;\n    } else if (SPECIAL_TREATMENT_DOMAINS.includes(topLevelDomain)) {\n      // For a single word special use domain, e.g. 'localhost' or 'invalid', per RFC 6761,\n      // \"Application software MAY recognize {localhost/invalid} names as special, or\n      // MAY pass them to name resolution APIs as they would for other domain names.\"\n      return `${topLevelDomain}`;\n    }\n  }\n\n  if (!ignoreError && SPECIAL_USE_DOMAINS.includes(topLevelDomain)) {\n    throw new Error(\n      `Cookie has domain set to the public suffix \"${topLevelDomain}\" which is a special use domain. To allow this, configure your CookieJar with {allowSpecialUseDomain:true, rejectPublicSuffixes: false}.`\n    );\n  }\n\n  return psl.get(domain);\n}\n\nexports.getPublicSuffix = getPublicSuffix;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/pubsuffix-psl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/store.js":
/*!************************************************!*\
  !*** ./node_modules/tough-cookie/lib/store.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/*!\n * Copyright (c) 2015, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\n\n/*jshint unused:false */\n\nclass Store {\n  constructor() {\n    this.synchronous = false;\n  }\n\n  findCookie(domain, path, key, cb) {\n    throw new Error(\"findCookie is not implemented\");\n  }\n\n  findCookies(domain, path, allowSpecialUseDomain, cb) {\n    throw new Error(\"findCookies is not implemented\");\n  }\n\n  putCookie(cookie, cb) {\n    throw new Error(\"putCookie is not implemented\");\n  }\n\n  updateCookie(oldCookie, newCookie, cb) {\n    // recommended default implementation:\n    // return this.putCookie(newCookie, cb);\n    throw new Error(\"updateCookie is not implemented\");\n  }\n\n  removeCookie(domain, path, key, cb) {\n    throw new Error(\"removeCookie is not implemented\");\n  }\n\n  removeCookies(domain, path, cb) {\n    throw new Error(\"removeCookies is not implemented\");\n  }\n\n  removeAllCookies(cb) {\n    throw new Error(\"removeAllCookies is not implemented\");\n  }\n\n  getAllCookies(cb) {\n    throw new Error(\n      \"getAllCookies is not implemented (therefore jar cannot be serialized)\"\n    );\n  }\n}\n\nexports.Store = Store;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/store.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/utilHelper.js":
/*!*****************************************************!*\
  !*** ./node_modules/tough-cookie/lib/utilHelper.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("function requireUtil() {\n  try {\n    // eslint-disable-next-line no-restricted-modules\n    return __webpack_require__(/*! util */ \"util\");\n  } catch (e) {\n    return null;\n  }\n}\n\n// for v10.12.0+\nfunction lookupCustomInspectSymbol() {\n  return Symbol.for(\"nodejs.util.inspect.custom\");\n}\n\n// for older node environments\nfunction tryReadingCustomSymbolFromUtilInspect(options) {\n  const _requireUtil = options.requireUtil || requireUtil;\n  const util = _requireUtil();\n  return util ? util.inspect.custom : null;\n}\n\nexports.getUtilInspect = function getUtilInspect(fallback, options = {}) {\n  const _requireUtil = options.requireUtil || requireUtil;\n  const util = _requireUtil();\n  return function inspect(value, showHidden, depth) {\n    return util ? util.inspect(value, showHidden, depth) : fallback(value);\n  };\n};\n\nexports.getCustomInspectSymbol = function getCustomInspectSymbol(options = {}) {\n  const _lookupCustomInspectSymbol =\n    options.lookupCustomInspectSymbol || lookupCustomInspectSymbol;\n\n  // get custom inspect symbol for node environments\n  return (\n    _lookupCustomInspectSymbol() ||\n    tryReadingCustomSymbolFromUtilInspect(options)\n  );\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/utilHelper.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/validators.js":
/*!*****************************************************!*\
  !*** ./node_modules/tough-cookie/lib/validators.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/* ************************************************************************************\nExtracted from check-types.js\nhttps://gitlab.com/philbooth/check-types.js\n\nMIT License\n\nCopyright (c) 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019 Phil Booth\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n************************************************************************************ */\n\n\n/* Validation functions copied from check-types package - https://www.npmjs.com/package/check-types */\n\nconst toString = Object.prototype.toString;\n\nfunction isFunction(data) {\n  return typeof data === \"function\";\n}\n\nfunction isNonEmptyString(data) {\n  return isString(data) && data !== \"\";\n}\n\nfunction isDate(data) {\n  return isInstanceStrict(data, Date) && isInteger(data.getTime());\n}\n\nfunction isEmptyString(data) {\n  return data === \"\" || (data instanceof String && data.toString() === \"\");\n}\n\nfunction isString(data) {\n  return typeof data === \"string\" || data instanceof String;\n}\n\nfunction isObject(data) {\n  return toString.call(data) === \"[object Object]\";\n}\nfunction isInstanceStrict(data, prototype) {\n  try {\n    return data instanceof prototype;\n  } catch (error) {\n    return false;\n  }\n}\n\nfunction isUrlStringOrObject(data) {\n  return (\n    isNonEmptyString(data) ||\n    (isObject(data) &&\n      \"hostname\" in data &&\n      \"pathname\" in data &&\n      \"protocol\" in data) ||\n    isInstanceStrict(data, URL)\n  );\n}\n\nfunction isInteger(data) {\n  return typeof data === \"number\" && data % 1 === 0;\n}\n/* End validation functions */\n\nfunction validate(bool, cb, options) {\n  if (!isFunction(cb)) {\n    options = cb;\n    cb = null;\n  }\n  if (!isObject(options)) options = { Error: \"Failed Check\" };\n  if (!bool) {\n    if (cb) {\n      cb(new ParameterError(options));\n    } else {\n      throw new ParameterError(options);\n    }\n  }\n}\n\nclass ParameterError extends Error {\n  constructor(...params) {\n    super(...params);\n  }\n}\n\nexports.ParameterError = ParameterError;\nexports.isFunction = isFunction;\nexports.isNonEmptyString = isNonEmptyString;\nexports.isDate = isDate;\nexports.isEmptyString = isEmptyString;\nexports.isString = isString;\nexports.isObject = isObject;\nexports.isUrlStringOrObject = isUrlStringOrObject;\nexports.validate = validate;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/validators.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tough-cookie/lib/version.js":
/*!**************************************************!*\
  !*** ./node_modules/tough-cookie/lib/version.js ***!
  \**************************************************/
/***/ ((module) => {

eval("// generated by genversion\nmodule.exports = '4.1.4'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdG91Z2gtY29va2llL2xpYi92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vbm9kZV9tb2R1bGVzL3RvdWdoLWNvb2tpZS9saWIvdmVyc2lvbi5qcz81YzYwIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGdlbmVyYXRlZCBieSBnZW52ZXJzaW9uXG5tb2R1bGUuZXhwb3J0cyA9ICc0LjEuNCdcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tough-cookie/lib/version.js\n");

/***/ })

};
;