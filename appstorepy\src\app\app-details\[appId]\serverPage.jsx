"use client";
import React, { useState, useEffect, useRef, useMemo } from "react";
import { useRouter } from "next/navigation";
import Link from 'next/link';
import CollectionsIcon from '@mui/icons-material/Collections';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import UpdateIcon from '@mui/icons-material/Update';
import PlayCircleFilledIcon from '@mui/icons-material/PlayCircleFilled';
import { makeStyles } from "@mui/styles";
import Container from "@mui/material/Container";
import Collapse from '@mui/material/Collapse';
import WorldMap from "react-svg-worldmap";
import Rating from "@mui/material/Rating";
import StarIcon from "@mui/icons-material/Star";
import LockIcon from "@mui/icons-material/Lock";
import { useDispatch } from 'react-redux';
import axios from 'axios';
import { styled } from '@mui/material/styles';
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import LinearProgress, { linearProgressClasses } from '@mui/material/LinearProgress';
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import TopAppRankTable from "@/app/components/topAppRankingTable/topAppRanking";
import Navbar from "@/app/components/navbar/navbar.jsx";
import {
  Typography,
  Button,
  TextField,
  Card,
  CardMedia,
  CardContent,
  Box,
  Grid,
  TableContainer,
  Table,
  Chip,
  TableBody,
  DialogTitle,
  Dialog,
  Tooltip,
  DialogContent,
  IconButton,
} from "@mui/material";
import { useSession } from "next-auth/react";
import MenuItem from "@mui/material/MenuItem";
import Image from "next/image";
import { AppAnalyticsInstallsChart } from "@/app/components/charts/appDownloads";
import { AppAnalyticsRatingsChart } from "@/app/components/charts/appRatings";
import { AppAnalyticsReviewsChart } from "@/app/components/charts/appReviews";
import { selectedUserCountry } from "@/app/redux/slice/topAppSlice";
import { countries, timezoneToCountryName } from "@/app/utils/countries";
import { BeatLoader } from "react-spinners";


const useStyles = makeStyles({
  image: {
    width: "9rem",
    height: "9rem",
    borderRadius: "40px",
    marginRight: "1rem",
    padding: "0.5rem",
  },
});
function AppDetailsUI({ appDetailsInfo }) {

  const galleryRef = useRef(null);
  const descriptionRef = useRef(null);
  const MAX_HEIGHT = 170;
  const MAX_LENGTH = 500;
  const router = useRouter();
  const { status } = useSession();
  const dispatch = useDispatch();
  const appDetails = appDetailsInfo.app.appDetails;
  const appRanks = appDetailsInfo.app.appRanks;

  const appAnalytics = appDetailsInfo.app.analyticsData;
  const [similarApps, setSimilarApps] = useState([]);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [toggleTable, setToggleTable] = useState(false);
  const [appRanking, setAppRanking] = useState({
    category: "",
    charts: "",
  });
  const [showSimilarAppsModel, setShowSimilarAppsModel] = useState(false);
  const [similarAppsLoading, setSimilarAppsLoading] = useState(false);
  const itemsPerPage = 4;
  const [currentPage, setCurrentPage] = useState(1);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  const displayedApps = similarApps?.slice(startIndex, endIndex);
  const emptyAnalytics = appAnalytics.every((data) => data === null);

  const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
    height: 10,
    borderRadius: 5,
    [`&.${linearProgressClasses.colorPrimary}`]: {
      backgroundColor: theme.palette.grey[theme.palette.mode === 'light' ? 200 : 800],
    },
    [`& .${linearProgressClasses.bar}`]: {
      borderRadius: 5,
      backgroundColor: '#00A3FF',
      transition: 'transform 0.4s ease-out',
    },
  }));
  const collectionNames = Array.from(
    new Set(appRanks.map((rank) => rank.collectionName))
  );
  const categoryNames = Array.from(
    new Set(appRanks.map((rank) => rank.category))
  );
  const filteredAppRanks = appRanks.filter(rank => {
    const matchesCollection = rank.collectionName === appRanking.charts?.toUpperCase();
    const matchesCategory = rank.category === appRanking.category?.toUpperCase();
    return matchesCategory && matchesCollection;
  });

  const filteredAppRanksByTable = appRanks.filter(rank => {
    const matchesCategory = rank.category === appRanking.category?.toUpperCase();
    return matchesCategory;
  });


  useEffect(() => {
    if (Intl) {
      let userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      let userCountry = timezoneToCountryName[userTimeZone];
      const cc = countries.find((x) => x.label == userCountry);
      dispatch(selectedUserCountry({
        countryCode: cc?.code || 'US',
        country: userCountry || 'United States'
      }));
    }


    let isFirstTime = true
    async function handleScrollEvent() {
      if (((window.innerHeight + window.scrollY) >= document.body.offsetHeight) && isFirstTime) {
        isFirstTime = false
        const similarApps = await getSimilarApps(appDetails.appId);
        setSimilarApps(similarApps.app)
      }
    }

    window.addEventListener('scroll', handleScrollEvent)

    return () => {
      window.removeEventListener('scroll', handleScrollEvent);
    }
  }, [])


  const truncatedDescription = useMemo(() => {
    return appDetails.description.length > MAX_LENGTH
      ? `${appDetails.description.substring(0, MAX_LENGTH)}...`
      : appDetails.description;
  }, [appDetails.description]);


  const toggleDescription = () => {
    const currentScroll = window.pageYOffset;
    const elementPosition = descriptionRef.current.getBoundingClientRect().top;
    setShowFullDescription(!showFullDescription);

    setTimeout(() => {
      window.scrollTo({
        top: currentScroll + descriptionRef.current.getBoundingClientRect().top - elementPosition,
        behavior: 'smooth'
      });
    }, 10);
  };

  const getSimilarApps = async (appId) => {
    setSimilarAppsLoading(true)
    try {
      const response = await axios.get(`/api/similar_apps?appId=${appId}`);
      if (response.status === 200) {
        const appData = response.data.app;

        const obfuscatedSimilarApps = appData?.map(app => {
          const obfuscatedApp = {
            appId: app.appId,
            icon: app.icon,
            scoreText: app.scoreText, //obfuscateField(app.scoreText, noscrape),
            title: app.title, //obfuscateField(app.title, noscrape),
            developer: app.developer,
            showDeveloper: app.developer, //obfuscateField(app.developer, noscrape),
            genre: app.genre//obfuscateField(app.genre, noscrape)
          };

          return obfuscatedApp;
        });
        const similarApps = obfuscatedSimilarApps
        return {
          app: similarApps,
        };
      } else if (response.status === 404) {
        console.error("App information not found");
        return { app: null };
      } else {
        console.error("Unexpected status code:", response.status);
        return { app: null };
      }
    } catch (error) {
      console.error("Error fetching app details:", error);
      return { app: null };
    } finally {
      setSimilarAppsLoading(false)
    }
  }

  const countryData = filteredAppRanks.map((rank) => ({
    country: rank.countryCode.toLowerCase(),
    value: rank.position,
  }));
  useEffect(() => {
    const firstCollectionName =
      appRanks.length > 0 ? appRanks[0].collectionName?.toUpperCase() : "";
    const firstCategory =
      appRanks.length > 0 ? appRanks[0].category?.toUpperCase() : "";

    setAppRanking((prevAppRanking) => ({
      ...prevAppRanking,
      charts: firstCollectionName,
      category: firstCategory
    }));
  }, [appRanks]);

  const handleAppClick = (appId) => {
    router.push(`/app-details/${appId}`);
  };
  const classes = useStyles();


  const InstallDetailItem = ({ label, value, icon, isLast }) => {
    return (
      <div
        style={{
          margin: "0.5rem",
          padding: "0.6rem",
          textAlign: "center",
          borderRight: isLast ? "none" : "inset",
        }}
      >
        {value === "access" ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            enableBackground="new 0 0 24 24"
            viewBox="0 0 24 24"
            width="25"
            height="25"
          >
            <path
              d="M19.6,3.6c-0.2-0.2-0.5-0.3-0.8-0.2c-2.2,0.5-4.4,0-6.2-1.3c-0.3-0.2-0.8-0.2-1.1,0C9.6,3.4,7.4,3.9,5.2,3.4	C4.7,3.3,4.1,3.7,4,4.2c0,0.1,0,0.1,0,0.2v7.5c0,2.9,1.4,5.6,3.8,7.3l3.7,2.6c0.3,0.2,0.8,0.2,1.2,0l3.7-2.6
	c2.4-1.7,3.8-4.4,3.8-7.3V4.4C20,4.1,19.9,3.8,19.6,3.6z M15,11C15,11,15,11,15,11l-3.4,3.4c-0.4,0.4-1,0.4-1.4,0l0,0l-1.6-1.6
	c-0.4-0.4-0.4-1,0-1.4c0.4-0.4,1-0.4,1.4,0l0.9,0.9l2.7-2.7c0.4-0.4,1-0.4,1.4,0S15.4,10.6,15,11z"
              fill="#000000"
              className="color000 svgShape"
            ></path>
          </svg>
        ) : (
          <div style={{ marginBottom: "1px", fontFamily: "noscrape-obfuscated" }}>{value ? value : icon}</div>
        )}

        <div style={{ fontFamily: "noscrape-obfuscated" }}>{label}</div>
      </div>
    );
  };

  const monthlyAverage = (value) => {
    if (appAnalytics && appAnalytics[0]) {
      if (value === "reviews") {
        const monthlyReviews = appAnalytics[0].monthlyTotal.reviews;
        return monthlyReviews;
      } else if (value === "installs") {
        const monthlyInstalls = appAnalytics[0].monthlyTotal.installs;
        return monthlyInstalls;
      } else if (value === "dailyInstalls") {
        const monthlyInstalls = appAnalytics[0].monthlyTotal.dailyInstalls;
        return monthlyInstalls;
      } else if (value === "ratings") {
        const monthlyRatings = appAnalytics[0].monthlyTotal.ratings;
        return monthlyRatings;
      }
    }
    return "-";
  };
  const installItems = [
    { label: "Total Installs", value: appDetails.maxInstalls },
    {
      label: "Monthly Installs",
      value:
        status === "authenticated" ? monthlyAverage("installs") : <LockIcon />,
    },
    {
      label: "Daily Installs",
      value:
        status === "authenticated" ? monthlyAverage("dailyInstalls") : <LockIcon />,
    },
    { label: "Total Reviews", value: appDetails.reviews },
    {
      label: "Monthly Reviews",
      value:
        status === "authenticated" ? monthlyAverage("reviews") : <LockIcon />,
    },
    {
      label: "Total Ratings",
      value: appDetails.ratings,
    },
    {
      label: "Monthly Ratings",
      value:
        status === "authenticated" ? monthlyAverage("ratings") : <LockIcon />,
    },
    { label: appDetails.contentRating, value: "access", isLast: true },
    // { label: "Top Countries", icon: "-", isLast: true },
  ];
  const KeyValueRow = ({ label, value }) => (
    <tr style={{ borderBottom: "1px solid #ddd" }}>
      <td style={{ padding: "0.5rem" }}>{label}</td>
      <td
        style={{
          padding: "0.5rem",
          fontWeight: "bold",
          textAlign: "right",
          fontFamily: "noscrape-obfuscated"
        }}
      >
        {value}
      </td>
    </tr>
  );
  const toggleTableButton = {
    transform: toggleTable ? "rotate(180deg)" : "rotate(0deg)",
    transition: "transform 0.4s",
  };
  const toggleDescriptionButton = {
    transform: showFullDescription ? "rotate(180deg)" : "rotate(0deg)",
    transition: "transform 0.4s",
  };

  const toggleTableDescription = () => {
    setToggleTable((prevState) => !prevState);
  };

  const formatCount = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  };

  return (
    <>

      {!showSimilarAppsModel ? (
        <Container sx={{ borderRadius: "1rem", marginTop: "5.5rem", padding: "1rem", boxShadow: "rgba(100, 100, 111, 0.2) 0px 7px 49px 0px" }}>
          <Box>
            <Box
              sx={{
                padding: "1rem",
              }}
            >
              {appDetails ? (
                <>

                  <Box sx={{
                    display: "flex",
                    flexDirection: { xs: "column", sm: "row" },
                    justifyContent: "space-between",
                    alignItems: { xs: "flex-start", sm: "center" },
                    gap: 2,
                    mb: 3
                  }}>
                    <Box sx={{
                      display: "flex",
                      flexDirection: { xs: "column", sm: "row" },
                      alignItems: { xs: "center", sm: "flex-start" },
                      gap: 2,
                      width: "100%"
                    }}>
                      <Image
                        src={appDetails.icon}
                        alt={appDetails.title}
                        priority
                        width={96}
                        height={96}
                        style={{
                          borderRadius: "16px",
                          border: "1px solid #f0f0f0",
                          objectFit: "contain",
                          backgroundColor: "#f8f9fa",
                          minWidth: "96px"
                        }}
                      />

                      <Box sx={{
                        display: "flex",
                        flexDirection: "column",
                        textAlign: { xs: "center", sm: "left" }
                      }}>
                        <Typography
                          variant="h5"
                          sx={{
                            fontSize: { xs: "1.1rem", sm: "1.2rem" },
                            lineHeight: "1.5",
                            fontWeight: 600,
                            mb: 0.5
                          }}
                        >
                          {appDetails.title}
                        </Typography>

                        <Typography
                          variant="body1"
                          sx={{
                            fontWeight: 600,
                            color: "green",
                            mb: 0.5,
                            "&:hover": {
                              textDecoration: "underline",
                            },
                          }}
                        >
                          <Link
                            href={`/developer-apps/${appDetails.developer}`}
                           
                          >
                           {appDetails.showDeveloper}
                          </Link>
                        </Typography>

                        {appDetails.offersIAP && (
                          <Typography variant="body2" sx={{ color: "text.secondary", mb: 0.5 }}>
                            Contains ads{appDetails.adSupported && " • In-app purchases"}
                          </Typography>
                        )}

                        <Box sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: { xs: "center", sm: "flex-start" }
                        }}>
                          <Typography sx={{ fontWeight: "bold", mr: 0.5 }}>
                            {appDetails.scoreText}
                          </Typography>
                          <StarIcon sx={{ fontSize: "1.2rem", color: "#FFB400" }} />
                        </Box>
                      </Box>
                    </Box>

                    <Box sx={{
                      alignSelf: { xs: "center", sm: "flex-end" },
                      mt: { xs: 1, sm: 0 },
                      width: { xs: "100%", sm: "auto" },
                      textAlign: "center"
                    }}>
                      <Link
                        href={`https://play.google.com/store/apps/details?id=${appDetails.appId}`}
                        passHref
                        legacyBehavior
                        prefetch={false}
                      >
                        <a target="_blank">
                          <img
                            src="https://w7.pngwing.com/pngs/918/845/png-transparent-google-play-logo-google-play-app-store-android-google-play-text-logo-sign-thumbnail.png"
                            alt="PlayStore"
                            title="PlayStore"
                            style={{
                              width: "120px",
                              height: "40px",
                              maxWidth: "100%"
                            }}
                            loading="lazy"
                          />
                        </a>
                      </Link>
                    </Box>
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "flex-end",
                      fontSize: "1rem",
                      fontWeight: "600",
                      overflow: "auto",
                    }}
                  >
                    {installItems.map((item, index) => (
                      <InstallDetailItem key={index} {...item} />
                    ))}
                  </Box>
                  <Grid container spacing={2} sx={{ marginTop: "1rem" }}>
                    <Grid item xs={12} lg={8}>
                      <Box>
                        <Typography
                          variant="h2"
                          gutterBottom
                          sx={{
                            fontSize: "1.2rem",
                            fontWeight: "bold",
                            marginBottom: "1rem",
                          }}
                        >
                          {appDetails.title} Global Top Charts: Interactive Map
                          of Country and Category Rankings
                        </Typography>
                        <Box
                          sx={{
                            width: "100%",
                            height: "auto",
                            mt: 2,
                            display: "flex",
                            justifyContent: "center",
                          }}
                        >
                          <WorldMap
                            color="#00A3FF"
                            size="md"
                            backgroundColor="none"
                            richInteraction={true}
                            data={countryData}
                            tooltipBgColor="black"
                            tooltipTextColor="white"
                            tooltipTextFunction={(context) =>
                              context.countryValue !== undefined
                                ? String.raw`Country: ${context.countryName}
                                   ${appRanking.charts}: ${context.countryValue}`
                                : ""
                            }
                          />
                        </Box>
                      </Box>
                    </Grid>
                    <Grid item xs={12} lg={4}>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "end",
                          mt: { xs: 1, lg: 4 },
                        }}
                      >
                        <TableContainer sx={{ border: "2px  solid #e7dede", borderRadius: "10px" }}>
                          <Table>
                            <TableBody >
                              <KeyValueRow
                                label="Category"
                                value={appDetails.genre}
                              />
                              <KeyValueRow
                                label="Release date"
                                value={
                                  appDetails.released
                                    ? appDetails.released
                                    : "-"
                                }
                              />
                              <KeyValueRow
                                label="Latest Update"
                                value={
                                  appDetails.updated
                                    ? appDetails.updated
                                    : "-"
                                }
                              />
                              <KeyValueRow
                                label="Version"
                                value={appDetails.version}
                              />
                              <KeyValueRow
                                label="Reviews"
                                value={appDetails.reviews}
                              />
                              <KeyValueRow
                                label="Rating Votes"
                                value={appDetails.ratings}
                              />
                              {appDetails.adSupported && (
                                <KeyValueRow
                                  label="ADs"
                                  value={
                                    appDetails.adSupported === true
                                      ? "Yes"
                                      : "No"
                                  }
                                />
                              )}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </Box>
                    </Grid>
                  </Grid>

                  {/* Images */}
                  <Box sx={{
                    mt: 3,
                    width: '100%',
                    maxWidth: '95vw'
                  }}>
                    <Typography variant="h5" sx={{
                      fontWeight: 700,
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <CollectionsIcon color="primary" />
                      {appDetails.title} Media Gallery
                    </Typography>

                    <Box sx={{
                      position: 'relative',
                      '&:hover .scroll-button': {
                        opacity: 1
                      }
                    }}>
                      {/* Left Scroll Button */}
                      <IconButton
                        className="scroll-button"
                        sx={{
                          position: 'absolute',
                          left: 10,
                          top: '50%',
                          transform: 'translateY(-50%)',
                          zIndex: 2,
                          backgroundColor: 'rgba(0,0,0,0.5)',
                          color: 'white',
                          opacity: 0,
                          transition: 'opacity 0.3s ease',
                          '&:hover': {
                            backgroundColor: 'rgba(0,0,0,0.7)'
                          }
                        }}
                        onClick={() => galleryRef.current.scrollBy({ left: -300, behavior: 'smooth' })}
                      >
                        <ChevronLeftIcon />
                      </IconButton>

                      {/* Gallery Container */}
                      <Box
                        ref={galleryRef}
                        sx={{
                          display: 'flex',
                          overflowX: 'auto',
                          gap: 3,
                          scrollSnapType: 'x mandatory',
                          scrollBehavior: 'smooth',
                          py: 1,
                          '&::-webkit-scrollbar': {
                            height: 8,
                          },
                          '&::-webkit-scrollbar-thumb': {
                            backgroundColor: 'primary.main',
                            borderRadius: 4,
                          },
                          '& > *': {
                            scrollSnapAlign: 'start',
                            flexShrink: 0
                          }
                        }}
                      >
                        {/* Header Image */}
                        <Box sx={{
                          position: 'relative',
                          minWidth: { xs: '85vw', sm: 500 },
                          height: 350,
                          borderRadius: 2,
                          overflow: 'hidden',
                          boxShadow: 3
                        }}>
                          <img
                            src={appDetails.headerImage}
                            alt={`${appDetails.title} header`}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                              backgroundColor: '#f5f5f5'
                            }}
                            loading="eager" // Load immediately
                            onError={(e) => {
                              e.target.src = '/placeholder-image.png';
                              e.target.style.objectFit = 'contain';
                            }}
                          />
                        </Box>

                        {/* Video */}
                        {appDetails.video && (
                          <Box sx={{
                            minWidth: { xs: '85vw', sm: 500 },
                            height: 350,
                            borderRadius: 2,
                            overflow: 'hidden',
                            boxShadow: 3,
                            position: 'relative'
                          }}>
                            <iframe
                              width="100%"
                              height="100%"
                              src={appDetails.video}
                              title={`${appDetails.title} promo video`}
                              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                              allowFullScreen
                              style={{
                                border: 'none',
                                backgroundColor: '#000'
                              }}
                            />
                            <PlayCircleFilledIcon sx={{
                              position: 'absolute',
                              top: '50%',
                              left: '50%',
                              transform: 'translate(-50%, -50%)',
                              color: 'white',
                              fontSize: 60,
                              opacity: 0.8,
                              pointerEvents: 'none'
                            }} />
                          </Box>
                        )}

                        {/* Screenshots */}
                        {appDetails.screenshots.map((url, index) => (
                          <Box key={index} sx={{
                            minWidth: { xs: '85vw', sm: 500 },
                            height: 350,
                            borderRadius: 2,
                            overflow: 'hidden',
                            boxShadow: 3,
                            position: 'relative',
                            backgroundColor: '#f5f5f5'
                          }}>
                            <img
                              src={url}
                              alt={`${appDetails.title} screenshot ${index + 1}`}
                              style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'contain',
                                backgroundColor: '#f5f5f5'
                              }}
                              loading="eager" // Force load all images
                              onError={(e) => {
                                e.target.src = '/placeholder-image.png';
                                e.target.style.objectFit = 'contain';
                              }}
                            />
                          </Box>
                        ))}
                      </Box>

                      {/* Right Scroll Button */}
                      <IconButton
                        className="scroll-button"
                        sx={{
                          position: 'absolute',
                          right: 10,
                          top: '50%',
                          transform: 'translateY(-50%)',
                          zIndex: 2,
                          backgroundColor: 'rgba(0,0,0,0.5)',
                          color: 'white',
                          opacity: 0,
                          transition: 'opacity 0.3s ease',
                          '&:hover': {
                            backgroundColor: 'rgba(0,0,0,0.7)'
                          }
                        }}
                        onClick={() => galleryRef.current.scrollBy({ left: 300, behavior: 'smooth' })}
                      >
                        <ChevronRightIcon />
                      </IconButton>
                    </Box>
                  </Box>
                  {/* Description */}
                  <Box
                    ref={descriptionRef}
                    sx={{
                      mt: 4,       // Margin top
                      mb: 3,       // Margin bottom
                      position: 'relative',
                      overflow: 'hidden',
                      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                    }}
                  >
                    <Collapse
                      in={showFullDescription}
                      collapsedSize={150}
                      sx={{
                        '& .MuiCollapse-wrapperInner': {
                          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                        }
                      }}
                    >
                      <Typography
                        variant="body1"
                        sx={{
                          fontFamily: 'Inter',
                          lineHeight: 1.7,
                          whiteSpace: 'pre-line',
                          overflowWrap: 'break-word',
                          color: 'text.primary',
                          pb: 2,
                          '& a': {
                            color: 'primary.main',
                            textDecoration: 'none',
                            '&:hover': {
                              textDecoration: 'underline'
                            }
                          }
                        }}
                        component="div"
                        dangerouslySetInnerHTML={{ __html: appDetails.description }}
                      />
                    </Collapse>

                    {appDetails.description.length > MAX_LENGTH && (
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        pt: 2,
                        pb: 1,
                        background: showFullDescription
                          ? 'none'
                          : 'linear-gradient(to top, rgba(255,255,255,1) 60%, rgba(255,255,255,0) 100%)'
                      }}>
                        <Button
                          onClick={toggleDescription}
                          variant="text"
                          color="primary"
                          endIcon={
                            <KeyboardArrowDownIcon sx={{
                              transition: 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                              transform: showFullDescription ? 'rotate(180deg)' : 'rotate(0deg)'
                            }} />
                          }
                          sx={{
                            fontWeight: 600,
                            textTransform: 'none',
                            px: 2,
                            py: 1,
                            borderRadius: 2,
                            '&:hover': {
                              backgroundColor: 'rgba(0, 163, 255, 0.08)',
                              transform: 'translateY(-2px)'
                            },
                            transition: 'all 0.3s ease'
                          }}
                        >
                          {showFullDescription ? 'Show Less' : 'Read More'}
                        </Button>
                      </Box>
                    )}
                  </Box>

                  <Box sx={{
                    backgroundColor: 'background.paper',
                    p: 3,
                    mb: 3,
                  }}>
                    <Typography variant="h5" sx={{
                      fontWeight: 700,
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <StarIcon color="primary" />
                      Ratings & Reviews
                    </Typography>

                    <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
                      {/* Rating Summary */}
                      <Box sx={{
                        minWidth: 150,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        p: 2,
                        backgroundColor: 'grey.50',
                        borderRadius: 2
                      }}>
                        <Typography variant="h2" sx={{
                          fontWeight: 400,
                          color: 'primary.main',
                          lineHeight: 1
                        }}>
                          {appDetails.scoreText}
                        </Typography>
                        <Rating
                          value={appDetails.score ? parseFloat(appDetails.score) : 0}
                          precision={0.1}
                          readOnly
                          sx={{ my: 1 }}
                        />
                        <Typography variant="body1" sx={{
                          fontWeight: 600,
                          color: 'text.secondary'
                        }}>
                          {appDetails.ratings} Reviews
                        </Typography>
                      </Box>

                      {/* Rating Breakdown */}
                      <Box sx={{ flex: 1 }}>
                        {appDetails.histogram ? (
                          [5, 4, 3, 2, 1].map((rating) => {
                            const count = appDetails.histogram[rating] || 0;
                            const percentage = (count / appDetails.linearRatings) * 100;

                            return (
                              <Box key={rating} sx={{
                                display: 'flex',
                                alignItems: 'center',
                                mb: 2,
                                '&:hover .progress-bar': {
                                  '& .MuiLinearProgress-bar': {
                                    transform: 'scaleX(1.03)'
                                  }
                                }
                              }}>
                                <Typography variant="body1" sx={{
                                  fontWeight: 700,
                                  minWidth: 30,
                                  color: 'text.secondary'
                                }}>
                                  {rating}
                                </Typography>
                                <Box sx={{
                                  flex: 1,
                                  mx: 2,
                                  display: 'flex',
                                  alignItems: 'center'
                                }}>
                                  <BorderLinearProgress
                                    className="progress-bar"
                                    variant="determinate"
                                    value={percentage}
                                    sx={{ flex: 1 }}
                                  />
                                  <Typography variant="body2" sx={{
                                    ml: 2,
                                    minWidth: 40,
                                    fontWeight: 600,
                                    color: 'text.secondary'
                                  }}>
                                    {Math.round(percentage)}%
                                  </Typography>
                                </Box>
                                <Typography variant="body2" sx={{
                                  minWidth: 50,
                                  textAlign: 'right',
                                  color: 'text.secondary'
                                }}>
                                  {formatCount(count)}
                                </Typography>
                              </Box>
                            );
                          })
                        ) : (
                          <Typography variant="body1" color="text.secondary">
                            No rating data available
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Box>
                  {appDetails.recentChanges && (
                    <Box sx={{
                      backgroundColor: '#f5f5f5',
                      borderRadius: '8px',
                      p: 2,
                      mt: 2,
                      borderLeft: '4px solid #00A3FF'
                    }}>
                      <Typography
                        variant="subtitle1"
                        sx={{
                          fontWeight: 600,
                          mb: 1,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          color: '#333'
                        }}
                      >
                        <UpdateIcon fontSize="small" />
                        What's New
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          fontFamily: 'Inter',
                          lineHeight: 1.5,
                          whiteSpace: 'pre-line',
                          overflowWrap: 'break-word',
                          color: '#555'
                        }}
                      >
                        {appDetails.recentChanges
                          .replace(/&gt;/g, '>')
                          .replace(/&lt;/g, '<')
                          .replace(/&amp;/g, '&')
                          .replace(/<br>/g, '\n')
                        }
                      </Typography>
                    </Box>
                  )}

                  <Box>
                    <Typography
                      variant="h5"
                      fontWeight="bold"
                      marginBottom="1rem"
                      fontSize="1.2rem"
                      marginTop="1rem"
                    >
                      {appDetails.title} Downloads & Revenue Estimates
                    </Typography>
                    {emptyAnalytics ? (
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          backgroundColor: "#f8f9fa",
                          padding: "5rem",
                        }}
                      >
                        <Typography sx={{ fontWeight: 600 }}>
                          No Downloads charts for this app yet
                        </Typography>
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          backgroundColor: "#f8f9fa",
                        }}
                      >
                        <AppAnalyticsInstallsChart status={status} analyticsData={appAnalytics} />
                      </Box>
                    )}
                  </Box>
                  <Box>
                    <Typography
                      variant="h5"
                      fontWeight="bold"
                      marginBottom="1rem"
                      fontSize="1.2rem"
                      marginTop="1rem"
                    >
                      {appDetails.title} Ratings Estimates
                    </Typography>
                    {emptyAnalytics ? (
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          backgroundColor: "#f8f9fa",
                          padding: "5rem",
                        }}
                      >
                        <Typography sx={{ fontWeight: 600 }}>
                          No Ratings charts for this app yet
                        </Typography>
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          backgroundColor: "#f8f9fa",
                        }}
                      >
                        <AppAnalyticsRatingsChart status={status} analyticsData={appAnalytics} />
                      </Box>
                    )}
                  </Box>
                  <Box>
                    <Typography
                      variant="h5"
                      fontWeight="bold"
                      marginBottom="1rem"
                      fontSize="1.2rem"
                      marginTop="1rem"
                    >
                      {appDetails.title} Reviews Estimates
                    </Typography>

                    {emptyAnalytics ? (
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          backgroundColor: "#f8f9fa",
                          padding: "5rem",
                        }}
                      >
                        <Typography sx={{ fontWeight: 600 }}>
                          No Reviews charts for this app yet
                        </Typography>
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          backgroundColor: "#f8f9fa",
                        }}
                      >
                        <AppAnalyticsReviewsChart status={status} analyticsData={appAnalytics} />
                      </Box>
                    )}
                  </Box>
                  <Box>
                    <Typography
                      variant="h5"
                      fontWeight="bold"
                      marginBottom="1rem"
                      marginTop="2rem"
                      fontSize="1.2rem"
                    >
                      {appDetails.title} Global Top Charts: Interactive Map of
                      Country and Category Rankings
                    </Typography>
                    <Box
                      sx={{
                        position: "relative",
                        width: "100%",
                        border: "1px solid #e0e0e0",
                        borderRadius: "4px",
                        background: "rgba(247, 249, 251, 1)",
                        padding: "1rem",
                      }}
                    >
                      {appRanks && appRanks.length > 0 ? (
                        <>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <Typography
                              variant="h3"
                              sx={{
                                fontSize: "20px",
                              }}
                              fontWeight="bold"
                            >
                              Ranking History
                            </Typography>
                            <Box>
                              <TextField
                                sx={{
                                  display: "inline-flex",
                                  flexDirection: "column",
                                  position: "relative",
                                  padding: "0px",
                                  border: " 0px",
                                  verticalAlign: "top",
                                  margin: "8px",
                                  minWidth: "200px",
                                }}
                                id="top-rankings"
                                select
                                label="Category"
                                value={appRanking.category || ""}
                                onChange={(e) =>
                                  setAppRanking({
                                    ...appRanking,
                                    category: e.target.value,
                                  })
                                }
                              >
                                {categoryNames.map((category) => (
                                  <MenuItem
                                    key={category}
                                    value={category}
                                  >
                                    {category}
                                  </MenuItem>
                                ))}
                              </TextField>
                              <TextField
                                sx={{
                                  display: "inline-flex",
                                  flexDirection: "column",
                                  position: "relative",
                                  padding: "0px",
                                  border: " 0px",
                                  verticalAlign: "top",
                                  margin: "8px",
                                  minWidth: "150px",
                                }}
                                id="top-rankings"
                                select
                                label="Top Charts"
                                value={appRanking.charts || ""}
                                onChange={(e) =>
                                  setAppRanking({
                                    ...appRanking,
                                    charts: e.target.value,
                                  })
                                }
                              >
                                {collectionNames.map((collectionName) => (
                                  <MenuItem
                                    key={collectionName}
                                    value={collectionName}
                                  >
                                    {collectionName}
                                  </MenuItem>
                                ))}
                              </TextField>
                            </Box>
                          </Box>
                          <Box
                            sx={{
                              width: "100%",
                              height: "auto",
                              marginTop: "24px",
                              position: "relative",
                              minHeight: "480px",
                              display: "flex",
                              justifyContent: "center",
                            }}
                          >
                            <WorldMap
                              color="#00A3FF"
                              size="responsive"
                              richInteraction={true}
                              data={countryData}
                              backgroundColor={"#fafcff"}
                              tooltipBgColor="black"
                              border
                              strokeWidth={3}
                              strokeOpacity={0.3}
                              borderColor="black"
                              tooltipTextColor="white"
                              frame
                              tooltipTextFunction={(context) =>
                                context.countryValue !== undefined
                                  ? String.raw`Country: ${context.countryName}
                                       ${appRanking.charts}: ${context.countryValue}`
                                  : ""
                              }
                            />
                          </Box>

                          <Box sx={{ mb: 0, pb: 0 }}>
                            {toggleTable && filteredAppRanksByTable.length > 0 ? (
                              <Box sx={{ padding: "1rem" }}>
                                <TopAppRankTable
                                  app={filteredAppRanksByTable}
                                />
                              </Box>
                            ) : null}
                            <Box
                              sx={{ display: "flex", justifyContent: "center" }}
                            >
                              <IconButton
                                onClick={toggleTableDescription}
                                style={toggleTableButton}
                                fullwidth="true"
                              >
                                {toggleTable ? (
                                  <KeyboardArrowDownIcon
                                    sx={{
                                      fontSize: "2.5rem",
                                      color: "#00A3FF",
                                    }}
                                  />
                                ) : (
                                  <KeyboardArrowDownIcon
                                    sx={{
                                      color: "#00A3FF",
                                      fontSize: "2.5rem",
                                    }}
                                  />
                                )}
                              </IconButton>

                            </Box>
                          </Box>
                        </>
                      ) : (
                        <Box
                          display="flex"
                          justifyContent="center"
                          fontWeight="700"
                          fontSize="1.2rem"
                          padding="2rem"
                        >
                          No Ranking history for this App
                        </Box>
                      )}
                    </Box>
                  </Box>
                  <Box style={{ opacity: similarAppsLoading ? 0.5 : 1 }}>
                    <Typography
                      variant="h5"
                      fontWeight="bold"
                      marginBottom="1rem"
                      marginTop="2rem"
                      display="flex"
                    >
                      Similar Apps &nbsp;
                      {
                        similarApps && similarApps.length > 0 ? <Button
                          variant="outlined"
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: "#00A3FF",
                            "&:hover": {
                              backgroundColor: "#00A3FF",
                              color: "#fff",
                            },
                            textTransform: "none",
                            letterSpacing: "2px",
                            borderRadius: "12px"
                          }}
                          onClick={() => setShowSimilarAppsModel(true)}
                        >
                          <ArrowForwardIcon />
                        </Button> : null
                      }

                    </Typography>
                    {similarAppsLoading ? (
                      <Box
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          height: "40vh"
                        }}
                      >
                        <BeatLoader color={"#00A3FF"} loading={similarAppsLoading} size={25} />
                      </Box>
                    ) : (
                      <Box>
                        {similarApps && similarApps.length > 0 ? (
                          <>
                            <Grid container spacing={2}>
                              {displayedApps.map((app) => (
                                <Grid item xs={12} sm={6} md={3} key={app.appId}>
                                  <Card sx={{
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    transition: 'all 0.2s ease',
                                    borderRadius: '12px',
                                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                    '&:hover': {
                                      boxShadow: '0 6px 16px rgba(0,0,0,0.15)',
                                      transform: 'translateY(-2px)'
                                    },
                                    position: 'relative',
                                    overflow: 'visible'
                                  }}>
                                    {/* Image Container */}
                                    <Box sx={{
                                      p: 2,
                                      pb: 1,
                                      textAlign: 'center',
                                      position: 'relative'
                                    }}>
                                      <Box sx={{
                                        width: '100%',
                                        height: 0,
                                        paddingBottom: '100%', // Creates square container
                                        position: 'relative',
                                        mx: 'auto'
                                      }}>
                                        <CardMedia
                                          component="img"
                                          sx={{
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            width: '100%',
                                            height: '100%',
                                            borderRadius: '12px',
                                            cursor: 'pointer',
                                            objectFit: 'contain',
                                            backgroundColor: '#f8f9fa',
                                            border: '1px solid #f0f0f0'
                                          }}
                                          image={app.icon}
                                          alt={app.title}
                                          onError={(e) => {
                                            e.currentTarget.onerror = null;
                                            e.currentTarget.src = "https://cdn-icons-png.flaticon.com/128/300/300218.png";
                                          }}
                                          onClick={() => handleAppClick(app.appId)}
                                        />
                                      </Box>
                                    </Box>

                                    {/* Content */}
                                    <CardContent sx={{
                                      flexGrow: 1,
                                      p: 2,
                                      pt: 0,
                                      display: 'flex',
                                      flexDirection: 'column'
                                    }}>
                                      <Tooltip title={app.title} arrow>
                                        <Typography
                                          gutterBottom
                                          variant="subtitle1"
                                          sx={{
                                            fontWeight: 600,
                                            whiteSpace: 'nowrap',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            mb: 1,
                                            fontSize: '0.95rem',
                                            lineHeight: 1.3,
                                            '& a': {
                                              color: 'text.primary',
                                              textDecoration: 'none',
                                              '&:hover': {
                                                color: 'primary.main'
                                              }
                                            }
                                          }}
                                        >
                                          <Link href={`/app-details/${app.appId}`}>
                                            {app.title}
                                          </Link>
                                        </Typography>
                                      </Tooltip>

                                      {/* Developer */}
                                      <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{
                                          mb: 1,
                                          fontSize: '0.8rem',
                                          whiteSpace: 'nowrap',
                                          overflow: 'hidden',
                                          textOverflow: 'ellipsis'
                                        }}
                                      >
                                        <Link href={`/developer-apps/${app.developer}`}>
                                          {app.showDeveloper}
                                        </Link>
                                      </Typography>

                                      {/* Genre */}
                                      <Chip
                                        label={app.genre}
                                        size="small"
                                        sx={{
                                          mb: 1.5,
                                          fontSize: '0.7rem',
                                          height: '24px',
                                          maxWidth: '100%',
                                          '& .MuiChip-label': {
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            whiteSpace: 'nowrap'
                                          }
                                        }}
                                      />

                                      {/* Rating */}
                                      <Box sx={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        mt: 'auto'
                                      }}>
                                        <StarIcon sx={{
                                          color: '#FFB400',
                                          fontSize: '18px',
                                          mr: 0.5
                                        }} />
                                        <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                                          {app.scoreText}
                                        </Typography>
                                        <Box sx={{ flexGrow: 1 }} />
                                        <Chip
                                          label="ACTIVE"
                                          size="small"
                                          sx={{
                                            fontSize: '0.7rem',
                                            height: '22px',
                                            bgcolor: '#E3F2FD',
                                            color: '#1976D2'
                                          }}
                                        />
                                      </Box>
                                    </CardContent>
                                  </Card>
                                </Grid>
                              ))}
                            </Grid>

                            <Box
                              sx={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                mt: 3,
                                px: 1
                              }}
                            >
                              <Typography variant="body2" sx={{ color: '#666' }}>
                                Showing {startIndex + 1}–{endIndex <= similarApps.length ? endIndex : similarApps.length} of {similarApps.length}
                              </Typography>

                              <Button
                                sx={{
                                  backgroundColor: "transparent",
                                  color: "#00A3FF",
                                  "&:hover": {
                                    backgroundColor: "rgba(0, 163, 255, 0.08)",
                                  },
                                  textTransform: "none",
                                  fontWeight: 600,
                                  fontSize: '0.9rem'
                                }}
                                variant="text"
                                endIcon={<ArrowForwardIcon fontSize="small" />}
                                onClick={() => setShowSimilarAppsModel(true)}
                              >
                                View All
                              </Button>
                            </Box>
                          </>
                        ) : (
                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            p: 3,
                            backgroundColor: '#fafafa',
                            borderRadius: '8px',
                            color: '#666'
                          }}>
                            <InfoOutlinedIcon sx={{ mr: 1 }} />
                            <Typography variant="body1">No similar apps found</Typography>
                          </Box>
                        )}
                      </Box>
                    )}
                  </Box>
                </>
              ) : (
                <div className="flex justify-center items-center h-full text-center text-2xl font-semibold text-gray-600">
                  No data available
                </div>
              )}
            </Box>
          </Box>
        </Container>
      ) : (
        <Box>
          <Dialog
            fullScreen
            open={showSimilarAppsModel}
            onClose={() => setShowSimilarAppsModel(false)}
            PaperProps={{
              sx: {
                backgroundColor: '#f8fafc' // Light background for the dialog
              }
            }}
          >
            {/* Header Section */}
            <Box sx={{
              position: "sticky",
              top: 0,
              zIndex: 1200,
              backgroundColor: "#ffffff",
              borderBottom: "1px solid #f0f0f0",
              p: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <Box sx={{ display: "flex", alignItems: 'center' }}>
                <IconButton
                  onClick={() => setShowSimilarAppsModel(false)}
                  sx={{
                    color: "#00A3FF",
                    mr: 2,
                    "&:hover": {
                      backgroundColor: "rgba(0, 163, 255, 0.1)"
                    }
                  }}
                >
                  <ArrowBackIcon />
                </IconButton>
                <Typography variant="h6" sx={{
                  fontWeight: 600,
                  color: "#00A3FF",
                }}>
                  Similar Apps
                </Typography>
              </Box>
              <Typography variant="body2" sx={{ color: '#666' }}>
                {similarApps.length} apps found
              </Typography>
            </Box>

            {/* Content Section */}
            <DialogContent sx={{
              p: { xs: 2, sm: 3 },
              backgroundColor: '#f8fafc'
            }}>
              <Grid container spacing={3}>
                {similarApps.map((app) => (
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    md={4}
                    lg={3}
                    key={app.appId}
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      transition: 'transform 0.2s',
                      '&:hover': {
                        transform: 'translateY(-4px)'
                      }
                    }}
                  >
                    <Card sx={{
                      width: '100%',
                      maxWidth: 280,
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      borderRadius: '12px',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                      '&:hover': {
                        boxShadow: '0 6px 16px rgba(0,0,0,0.12)',
                      },
                      border: '1px solid #eaeaea'
                    }}>
                      {/* Image Container */}
                      <Box sx={{
                        p: 2,
                        pb: 1,
                        textAlign: 'center',
                        position: 'relative'
                      }}>
                        <Box sx={{
                          width: '100%',
                          height: 0,
                          paddingBottom: '100%',
                          position: 'relative',
                          mx: 'auto'
                        }}>
                          <CardMedia
                            component="img"
                            sx={{
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              width: '100%',
                              height: '100%',
                              borderRadius: '12px',
                              cursor: 'pointer',
                              objectFit: 'contain',
                              backgroundColor: '#f8f9fa',
                              border: '1px solid #f0f0f0'
                            }}
                            image={app.icon}
                            alt={app.title}
                            onError={(e) => {
                              e.currentTarget.onerror = null;
                              e.currentTarget.src = "https://cdn-icons-png.flaticon.com/128/300/300218.png";
                            }}
                            onClick={() => handleAppClick(app.appId)}
                          />
                        </Box>
                      </Box>

                      {/* Content */}
                      <CardContent sx={{
                        flexGrow: 1,
                        p: 2,
                        pt: 0,
                        display: 'flex',
                        flexDirection: 'column'
                      }}>
                        <Tooltip title={app.title} arrow>
                          <Typography
                            gutterBottom
                            variant="subtitle1"
                            sx={{
                              fontWeight: 600,
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              mb: 1,
                              fontSize: '1rem',
                              lineHeight: 1.3,
                              '& a': {
                                color: 'text.primary',
                                textDecoration: 'none',
                                '&:hover': {
                                  color: 'primary.main'
                                }
                              }
                            }}
                          >
                            <Link href={`/app-details/${app.appId}`}>
                              {app.title}
                            </Link>
                          </Typography>
                        </Tooltip>

                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            mb: 1.5,
                            fontSize: '0.8rem',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis'
                          }}
                        >
                          <Link href={`/developer-apps/${app.developer}`}>
                            {app.showDeveloper}
                          </Link>
                        </Typography>

                        <Chip
                          label={app.genre}
                          size="small"
                          sx={{
                            mb: 2,
                            fontSize: '0.7rem',
                            height: '24px',
                            maxWidth: '100%',
                            '& .MuiChip-label': {
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap'
                            }
                          }}
                        />

                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          mt: 'auto',
                          justifyContent: 'space-between'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <StarIcon sx={{
                              color: '#FFB400',
                              fontSize: '18px',
                              mr: 0.5
                            }} />
                            <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                              {app.scoreText}
                            </Typography>
                          </Box>
                          <Chip
                            label="ACTIVE"
                            size="small"
                            sx={{
                              fontSize: '0.7rem',
                              height: '22px',
                              bgcolor: '#E3F2FD',
                              color: '#1976D2'
                            }}
                          />
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </DialogContent>
          </Dialog>
        </Box>
      )}
    </>
  );
}

export default AppDetailsUI;
