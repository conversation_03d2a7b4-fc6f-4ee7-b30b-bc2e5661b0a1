"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nth-check";
exports.ids = ["vendor-chunks/nth-check"];
exports.modules = {

/***/ "(rsc)/./node_modules/nth-check/lib/esm/compile.js":
/*!***************************************************!*\
  !*** ./node_modules/nth-check/lib/esm/compile.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compile: () => (/* binding */ compile),\n/* harmony export */   generate: () => (/* binding */ generate)\n/* harmony export */ });\n/* harmony import */ var boolbase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! boolbase */ \"(rsc)/./node_modules/boolbase/index.js\");\n\n/**\n * Returns a function that checks if an elements index matches the given rule\n * highly optimized to return the fastest solution.\n *\n * @param parsed A tuple [a, b], as returned by `parse`.\n * @returns A highly optimized function that returns whether an index matches the nth-check.\n * @example\n *\n * ```js\n * const check = nthCheck.compile([2, 3]);\n *\n * check(0); // `false`\n * check(1); // `false`\n * check(2); // `true`\n * check(3); // `false`\n * check(4); // `true`\n * check(5); // `false`\n * check(6); // `true`\n * ```\n */\nfunction compile(parsed) {\n    const a = parsed[0];\n    // Subtract 1 from `b`, to convert from one- to zero-indexed.\n    const b = parsed[1] - 1;\n    /*\n     * When `b <= 0`, `a * n` won't be lead to any matches for `a < 0`.\n     * Besides, the specification states that no elements are\n     * matched when `a` and `b` are 0.\n     *\n     * `b < 0` here as we subtracted 1 from `b` above.\n     */\n    if (b < 0 && a <= 0)\n        return boolbase__WEBPACK_IMPORTED_MODULE_0__.falseFunc;\n    // When `a` is in the range -1..1, it matches any element (so only `b` is checked).\n    if (a === -1)\n        return (index) => index <= b;\n    if (a === 0)\n        return (index) => index === b;\n    // When `b <= 0` and `a === 1`, they match any element.\n    if (a === 1)\n        return b < 0 ? boolbase__WEBPACK_IMPORTED_MODULE_0__.trueFunc : (index) => index >= b;\n    /*\n     * Otherwise, modulo can be used to check if there is a match.\n     *\n     * Modulo doesn't care about the sign, so let's use `a`s absolute value.\n     */\n    const absA = Math.abs(a);\n    // Get `b mod a`, + a if this is negative.\n    const bMod = ((b % absA) + absA) % absA;\n    return a > 1\n        ? (index) => index >= b && index % absA === bMod\n        : (index) => index <= b && index % absA === bMod;\n}\n/**\n * Returns a function that produces a monotonously increasing sequence of indices.\n *\n * If the sequence has an end, the returned function will return `null` after\n * the last index in the sequence.\n *\n * @param parsed A tuple [a, b], as returned by `parse`.\n * @returns A function that produces a sequence of indices.\n * @example <caption>Always increasing (2n+3)</caption>\n *\n * ```js\n * const gen = nthCheck.generate([2, 3])\n *\n * gen() // `1`\n * gen() // `3`\n * gen() // `5`\n * gen() // `8`\n * gen() // `11`\n * ```\n *\n * @example <caption>With end value (-2n+10)</caption>\n *\n * ```js\n *\n * const gen = nthCheck.generate([-2, 5]);\n *\n * gen() // 0\n * gen() // 2\n * gen() // 4\n * gen() // null\n * ```\n */\nfunction generate(parsed) {\n    const a = parsed[0];\n    // Subtract 1 from `b`, to convert from one- to zero-indexed.\n    let b = parsed[1] - 1;\n    let n = 0;\n    // Make sure to always return an increasing sequence\n    if (a < 0) {\n        const aPos = -a;\n        // Get `b mod a`\n        const minValue = ((b % aPos) + aPos) % aPos;\n        return () => {\n            const val = minValue + aPos * n++;\n            return val > b ? null : val;\n        };\n    }\n    if (a === 0)\n        return b < 0\n            ? // There are no result — always return `null`\n                () => null\n            : // Return `b` exactly once\n                () => (n++ === 0 ? b : null);\n    if (b < 0) {\n        b += a * Math.ceil(-b / a);\n    }\n    return () => a * n++ + b;\n}\n//# sourceMappingURL=compile.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/nth-check/lib/esm/compile.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/nth-check/lib/esm/index.js":
/*!*************************************************!*\
  !*** ./node_modules/nth-check/lib/esm/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compile: () => (/* reexport safe */ _compile_js__WEBPACK_IMPORTED_MODULE_1__.compile),\n/* harmony export */   \"default\": () => (/* binding */ nthCheck),\n/* harmony export */   generate: () => (/* reexport safe */ _compile_js__WEBPACK_IMPORTED_MODULE_1__.generate),\n/* harmony export */   parse: () => (/* reexport safe */ _parse_js__WEBPACK_IMPORTED_MODULE_0__.parse),\n/* harmony export */   sequence: () => (/* binding */ sequence)\n/* harmony export */ });\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parse.js */ \"(rsc)/./node_modules/nth-check/lib/esm/parse.js\");\n/* harmony import */ var _compile_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./compile.js */ \"(rsc)/./node_modules/nth-check/lib/esm/compile.js\");\n\n\n\n/**\n * Parses and compiles a formula to a highly optimized function.\n * Combination of {@link parse} and {@link compile}.\n *\n * If the formula doesn't match any elements,\n * it returns [`boolbase`](https://github.com/fb55/boolbase)'s `falseFunc`.\n * Otherwise, a function accepting an _index_ is returned, which returns\n * whether or not the passed _index_ matches the formula.\n *\n * Note: The nth-rule starts counting at `1`, the returned function at `0`.\n *\n * @param formula The formula to compile.\n * @example\n * const check = nthCheck(\"2n+3\");\n *\n * check(0); // `false`\n * check(1); // `false`\n * check(2); // `true`\n * check(3); // `false`\n * check(4); // `true`\n * check(5); // `false`\n * check(6); // `true`\n */\nfunction nthCheck(formula) {\n    return (0,_compile_js__WEBPACK_IMPORTED_MODULE_1__.compile)((0,_parse_js__WEBPACK_IMPORTED_MODULE_0__.parse)(formula));\n}\n/**\n * Parses and compiles a formula to a generator that produces a sequence of indices.\n * Combination of {@link parse} and {@link generate}.\n *\n * @param formula The formula to compile.\n * @returns A function that produces a sequence of indices.\n * @example <caption>Always increasing</caption>\n *\n * ```js\n * const gen = nthCheck.sequence('2n+3')\n *\n * gen() // `1`\n * gen() // `3`\n * gen() // `5`\n * gen() // `8`\n * gen() // `11`\n * ```\n *\n * @example <caption>With end value</caption>\n *\n * ```js\n *\n * const gen = nthCheck.sequence('-2n+5');\n *\n * gen() // 0\n * gen() // 2\n * gen() // 4\n * gen() // null\n * ```\n */\nfunction sequence(formula) {\n    return (0,_compile_js__WEBPACK_IMPORTED_MODULE_1__.generate)((0,_parse_js__WEBPACK_IMPORTED_MODULE_0__.parse)(formula));\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/nth-check/lib/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/nth-check/lib/esm/parse.js":
/*!*************************************************!*\
  !*** ./node_modules/nth-check/lib/esm/parse.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n// Following http://www.w3.org/TR/css3-selectors/#nth-child-pseudo\n// Whitespace as per https://www.w3.org/TR/selectors-3/#lex is \" \\t\\r\\n\\f\"\nconst whitespace = new Set([9, 10, 12, 13, 32]);\nconst ZERO = \"0\".charCodeAt(0);\nconst NINE = \"9\".charCodeAt(0);\n/**\n * Parses an expression.\n *\n * @throws An `Error` if parsing fails.\n * @returns An array containing the integer step size and the integer offset of the nth rule.\n * @example nthCheck.parse(\"2n+3\"); // returns [2, 3]\n */\nfunction parse(formula) {\n    formula = formula.trim().toLowerCase();\n    if (formula === \"even\") {\n        return [2, 0];\n    }\n    else if (formula === \"odd\") {\n        return [2, 1];\n    }\n    // Parse [ ['-'|'+']? INTEGER? {N} [ S* ['-'|'+'] S* INTEGER ]?\n    let idx = 0;\n    let a = 0;\n    let sign = readSign();\n    let number = readNumber();\n    if (idx < formula.length && formula.charAt(idx) === \"n\") {\n        idx++;\n        a = sign * (number !== null && number !== void 0 ? number : 1);\n        skipWhitespace();\n        if (idx < formula.length) {\n            sign = readSign();\n            skipWhitespace();\n            number = readNumber();\n        }\n        else {\n            sign = number = 0;\n        }\n    }\n    // Throw if there is anything else\n    if (number === null || idx < formula.length) {\n        throw new Error(`n-th rule couldn't be parsed ('${formula}')`);\n    }\n    return [a, sign * number];\n    function readSign() {\n        if (formula.charAt(idx) === \"-\") {\n            idx++;\n            return -1;\n        }\n        if (formula.charAt(idx) === \"+\") {\n            idx++;\n        }\n        return 1;\n    }\n    function readNumber() {\n        const start = idx;\n        let value = 0;\n        while (idx < formula.length &&\n            formula.charCodeAt(idx) >= ZERO &&\n            formula.charCodeAt(idx) <= NINE) {\n            value = value * 10 + (formula.charCodeAt(idx) - ZERO);\n            idx++;\n        }\n        // Return `null` if we didn't read anything.\n        return idx === start ? null : value;\n    }\n    function skipWhitespace() {\n        while (idx < formula.length &&\n            whitespace.has(formula.charCodeAt(idx))) {\n            idx++;\n        }\n    }\n}\n//# sourceMappingURL=parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbnRoLWNoZWNrL2xpYi9lc20vcGFyc2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DO0FBQ3BDO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxHQUFHO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMERBQTBELFFBQVE7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Fway1taXJyb3IvLi9ub2RlX21vZHVsZXMvbnRoLWNoZWNrL2xpYi9lc20vcGFyc2UuanM/MjRjMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGb2xsb3dpbmcgaHR0cDovL3d3dy53My5vcmcvVFIvY3NzMy1zZWxlY3RvcnMvI250aC1jaGlsZC1wc2V1ZG9cbi8vIFdoaXRlc3BhY2UgYXMgcGVyIGh0dHBzOi8vd3d3LnczLm9yZy9UUi9zZWxlY3RvcnMtMy8jbGV4IGlzIFwiIFxcdFxcclxcblxcZlwiXG5jb25zdCB3aGl0ZXNwYWNlID0gbmV3IFNldChbOSwgMTAsIDEyLCAxMywgMzJdKTtcbmNvbnN0IFpFUk8gPSBcIjBcIi5jaGFyQ29kZUF0KDApO1xuY29uc3QgTklORSA9IFwiOVwiLmNoYXJDb2RlQXQoMCk7XG4vKipcbiAqIFBhcnNlcyBhbiBleHByZXNzaW9uLlxuICpcbiAqIEB0aHJvd3MgQW4gYEVycm9yYCBpZiBwYXJzaW5nIGZhaWxzLlxuICogQHJldHVybnMgQW4gYXJyYXkgY29udGFpbmluZyB0aGUgaW50ZWdlciBzdGVwIHNpemUgYW5kIHRoZSBpbnRlZ2VyIG9mZnNldCBvZiB0aGUgbnRoIHJ1bGUuXG4gKiBAZXhhbXBsZSBudGhDaGVjay5wYXJzZShcIjJuKzNcIik7IC8vIHJldHVybnMgWzIsIDNdXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZShmb3JtdWxhKSB7XG4gICAgZm9ybXVsYSA9IGZvcm11bGEudHJpbSgpLnRvTG93ZXJDYXNlKCk7XG4gICAgaWYgKGZvcm11bGEgPT09IFwiZXZlblwiKSB7XG4gICAgICAgIHJldHVybiBbMiwgMF07XG4gICAgfVxuICAgIGVsc2UgaWYgKGZvcm11bGEgPT09IFwib2RkXCIpIHtcbiAgICAgICAgcmV0dXJuIFsyLCAxXTtcbiAgICB9XG4gICAgLy8gUGFyc2UgWyBbJy0nfCcrJ10/IElOVEVHRVI/IHtOfSBbIFMqIFsnLSd8JysnXSBTKiBJTlRFR0VSIF0/XG4gICAgbGV0IGlkeCA9IDA7XG4gICAgbGV0IGEgPSAwO1xuICAgIGxldCBzaWduID0gcmVhZFNpZ24oKTtcbiAgICBsZXQgbnVtYmVyID0gcmVhZE51bWJlcigpO1xuICAgIGlmIChpZHggPCBmb3JtdWxhLmxlbmd0aCAmJiBmb3JtdWxhLmNoYXJBdChpZHgpID09PSBcIm5cIikge1xuICAgICAgICBpZHgrKztcbiAgICAgICAgYSA9IHNpZ24gKiAobnVtYmVyICE9PSBudWxsICYmIG51bWJlciAhPT0gdm9pZCAwID8gbnVtYmVyIDogMSk7XG4gICAgICAgIHNraXBXaGl0ZXNwYWNlKCk7XG4gICAgICAgIGlmIChpZHggPCBmb3JtdWxhLmxlbmd0aCkge1xuICAgICAgICAgICAgc2lnbiA9IHJlYWRTaWduKCk7XG4gICAgICAgICAgICBza2lwV2hpdGVzcGFjZSgpO1xuICAgICAgICAgICAgbnVtYmVyID0gcmVhZE51bWJlcigpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgc2lnbiA9IG51bWJlciA9IDA7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLy8gVGhyb3cgaWYgdGhlcmUgaXMgYW55dGhpbmcgZWxzZVxuICAgIGlmIChudW1iZXIgPT09IG51bGwgfHwgaWR4IDwgZm9ybXVsYS5sZW5ndGgpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBuLXRoIHJ1bGUgY291bGRuJ3QgYmUgcGFyc2VkICgnJHtmb3JtdWxhfScpYCk7XG4gICAgfVxuICAgIHJldHVybiBbYSwgc2lnbiAqIG51bWJlcl07XG4gICAgZnVuY3Rpb24gcmVhZFNpZ24oKSB7XG4gICAgICAgIGlmIChmb3JtdWxhLmNoYXJBdChpZHgpID09PSBcIi1cIikge1xuICAgICAgICAgICAgaWR4Kys7XG4gICAgICAgICAgICByZXR1cm4gLTE7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGZvcm11bGEuY2hhckF0KGlkeCkgPT09IFwiK1wiKSB7XG4gICAgICAgICAgICBpZHgrKztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gMTtcbiAgICB9XG4gICAgZnVuY3Rpb24gcmVhZE51bWJlcigpIHtcbiAgICAgICAgY29uc3Qgc3RhcnQgPSBpZHg7XG4gICAgICAgIGxldCB2YWx1ZSA9IDA7XG4gICAgICAgIHdoaWxlIChpZHggPCBmb3JtdWxhLmxlbmd0aCAmJlxuICAgICAgICAgICAgZm9ybXVsYS5jaGFyQ29kZUF0KGlkeCkgPj0gWkVSTyAmJlxuICAgICAgICAgICAgZm9ybXVsYS5jaGFyQ29kZUF0KGlkeCkgPD0gTklORSkge1xuICAgICAgICAgICAgdmFsdWUgPSB2YWx1ZSAqIDEwICsgKGZvcm11bGEuY2hhckNvZGVBdChpZHgpIC0gWkVSTyk7XG4gICAgICAgICAgICBpZHgrKztcbiAgICAgICAgfVxuICAgICAgICAvLyBSZXR1cm4gYG51bGxgIGlmIHdlIGRpZG4ndCByZWFkIGFueXRoaW5nLlxuICAgICAgICByZXR1cm4gaWR4ID09PSBzdGFydCA/IG51bGwgOiB2YWx1ZTtcbiAgICB9XG4gICAgZnVuY3Rpb24gc2tpcFdoaXRlc3BhY2UoKSB7XG4gICAgICAgIHdoaWxlIChpZHggPCBmb3JtdWxhLmxlbmd0aCAmJlxuICAgICAgICAgICAgd2hpdGVzcGFjZS5oYXMoZm9ybXVsYS5jaGFyQ29kZUF0KGlkeCkpKSB7XG4gICAgICAgICAgICBpZHgrKztcbiAgICAgICAgfVxuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhcnNlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/nth-check/lib/esm/parse.js\n");

/***/ })

};
;