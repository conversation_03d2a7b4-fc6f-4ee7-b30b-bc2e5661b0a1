"use client";
import { useRef } from 'react';
import { Container, Grid, Typo<PERSON>, <PERSON><PERSON>, Card, CardContent, Box } from '@mui/material';
import Link from 'next/link';
import { TrendingUp, Insights, Visibility, Api, Campaign, Star, ArrowForward } from '@mui/icons-material';
import RenderApps from './RenderComponents';

export default function Home() {
  const showcaseRef = useRef(null);

   const scrollToShowcase = () => {
    showcaseRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  return (
    <main>
      {/* Hero Section */}
      <Box sx={{
        background: 'linear-gradient(135deg, #0a2540 0%, #1a365d 100%)',
        color: 'white',
        py: { xs: 8, md: 12 },
        position: 'relative',
        overflow: 'hidden',
        '&:before': {
          content: '""',
          position: 'absolute',
          top: 0,
          right: 0,
          bottom: 0,
          left: 0,
          background: 'radial-gradient(circle at 80% 50%, rgba(0, 163, 255, 0.15) 0%, transparent 60%)',
        }
      }}>
        <Container>
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography
                variant="h2"
                component="h1"
                gutterBottom
                sx={{
                  fontWeight: 800,
                  fontSize: { xs: '2.5rem', md: '3.5rem' },
                  lineHeight: 1.2
                }}
              >
                Launch Your Next <Box component="span" sx={{ color: '#00A3FF' }}>App Breakthrough</Box>
              </Typography>
              <Typography
                variant="h5"
                component="p"
                gutterBottom
                sx={{
                  mb: 4,
                  fontSize: { xs: '1.1rem', md: '1.25rem' },
                  opacity: 0.9
                }}
              >
                Dive into real-time insights from over <strong>10 million apps</strong> with AppsStoreSpy. Skyrocket your Android app's growth with powerful analytics, ASO strategies, and user acquisition tools.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Link href="/login" passHref>
                  <Button
                    sx={{
                      px: 4,
                      py: 1.5,
                      background: "#00A3FF",
                      "&:hover": {
                        backgroundColor: "#0088cc",
                      },
                      textTransform: "none",
                      fontWeight: 600,
                      fontSize: '1.1rem',
                      borderRadius: '8px'
                    }}
                    size="large"
                    variant="contained"
                    endIcon={<ArrowForward />}
                  >
                    Start Exploring
                  </Button>
                </Link>
                <Button
                  variant="outlined"
                  onClick={scrollToShowcase} 
                  sx={{
                    px: 4,
                    py: 1.5,
                    borderColor: 'white',
                    color: 'white',
                    "&:hover": {
                      borderColor: '#00A3FF',
                      color: '#00A3FF',
                    },
                    textTransform: "none",
                    fontWeight: 600,
                    fontSize: '1.1rem',
                    borderRadius: '8px'
                  }}
                  size="large"
                >
                  See Demo
                </Button>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{
                borderRadius: '16px',
                overflow: 'hidden',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
                transform: 'perspective(1000px) rotateY(-5deg)',
                transition: 'transform 0.3s ease',
                '&:hover': {
                  transform: 'perspective(1000px) rotateY(0deg)'
                }
              }}>
                <img
                  src="/Adjust-App-Analytics-Dashboard-Dark.png"
                  alt="App Analytics Dashboard"
                  style={{
                    width: '100%',
                    display: 'block',
                  }}
                />
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Box sx={{ py: { xs: 8, md: 12 }, backgroundColor: '#f9fbfd' }}>
        <Container>
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography
              variant="h3"
              component="h2"
              sx={{
                fontWeight: 700,
                mb: 2,
                position: 'relative',
                display: 'inline-block',
                '&:after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -8,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '80px',
                  height: '4px',
                  background: '#00A3FF',
                  borderRadius: '2px'
                }
              }}
            >
              Supercharge Your App Strategy
            </Typography>
            <Typography
              variant="body1"
              sx={{
                maxWidth: '700px',
                mx: 'auto',
                color: 'text.secondary',
                fontSize: '1.1rem'
              }}
            >
              Powerful tools designed to give you the competitive edge in the app marketplace
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {[
              {
                title: 'Real-Time Data from 10M+ Apps',
                description: 'Access insights from over 10 million apps to discover new niches and track fast-growing industries.',
                icon: <TrendingUp sx={{ fontSize: 40, color: '#00A3FF' }} />,
              },
              {
                title: 'Boost App Growth',
                description: 'Promote your Android app with cost-per-install pricing, non-incentivized traffic, and high retention rates.',
                icon: <Insights sx={{ fontSize: 40, color: '#00A3FF' }} />,
              },
              {
                title: 'Dive into ASO',
                description: 'Enhance visibility with in-depth mobile app audits and data-driven app store optimization.',
                icon: <Visibility sx={{ fontSize: 40, color: '#00A3FF' }} />,
              },
              {
                title: 'Powerful API Integration',
                description: 'Stream AppsStoreSpy data with your tech stack for automated, customized analytics.',
                icon: <Api sx={{ fontSize: 40, color: '#00A3FF' }} />,
              },
              {
                title: 'User Acquisition Campaigns',
                description: 'Create targeted campaigns in minutes with real-time reporting and CPI boost rules.',
                icon: <Campaign sx={{ fontSize: 40, color: '#00A3FF' }} />,
              },
              {
                title: 'Track Top Apps & Trends',
                description: 'Monitor selected apps, rankings, reviews, and market trends to stay ahead of the competition.',
                icon: <Star sx={{ fontSize: 40, color: '#00A3FF' }} />,
              },
            ].map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card sx={{
                  height: '100%',
                  border: '1px solid rgba(0, 0, 0, 0.05)',
                  boxShadow: '0 8px 24px rgba(0, 0, 0, 0.05)',
                  transition: 'transform 0.3s, box-shadow 0.3s',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: '0 12px 28px rgba(0, 0, 0, 0.1)'
                  }
                }}>
                  <CardContent sx={{
                    p: 4,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    textAlign: 'center'
                  }}>
                    <Box sx={{
                      width: 80,
                      height: 80,
                      borderRadius: '50%',
                      backgroundColor: 'rgba(0, 163, 255, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 3
                    }}>
                      {feature.icon}
                    </Box>
                    <Typography variant="h6" component="h3" gutterBottom sx={{ fontWeight: 600 }}>
                      {feature.title}
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                      {feature.description}
                    </Typography>
                    {/* <Button
                      size="small"
                      color="primary"
                      endIcon={<ArrowForward />}
                      sx={{ mt: 'auto', fontWeight: 600 }}
                    >
                      Learn more
                    </Button> */}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Visual Insights Section */}
      <Box sx={{ py: { xs: 8, md: 12 } }}>
        <Container>
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography
              variant="h3"
              component="h2"
              sx={{
                fontWeight: 700,
                mb: 2
              }}
            >
              Actionable Insights at a Glance
            </Typography>
            <Typography
              variant="body1"
              sx={{
                maxWidth: '700px',
                mx: 'auto',
                color: 'text.secondary',
                fontSize: '1.1rem'
              }}
            >
              Visualize your app's performance and stay ahead of your competition with powerful visuals.
            </Typography>
          </Box>

          {[
            {
              title: "Daily App Install Trends",
              description: "Track how installs evolve daily with beautifully rendered graphs. Spot surges, drops, and emerging trends instantly.",
              image: "/app-installs-graph.png",
              reverse: false,
            },
            {
              title: "Top Ranked Apps/Games Country-wise",
              description: "Get a breakdown of the most popular games across different countries and regions. Understand what's trending where.",
              image: "/top-games-country.png",
              reverse: true,
            },
            {
              title: "Live App Rankings by Country",
              description: "Monitor live rankings of apps in every category. View rankings shift over time to inform your market strategy.",
              image: "/app-ranking-country.png",
              reverse: false,
            },
          ].map(({ title, description, image, reverse }, index) => (
            <Grid
              container
              spacing={6}
              alignItems="center"
              justifyContent="center"
              direction={reverse ? "row-reverse" : "row"}
              sx={{ my: 8 }}
              key={index}
            >
              <Grid item xs={12} md={6}>
                <Box sx={{
                  borderRadius: '16px',
                  overflow: 'hidden',
                  boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
                  border: '1px solid rgba(0, 0, 0, 0.05)'
                }}>
                  <img
                    src={image}
                    alt={title}
                    style={{
                      width: '100%',
                      display: 'block'
                    }}
                  />
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography
                  variant="h4"
                  component="h3"
                  gutterBottom
                  sx={{
                    fontWeight: 700,
                    mb: 3
                  }}
                >
                  {title}
                </Typography>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{
                    mb: 3,
                    fontSize: '1.1rem'
                  }}
                >
                  {description}
                </Typography>
                {/* <Button
                  variant="outlined"
                  color="primary"
                  endIcon={<ArrowForward />}
                  sx={{ fontWeight: 600 }}
                >
                  See how it works
                </Button> */}
              </Grid>
            </Grid>
          ))}
        </Container>
      </Box>

      {/* App Showcase Section */}
      <Box
       ref={showcaseRef}
      sx={{ 
       px: { xs: 2, sm: 4 }, py: 4,
        background: 'linear-gradient(to bottom, #f9fbfd 0%, #ffffff 100%)'
      }}>
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography 
              variant="h3" 
              component="h2" 
              sx={{ 
                fontWeight: 700,
                mb: 2
              }}
            >
              Explore Top Apps & Insights
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                maxWidth: '700px',
                mx: 'auto',
                color: 'text.secondary',
                fontSize: '1.1rem'
              }}
            >
              Discover trending apps, rankings, reviews, and ratings with real-time data.
            </Typography>
          </Box>
          <RenderApps category="APPLICATION" type="Apps" />
      </Box>

      {/* CTA Section */}
      <Box sx={{
        py: { xs: 6, md: 8 },
        background: 'linear-gradient(135deg, #00A3FF 0%, #0066CC 100%)',
        color: 'white',
        textAlign: 'center',
        position: 'relative',
        overflow: 'hidden',
        '&:before': {
          content: '""',
          position: 'absolute',
          top: 0,
          right: 0,
          bottom: 0,
          left: 0,
          background: 'radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 60%)',
        }
      }}>
        <Container>
          <Typography
            variant="h3"
            component="h2"
            gutterBottom
            sx={{
              fontWeight: 800,
              mb: 3,
              position: 'relative',
              zIndex: 1
            }}
          >
            Unleash the Power of AppsStoreSpy
          </Typography>
          <Typography
            variant="h5"
            component="p"
            gutterBottom
            sx={{
              mb: 5,
              opacity: 0.9,
              position: 'relative',
              zIndex: 1
            }}
          >
            Start exploring real-time data, optimizing your app, and driving growth today.
          </Typography>
          <Box sx={{ position: 'relative', zIndex: 1 }}>
            <Link href="/register" passHref>
              <Button
                variant="contained"
                size="large"
                sx={{
                  px: 6,
                  py: 1.5,
                  background: 'white',
                  color: '#00A3FF',
                  fontWeight: 700,
                  fontSize: '1.1rem',
                  borderRadius: '8px',
                  '&:hover': {
                    background: 'rgba(255, 255, 255, 0.9)'
                  }
                }}
                endIcon={<ArrowForward />}
              >
                Get Started Now
              </Button>
            </Link>
            {/* <Typography
              variant="body2"
              sx={{
                mt: 3,
                opacity: 0.8
              }}
            >
              No credit card required • 14-day free trial
            </Typography> */}
          </Box>
        </Container>
      </Box>
    </main>
  );
}