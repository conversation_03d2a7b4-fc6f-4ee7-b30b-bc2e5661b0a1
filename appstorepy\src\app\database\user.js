
"use strict";
import mongoose, { Schema, models } from "mongoose";

const userSchema = new Schema(
  {
    email: {
      type: String,
      required: true,
      unique: true,
    },
    password: {
      type: String,
      required: true,
    },
    resetToken: {
      type: String,
      required: false,
    },
    resetTokenExpiry: {
      type: Date,
      required: false,
    },
    role: { type: String, enum: ['free', 'paid', 'admin'], default: 'free' }
  },
  { timestamps: true }
);

const User = models?.User || mongoose.model("User", userSchema);
export default User;
