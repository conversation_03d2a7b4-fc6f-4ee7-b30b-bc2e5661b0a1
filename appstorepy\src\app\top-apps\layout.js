import { AppRouterCacheProvider } from "@mui/material-nextjs/v13-appRouter";
import BootstrapClient from "../components/BootstrapClient";
import "bootstrap/dist/css/bootstrap.min.css";
import "../globals.css";
import Navbar from "../components/navbar/navbar.jsx";
import Layout from "../components/Layout.jsx";

export const metadata = {
  title: "Top Apps By Google Play Store",
  description: "App analytics",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
      </head>
      <Layout>
        <body style={{ margin: 0, padding: 0 }}>
          <Navbar />
          <AppRouterCacheProvider>
            <main style={{ 
              marginTop: '64px',
              minHeight: 'calc(100vh - 64px)'
            }}>
              {children}
            </main>
            <BootstrapClient />
          </AppRouterCacheProvider>
        </body>
      </Layout>
    </html>
  );
}