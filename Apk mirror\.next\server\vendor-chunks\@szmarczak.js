"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@szmarczak";
exports.ids = ["vendor-chunks/@szmarczak"];
exports.modules = {

/***/ "(rsc)/./node_modules/@szmarczak/http-timer/dist/source/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@szmarczak/http-timer/dist/source/index.js ***!
  \*****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst defer_to_connect_1 = __webpack_require__(/*! defer-to-connect */ \"(rsc)/./node_modules/defer-to-connect/dist/source/index.js\");\nconst util_1 = __webpack_require__(/*! util */ \"util\");\nconst nodejsMajorVersion = Number(process.versions.node.split('.')[0]);\nconst timer = (request) => {\n    if (request.timings) {\n        return request.timings;\n    }\n    const timings = {\n        start: Date.now(),\n        socket: undefined,\n        lookup: undefined,\n        connect: undefined,\n        secureConnect: undefined,\n        upload: undefined,\n        response: undefined,\n        end: undefined,\n        error: undefined,\n        abort: undefined,\n        phases: {\n            wait: undefined,\n            dns: undefined,\n            tcp: undefined,\n            tls: undefined,\n            request: undefined,\n            firstByte: undefined,\n            download: undefined,\n            total: undefined\n        }\n    };\n    request.timings = timings;\n    const handleError = (origin) => {\n        const emit = origin.emit.bind(origin);\n        origin.emit = (event, ...args) => {\n            // Catches the `error` event\n            if (event === 'error') {\n                timings.error = Date.now();\n                timings.phases.total = timings.error - timings.start;\n                origin.emit = emit;\n            }\n            // Saves the original behavior\n            return emit(event, ...args);\n        };\n    };\n    handleError(request);\n    const onAbort = () => {\n        timings.abort = Date.now();\n        // Let the `end` response event be responsible for setting the total phase,\n        // unless the Node.js major version is >= 13.\n        if (!timings.response || nodejsMajorVersion >= 13) {\n            timings.phases.total = Date.now() - timings.start;\n        }\n    };\n    request.prependOnceListener('abort', onAbort);\n    const onSocket = (socket) => {\n        timings.socket = Date.now();\n        timings.phases.wait = timings.socket - timings.start;\n        if (util_1.types.isProxy(socket)) {\n            return;\n        }\n        const lookupListener = () => {\n            timings.lookup = Date.now();\n            timings.phases.dns = timings.lookup - timings.socket;\n        };\n        socket.prependOnceListener('lookup', lookupListener);\n        defer_to_connect_1.default(socket, {\n            connect: () => {\n                timings.connect = Date.now();\n                if (timings.lookup === undefined) {\n                    socket.removeListener('lookup', lookupListener);\n                    timings.lookup = timings.connect;\n                    timings.phases.dns = timings.lookup - timings.socket;\n                }\n                timings.phases.tcp = timings.connect - timings.lookup;\n                // This callback is called before flushing any data,\n                // so we don't need to set `timings.phases.request` here.\n            },\n            secureConnect: () => {\n                timings.secureConnect = Date.now();\n                timings.phases.tls = timings.secureConnect - timings.connect;\n            }\n        });\n    };\n    if (request.socket) {\n        onSocket(request.socket);\n    }\n    else {\n        request.prependOnceListener('socket', onSocket);\n    }\n    const onUpload = () => {\n        var _a;\n        timings.upload = Date.now();\n        timings.phases.request = timings.upload - ((_a = timings.secureConnect) !== null && _a !== void 0 ? _a : timings.connect);\n    };\n    const writableFinished = () => {\n        if (typeof request.writableFinished === 'boolean') {\n            return request.writableFinished;\n        }\n        // Node.js doesn't have `request.writableFinished` property\n        return request.finished && request.outputSize === 0 && (!request.socket || request.socket.writableLength === 0);\n    };\n    if (writableFinished()) {\n        onUpload();\n    }\n    else {\n        request.prependOnceListener('finish', onUpload);\n    }\n    request.prependOnceListener('response', (response) => {\n        timings.response = Date.now();\n        timings.phases.firstByte = timings.response - timings.upload;\n        response.timings = timings;\n        handleError(response);\n        response.prependOnceListener('end', () => {\n            timings.end = Date.now();\n            timings.phases.download = timings.end - timings.response;\n            timings.phases.total = timings.end - timings.start;\n        });\n        response.prependOnceListener('aborted', onAbort);\n    });\n    return timings;\n};\nexports[\"default\"] = timer;\n// For CommonJS default export support\nmodule.exports = timer;\nmodule.exports[\"default\"] = timer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@szmarczak/http-timer/dist/source/index.js\n");

/***/ })

};
;