/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/apps/[category]/page";
exports.ids = ["app/apps/[category]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapps%2F%5Bcategory%5D%2Fpage&page=%2Fapps%2F%5Bcategory%5D%2Fpage&appPaths=%2Fapps%2F%5Bcategory%5D%2Fpage&pagePath=private-next-app-dir%2Fapps%2F%5Bcategory%5D%2Fpage.jsx&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapps%2F%5Bcategory%5D%2Fpage&page=%2Fapps%2F%5Bcategory%5D%2Fpage&appPaths=%2Fapps%2F%5Bcategory%5D%2Fpage&pagePath=private-next-app-dir%2Fapps%2F%5Bcategory%5D%2Fpage.jsx&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'apps',\n        {\n        children: [\n        '[category]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/apps/[category]/page.jsx */ \"(rsc)/./src/app/apps/[category]/page.jsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\[category]\\\\page.jsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\[category]\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/apps/[category]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/apps/[category]/page\",\n        pathname: \"/apps/[category]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapps%2F%5Bcategory%5D%2Fpage&page=%2Fapps%2F%5Bcategory%5D%2Fpage&appPaths=%2Fapps%2F%5Bcategory%5D%2Fpage&pagePath=private-next-app-dir%2Fapps%2F%5Bcategory%5D%2Fpage.jsx&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CComponents%5C%5Cnavbar%5C%5Cnavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CReduxLayout%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CComponents%5C%5Cnavbar%5C%5Cnavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CReduxLayout%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/Components/navbar/navbar.jsx */ \"(ssr)/./src/app/Components/navbar/navbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/ReduxLayout/layout.js */ \"(ssr)/./src/app/ReduxLayout/layout.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CComponents%5C%5Cnavbar%5C%5Cnavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CReduxLayout%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Ccategory.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Ccategory.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/category.jsx */ \"(ssr)/./src/app/category.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0JodXNoYW4lMjBwYXRpbCU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q0FuY2hvcmluZyU1QyU1Q0FwayUyMG1pcnJvciU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2NhdGVnb3J5LmpzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdKQUE0SiIsInNvdXJjZXMiOlsid2VicGFjazovL2Fway1taXJyb3IvPzE3YTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcQmh1c2hhbiBwYXRpbFxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXEFuY2hvcmluZ1xcXFxBcGsgbWlycm9yXFxcXHNyY1xcXFxhcHBcXFxcY2F0ZWdvcnkuanN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Ccategory.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/Ads.jsx":
/*!*************************!*\
  !*** ./src/app/Ads.jsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Ads = ({ slot = \"\", className })=>{\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [insSize, setInsSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const insRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setUrl(window.location.hostname);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAd = ()=>{\n            try {\n                const adsGoogle = window.adsbygoogle || [];\n                adsGoogle.push({});\n            } catch (err) {}\n        };\n        const adScript = document.createElement(\"script\");\n        adScript.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8916641928046583`;\n        adScript.async = true;\n        adScript.onload = initializeAd;\n        document.body.appendChild(adScript);\n        const checkInsSize = setTimeout(()=>{\n            if (insRef.current) {\n                const { offsetWidth, offsetHeight } = insRef.current;\n                setInsSize({\n                    width: offsetWidth,\n                    height: offsetHeight\n                });\n            }\n        }, 1500); // Delay by 1.5 seconds to allow ad to load\n        return ()=>clearTimeout(checkInsSize);\n    }, [\n        slot\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n            className: \"adsbygoogle\",\n            style: {\n                \"display\": \"block\"\n            },\n            \"data-ad-client\": \"ca-pub-8916641928046583\",\n            \"data-ad-slot\": \"8457249210\",\n            \"data-ad-format\": \"auto\",\n            \"data-full-width-responsive\": \"true\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Ads.jsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Ads.jsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Ads);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Ads.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/Components/navbar/navbar.jsx":
/*!**********************************************!*\
  !*** ./src/app/Components/navbar/navbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Navbar = ()=>{\n    const pathName = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [searchTerm, setSearchTerm] = useState(\"\");\n    const [showSearch, setShowSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSearch(!pathName.includes(\"/app-search\"));\n    }, [\n        pathName\n    ]);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    // const onHandleChange = (e) => {\n    //   setSearchTerm(e.target.value);\n    // };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 left-0 right-0 z-10 bg-white shadow\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"bg-white border-gray-200 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/\",\n                        className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"self-center text-2xl font-semibold whitespace-nowrap dark:text-white\",\n                            children: \"APKExplorer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex md:order-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            \"aria-controls\": \"navbar-search\",\n                            \"aria-expanded\": isMenuOpen,\n                            className: \"grid place-items-center md:hidden text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5\",\n                            onClick: toggleMenu,\n                            children: [\n                                isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    \"aria-hidden\": \"true\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        stroke: \"currentColor\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    \"aria-hidden\": \"true\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        stroke: \"currentColor\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M3 6h14M3 10h14m-7 4h7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Toggle navigation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-full md:flex ml-auto md:w-auto md:order-1 ${isMenuOpen ? \"block\" : \"hidden\"}`,\n                        id: \"navbar-search\",\n                        onClick: toggleMenu,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"flex flex-col p-4 md:p-0 mt-4 font-medium border text-transform: uppercase border-gray-100 rounded-lg bg-gray-50 md:space-x-6 lg:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-white dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700\n                    ${pathName === \"/\" && \"md:text-blue-700 decoration-blue-700\"}\n                 `,\n                                        \"aria-current\": \"page\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/apps\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700\n                     ${pathName.includes(\"/apps\") && \"md:text-blue-700 focus:ring-violet-300 decoration-blue-700\"}\n                  `,\n                                        children: \"Android Apps\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/games\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 ${pathName.includes(\"/games\") && \"md:text-blue-700 decoration-blue-700\"}`,\n                                        children: \"Android Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/app-search\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 ${pathName.includes(\"/app-search\") && \"md:text-blue-700 decoration-blue-700\"}`,\n                                        children: \"Search Apps & Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Components/navbar/navbar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/Loading.jsx":
/*!*****************************!*\
  !*** ./src/app/Loading.jsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst LoadingComponent = ({ length, md = 2, lg })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `grid grid-cols-1 md:grid-cols-${md} lg:grid-cols-${lg} gap-4 p-4`,\n        children: Array.from({\n            length: length\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse hover:bg-gray-100 p-1 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md bg-slate-200 h-20 w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                            lineNumber: 10,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2.5 bg-gray-200 rounded-full  w-48 my-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2 bg-gray-200 rounded-full  w-48 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2 bg-gray-200 rounded-full  w-48 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2.5 bg-gray-200 rounded-full  w-32 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                            lineNumber: 11,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, undefined)\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingComponent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Loading.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/ReduxLayout/layout.js":
/*!***************************************!*\
  !*** ./src/app/ReduxLayout/layout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReduxProvider: () => (/* binding */ ReduxProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _redux_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../redux/store */ \"(ssr)/./src/app/redux/store.js\");\n/* __next_internal_client_entry_do_not_use__ ReduxProvider auto */ \n\n\nfunction ReduxProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_2__.Provider, {\n        store: _redux_store__WEBPACK_IMPORTED_MODULE_1__.store,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\layout.js\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1JlZHV4TGF5b3V0L2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDdUM7QUFDQTtBQUVoQyxTQUFTRSxjQUFjLEVBQUVDLFFBQVEsRUFBRTtJQUN4QyxxQkFBTyw4REFBQ0gsaURBQVFBO1FBQUNDLE9BQU9BLCtDQUFLQTtrQkFBR0U7Ozs7OztBQUNsQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Fway1taXJyb3IvLi9zcmMvYXBwL1JlZHV4TGF5b3V0L2xheW91dC5qcz8xYzE1Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHsgUHJvdmlkZXIgfSBmcm9tIFwicmVhY3QtcmVkdXhcIjtcbmltcG9ydCB7IHN0b3JlIH0gZnJvbSBcIi4uL3JlZHV4L3N0b3JlXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBSZWR1eFByb3ZpZGVyKHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gPFByb3ZpZGVyIHN0b3JlPXtzdG9yZX0+e2NoaWxkcmVufTwvUHJvdmlkZXI+O1xufVxuIl0sIm5hbWVzIjpbIlByb3ZpZGVyIiwic3RvcmUiLCJSZWR1eFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/ReduxLayout/layout.js\n");

/***/ }),

/***/ "(ssr)/./src/app/SideBar.jsx":
/*!*****************************!*\
  !*** ./src/app/SideBar.jsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Loading__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Loading */ \"(ssr)/./src/app/Loading.jsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst SideBar = ({ sideappDetails, header = \"RECENTLY UPDATED APPS\", isLoading })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-3.5 p-5 bg-white rounded-md shadow-md \",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"mb-2.5 font-normal text-slate-500 tracking-wider uppercase\",\n                children: header\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loading__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                length: 5,\n                md: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid sm:grid-cols-2 lg:grid-cols-1\",\n                children: sideappDetails?.map((appDetails)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: `/apps/appdetails/${appDetails.appId}`,\n                        target: \"_blank\",\n                        prefetch: false,\n                        className: \"mt-2.5 p-1.5 hover:bg-gray-100 p-1 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" grid grid-cols-4 lg:grid-cols-3 xl:grid-cols-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    width: 75,\n                                    height: 75,\n                                    className: \"rounded-2xl\",\n                                    src: appDetails.icon,\n                                    alt: `${appDetails.title} Icon`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 17\n                                }, undefined),\n                                header === \"RECENTLY UPDATED APPS\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-2 col-span-2 \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium truncate\",\n                                            title: appDetails.title,\n                                            children: appDetails.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-400 tracking-wider\",\n                                            children: [\n                                                \"VERSION \",\n                                                appDetails.latestVersion?.split(\" \")[0]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-400 uppercase tracking-wider\",\n                                            children: appDetails.updated\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-yellow-400 me-1\",\n                                                    \"aria-hidden\": \"true\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 22 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-slate-400 dark:text-slate-400\",\n                                                    children: appDetails.scoreText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-2 col-span-2 \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium truncate\",\n                                            title: appDetails.title,\n                                            children: appDetails.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-400 tracking-wider uppercase\",\n                                            children: appDetails.developer\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-yellow-400 me-1\",\n                                                    \"aria-hidden\": \"true\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 22 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-slate-400 dark:text-slate-400\",\n                                                    children: appDetails.scoreText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                            lineNumber: 28,\n                            columnNumber: 15\n                        }, undefined)\n                    }, appDetails.appId, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                        lineNumber: 21,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SideBar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/SideBar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/category.jsx":
/*!******************************!*\
  !*** ./src/app/category.jsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _SideBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SideBar */ \"(ssr)/./src/app/SideBar.jsx\");\n/* harmony import */ var _Loading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Loading */ \"(ssr)/./src/app/Loading.jsx\");\n/* harmony import */ var _Ads__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Ads */ \"(ssr)/./src/app/Ads.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst Category = ({ name, category })=>{\n    const [categoryApps, setCategoryApps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentlyUpdatedApps, setRecentlyUpdatedApps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const isAdsServe = JSON.parse(\"true\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getData = async ()=>{\n            try {\n                setIsLoading(true);\n                const parseCategory = encodeURIComponent(category);\n                const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(`/api/get_by_category?category=${parseCategory}`);\n                if (response && response.status === 200) {\n                    setCategoryApps(response.data.apps);\n                    setRecentlyUpdatedApps(response.data.recentlyUpdatedApps);\n                } else {\n                    setCategoryApps([]);\n                }\n            } catch (errors) {\n                console.error(errors);\n                setError(\"An error occurred while fetching data.\");\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        getData();\n    }, [\n        category\n    ]);\n    return(// <div className=\"p-20 mt-5 flex\">\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"lg:container flex flex-col items-center justify-between mt-0.5 mx-5 sm:mx-0 md:mx-20 lg:mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto max-w-screen-xl\",\n                children: [\n                    isAdsServe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Ads__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        slot: 1,\n                        className: \"mb-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                        lineNumber: 45,\n                        columnNumber: 26\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full justify-center flex flex-wrap\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"w-full xl:w-4/6 mt-4 relative px-0 xl:px-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-3.5 p-3 bg-white rounded-md shadow-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[10px] sm:text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"\\xa0/\\xa0\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: `${name}`,\n                                                    children: [\n                                                        \"Android \",\n                                                        name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"\\xa0/\\xa0\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-slate-500\",\n                                                    children: category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-3.5 p-5 bg-white rounded-lg shadow-md flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"mb-2.5 text-base font-normal text-slate-500 uppercase tracking-wider\",\n                                                children: [\n                                                    category,\n                                                    \" ANDROID \",\n                                                    name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loading__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                length: 6\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 30\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: categoryApps && categoryApps.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                                    children: categoryApps.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"hover:bg-gray-100 p-1 rounded-md\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                href: `/${name}/appdetails/${app.appId}`,\n                                                                target: \"_blank\",\n                                                                prefetch: false,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex truncate\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            className: \"rounded-2xl\",\n                                                                            src: app.icon,\n                                                                            alt: `${app.title} Icon`,\n                                                                            width: 96,\n                                                                            height: 96\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                                            lineNumber: 82,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"text-md font-medium\",\n                                                                                    children: app.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                                                    lineNumber: 90,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-slate-400 tracking-wider\",\n                                                                                    children: [\n                                                                                        \"DEVELOPER \",\n                                                                                        app.developer\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                                                    lineNumber: 91,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-slate-400 tracking-wider\",\n                                                                                    children: [\n                                                                                        \"VERSION \",\n                                                                                        app.latestVersion\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                                                    lineNumber: 94,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-slate-400 uppercase tracking-wider\",\n                                                                                    children: app.updated\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                                                    lineNumber: 97,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4 text-yellow-400 me-1\",\n                                                                                            \"aria-hidden\": \"true\",\n                                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                            fill: \"currentColor\",\n                                                                                            viewBox: \"0 0 22 20\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                d: \"M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                                                                lineNumber: 108,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                                                            lineNumber: 101,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm font-bold text-slate-400 dark:text-slate-400\",\n                                                                                            children: app.scoreText\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                                                            lineNumber: 110,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                                                    lineNumber: 100,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                                            lineNumber: 89,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                                    lineNumber: 81,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                                lineNumber: 77,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, app.appId, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                            lineNumber: 76,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center text-lg font-bold text-slate-400 dark:text-white\",\n                                                    children: [\n                                                        \"No data found for this category: \",\n                                                        category\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 64\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isAdsServe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Ads__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        slot: 2,\n                                        className: \"mt-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 30\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                                className: \" sidebar-container w-full xl:w-2/6 xl:px-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideBar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        sideappDetails: recentlyUpdatedApps,\n                                        isLoading: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isAdsServe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Ads__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        slot: 3,\n                                        className: \"\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 30\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\category.jsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Category);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/category.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/redux/features/appSlice.js":
/*!********************************************!*\
  !*** ./src/app/redux/features/appSlice.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAppDetails: () => (/* binding */ getAppDetails)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n\nconst getAppDetails = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)(\"app/getAppDetails\", async (appId, { rejectWithValue })=>{\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(`/api/app_by_name_id?appId=${appId}`);\n        if (response && response.status === 200) {\n            return response.data;\n        }\n    } catch (error) {\n        return rejectWithValue(\"App Version not available\");\n    }\n});\nconst initialState = {\n    loading: false,\n    appDetail: [],\n    error: null\n};\nconst appSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: \"app\",\n    initialState,\n    reducers: {},\n    extraReducers: (builder)=>{\n        builder.addCase(getAppDetails.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(getAppDetails.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.appDetail = action.payload;\n            state.error = null;\n        }).addCase(getAppDetails.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (appSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/redux/features/appSlice.js\n");

/***/ }),

/***/ "(ssr)/./src/app/redux/store.js":
/*!********************************!*\
  !*** ./src/app/redux/store.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: () => (/* binding */ store)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var _features_appSlice__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./features/appSlice */ \"(ssr)/./src/app/redux/features/appSlice.js\");\n\n\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.configureStore)({\n    reducer: {\n        app: _features_appSlice__WEBPACK_IMPORTED_MODULE_0__[\"default\"]\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3JlZHV4L3N0b3JlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpRDtBQUNQO0FBRW5DLE1BQU1FLFFBQVFGLGdFQUFjQSxDQUFDO0lBQ2xDRyxTQUFTO1FBQ1BDLEtBQU1ILDBEQUFRQTtJQUNoQjtBQUNGLEdBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vc3JjL2FwcC9yZWR1eC9zdG9yZS5qcz9jZjA2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbmZpZ3VyZVN0b3JlIH0gZnJvbSAnQHJlZHV4anMvdG9vbGtpdCdcclxuaW1wb3J0IGFwcFNsaWNlIGZyb20gJy4vZmVhdHVyZXMvYXBwU2xpY2UnXHJcblxyXG5leHBvcnQgY29uc3Qgc3RvcmUgPSBjb25maWd1cmVTdG9yZSh7XHJcbiAgcmVkdWNlcjoge1xyXG4gICAgYXBwIDogYXBwU2xpY2VcclxuICB9LFxyXG59KSJdLCJuYW1lcyI6WyJjb25maWd1cmVTdG9yZSIsImFwcFNsaWNlIiwic3RvcmUiLCJyZWR1Y2VyIiwiYXBwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/redux/store.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8ebd5d55d030\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZTk5YSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhlYmQ1ZDU1ZDAzMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/Components/navbar/navbar.jsx":
/*!**********************************************!*\
  !*** ./src/app/Components/navbar/navbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring\Apk mirror\src\app\Components\navbar\navbar.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/Constant/staticData.js":
/*!****************************************!*\
  !*** ./src/app/Constant/staticData.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   androidApps: () => (/* binding */ androidApps),\n/* harmony export */   androidGames: () => (/* binding */ androidGames)\n/* harmony export */ });\nconst androidApps = [\n    {\n        icon: \"fa-solid fa-paintbrush\",\n        name: \"Art & Design\",\n        category: \"art-design\"\n    },\n    {\n        icon: \"fa-solid fa-car\",\n        name: \"Auto & Vehicles\",\n        category: \"auto-vehicles\"\n    },\n    {\n        icon: \"fa-solid fa-person-dress\",\n        name: \"Beauty\",\n        category: \"beauty\"\n    },\n    {\n        icon: \"fa-solid fa-book\",\n        name: \"Books & Reference\",\n        category: \"books-reference\"\n    },\n    {\n        icon: \"fa-solid fa-briefcase\",\n        name: \"Business\",\n        category: \"business\"\n    },\n    {\n        icon: \"fa-solid fa-paintbrush\",\n        name: \"Comics\",\n        category: \"comics\"\n    },\n    {\n        icon: \"fa-regular fa-comments\",\n        name: \"Communication\",\n        category: \"communication\"\n    },\n    {\n        icon: \"fa-solid fa-heart\",\n        name: \"Dating\",\n        category: \"dating\"\n    },\n    {\n        icon: \"fa-solid fa-graduation-cap\",\n        name: \"Education\",\n        category: \"education\"\n    },\n    {\n        icon: \"fa-solid fa-video\",\n        name: \"Entertainment\",\n        category: \"entertainment\"\n    },\n    {\n        icon: \"fa-regular fa-money-bill-1\",\n        name: \"Finance\",\n        category: \"finance\"\n    },\n    {\n        icon: \"fa-solid fa-utensils\",\n        name: \"Food & Drink\",\n        category: \"food-drink\"\n    },\n    {\n        icon: \"fa-solid fa-heart-pulse\",\n        name: \"Health & Fitness\",\n        category: \"health-fitness\"\n    },\n    {\n        icon: \"fa-solid fa-house-chimney\",\n        name: \"House & Home\",\n        category: \"house-home\"\n    },\n    {\n        icon: \"fa-regular fa-file-code\",\n        name: \"Libraries & Demo\",\n        category: \"libraries-demo\"\n    },\n    {\n        icon: \"fa-solid fa-person\",\n        name: \"Lifestyle\",\n        category: \"lifestyle\"\n    },\n    {\n        icon: \"fa-solid fa-suitcase-medical\",\n        name: \"Medical\",\n        category: \"medical\"\n    },\n    {\n        icon: \"fa-solid fa-headphones-simple\",\n        name: \"Music & Audio\",\n        category: \"music-audio\"\n    },\n    {\n        icon: \"fa-regular fa-newspaper\",\n        name: \"News & Magazines\",\n        category: \"news-magazines\"\n    },\n    {\n        icon: \"fa-solid fa-user\",\n        name: \"Personalization\",\n        category: \"personalization\"\n    },\n    {\n        icon: \"fa-solid fa-camera-retro\",\n        name: \"Photography\",\n        category: \"photography\"\n    },\n    {\n        icon: \"fa-solid fa-gears\",\n        name: \"Productivity\",\n        category: \"productivity\"\n    },\n    {\n        icon: \"fa-solid fa-cart-shopping\",\n        name: \"Shopping\",\n        category: \"shopping\"\n    },\n    {\n        icon: \"fa-solid fa-earth-americas\",\n        name: \"Social\",\n        category: \"social\"\n    },\n    {\n        icon: \"fa-regular fa-futbol\",\n        name: \"Sports\",\n        category: \"sports\"\n    },\n    {\n        icon: \"fa-solid fa-wrench\",\n        name: \"Tools\",\n        category: \"tools\"\n    },\n    {\n        icon: \"fa-solid fa-signs-post\",\n        name: \"Travel & Local\",\n        category: \"travel-local\"\n    },\n    {\n        icon: \"fa-regular fa-sun\",\n        name: \"Weather\",\n        category: \"weather\"\n    }\n];\nconst androidGames = [\n    {\n        icon: \"fa-solid fa-gamepad\",\n        name: \"Action\",\n        category: \"action\"\n    },\n    {\n        icon: \"fa-solid fa-chess-knight\",\n        name: \"Board\",\n        category: \"board\"\n    },\n    {\n        icon: \"fa-solid fa-mug-hot\",\n        name: \"Casual\",\n        category: \"casual\"\n    },\n    {\n        icon: \"fa-solid fa-puzzle-piece\",\n        name: \"Puzzle\",\n        category: \"puzzle\"\n    },\n    {\n        icon: \"fa-solid fa-clipboard-question\",\n        name: \"Simulation\",\n        category: \"simulation\"\n    },\n    {\n        icon: \"fa-regular fa-circle-question\",\n        name: \"Trivia\",\n        category: \"trivia\"\n    },\n    {\n        icon: \"fa-brands fa-space-awesome\",\n        name: \"Adventure\",\n        category: \"adventure\"\n    },\n    {\n        icon: \"fa-regular fa-heart\",\n        name: \"Card\",\n        category: \"card\"\n    },\n    {\n        icon: \"fa-solid fa-car\",\n        name: \"Racing\",\n        category: \"racing\"\n    },\n    {\n        icon: \"fa-regular fa-futbol\",\n        name: \"Sports\",\n        category: \"sports\"\n    },\n    {\n        icon: \"fa-solid fa-pen-to-square\",\n        name: \"Word\",\n        category: \"word\"\n    },\n    {\n        icon: \"fa-solid fa-trophy\",\n        name: \"Arcade\",\n        category: \"arcade\"\n    },\n    {\n        icon: \"fa-regular fa-square\",\n        name: \"Casino\",\n        category: \"casino\"\n    },\n    {\n        icon: \"fa-solid fa-music\",\n        name: \"Music\",\n        category: \"music\"\n    },\n    {\n        icon: \"fa-solid fa-users\",\n        name: \"Role Playing\",\n        category: \"role-playing\"\n    },\n    {\n        icon: \"fa-regular fa-lightbulb\",\n        name: \"Strategy\",\n        category: \"strategy\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/Constant/staticData.js\n");

/***/ }),

/***/ "(rsc)/./src/app/ReduxLayout/layout.js":
/*!***************************************!*\
  !*** ./src/app/ReduxLayout/layout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReduxProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring\Apk mirror\src\app\ReduxLayout\layout.js#ReduxProvider`);


/***/ }),

/***/ "(rsc)/./src/app/apps/[category]/page.jsx":
/*!******************************************!*\
  !*** ./src/app/apps/[category]/page.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_category__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/category */ \"(rsc)/./src/app/category.jsx\");\n/* harmony import */ var _Constant_staticData__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Constant/staticData */ \"(rsc)/./src/app/Constant/staticData.js\");\n\n\n\n\nconst getNameFromCategory = (category)=>{\n    const foundCategory = _Constant_staticData__WEBPACK_IMPORTED_MODULE_3__.androidApps.find((item)=>item.category === category);\n    return foundCategory ? foundCategory.name : null;\n};\nconst Page = ({ params: { category } })=>{\n    const categoryName = getNameFromCategory(category);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_category__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        name: \"apps\",\n        category: categoryName\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\[category]\\\\page.jsx\",\n        lineNumber: 13,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Page);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwcHMvW2NhdGVnb3J5XS9wYWdlLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwQjtBQUNZO0FBQ2tCO0FBRXhELE1BQU1HLHNCQUFzQixDQUFDQztJQUMzQixNQUFNQyxnQkFBZ0JILDZEQUFXQSxDQUFDSSxJQUFJLENBQUNDLENBQUFBLE9BQVFBLEtBQUtILFFBQVEsS0FBS0E7SUFDakUsT0FBT0MsZ0JBQWdCQSxjQUFjRyxJQUFJLEdBQUc7QUFDOUM7QUFFQSxNQUFNQyxPQUFPLENBQUMsRUFBRUMsUUFBUSxFQUFFTixRQUFRLEVBQUUsRUFBRTtJQUNwQyxNQUFNTyxlQUFlUixvQkFBb0JDO0lBRXpDLHFCQUFPLDhEQUFDSCxxREFBUUE7UUFBQ08sTUFBSztRQUFPSixVQUFVTzs7Ozs7O0FBQ3pDO0FBRUEsaUVBQWVGLElBQUlBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vc3JjL2FwcC9hcHBzL1tjYXRlZ29yeV0vcGFnZS5qc3g/MWQ0ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBDYXRlZ29yeSBmcm9tIFwiQC9hcHAvY2F0ZWdvcnlcIjtcclxuaW1wb3J0IHsgYW5kcm9pZEFwcHMgfSBmcm9tIFwiLi4vLi4vQ29uc3RhbnQvc3RhdGljRGF0YVwiO1xyXG5cclxuY29uc3QgZ2V0TmFtZUZyb21DYXRlZ29yeSA9IChjYXRlZ29yeSkgPT4ge1xyXG4gIGNvbnN0IGZvdW5kQ2F0ZWdvcnkgPSBhbmRyb2lkQXBwcy5maW5kKGl0ZW0gPT4gaXRlbS5jYXRlZ29yeSA9PT0gY2F0ZWdvcnkpO1xyXG4gIHJldHVybiBmb3VuZENhdGVnb3J5ID8gZm91bmRDYXRlZ29yeS5uYW1lIDogbnVsbDtcclxufTtcclxuXHJcbmNvbnN0IFBhZ2UgPSAoeyBwYXJhbXM6IHsgY2F0ZWdvcnkgfSB9KSA9PiB7XHJcbiAgY29uc3QgY2F0ZWdvcnlOYW1lID0gZ2V0TmFtZUZyb21DYXRlZ29yeShjYXRlZ29yeSk7XHJcblxyXG4gIHJldHVybiA8Q2F0ZWdvcnkgbmFtZT0nYXBwcycgY2F0ZWdvcnk9e2NhdGVnb3J5TmFtZX0gLz5cclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IFBhZ2U7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNhdGVnb3J5IiwiYW5kcm9pZEFwcHMiLCJnZXROYW1lRnJvbUNhdGVnb3J5IiwiY2F0ZWdvcnkiLCJmb3VuZENhdGVnb3J5IiwiZmluZCIsIml0ZW0iLCJuYW1lIiwiUGFnZSIsInBhcmFtcyIsImNhdGVnb3J5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/apps/[category]/page.jsx\n");

/***/ }),

/***/ "(rsc)/./src/app/category.jsx":
/*!******************************!*\
  !*** ./src/app/category.jsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring\Apk mirror\src\app\category.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _fortawesome_fontawesome_svg_core_styles_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/fontawesome-svg-core/styles.css */ \"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css\");\n/* harmony import */ var _fortawesome_fontawesome_svg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/fontawesome-svg-core */ \"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/index.mjs\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _Components_navbar_navbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Components/navbar/navbar */ \"(rsc)/./src/app/Components/navbar/navbar.jsx\");\n/* harmony import */ var _ReduxLayout_layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ReduxLayout/layout */ \"(rsc)/./src/app/ReduxLayout/layout.js\");\n\n\n\n\n\n_fortawesome_fontawesome_svg_core__WEBPACK_IMPORTED_MODULE_2__.config.autoAddCss = false;\n\n\n\nconst metadata = {\n    title: \"APKExplorer - Fast Android APK Downloader\",\n    description: \"APKExplorer is your go-to source for downloading Android APKs quickly and securely. Discover a vast library of apps, explore different versions, and stay updated with the latest releases.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        async: true,\n                        src: \"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8916641928046583\",\n                        crossOrigin: \"anonymous\",\n                        \"data-adtest\": \"on\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: \"https://cdn.jsdelivr.net/npm/@fingerprintjs/fingerprintjs@3/dist/fp.min.js\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        async: true,\n                        src: \"https://www.googletagmanager.com/gtag/js?id=G-TW5T46HGBD\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        id: \"google-analytics\",\n                        strategy: \"afterInteractive\",\n                        children: `\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n            gtag('js', new Date());\n            gtag('config', 'G-TW5T46HGBD');\n          `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n                (function(c,l,a,r,i,t,y){\n                    c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };\n                    t=l.createElement(r);\n                    t.async=1;\n                    t.src=\"https://www.clarity.ms/tag/\"+i;\n                    y=l.getElementsByTagName(r)[0];\n                    y.parentNode.insertBefore(t,y);\n                })(window, document, \"clarity\", \"script\", \"oa4v7ql0yx\");`\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReduxLayout_layout__WEBPACK_IMPORTED_MODULE_6__.ReduxProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_navbar_navbar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/debug","vendor-chunks/ms","vendor-chunks/@fortawesome","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@reduxjs","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/reselect","vendor-chunks/follow-redirects","vendor-chunks/redux","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/use-sync-external-store","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/redux-thunk","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapps%2F%5Bcategory%5D%2Fpage&page=%2Fapps%2F%5Bcategory%5D%2Fpage&appPaths=%2Fapps%2F%5Bcategory%5D%2Fpage&pagePath=private-next-app-dir%2Fapps%2F%5Bcategory%5D%2Fpage.jsx&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();