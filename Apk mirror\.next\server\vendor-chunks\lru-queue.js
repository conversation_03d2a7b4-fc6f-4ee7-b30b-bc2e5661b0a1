"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lru-queue";
exports.ids = ["vendor-chunks/lru-queue"];
exports.modules = {

/***/ "(rsc)/./node_modules/lru-queue/index.js":
/*!*****************************************!*\
  !*** ./node_modules/lru-queue/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar toPosInt = __webpack_require__(/*! es5-ext/number/to-pos-integer */ \"(rsc)/./node_modules/es5-ext/number/to-pos-integer.js\")\n\n  , create = Object.create, hasOwnProperty = Object.prototype.hasOwnProperty;\n\nmodule.exports = function (limit) {\n\tvar size = 0, base = 1, queue = create(null), map = create(null), index = 0, del;\n\tlimit = toPosInt(limit);\n\treturn {\n\t\thit: function (id) {\n\t\t\tvar oldIndex = map[id], nuIndex = ++index;\n\t\t\tqueue[nuIndex] = id;\n\t\t\tmap[id] = nuIndex;\n\t\t\tif (!oldIndex) {\n\t\t\t\t++size;\n\t\t\t\tif (size <= limit) return;\n\t\t\t\tid = queue[base];\n\t\t\t\tdel(id);\n\t\t\t\treturn id;\n\t\t\t}\n\t\t\tdelete queue[oldIndex];\n\t\t\tif (base !== oldIndex) return;\n\t\t\twhile (!hasOwnProperty.call(queue, ++base)) continue; //jslint: skip\n\t\t},\n\t\tdelete: del = function (id) {\n\t\t\tvar oldIndex = map[id];\n\t\t\tif (!oldIndex) return;\n\t\t\tdelete queue[oldIndex];\n\t\t\tdelete map[id];\n\t\t\t--size;\n\t\t\tif (base !== oldIndex) return;\n\t\t\tif (!size) {\n\t\t\t\tindex = 0;\n\t\t\t\tbase = 1;\n\t\t\t\treturn;\n\t\t\t}\n\t\t\twhile (!hasOwnProperty.call(queue, ++base)) continue; //jslint: skip\n\t\t},\n\t\tclear: function () {\n\t\t\tsize = 0;\n\t\t\tbase = 1;\n\t\t\tqueue = create(null);\n\t\t\tmap = create(null);\n\t\t\tindex = 0;\n\t\t}\n\t};\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbHJ1LXF1ZXVlL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGVBQWUsbUJBQU8sQ0FBQyw0RkFBK0I7O0FBRXREOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5REFBeUQ7QUFDekQsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RDtBQUN6RCxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL25vZGVfbW9kdWxlcy9scnUtcXVldWUvaW5kZXguanM/ODNiYSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciB0b1Bvc0ludCA9IHJlcXVpcmUoJ2VzNS1leHQvbnVtYmVyL3RvLXBvcy1pbnRlZ2VyJylcblxuICAsIGNyZWF0ZSA9IE9iamVjdC5jcmVhdGUsIGhhc093blByb3BlcnR5ID0gT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eTtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAobGltaXQpIHtcblx0dmFyIHNpemUgPSAwLCBiYXNlID0gMSwgcXVldWUgPSBjcmVhdGUobnVsbCksIG1hcCA9IGNyZWF0ZShudWxsKSwgaW5kZXggPSAwLCBkZWw7XG5cdGxpbWl0ID0gdG9Qb3NJbnQobGltaXQpO1xuXHRyZXR1cm4ge1xuXHRcdGhpdDogZnVuY3Rpb24gKGlkKSB7XG5cdFx0XHR2YXIgb2xkSW5kZXggPSBtYXBbaWRdLCBudUluZGV4ID0gKytpbmRleDtcblx0XHRcdHF1ZXVlW251SW5kZXhdID0gaWQ7XG5cdFx0XHRtYXBbaWRdID0gbnVJbmRleDtcblx0XHRcdGlmICghb2xkSW5kZXgpIHtcblx0XHRcdFx0KytzaXplO1xuXHRcdFx0XHRpZiAoc2l6ZSA8PSBsaW1pdCkgcmV0dXJuO1xuXHRcdFx0XHRpZCA9IHF1ZXVlW2Jhc2VdO1xuXHRcdFx0XHRkZWwoaWQpO1xuXHRcdFx0XHRyZXR1cm4gaWQ7XG5cdFx0XHR9XG5cdFx0XHRkZWxldGUgcXVldWVbb2xkSW5kZXhdO1xuXHRcdFx0aWYgKGJhc2UgIT09IG9sZEluZGV4KSByZXR1cm47XG5cdFx0XHR3aGlsZSAoIWhhc093blByb3BlcnR5LmNhbGwocXVldWUsICsrYmFzZSkpIGNvbnRpbnVlOyAvL2pzbGludDogc2tpcFxuXHRcdH0sXG5cdFx0ZGVsZXRlOiBkZWwgPSBmdW5jdGlvbiAoaWQpIHtcblx0XHRcdHZhciBvbGRJbmRleCA9IG1hcFtpZF07XG5cdFx0XHRpZiAoIW9sZEluZGV4KSByZXR1cm47XG5cdFx0XHRkZWxldGUgcXVldWVbb2xkSW5kZXhdO1xuXHRcdFx0ZGVsZXRlIG1hcFtpZF07XG5cdFx0XHQtLXNpemU7XG5cdFx0XHRpZiAoYmFzZSAhPT0gb2xkSW5kZXgpIHJldHVybjtcblx0XHRcdGlmICghc2l6ZSkge1xuXHRcdFx0XHRpbmRleCA9IDA7XG5cdFx0XHRcdGJhc2UgPSAxO1xuXHRcdFx0XHRyZXR1cm47XG5cdFx0XHR9XG5cdFx0XHR3aGlsZSAoIWhhc093blByb3BlcnR5LmNhbGwocXVldWUsICsrYmFzZSkpIGNvbnRpbnVlOyAvL2pzbGludDogc2tpcFxuXHRcdH0sXG5cdFx0Y2xlYXI6IGZ1bmN0aW9uICgpIHtcblx0XHRcdHNpemUgPSAwO1xuXHRcdFx0YmFzZSA9IDE7XG5cdFx0XHRxdWV1ZSA9IGNyZWF0ZShudWxsKTtcblx0XHRcdG1hcCA9IGNyZWF0ZShudWxsKTtcblx0XHRcdGluZGV4ID0gMDtcblx0XHR9XG5cdH07XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/lru-queue/index.js\n");

/***/ })

};
;