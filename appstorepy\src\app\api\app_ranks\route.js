import { NextResponse } from "next/server";
import AppRank from "@/app/database/appRanks";
import ApplicationCategory from "../../database/appCategory";
import connectDB from "@/app/database/mongoose";
const collectionKeyName = {
  TOP_FREE: "topFreeApps",
  TOP_PAID: "topPaidApps",
  GROSSING: "topGrossingApps",
  TOP_NEW_FREE:"topNewFreeApps",
  TOP_NEW_PAID:"topNewPaidApps",
};
export const revalidate = 3600;

const formatAppsInDetails = (appRanks) => {
  return appRanks.map((rank) => ({
    appId: rank.appId.appId,
    title: rank.appId.title,
    developer: rank.appId.developer,
    icon: rank.appId.icon,
    reviews: rank.appId.reviews,
    ratings: rank.appId.ratings,
    scoreText: rank.appId.scoreText,
    maxInstalls: rank.appId.maxInstalls,
    adSupported: rank.appId.adSupported,
    released: rank.appId.released,
    updated: rank.appId.updated,
    free: rank.appId.free,
    genre: rank.appId.genre,
    ranking: rank.ranking,
    position: rank.position,
    updatedAt: rank.scrapDateTime || null,
  }));
};

const formatShortsAppsInDetails = (appRanks) => {
  return appRanks.map((rank) => ({
    appId: rank.appId.appId,
    title: rank.appId.title,
    developer: rank.appId.developer,
    icon: rank.appId.icon,
    position: rank.position,
    ranking: rank.ranking,
    updatedAt: rank.scrapDateTime || null,
  }));
};


export const POST = async (request) => {
  const { countryCode, collection = "", category = "" } = await request.json();

  try {
    await connectDB();

    if (collection) {
      let appRanks;
      if (category === "" || category === "APPLICATION" || category === "GAME") {
        appRanks = await ApplicationCategory.aggregate([
          {
            $match: {
              collectionName: collection,
              countryCode: countryCode || "US",
              category: category || "APPLICATION",
            },
          },
          { $sort: { position: 1 } },
          {
            $lookup: {
              from: "apps",
              localField: "appId",
              foreignField: "appId",
              as: "appId",
            },
          },
          {
            $unwind: "$appId",
          },
          {
            $project: {
              appId: {
                appId: "$appId.appId",
                title: "$appId.title",
                developer: "$appId.developer",
                icon: "$appId.icon",
                reviews: "$appId.reviews",
                ratings: "$appId.ratings",
                scoreText: "$appId.scoreText",
                maxInstalls: "$appId.maxInstalls",
                adSupported: "$appId.adSupported",
                released: "$appId.released",
                updated: "$appId.updated",
                free: "$appId.free",
                genre: "$appId.genre",
              },
              position: 1,
              ranking: 1,
              scrapDateTime: 1,
            },
          },
        ]);
      } else {
        appRanks = await AppRank.aggregate([
          {
            $match: {
              collectionName: collection,
              countryCode: countryCode || "US",
              category: category || "APPLICATION",
            },
          },
          { $sort: { position: 1 } },
          {
            $lookup: {
              from: "apps",
              localField: "appId",
              foreignField: "appId",
              as: "appId",
            },
          },
          {
            $unwind: "$appId",
          },
          {
            $project: {
              appId: {
                appId: "$appId.appId",
                title: "$appId.title",
                developer: "$appId.developer",
                icon: "$appId.icon",
                reviews: "$appId.reviews",
                ratings: "$appId.ratings",
                scoreText: "$appId.scoreText",
                maxInstalls: "$appId.maxInstalls",
                adSupported: "$appId.adSupported",
                released: "$appId.released",
                updated: "$appId.updated",
                free: "$appId.free",
                genre: "$appId.genre",
              },
              position: 1,
              ranking: 1,
              scrapDateTime: 1,
            },
          },
        ]);
      }

      const fetchedApps = formatAppsInDetails(appRanks);

      return NextResponse.json(
        {
          [collectionKeyName[collection]]: fetchedApps || [],
        },
        { status: 200 }
      );
    } else {
      const collections = ["TOP_FREE", "GROSSING", "TOP_PAID", "TOP_NEW_FREE", "TOP_NEW_PAID"];
      const fetchedApps = {};

      for (const col of collections) {
        const initialAppRanks = await ApplicationCategory.aggregate([
          {
            $match: {
              collectionName: col,
              countryCode: countryCode || "US",
              category: category || "APPLICATION",
            },
          },
          { $sort: { position: 1 } },
          {
            $lookup: {
              from: "apps",
              localField: "appId",
              foreignField: "appId",
              as: "appId",
              pipeline: [
                {
                  $project: {
                    appId: 1,
                    title: 1,
                    developer: 1,
                    icon: 1,
                  },
                },
                {
                  $match: {
                    $and: [
                      { title: { $exists: true } },
                      { developer: { $exists: true } },
                      { icon: { $exists: true } },
                    ],
                  },
                },
              ],
            },
          },
          {
            $unwind: {
              path: "$appId",
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $project: {
              appId: {
                $cond: {
                  if: { $isArray: "$appId" },
                  then: null,
                  else: "$appId",
                },
              },
              position: 1,
              ranking: 1,
              scrapDateTime: 1,
            },
          },
          {
            $match: {
              appId: { $ne: null },
            },
          },
          { $limit: 10 },
        ]);

        const formattedApps = formatShortsAppsInDetails(initialAppRanks);
        fetchedApps[col] = formattedApps;
      }

      return NextResponse.json(
        {
          topFreeApps: fetchedApps["TOP_FREE"] || [],
          topGrossingApps: fetchedApps["GROSSING"] || [],
          topPaidApps: fetchedApps["TOP_PAID"] || [],
          topNewFreeApps: fetchedApps["TOP_NEW_FREE"] || [],
          topNewPaidApps: fetchedApps["TOP_NEW_PAID"] || [],
        },
        { status: 200 }
      );
    }
  } catch (error) {
    console.error("Error:", error);
    return NextResponse.json({ message: "Error fetching apps", error }, { status: 500 });
  }
};