"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/http-cache-semantics";
exports.ids = ["vendor-chunks/http-cache-semantics"];
exports.modules = {

/***/ "(rsc)/./node_modules/http-cache-semantics/index.js":
/*!****************************************************!*\
  !*** ./node_modules/http-cache-semantics/index.js ***!
  \****************************************************/
/***/ ((module) => {

eval("\n\n/**\n * @typedef {Object} HttpRequest\n * @property {Record<string, string>} headers - Request headers\n * @property {string} [method] - HTTP method\n * @property {string} [url] - Request URL\n */\n\n/**\n * @typedef {Object} HttpResponse\n * @property {Record<string, string>} headers - Response headers\n * @property {number} [status] - HTTP status code\n */\n\n/**\n * Set of default cacheable status codes per RFC 7231 section 6.1.\n * @type {Set<number>}\n */\nconst statusCodeCacheableByDefault = new Set([\n    200,\n    203,\n    204,\n    206,\n    300,\n    301,\n    308,\n    404,\n    405,\n    410,\n    414,\n    501,\n]);\n\n/**\n * Set of HTTP status codes that the cache implementation understands.\n * Note: This implementation does not understand partial responses (206).\n * @type {Set<number>}\n */\nconst understoodStatuses = new Set([\n    200,\n    203,\n    204,\n    300,\n    301,\n    302,\n    303,\n    307,\n    308,\n    404,\n    405,\n    410,\n    414,\n    501,\n]);\n\n/**\n * Set of HTTP error status codes.\n * @type {Set<number>}\n */\nconst errorStatusCodes = new Set([\n    500,\n    502,\n    503,\n    504,\n]);\n\n/**\n * Object representing hop-by-hop headers that should be removed.\n * @type {Record<string, boolean>}\n */\nconst hopByHopHeaders = {\n    date: true, // included, because we add Age update Date\n    connection: true,\n    'keep-alive': true,\n    'proxy-authenticate': true,\n    'proxy-authorization': true,\n    te: true,\n    trailer: true,\n    'transfer-encoding': true,\n    upgrade: true,\n};\n\n/**\n * Headers that are excluded from revalidation update.\n * @type {Record<string, boolean>}\n */\nconst excludedFromRevalidationUpdate = {\n    // Since the old body is reused, it doesn't make sense to change properties of the body\n    'content-length': true,\n    'content-encoding': true,\n    'transfer-encoding': true,\n    'content-range': true,\n};\n\n/**\n * Converts a string to a number or returns zero if the conversion fails.\n * @param {string} s - The string to convert.\n * @returns {number} The parsed number or 0.\n */\nfunction toNumberOrZero(s) {\n    const n = parseInt(s, 10);\n    return isFinite(n) ? n : 0;\n}\n\n/**\n * Determines if the given response is an error response.\n * Implements RFC 5861 behavior.\n * @param {HttpResponse|undefined} response - The HTTP response object.\n * @returns {boolean} true if the response is an error or undefined, false otherwise.\n */\nfunction isErrorResponse(response) {\n    // consider undefined response as faulty\n    if (!response) {\n        return true;\n    }\n    return errorStatusCodes.has(response.status);\n}\n\n/**\n * Parses a Cache-Control header string into an object.\n * @param {string} [header] - The Cache-Control header value.\n * @returns {Record<string, string|boolean>} An object representing Cache-Control directives.\n */\nfunction parseCacheControl(header) {\n    /** @type {Record<string, string|boolean>} */\n    const cc = {};\n    if (!header) return cc;\n\n    // TODO: When there is more than one value present for a given directive (e.g., two Expires header fields, multiple Cache-Control: max-age directives),\n    // the directive's value is considered invalid. Caches are encouraged to consider responses that have invalid freshness information to be stale\n    const parts = header.trim().split(/,/);\n    for (const part of parts) {\n        const [k, v] = part.split(/=/, 2);\n        cc[k.trim()] = v === undefined ? true : v.trim().replace(/^\"|\"$/g, '');\n    }\n\n    return cc;\n}\n\n/**\n * Formats a Cache-Control directives object into a header string.\n * @param {Record<string, string|boolean>} cc - The Cache-Control directives.\n * @returns {string|undefined} A formatted Cache-Control header string or undefined if empty.\n */\nfunction formatCacheControl(cc) {\n    let parts = [];\n    for (const k in cc) {\n        const v = cc[k];\n        parts.push(v === true ? k : k + '=' + v);\n    }\n    if (!parts.length) {\n        return undefined;\n    }\n    return parts.join(', ');\n}\n\nmodule.exports = class CachePolicy {\n    /**\n     * Creates a new CachePolicy instance.\n     * @param {HttpRequest} req - Incoming client request.\n     * @param {HttpResponse} res - Received server response.\n     * @param {Object} [options={}] - Configuration options.\n     * @param {boolean} [options.shared=true] - Is the cache shared (a public proxy)? `false` for personal browser caches.\n     * @param {number} [options.cacheHeuristic=0.1] - Fallback heuristic (age fraction) for cache duration.\n     * @param {number} [options.immutableMinTimeToLive=86400000] - Minimum TTL for immutable responses in milliseconds.\n     * @param {boolean} [options.ignoreCargoCult=false] - Detect nonsense cache headers, and override them.\n     * @param {any} [options._fromObject] - Internal parameter for deserialization. Do not use.\n     */\n    constructor(\n        req,\n        res,\n        {\n            shared,\n            cacheHeuristic,\n            immutableMinTimeToLive,\n            ignoreCargoCult,\n            _fromObject,\n        } = {}\n    ) {\n        if (_fromObject) {\n            this._fromObject(_fromObject);\n            return;\n        }\n\n        if (!res || !res.headers) {\n            throw Error('Response headers missing');\n        }\n        this._assertRequestHasHeaders(req);\n\n        /** @type {number} Timestamp when the response was received */\n        this._responseTime = this.now();\n        /** @type {boolean} Indicates if the cache is shared */\n        this._isShared = shared !== false;\n        /** @type {boolean} Indicates if legacy cargo cult directives should be ignored */\n        this._ignoreCargoCult = !!ignoreCargoCult;\n        /** @type {number} Heuristic cache fraction */\n        this._cacheHeuristic =\n            undefined !== cacheHeuristic ? cacheHeuristic : 0.1; // 10% matches IE\n        /** @type {number} Minimum TTL for immutable responses in ms */\n        this._immutableMinTtl =\n            undefined !== immutableMinTimeToLive\n                ? immutableMinTimeToLive\n                : 24 * 3600 * 1000;\n\n        /** @type {number} HTTP status code */\n        this._status = 'status' in res ? res.status : 200;\n        /** @type {Record<string, string>} Response headers */\n        this._resHeaders = res.headers;\n        /** @type {Record<string, string|boolean>} Parsed Cache-Control directives from response */\n        this._rescc = parseCacheControl(res.headers['cache-control']);\n        /** @type {string} HTTP method (e.g., GET, POST) */\n        this._method = 'method' in req ? req.method : 'GET';\n        /** @type {string} Request URL */\n        this._url = req.url;\n        /** @type {string} Host header from the request */\n        this._host = req.headers.host;\n        /** @type {boolean} Whether the request does not include an Authorization header */\n        this._noAuthorization = !req.headers.authorization;\n        /** @type {Record<string, string>|null} Request headers used for Vary matching */\n        this._reqHeaders = res.headers.vary ? req.headers : null; // Don't keep all request headers if they won't be used\n        /** @type {Record<string, string|boolean>} Parsed Cache-Control directives from request */\n        this._reqcc = parseCacheControl(req.headers['cache-control']);\n\n        // Assume that if someone uses legacy, non-standard uncecessary options they don't understand caching,\n        // so there's no point stricly adhering to the blindly copy&pasted directives.\n        if (\n            this._ignoreCargoCult &&\n            'pre-check' in this._rescc &&\n            'post-check' in this._rescc\n        ) {\n            delete this._rescc['pre-check'];\n            delete this._rescc['post-check'];\n            delete this._rescc['no-cache'];\n            delete this._rescc['no-store'];\n            delete this._rescc['must-revalidate'];\n            this._resHeaders = Object.assign({}, this._resHeaders, {\n                'cache-control': formatCacheControl(this._rescc),\n            });\n            delete this._resHeaders.expires;\n            delete this._resHeaders.pragma;\n        }\n\n        // When the Cache-Control header field is not present in a request, caches MUST consider the no-cache request pragma-directive\n        // as having the same effect as if \"Cache-Control: no-cache\" were present (see Section 5.2.1).\n        if (\n            res.headers['cache-control'] == null &&\n            /no-cache/.test(res.headers.pragma)\n        ) {\n            this._rescc['no-cache'] = true;\n        }\n    }\n\n    /**\n     * You can monkey-patch it for testing.\n     * @returns {number} Current time in milliseconds.\n     */\n    now() {\n        return Date.now();\n    }\n\n    /**\n     * Determines if the response is storable in a cache.\n     * @returns {boolean} `false` if can never be cached.\n     */\n    storable() {\n        // The \"no-store\" request directive indicates that a cache MUST NOT store any part of either this request or any response to it.\n        return !!(\n            !this._reqcc['no-store'] &&\n            // A cache MUST NOT store a response to any request, unless:\n            // The request method is understood by the cache and defined as being cacheable, and\n            ('GET' === this._method ||\n                'HEAD' === this._method ||\n                ('POST' === this._method && this._hasExplicitExpiration())) &&\n            // the response status code is understood by the cache, and\n            understoodStatuses.has(this._status) &&\n            // the \"no-store\" cache directive does not appear in request or response header fields, and\n            !this._rescc['no-store'] &&\n            // the \"private\" response directive does not appear in the response, if the cache is shared, and\n            (!this._isShared || !this._rescc.private) &&\n            // the Authorization header field does not appear in the request, if the cache is shared,\n            (!this._isShared ||\n                this._noAuthorization ||\n                this._allowsStoringAuthenticated()) &&\n            // the response either:\n            // contains an Expires header field, or\n            (this._resHeaders.expires ||\n                // contains a max-age response directive, or\n                // contains a s-maxage response directive and the cache is shared, or\n                // contains a public response directive.\n                this._rescc['max-age'] ||\n                (this._isShared && this._rescc['s-maxage']) ||\n                this._rescc.public ||\n                // has a status code that is defined as cacheable by default\n                statusCodeCacheableByDefault.has(this._status))\n        );\n    }\n\n    /**\n     * @returns {boolean} true if expiration is explicitly defined.\n     */\n    _hasExplicitExpiration() {\n        // 4.2.1 Calculating Freshness Lifetime\n        return !!(\n            (this._isShared && this._rescc['s-maxage']) ||\n            this._rescc['max-age'] ||\n            this._resHeaders.expires\n        );\n    }\n\n    /**\n     * @param {HttpRequest} req - a request\n     * @throws {Error} if the headers are missing.\n     */\n    _assertRequestHasHeaders(req) {\n        if (!req || !req.headers) {\n            throw Error('Request headers missing');\n        }\n    }\n\n    /**\n     * Checks if the request matches the cache and can be satisfied from the cache immediately,\n     * without having to make a request to the server.\n     *\n     * This doesn't support `stale-while-revalidate`. See `evaluateRequest()` for a more complete solution.\n     *\n     * @param {HttpRequest} req - The new incoming HTTP request.\n     * @returns {boolean} `true`` if the cached response used to construct this cache policy satisfies the request without revalidation.\n     */\n    satisfiesWithoutRevalidation(req) {\n        const result = this.evaluateRequest(req);\n        return !result.revalidation;\n    }\n\n    /**\n     * @param {{headers: Record<string, string>, synchronous: boolean}|undefined} revalidation - Revalidation information, if any.\n     * @returns {{response: {headers: Record<string, string>}, revalidation: {headers: Record<string, string>, synchronous: boolean}|undefined}} An object with a cached response headers and revalidation info.\n     */\n    _evaluateRequestHitResult(revalidation) {\n        return {\n            response: {\n                headers: this.responseHeaders(),\n            },\n            revalidation,\n        };\n    }\n\n    /**\n     * @param {HttpRequest} request - new incoming\n     * @param {boolean} synchronous - whether revalidation must be synchronous (not s-w-r).\n     * @returns {{headers: Record<string, string>, synchronous: boolean}} An object with revalidation headers and a synchronous flag.\n     */\n    _evaluateRequestRevalidation(request, synchronous) {\n        return {\n            synchronous,\n            headers: this.revalidationHeaders(request),\n        };\n    }\n\n    /**\n     * @param {HttpRequest} request - new incoming\n     * @returns {{response: undefined, revalidation: {headers: Record<string, string>, synchronous: boolean}}} An object indicating no cached response and revalidation details.\n     */\n    _evaluateRequestMissResult(request) {\n        return {\n            response: undefined,\n            revalidation: this._evaluateRequestRevalidation(request, true),\n        };\n    }\n\n    /**\n     * Checks if the given request matches this cache entry, and how the cache can be used to satisfy it. Returns an object with:\n     *\n     * ```\n     * {\n     *     // If defined, you must send a request to the server.\n     *     revalidation: {\n     *         headers: {}, // HTTP headers to use when sending the revalidation response\n     *         // If true, you MUST wait for a response from the server before using the cache\n     *         // If false, this is stale-while-revalidate. The cache is stale, but you can use it while you update it asynchronously.\n     *         synchronous: bool,\n     *     },\n     *     // If defined, you can use this cached response.\n     *     response: {\n     *         headers: {}, // Updated cached HTTP headers you must use when responding to the client\n     *     },\n     * }\n     * ```\n     * @param {HttpRequest} req - new incoming HTTP request\n     * @returns {{response: {headers: Record<string, string>}|undefined, revalidation: {headers: Record<string, string>, synchronous: boolean}|undefined}} An object containing keys:\n     *   - revalidation: { headers: Record<string, string>, synchronous: boolean } Set if you should send this to the origin server\n     *   - response: { headers: Record<string, string> } Set if you can respond to the client with these cached headers\n     */\n    evaluateRequest(req) {\n        this._assertRequestHasHeaders(req);\n\n        // In all circumstances, a cache MUST NOT ignore the must-revalidate directive\n        if (this._rescc['must-revalidate']) {\n            return this._evaluateRequestMissResult(req);\n        }\n\n        if (!this._requestMatches(req, false)) {\n            return this._evaluateRequestMissResult(req);\n        }\n\n        // When presented with a request, a cache MUST NOT reuse a stored response, unless:\n        // the presented request does not contain the no-cache pragma (Section 5.4), nor the no-cache cache directive,\n        // unless the stored response is successfully validated (Section 4.3), and\n        const requestCC = parseCacheControl(req.headers['cache-control']);\n\n        if (requestCC['no-cache'] || /no-cache/.test(req.headers.pragma)) {\n            return this._evaluateRequestMissResult(req);\n        }\n\n        if (requestCC['max-age'] && this.age() > toNumberOrZero(requestCC['max-age'])) {\n            return this._evaluateRequestMissResult(req);\n        }\n\n        if (requestCC['min-fresh'] && this.maxAge() - this.age() < toNumberOrZero(requestCC['min-fresh'])) {\n            return this._evaluateRequestMissResult(req);\n        }\n\n        // the stored response is either:\n        // fresh, or allowed to be served stale\n        if (this.stale()) {\n            // If a value is present, then the client is willing to accept a response that has\n            // exceeded its freshness lifetime by no more than the specified number of seconds\n            const allowsStaleWithoutRevalidation = 'max-stale' in requestCC &&\n                (true === requestCC['max-stale'] || requestCC['max-stale'] > this.age() - this.maxAge());\n\n            if (allowsStaleWithoutRevalidation) {\n                return this._evaluateRequestHitResult(undefined);\n            }\n\n            if (this.useStaleWhileRevalidate()) {\n                return this._evaluateRequestHitResult(this._evaluateRequestRevalidation(req, false));\n            }\n\n            return this._evaluateRequestMissResult(req);\n        }\n\n        return this._evaluateRequestHitResult(undefined);\n    }\n\n    /**\n     * @param {HttpRequest} req - check if this is for the same cache entry\n     * @param {boolean} allowHeadMethod - allow a HEAD method to match.\n     * @returns {boolean} `true` if the request matches.\n     */\n    _requestMatches(req, allowHeadMethod) {\n        // The presented effective request URI and that of the stored response match, and\n        return !!(\n            (!this._url || this._url === req.url) &&\n            this._host === req.headers.host &&\n            // the request method associated with the stored response allows it to be used for the presented request, and\n            (!req.method ||\n                this._method === req.method ||\n                (allowHeadMethod && 'HEAD' === req.method)) &&\n            // selecting header fields nominated by the stored response (if any) match those presented, and\n            this._varyMatches(req)\n        );\n    }\n\n    /**\n     * Determines whether storing authenticated responses is allowed.\n     * @returns {boolean} `true` if allowed.\n     */\n    _allowsStoringAuthenticated() {\n        // following Cache-Control response directives (Section 5.2.2) have such an effect: must-revalidate, public, and s-maxage.\n        return !!(\n            this._rescc['must-revalidate'] ||\n            this._rescc.public ||\n            this._rescc['s-maxage']\n        );\n    }\n\n    /**\n     * Checks whether the Vary header in the response matches the new request.\n     * @param {HttpRequest} req - incoming HTTP request\n     * @returns {boolean} `true` if the vary headers match.\n     */\n    _varyMatches(req) {\n        if (!this._resHeaders.vary) {\n            return true;\n        }\n\n        // A Vary header field-value of \"*\" always fails to match\n        if (this._resHeaders.vary === '*') {\n            return false;\n        }\n\n        const fields = this._resHeaders.vary\n            .trim()\n            .toLowerCase()\n            .split(/\\s*,\\s*/);\n        for (const name of fields) {\n            if (req.headers[name] !== this._reqHeaders[name]) return false;\n        }\n        return true;\n    }\n\n    /**\n     * Creates a copy of the given headers without any hop-by-hop headers.\n     * @param {Record<string, string>} inHeaders - old headers from the cached response\n     * @returns {Record<string, string>} A new headers object without hop-by-hop headers.\n     */\n    _copyWithoutHopByHopHeaders(inHeaders) {\n        /** @type {Record<string, string>} */\n        const headers = {};\n        for (const name in inHeaders) {\n            if (hopByHopHeaders[name]) continue;\n            headers[name] = inHeaders[name];\n        }\n        // 9.1.  Connection\n        if (inHeaders.connection) {\n            const tokens = inHeaders.connection.trim().split(/\\s*,\\s*/);\n            for (const name of tokens) {\n                delete headers[name];\n            }\n        }\n        if (headers.warning) {\n            const warnings = headers.warning.split(/,/).filter(warning => {\n                return !/^\\s*1[0-9][0-9]/.test(warning);\n            });\n            if (!warnings.length) {\n                delete headers.warning;\n            } else {\n                headers.warning = warnings.join(',').trim();\n            }\n        }\n        return headers;\n    }\n\n    /**\n     * Returns the response headers adjusted for serving the cached response.\n     * Removes hop-by-hop headers and updates the Age and Date headers.\n     * @returns {Record<string, string>} The adjusted response headers.\n     */\n    responseHeaders() {\n        const headers = this._copyWithoutHopByHopHeaders(this._resHeaders);\n        const age = this.age();\n\n        // A cache SHOULD generate 113 warning if it heuristically chose a freshness\n        // lifetime greater than 24 hours and the response's age is greater than 24 hours.\n        if (\n            age > 3600 * 24 &&\n            !this._hasExplicitExpiration() &&\n            this.maxAge() > 3600 * 24\n        ) {\n            headers.warning =\n                (headers.warning ? `${headers.warning}, ` : '') +\n                '113 - \"rfc7234 5.5.4\"';\n        }\n        headers.age = `${Math.round(age)}`;\n        headers.date = new Date(this.now()).toUTCString();\n        return headers;\n    }\n\n    /**\n     * Returns the Date header value from the response or the current time if invalid.\n     * @returns {number} Timestamp (in milliseconds) representing the Date header or response time.\n     */\n    date() {\n        const serverDate = Date.parse(this._resHeaders.date);\n        if (isFinite(serverDate)) {\n            return serverDate;\n        }\n        return this._responseTime;\n    }\n\n    /**\n     * Value of the Age header, in seconds, updated for the current time.\n     * May be fractional.\n     * @returns {number} The age in seconds.\n     */\n    age() {\n        let age = this._ageValue();\n\n        const residentTime = (this.now() - this._responseTime) / 1000;\n        return age + residentTime;\n    }\n\n    /**\n     * @returns {number} The Age header value as a number.\n     */\n    _ageValue() {\n        return toNumberOrZero(this._resHeaders.age);\n    }\n\n    /**\n     * Possibly outdated value of applicable max-age (or heuristic equivalent) in seconds.\n     * This counts since response's `Date`.\n     *\n     * For an up-to-date value, see `timeToLive()`.\n     *\n     * Returns the maximum age (freshness lifetime) of the response in seconds.\n     * @returns {number} The max-age value in seconds.\n     */\n    maxAge() {\n        if (!this.storable() || this._rescc['no-cache']) {\n            return 0;\n        }\n\n        // Shared responses with cookies are cacheable according to the RFC, but IMHO it'd be unwise to do so by default\n        // so this implementation requires explicit opt-in via public header\n        if (\n            this._isShared &&\n            (this._resHeaders['set-cookie'] &&\n                !this._rescc.public &&\n                !this._rescc.immutable)\n        ) {\n            return 0;\n        }\n\n        if (this._resHeaders.vary === '*') {\n            return 0;\n        }\n\n        if (this._isShared) {\n            if (this._rescc['proxy-revalidate']) {\n                return 0;\n            }\n            // if a response includes the s-maxage directive, a shared cache recipient MUST ignore the Expires field.\n            if (this._rescc['s-maxage']) {\n                return toNumberOrZero(this._rescc['s-maxage']);\n            }\n        }\n\n        // If a response includes a Cache-Control field with the max-age directive, a recipient MUST ignore the Expires field.\n        if (this._rescc['max-age']) {\n            return toNumberOrZero(this._rescc['max-age']);\n        }\n\n        const defaultMinTtl = this._rescc.immutable ? this._immutableMinTtl : 0;\n\n        const serverDate = this.date();\n        if (this._resHeaders.expires) {\n            const expires = Date.parse(this._resHeaders.expires);\n            // A cache recipient MUST interpret invalid date formats, especially the value \"0\", as representing a time in the past (i.e., \"already expired\").\n            if (Number.isNaN(expires) || expires < serverDate) {\n                return 0;\n            }\n            return Math.max(defaultMinTtl, (expires - serverDate) / 1000);\n        }\n\n        if (this._resHeaders['last-modified']) {\n            const lastModified = Date.parse(this._resHeaders['last-modified']);\n            if (isFinite(lastModified) && serverDate > lastModified) {\n                return Math.max(\n                    defaultMinTtl,\n                    ((serverDate - lastModified) / 1000) * this._cacheHeuristic\n                );\n            }\n        }\n\n        return defaultMinTtl;\n    }\n\n    /**\n     * Remaining time this cache entry may be useful for, in *milliseconds*.\n     * You can use this as an expiration time for your cache storage.\n     *\n     * Prefer this method over `maxAge()`, because it includes other factors like `age` and `stale-while-revalidate`.\n     * @returns {number} Time-to-live in milliseconds.\n     */\n    timeToLive() {\n        const age = this.maxAge() - this.age();\n        const staleIfErrorAge = age + toNumberOrZero(this._rescc['stale-if-error']);\n        const staleWhileRevalidateAge = age + toNumberOrZero(this._rescc['stale-while-revalidate']);\n        return Math.round(Math.max(0, age, staleIfErrorAge, staleWhileRevalidateAge) * 1000);\n    }\n\n    /**\n     * If true, this cache entry is past its expiration date.\n     * Note that stale cache may be useful sometimes, see `evaluateRequest()`.\n     * @returns {boolean} `false` doesn't mean it's fresh nor usable\n     */\n    stale() {\n        return this.maxAge() <= this.age();\n    }\n\n    /**\n     * @returns {boolean} `true` if `stale-if-error` condition allows use of a stale response.\n     */\n    _useStaleIfError() {\n        return this.maxAge() + toNumberOrZero(this._rescc['stale-if-error']) > this.age();\n    }\n\n    /** See `evaluateRequest()` for a more complete solution\n     * @returns {boolean} `true` if `stale-while-revalidate` is currently allowed.\n     */\n    useStaleWhileRevalidate() {\n        const swr = toNumberOrZero(this._rescc['stale-while-revalidate']);\n        return swr > 0 && this.maxAge() + swr > this.age();\n    }\n\n    /**\n     * Creates a `CachePolicy` instance from a serialized object.\n     * @param {Object} obj - The serialized object.\n     * @returns {CachePolicy} A new CachePolicy instance.\n     */\n    static fromObject(obj) {\n        return new this(undefined, undefined, { _fromObject: obj });\n    }\n\n    /**\n     * @param {any} obj - The serialized object.\n     * @throws {Error} If already initialized or if the object is invalid.\n     */\n    _fromObject(obj) {\n        if (this._responseTime) throw Error('Reinitialized');\n        if (!obj || obj.v !== 1) throw Error('Invalid serialization');\n\n        this._responseTime = obj.t;\n        this._isShared = obj.sh;\n        this._cacheHeuristic = obj.ch;\n        this._immutableMinTtl =\n            obj.imm !== undefined ? obj.imm : 24 * 3600 * 1000;\n        this._ignoreCargoCult = !!obj.icc;\n        this._status = obj.st;\n        this._resHeaders = obj.resh;\n        this._rescc = obj.rescc;\n        this._method = obj.m;\n        this._url = obj.u;\n        this._host = obj.h;\n        this._noAuthorization = obj.a;\n        this._reqHeaders = obj.reqh;\n        this._reqcc = obj.reqcc;\n    }\n\n    /**\n     * Serializes the `CachePolicy` instance into a JSON-serializable object.\n     * @returns {Object} The serialized object.\n     */\n    toObject() {\n        return {\n            v: 1,\n            t: this._responseTime,\n            sh: this._isShared,\n            ch: this._cacheHeuristic,\n            imm: this._immutableMinTtl,\n            icc: this._ignoreCargoCult,\n            st: this._status,\n            resh: this._resHeaders,\n            rescc: this._rescc,\n            m: this._method,\n            u: this._url,\n            h: this._host,\n            a: this._noAuthorization,\n            reqh: this._reqHeaders,\n            reqcc: this._reqcc,\n        };\n    }\n\n    /**\n     * Headers for sending to the origin server to revalidate stale response.\n     * Allows server to return 304 to allow reuse of the previous response.\n     *\n     * Hop by hop headers are always stripped.\n     * Revalidation headers may be added or removed, depending on request.\n     * @param {HttpRequest} incomingReq - The incoming HTTP request.\n     * @returns {Record<string, string>} The headers for the revalidation request.\n     */\n    revalidationHeaders(incomingReq) {\n        this._assertRequestHasHeaders(incomingReq);\n        const headers = this._copyWithoutHopByHopHeaders(incomingReq.headers);\n\n        // This implementation does not understand range requests\n        delete headers['if-range'];\n\n        if (!this._requestMatches(incomingReq, true) || !this.storable()) {\n            // revalidation allowed via HEAD\n            // not for the same resource, or wasn't allowed to be cached anyway\n            delete headers['if-none-match'];\n            delete headers['if-modified-since'];\n            return headers;\n        }\n\n        /* MUST send that entity-tag in any cache validation request (using If-Match or If-None-Match) if an entity-tag has been provided by the origin server. */\n        if (this._resHeaders.etag) {\n            headers['if-none-match'] = headers['if-none-match']\n                ? `${headers['if-none-match']}, ${this._resHeaders.etag}`\n                : this._resHeaders.etag;\n        }\n\n        // Clients MAY issue simple (non-subrange) GET requests with either weak validators or strong validators. Clients MUST NOT use weak validators in other forms of request.\n        const forbidsWeakValidators =\n            headers['accept-ranges'] ||\n            headers['if-match'] ||\n            headers['if-unmodified-since'] ||\n            (this._method && this._method != 'GET');\n\n        /* SHOULD send the Last-Modified value in non-subrange cache validation requests (using If-Modified-Since) if only a Last-Modified value has been provided by the origin server.\n        Note: This implementation does not understand partial responses (206) */\n        if (forbidsWeakValidators) {\n            delete headers['if-modified-since'];\n\n            if (headers['if-none-match']) {\n                const etags = headers['if-none-match']\n                    .split(/,/)\n                    .filter(etag => {\n                        return !/^\\s*W\\//.test(etag);\n                    });\n                if (!etags.length) {\n                    delete headers['if-none-match'];\n                } else {\n                    headers['if-none-match'] = etags.join(',').trim();\n                }\n            }\n        } else if (\n            this._resHeaders['last-modified'] &&\n            !headers['if-modified-since']\n        ) {\n            headers['if-modified-since'] = this._resHeaders['last-modified'];\n        }\n\n        return headers;\n    }\n\n    /**\n     * Creates new CachePolicy with information combined from the previews response,\n     * and the new revalidation response.\n     *\n     * Returns {policy, modified} where modified is a boolean indicating\n     * whether the response body has been modified, and old cached body can't be used.\n     *\n     * @param {HttpRequest} request - The latest HTTP request asking for the cached entry.\n     * @param {HttpResponse} response - The latest revalidation HTTP response from the origin server.\n     * @returns {{policy: CachePolicy, modified: boolean, matches: boolean}} The updated policy and modification status.\n     * @throws {Error} If the response headers are missing.\n     */\n    revalidatedPolicy(request, response) {\n        this._assertRequestHasHeaders(request);\n\n        if (this._useStaleIfError() && isErrorResponse(response)) {\n          return {\n              policy: this,\n              modified: false,\n              matches: true,\n          };\n        }\n\n        if (!response || !response.headers) {\n            throw Error('Response headers missing');\n        }\n\n        // These aren't going to be supported exactly, since one CachePolicy object\n        // doesn't know about all the other cached objects.\n        let matches = false;\n        if (response.status !== undefined && response.status != 304) {\n            matches = false;\n        } else if (\n            response.headers.etag &&\n            !/^\\s*W\\//.test(response.headers.etag)\n        ) {\n            // \"All of the stored responses with the same strong validator are selected.\n            // If none of the stored responses contain the same strong validator,\n            // then the cache MUST NOT use the new response to update any stored responses.\"\n            matches =\n                this._resHeaders.etag &&\n                this._resHeaders.etag.replace(/^\\s*W\\//, '') ===\n                    response.headers.etag;\n        } else if (this._resHeaders.etag && response.headers.etag) {\n            // \"If the new response contains a weak validator and that validator corresponds\n            // to one of the cache's stored responses,\n            // then the most recent of those matching stored responses is selected for update.\"\n            matches =\n                this._resHeaders.etag.replace(/^\\s*W\\//, '') ===\n                response.headers.etag.replace(/^\\s*W\\//, '');\n        } else if (this._resHeaders['last-modified']) {\n            matches =\n                this._resHeaders['last-modified'] ===\n                response.headers['last-modified'];\n        } else {\n            // If the new response does not include any form of validator (such as in the case where\n            // a client generates an If-Modified-Since request from a source other than the Last-Modified\n            // response header field), and there is only one stored response, and that stored response also\n            // lacks a validator, then that stored response is selected for update.\n            if (\n                !this._resHeaders.etag &&\n                !this._resHeaders['last-modified'] &&\n                !response.headers.etag &&\n                !response.headers['last-modified']\n            ) {\n                matches = true;\n            }\n        }\n\n        const optionsCopy = {\n            shared: this._isShared,\n            cacheHeuristic: this._cacheHeuristic,\n            immutableMinTimeToLive: this._immutableMinTtl,\n            ignoreCargoCult: this._ignoreCargoCult,\n        };\n\n        if (!matches) {\n            return {\n                policy: new this.constructor(request, response, optionsCopy),\n                // Client receiving 304 without body, even if it's invalid/mismatched has no option\n                // but to reuse a cached body. We don't have a good way to tell clients to do\n                // error recovery in such case.\n                modified: response.status != 304,\n                matches: false,\n            };\n        }\n\n        // use other header fields provided in the 304 (Not Modified) response to replace all instances\n        // of the corresponding header fields in the stored response.\n        const headers = {};\n        for (const k in this._resHeaders) {\n            headers[k] =\n                k in response.headers && !excludedFromRevalidationUpdate[k]\n                    ? response.headers[k]\n                    : this._resHeaders[k];\n        }\n\n        const newResponse = Object.assign({}, response, {\n            status: this._status,\n            method: this._method,\n            headers,\n        });\n        return {\n            policy: new this.constructor(request, newResponse, optionsCopy),\n            modified: false,\n            matches: true,\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaHR0cC1jYWNoZS1zZW1hbnRpY3MvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQSxhQUFhLFFBQVE7QUFDckIsY0FBYyx3QkFBd0I7QUFDdEMsY0FBYyxRQUFRO0FBQ3RCLGNBQWMsUUFBUTtBQUN0Qjs7QUFFQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixjQUFjLHdCQUF3QjtBQUN0QyxjQUFjLFFBQVE7QUFDdEI7O0FBRUE7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLHdCQUF3QjtBQUNuQyxhQUFhLFNBQVM7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWEsZ0NBQWdDO0FBQzdDO0FBQ0E7QUFDQSxlQUFlLGdDQUFnQztBQUMvQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFdBQVcsZ0NBQWdDO0FBQzNDLGFBQWEsa0JBQWtCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGFBQWE7QUFDNUIsZUFBZSxjQUFjO0FBQzdCLGVBQWUsUUFBUSxXQUFXO0FBQ2xDLGVBQWUsU0FBUztBQUN4QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsU0FBUztBQUN4QixlQUFlLEtBQUs7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxtQkFBbUIsUUFBUTtBQUMzQjtBQUNBLG1CQUFtQixTQUFTO0FBQzVCO0FBQ0EsbUJBQW1CLFNBQVM7QUFDNUI7QUFDQSxtQkFBbUIsUUFBUTtBQUMzQjtBQUNBLGlFQUFpRTtBQUNqRSxtQkFBbUIsUUFBUTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxtQkFBbUIsUUFBUTtBQUMzQjtBQUNBLG1CQUFtQix3QkFBd0I7QUFDM0M7QUFDQSxtQkFBbUIsZ0NBQWdDO0FBQ25EO0FBQ0EsbUJBQW1CLFFBQVE7QUFDM0I7QUFDQSxtQkFBbUIsUUFBUTtBQUMzQjtBQUNBLG1CQUFtQixRQUFRO0FBQzNCO0FBQ0EsbUJBQW1CLFNBQVM7QUFDNUI7QUFDQSxtQkFBbUIsNkJBQTZCO0FBQ2hELGtFQUFrRTtBQUNsRSxtQkFBbUIsZ0NBQWdDO0FBQ25EOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtDQUErQztBQUMvQztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxpQkFBaUIsUUFBUTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsaUJBQWlCLFNBQVM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsaUJBQWlCLFNBQVM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSxhQUFhO0FBQzVCLGdCQUFnQixPQUFPO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGFBQWE7QUFDNUIsaUJBQWlCLFNBQVM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGdCQUFnQixzREFBc0QsWUFBWTtBQUNsRixrQkFBa0IsV0FBVyxnQ0FBZ0MsaUJBQWlCLHNEQUFzRCxhQUFhO0FBQ2pKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSxhQUFhO0FBQzVCLGVBQWUsU0FBUztBQUN4QixrQkFBa0Isd0RBQXdEO0FBQzFFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSxhQUFhO0FBQzVCLGtCQUFrQixvQ0FBb0MseURBQXlEO0FBQy9HO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsMEJBQTBCO0FBQzFCLFlBQVk7QUFDWjtBQUNBO0FBQ0EsZUFBZSxhQUFhO0FBQzVCLGtCQUFrQixXQUFXLGdDQUFnQywyQkFBMkIsc0RBQXNELGFBQWE7QUFDM0osMkJBQTJCLHdEQUF3RDtBQUNuRix1QkFBdUIsa0NBQWtDO0FBQ3pEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxlQUFlLGFBQWE7QUFDNUIsZUFBZSxTQUFTO0FBQ3hCLGlCQUFpQixTQUFTO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGlCQUFpQixTQUFTO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsZUFBZSxhQUFhO0FBQzVCLGlCQUFpQixTQUFTO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxlQUFlLHdCQUF3QjtBQUN2QyxpQkFBaUIsd0JBQXdCO0FBQ3pDO0FBQ0E7QUFDQSxtQkFBbUIsd0JBQXdCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLHdCQUF3QjtBQUN6QztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLGdCQUFnQjtBQUN0RDtBQUNBO0FBQ0EseUJBQXlCLGdCQUFnQjtBQUN6QztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGlCQUFpQixRQUFRO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLFFBQVE7QUFDekI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGlCQUFpQixRQUFRO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLFFBQVE7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLFFBQVE7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLFNBQVM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxpQkFBaUIsU0FBUztBQUMxQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGlCQUFpQixTQUFTO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QixpQkFBaUIsYUFBYTtBQUM5QjtBQUNBO0FBQ0EsZ0RBQWdELGtCQUFrQjtBQUNsRTs7QUFFQTtBQUNBLGVBQWUsS0FBSztBQUNwQixnQkFBZ0IsT0FBTztBQUN2QjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsaUJBQWlCLFFBQVE7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGFBQWE7QUFDNUIsaUJBQWlCLHdCQUF3QjtBQUN6QztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQix5QkFBeUIsSUFBSSxzQkFBc0I7QUFDeEU7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0Isa0JBQWtCO0FBQ2xDO0FBQ0E7QUFDQSxlQUFlLGFBQWE7QUFDNUIsZUFBZSxjQUFjO0FBQzdCLGtCQUFrQiwyREFBMkQ7QUFDN0UsZ0JBQWdCLE9BQU87QUFDdkI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSw0Q0FBNEM7QUFDNUM7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL25vZGVfbW9kdWxlcy9odHRwLWNhY2hlLXNlbWFudGljcy9pbmRleC5qcz8wNzRhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLyoqXG4gKiBAdHlwZWRlZiB7T2JqZWN0fSBIdHRwUmVxdWVzdFxuICogQHByb3BlcnR5IHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSBoZWFkZXJzIC0gUmVxdWVzdCBoZWFkZXJzXG4gKiBAcHJvcGVydHkge3N0cmluZ30gW21ldGhvZF0gLSBIVFRQIG1ldGhvZFxuICogQHByb3BlcnR5IHtzdHJpbmd9IFt1cmxdIC0gUmVxdWVzdCBVUkxcbiAqL1xuXG4vKipcbiAqIEB0eXBlZGVmIHtPYmplY3R9IEh0dHBSZXNwb25zZVxuICogQHByb3BlcnR5IHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSBoZWFkZXJzIC0gUmVzcG9uc2UgaGVhZGVyc1xuICogQHByb3BlcnR5IHtudW1iZXJ9IFtzdGF0dXNdIC0gSFRUUCBzdGF0dXMgY29kZVxuICovXG5cbi8qKlxuICogU2V0IG9mIGRlZmF1bHQgY2FjaGVhYmxlIHN0YXR1cyBjb2RlcyBwZXIgUkZDIDcyMzEgc2VjdGlvbiA2LjEuXG4gKiBAdHlwZSB7U2V0PG51bWJlcj59XG4gKi9cbmNvbnN0IHN0YXR1c0NvZGVDYWNoZWFibGVCeURlZmF1bHQgPSBuZXcgU2V0KFtcbiAgICAyMDAsXG4gICAgMjAzLFxuICAgIDIwNCxcbiAgICAyMDYsXG4gICAgMzAwLFxuICAgIDMwMSxcbiAgICAzMDgsXG4gICAgNDA0LFxuICAgIDQwNSxcbiAgICA0MTAsXG4gICAgNDE0LFxuICAgIDUwMSxcbl0pO1xuXG4vKipcbiAqIFNldCBvZiBIVFRQIHN0YXR1cyBjb2RlcyB0aGF0IHRoZSBjYWNoZSBpbXBsZW1lbnRhdGlvbiB1bmRlcnN0YW5kcy5cbiAqIE5vdGU6IFRoaXMgaW1wbGVtZW50YXRpb24gZG9lcyBub3QgdW5kZXJzdGFuZCBwYXJ0aWFsIHJlc3BvbnNlcyAoMjA2KS5cbiAqIEB0eXBlIHtTZXQ8bnVtYmVyPn1cbiAqL1xuY29uc3QgdW5kZXJzdG9vZFN0YXR1c2VzID0gbmV3IFNldChbXG4gICAgMjAwLFxuICAgIDIwMyxcbiAgICAyMDQsXG4gICAgMzAwLFxuICAgIDMwMSxcbiAgICAzMDIsXG4gICAgMzAzLFxuICAgIDMwNyxcbiAgICAzMDgsXG4gICAgNDA0LFxuICAgIDQwNSxcbiAgICA0MTAsXG4gICAgNDE0LFxuICAgIDUwMSxcbl0pO1xuXG4vKipcbiAqIFNldCBvZiBIVFRQIGVycm9yIHN0YXR1cyBjb2Rlcy5cbiAqIEB0eXBlIHtTZXQ8bnVtYmVyPn1cbiAqL1xuY29uc3QgZXJyb3JTdGF0dXNDb2RlcyA9IG5ldyBTZXQoW1xuICAgIDUwMCxcbiAgICA1MDIsXG4gICAgNTAzLFxuICAgIDUwNCxcbl0pO1xuXG4vKipcbiAqIE9iamVjdCByZXByZXNlbnRpbmcgaG9wLWJ5LWhvcCBoZWFkZXJzIHRoYXQgc2hvdWxkIGJlIHJlbW92ZWQuXG4gKiBAdHlwZSB7UmVjb3JkPHN0cmluZywgYm9vbGVhbj59XG4gKi9cbmNvbnN0IGhvcEJ5SG9wSGVhZGVycyA9IHtcbiAgICBkYXRlOiB0cnVlLCAvLyBpbmNsdWRlZCwgYmVjYXVzZSB3ZSBhZGQgQWdlIHVwZGF0ZSBEYXRlXG4gICAgY29ubmVjdGlvbjogdHJ1ZSxcbiAgICAna2VlcC1hbGl2ZSc6IHRydWUsXG4gICAgJ3Byb3h5LWF1dGhlbnRpY2F0ZSc6IHRydWUsXG4gICAgJ3Byb3h5LWF1dGhvcml6YXRpb24nOiB0cnVlLFxuICAgIHRlOiB0cnVlLFxuICAgIHRyYWlsZXI6IHRydWUsXG4gICAgJ3RyYW5zZmVyLWVuY29kaW5nJzogdHJ1ZSxcbiAgICB1cGdyYWRlOiB0cnVlLFxufTtcblxuLyoqXG4gKiBIZWFkZXJzIHRoYXQgYXJlIGV4Y2x1ZGVkIGZyb20gcmV2YWxpZGF0aW9uIHVwZGF0ZS5cbiAqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBib29sZWFuPn1cbiAqL1xuY29uc3QgZXhjbHVkZWRGcm9tUmV2YWxpZGF0aW9uVXBkYXRlID0ge1xuICAgIC8vIFNpbmNlIHRoZSBvbGQgYm9keSBpcyByZXVzZWQsIGl0IGRvZXNuJ3QgbWFrZSBzZW5zZSB0byBjaGFuZ2UgcHJvcGVydGllcyBvZiB0aGUgYm9keVxuICAgICdjb250ZW50LWxlbmd0aCc6IHRydWUsXG4gICAgJ2NvbnRlbnQtZW5jb2RpbmcnOiB0cnVlLFxuICAgICd0cmFuc2Zlci1lbmNvZGluZyc6IHRydWUsXG4gICAgJ2NvbnRlbnQtcmFuZ2UnOiB0cnVlLFxufTtcblxuLyoqXG4gKiBDb252ZXJ0cyBhIHN0cmluZyB0byBhIG51bWJlciBvciByZXR1cm5zIHplcm8gaWYgdGhlIGNvbnZlcnNpb24gZmFpbHMuXG4gKiBAcGFyYW0ge3N0cmluZ30gcyAtIFRoZSBzdHJpbmcgdG8gY29udmVydC5cbiAqIEByZXR1cm5zIHtudW1iZXJ9IFRoZSBwYXJzZWQgbnVtYmVyIG9yIDAuXG4gKi9cbmZ1bmN0aW9uIHRvTnVtYmVyT3JaZXJvKHMpIHtcbiAgICBjb25zdCBuID0gcGFyc2VJbnQocywgMTApO1xuICAgIHJldHVybiBpc0Zpbml0ZShuKSA/IG4gOiAwO1xufVxuXG4vKipcbiAqIERldGVybWluZXMgaWYgdGhlIGdpdmVuIHJlc3BvbnNlIGlzIGFuIGVycm9yIHJlc3BvbnNlLlxuICogSW1wbGVtZW50cyBSRkMgNTg2MSBiZWhhdmlvci5cbiAqIEBwYXJhbSB7SHR0cFJlc3BvbnNlfHVuZGVmaW5lZH0gcmVzcG9uc2UgLSBUaGUgSFRUUCByZXNwb25zZSBvYmplY3QuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn0gdHJ1ZSBpZiB0aGUgcmVzcG9uc2UgaXMgYW4gZXJyb3Igb3IgdW5kZWZpbmVkLCBmYWxzZSBvdGhlcndpc2UuXG4gKi9cbmZ1bmN0aW9uIGlzRXJyb3JSZXNwb25zZShyZXNwb25zZSkge1xuICAgIC8vIGNvbnNpZGVyIHVuZGVmaW5lZCByZXNwb25zZSBhcyBmYXVsdHlcbiAgICBpZiAoIXJlc3BvbnNlKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICByZXR1cm4gZXJyb3JTdGF0dXNDb2Rlcy5oYXMocmVzcG9uc2Uuc3RhdHVzKTtcbn1cblxuLyoqXG4gKiBQYXJzZXMgYSBDYWNoZS1Db250cm9sIGhlYWRlciBzdHJpbmcgaW50byBhbiBvYmplY3QuXG4gKiBAcGFyYW0ge3N0cmluZ30gW2hlYWRlcl0gLSBUaGUgQ2FjaGUtQ29udHJvbCBoZWFkZXIgdmFsdWUuXG4gKiBAcmV0dXJucyB7UmVjb3JkPHN0cmluZywgc3RyaW5nfGJvb2xlYW4+fSBBbiBvYmplY3QgcmVwcmVzZW50aW5nIENhY2hlLUNvbnRyb2wgZGlyZWN0aXZlcy5cbiAqL1xuZnVuY3Rpb24gcGFyc2VDYWNoZUNvbnRyb2woaGVhZGVyKSB7XG4gICAgLyoqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmd8Ym9vbGVhbj59ICovXG4gICAgY29uc3QgY2MgPSB7fTtcbiAgICBpZiAoIWhlYWRlcikgcmV0dXJuIGNjO1xuXG4gICAgLy8gVE9ETzogV2hlbiB0aGVyZSBpcyBtb3JlIHRoYW4gb25lIHZhbHVlIHByZXNlbnQgZm9yIGEgZ2l2ZW4gZGlyZWN0aXZlIChlLmcuLCB0d28gRXhwaXJlcyBoZWFkZXIgZmllbGRzLCBtdWx0aXBsZSBDYWNoZS1Db250cm9sOiBtYXgtYWdlIGRpcmVjdGl2ZXMpLFxuICAgIC8vIHRoZSBkaXJlY3RpdmUncyB2YWx1ZSBpcyBjb25zaWRlcmVkIGludmFsaWQuIENhY2hlcyBhcmUgZW5jb3VyYWdlZCB0byBjb25zaWRlciByZXNwb25zZXMgdGhhdCBoYXZlIGludmFsaWQgZnJlc2huZXNzIGluZm9ybWF0aW9uIHRvIGJlIHN0YWxlXG4gICAgY29uc3QgcGFydHMgPSBoZWFkZXIudHJpbSgpLnNwbGl0KC8sLyk7XG4gICAgZm9yIChjb25zdCBwYXJ0IG9mIHBhcnRzKSB7XG4gICAgICAgIGNvbnN0IFtrLCB2XSA9IHBhcnQuc3BsaXQoLz0vLCAyKTtcbiAgICAgICAgY2Nbay50cmltKCldID0gdiA9PT0gdW5kZWZpbmVkID8gdHJ1ZSA6IHYudHJpbSgpLnJlcGxhY2UoL15cInxcIiQvZywgJycpO1xuICAgIH1cblxuICAgIHJldHVybiBjYztcbn1cblxuLyoqXG4gKiBGb3JtYXRzIGEgQ2FjaGUtQ29udHJvbCBkaXJlY3RpdmVzIG9iamVjdCBpbnRvIGEgaGVhZGVyIHN0cmluZy5cbiAqIEBwYXJhbSB7UmVjb3JkPHN0cmluZywgc3RyaW5nfGJvb2xlYW4+fSBjYyAtIFRoZSBDYWNoZS1Db250cm9sIGRpcmVjdGl2ZXMuXG4gKiBAcmV0dXJucyB7c3RyaW5nfHVuZGVmaW5lZH0gQSBmb3JtYXR0ZWQgQ2FjaGUtQ29udHJvbCBoZWFkZXIgc3RyaW5nIG9yIHVuZGVmaW5lZCBpZiBlbXB0eS5cbiAqL1xuZnVuY3Rpb24gZm9ybWF0Q2FjaGVDb250cm9sKGNjKSB7XG4gICAgbGV0IHBhcnRzID0gW107XG4gICAgZm9yIChjb25zdCBrIGluIGNjKSB7XG4gICAgICAgIGNvbnN0IHYgPSBjY1trXTtcbiAgICAgICAgcGFydHMucHVzaCh2ID09PSB0cnVlID8gayA6IGsgKyAnPScgKyB2KTtcbiAgICB9XG4gICAgaWYgKCFwYXJ0cy5sZW5ndGgpIHtcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG4gICAgcmV0dXJuIHBhcnRzLmpvaW4oJywgJyk7XG59XG5cbm1vZHVsZS5leHBvcnRzID0gY2xhc3MgQ2FjaGVQb2xpY3kge1xuICAgIC8qKlxuICAgICAqIENyZWF0ZXMgYSBuZXcgQ2FjaGVQb2xpY3kgaW5zdGFuY2UuXG4gICAgICogQHBhcmFtIHtIdHRwUmVxdWVzdH0gcmVxIC0gSW5jb21pbmcgY2xpZW50IHJlcXVlc3QuXG4gICAgICogQHBhcmFtIHtIdHRwUmVzcG9uc2V9IHJlcyAtIFJlY2VpdmVkIHNlcnZlciByZXNwb25zZS5cbiAgICAgKiBAcGFyYW0ge09iamVjdH0gW29wdGlvbnM9e31dIC0gQ29uZmlndXJhdGlvbiBvcHRpb25zLlxuICAgICAqIEBwYXJhbSB7Ym9vbGVhbn0gW29wdGlvbnMuc2hhcmVkPXRydWVdIC0gSXMgdGhlIGNhY2hlIHNoYXJlZCAoYSBwdWJsaWMgcHJveHkpPyBgZmFsc2VgIGZvciBwZXJzb25hbCBicm93c2VyIGNhY2hlcy5cbiAgICAgKiBAcGFyYW0ge251bWJlcn0gW29wdGlvbnMuY2FjaGVIZXVyaXN0aWM9MC4xXSAtIEZhbGxiYWNrIGhldXJpc3RpYyAoYWdlIGZyYWN0aW9uKSBmb3IgY2FjaGUgZHVyYXRpb24uXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IFtvcHRpb25zLmltbXV0YWJsZU1pblRpbWVUb0xpdmU9ODY0MDAwMDBdIC0gTWluaW11bSBUVEwgZm9yIGltbXV0YWJsZSByZXNwb25zZXMgaW4gbWlsbGlzZWNvbmRzLlxuICAgICAqIEBwYXJhbSB7Ym9vbGVhbn0gW29wdGlvbnMuaWdub3JlQ2FyZ29DdWx0PWZhbHNlXSAtIERldGVjdCBub25zZW5zZSBjYWNoZSBoZWFkZXJzLCBhbmQgb3ZlcnJpZGUgdGhlbS5cbiAgICAgKiBAcGFyYW0ge2FueX0gW29wdGlvbnMuX2Zyb21PYmplY3RdIC0gSW50ZXJuYWwgcGFyYW1ldGVyIGZvciBkZXNlcmlhbGl6YXRpb24uIERvIG5vdCB1c2UuXG4gICAgICovXG4gICAgY29uc3RydWN0b3IoXG4gICAgICAgIHJlcSxcbiAgICAgICAgcmVzLFxuICAgICAgICB7XG4gICAgICAgICAgICBzaGFyZWQsXG4gICAgICAgICAgICBjYWNoZUhldXJpc3RpYyxcbiAgICAgICAgICAgIGltbXV0YWJsZU1pblRpbWVUb0xpdmUsXG4gICAgICAgICAgICBpZ25vcmVDYXJnb0N1bHQsXG4gICAgICAgICAgICBfZnJvbU9iamVjdCxcbiAgICAgICAgfSA9IHt9XG4gICAgKSB7XG4gICAgICAgIGlmIChfZnJvbU9iamVjdCkge1xuICAgICAgICAgICAgdGhpcy5fZnJvbU9iamVjdChfZnJvbU9iamVjdCk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIXJlcyB8fCAhcmVzLmhlYWRlcnMpIHtcbiAgICAgICAgICAgIHRocm93IEVycm9yKCdSZXNwb25zZSBoZWFkZXJzIG1pc3NpbmcnKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9hc3NlcnRSZXF1ZXN0SGFzSGVhZGVycyhyZXEpO1xuXG4gICAgICAgIC8qKiBAdHlwZSB7bnVtYmVyfSBUaW1lc3RhbXAgd2hlbiB0aGUgcmVzcG9uc2Ugd2FzIHJlY2VpdmVkICovXG4gICAgICAgIHRoaXMuX3Jlc3BvbnNlVGltZSA9IHRoaXMubm93KCk7XG4gICAgICAgIC8qKiBAdHlwZSB7Ym9vbGVhbn0gSW5kaWNhdGVzIGlmIHRoZSBjYWNoZSBpcyBzaGFyZWQgKi9cbiAgICAgICAgdGhpcy5faXNTaGFyZWQgPSBzaGFyZWQgIT09IGZhbHNlO1xuICAgICAgICAvKiogQHR5cGUge2Jvb2xlYW59IEluZGljYXRlcyBpZiBsZWdhY3kgY2FyZ28gY3VsdCBkaXJlY3RpdmVzIHNob3VsZCBiZSBpZ25vcmVkICovXG4gICAgICAgIHRoaXMuX2lnbm9yZUNhcmdvQ3VsdCA9ICEhaWdub3JlQ2FyZ29DdWx0O1xuICAgICAgICAvKiogQHR5cGUge251bWJlcn0gSGV1cmlzdGljIGNhY2hlIGZyYWN0aW9uICovXG4gICAgICAgIHRoaXMuX2NhY2hlSGV1cmlzdGljID1cbiAgICAgICAgICAgIHVuZGVmaW5lZCAhPT0gY2FjaGVIZXVyaXN0aWMgPyBjYWNoZUhldXJpc3RpYyA6IDAuMTsgLy8gMTAlIG1hdGNoZXMgSUVcbiAgICAgICAgLyoqIEB0eXBlIHtudW1iZXJ9IE1pbmltdW0gVFRMIGZvciBpbW11dGFibGUgcmVzcG9uc2VzIGluIG1zICovXG4gICAgICAgIHRoaXMuX2ltbXV0YWJsZU1pblR0bCA9XG4gICAgICAgICAgICB1bmRlZmluZWQgIT09IGltbXV0YWJsZU1pblRpbWVUb0xpdmVcbiAgICAgICAgICAgICAgICA/IGltbXV0YWJsZU1pblRpbWVUb0xpdmVcbiAgICAgICAgICAgICAgICA6IDI0ICogMzYwMCAqIDEwMDA7XG5cbiAgICAgICAgLyoqIEB0eXBlIHtudW1iZXJ9IEhUVFAgc3RhdHVzIGNvZGUgKi9cbiAgICAgICAgdGhpcy5fc3RhdHVzID0gJ3N0YXR1cycgaW4gcmVzID8gcmVzLnN0YXR1cyA6IDIwMDtcbiAgICAgICAgLyoqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSBSZXNwb25zZSBoZWFkZXJzICovXG4gICAgICAgIHRoaXMuX3Jlc0hlYWRlcnMgPSByZXMuaGVhZGVycztcbiAgICAgICAgLyoqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmd8Ym9vbGVhbj59IFBhcnNlZCBDYWNoZS1Db250cm9sIGRpcmVjdGl2ZXMgZnJvbSByZXNwb25zZSAqL1xuICAgICAgICB0aGlzLl9yZXNjYyA9IHBhcnNlQ2FjaGVDb250cm9sKHJlcy5oZWFkZXJzWydjYWNoZS1jb250cm9sJ10pO1xuICAgICAgICAvKiogQHR5cGUge3N0cmluZ30gSFRUUCBtZXRob2QgKGUuZy4sIEdFVCwgUE9TVCkgKi9cbiAgICAgICAgdGhpcy5fbWV0aG9kID0gJ21ldGhvZCcgaW4gcmVxID8gcmVxLm1ldGhvZCA6ICdHRVQnO1xuICAgICAgICAvKiogQHR5cGUge3N0cmluZ30gUmVxdWVzdCBVUkwgKi9cbiAgICAgICAgdGhpcy5fdXJsID0gcmVxLnVybDtcbiAgICAgICAgLyoqIEB0eXBlIHtzdHJpbmd9IEhvc3QgaGVhZGVyIGZyb20gdGhlIHJlcXVlc3QgKi9cbiAgICAgICAgdGhpcy5faG9zdCA9IHJlcS5oZWFkZXJzLmhvc3Q7XG4gICAgICAgIC8qKiBAdHlwZSB7Ym9vbGVhbn0gV2hldGhlciB0aGUgcmVxdWVzdCBkb2VzIG5vdCBpbmNsdWRlIGFuIEF1dGhvcml6YXRpb24gaGVhZGVyICovXG4gICAgICAgIHRoaXMuX25vQXV0aG9yaXphdGlvbiA9ICFyZXEuaGVhZGVycy5hdXRob3JpemF0aW9uO1xuICAgICAgICAvKiogQHR5cGUge1JlY29yZDxzdHJpbmcsIHN0cmluZz58bnVsbH0gUmVxdWVzdCBoZWFkZXJzIHVzZWQgZm9yIFZhcnkgbWF0Y2hpbmcgKi9cbiAgICAgICAgdGhpcy5fcmVxSGVhZGVycyA9IHJlcy5oZWFkZXJzLnZhcnkgPyByZXEuaGVhZGVycyA6IG51bGw7IC8vIERvbid0IGtlZXAgYWxsIHJlcXVlc3QgaGVhZGVycyBpZiB0aGV5IHdvbid0IGJlIHVzZWRcbiAgICAgICAgLyoqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmd8Ym9vbGVhbj59IFBhcnNlZCBDYWNoZS1Db250cm9sIGRpcmVjdGl2ZXMgZnJvbSByZXF1ZXN0ICovXG4gICAgICAgIHRoaXMuX3JlcWNjID0gcGFyc2VDYWNoZUNvbnRyb2wocmVxLmhlYWRlcnNbJ2NhY2hlLWNvbnRyb2wnXSk7XG5cbiAgICAgICAgLy8gQXNzdW1lIHRoYXQgaWYgc29tZW9uZSB1c2VzIGxlZ2FjeSwgbm9uLXN0YW5kYXJkIHVuY2VjZXNzYXJ5IG9wdGlvbnMgdGhleSBkb24ndCB1bmRlcnN0YW5kIGNhY2hpbmcsXG4gICAgICAgIC8vIHNvIHRoZXJlJ3Mgbm8gcG9pbnQgc3RyaWNseSBhZGhlcmluZyB0byB0aGUgYmxpbmRseSBjb3B5JnBhc3RlZCBkaXJlY3RpdmVzLlxuICAgICAgICBpZiAoXG4gICAgICAgICAgICB0aGlzLl9pZ25vcmVDYXJnb0N1bHQgJiZcbiAgICAgICAgICAgICdwcmUtY2hlY2snIGluIHRoaXMuX3Jlc2NjICYmXG4gICAgICAgICAgICAncG9zdC1jaGVjaycgaW4gdGhpcy5fcmVzY2NcbiAgICAgICAgKSB7XG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fcmVzY2NbJ3ByZS1jaGVjayddO1xuICAgICAgICAgICAgZGVsZXRlIHRoaXMuX3Jlc2NjWydwb3N0LWNoZWNrJ107XG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fcmVzY2NbJ25vLWNhY2hlJ107XG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fcmVzY2NbJ25vLXN0b3JlJ107XG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fcmVzY2NbJ211c3QtcmV2YWxpZGF0ZSddO1xuICAgICAgICAgICAgdGhpcy5fcmVzSGVhZGVycyA9IE9iamVjdC5hc3NpZ24oe30sIHRoaXMuX3Jlc0hlYWRlcnMsIHtcbiAgICAgICAgICAgICAgICAnY2FjaGUtY29udHJvbCc6IGZvcm1hdENhY2hlQ29udHJvbCh0aGlzLl9yZXNjYyksXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLl9yZXNIZWFkZXJzLmV4cGlyZXM7XG4gICAgICAgICAgICBkZWxldGUgdGhpcy5fcmVzSGVhZGVycy5wcmFnbWE7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBXaGVuIHRoZSBDYWNoZS1Db250cm9sIGhlYWRlciBmaWVsZCBpcyBub3QgcHJlc2VudCBpbiBhIHJlcXVlc3QsIGNhY2hlcyBNVVNUIGNvbnNpZGVyIHRoZSBuby1jYWNoZSByZXF1ZXN0IHByYWdtYS1kaXJlY3RpdmVcbiAgICAgICAgLy8gYXMgaGF2aW5nIHRoZSBzYW1lIGVmZmVjdCBhcyBpZiBcIkNhY2hlLUNvbnRyb2w6IG5vLWNhY2hlXCIgd2VyZSBwcmVzZW50IChzZWUgU2VjdGlvbiA1LjIuMSkuXG4gICAgICAgIGlmIChcbiAgICAgICAgICAgIHJlcy5oZWFkZXJzWydjYWNoZS1jb250cm9sJ10gPT0gbnVsbCAmJlxuICAgICAgICAgICAgL25vLWNhY2hlLy50ZXN0KHJlcy5oZWFkZXJzLnByYWdtYSlcbiAgICAgICAgKSB7XG4gICAgICAgICAgICB0aGlzLl9yZXNjY1snbm8tY2FjaGUnXSA9IHRydWU7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBZb3UgY2FuIG1vbmtleS1wYXRjaCBpdCBmb3IgdGVzdGluZy5cbiAgICAgKiBAcmV0dXJucyB7bnVtYmVyfSBDdXJyZW50IHRpbWUgaW4gbWlsbGlzZWNvbmRzLlxuICAgICAqL1xuICAgIG5vdygpIHtcbiAgICAgICAgcmV0dXJuIERhdGUubm93KCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogRGV0ZXJtaW5lcyBpZiB0aGUgcmVzcG9uc2UgaXMgc3RvcmFibGUgaW4gYSBjYWNoZS5cbiAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0gYGZhbHNlYCBpZiBjYW4gbmV2ZXIgYmUgY2FjaGVkLlxuICAgICAqL1xuICAgIHN0b3JhYmxlKCkge1xuICAgICAgICAvLyBUaGUgXCJuby1zdG9yZVwiIHJlcXVlc3QgZGlyZWN0aXZlIGluZGljYXRlcyB0aGF0IGEgY2FjaGUgTVVTVCBOT1Qgc3RvcmUgYW55IHBhcnQgb2YgZWl0aGVyIHRoaXMgcmVxdWVzdCBvciBhbnkgcmVzcG9uc2UgdG8gaXQuXG4gICAgICAgIHJldHVybiAhIShcbiAgICAgICAgICAgICF0aGlzLl9yZXFjY1snbm8tc3RvcmUnXSAmJlxuICAgICAgICAgICAgLy8gQSBjYWNoZSBNVVNUIE5PVCBzdG9yZSBhIHJlc3BvbnNlIHRvIGFueSByZXF1ZXN0LCB1bmxlc3M6XG4gICAgICAgICAgICAvLyBUaGUgcmVxdWVzdCBtZXRob2QgaXMgdW5kZXJzdG9vZCBieSB0aGUgY2FjaGUgYW5kIGRlZmluZWQgYXMgYmVpbmcgY2FjaGVhYmxlLCBhbmRcbiAgICAgICAgICAgICgnR0VUJyA9PT0gdGhpcy5fbWV0aG9kIHx8XG4gICAgICAgICAgICAgICAgJ0hFQUQnID09PSB0aGlzLl9tZXRob2QgfHxcbiAgICAgICAgICAgICAgICAoJ1BPU1QnID09PSB0aGlzLl9tZXRob2QgJiYgdGhpcy5faGFzRXhwbGljaXRFeHBpcmF0aW9uKCkpKSAmJlxuICAgICAgICAgICAgLy8gdGhlIHJlc3BvbnNlIHN0YXR1cyBjb2RlIGlzIHVuZGVyc3Rvb2QgYnkgdGhlIGNhY2hlLCBhbmRcbiAgICAgICAgICAgIHVuZGVyc3Rvb2RTdGF0dXNlcy5oYXModGhpcy5fc3RhdHVzKSAmJlxuICAgICAgICAgICAgLy8gdGhlIFwibm8tc3RvcmVcIiBjYWNoZSBkaXJlY3RpdmUgZG9lcyBub3QgYXBwZWFyIGluIHJlcXVlc3Qgb3IgcmVzcG9uc2UgaGVhZGVyIGZpZWxkcywgYW5kXG4gICAgICAgICAgICAhdGhpcy5fcmVzY2NbJ25vLXN0b3JlJ10gJiZcbiAgICAgICAgICAgIC8vIHRoZSBcInByaXZhdGVcIiByZXNwb25zZSBkaXJlY3RpdmUgZG9lcyBub3QgYXBwZWFyIGluIHRoZSByZXNwb25zZSwgaWYgdGhlIGNhY2hlIGlzIHNoYXJlZCwgYW5kXG4gICAgICAgICAgICAoIXRoaXMuX2lzU2hhcmVkIHx8ICF0aGlzLl9yZXNjYy5wcml2YXRlKSAmJlxuICAgICAgICAgICAgLy8gdGhlIEF1dGhvcml6YXRpb24gaGVhZGVyIGZpZWxkIGRvZXMgbm90IGFwcGVhciBpbiB0aGUgcmVxdWVzdCwgaWYgdGhlIGNhY2hlIGlzIHNoYXJlZCxcbiAgICAgICAgICAgICghdGhpcy5faXNTaGFyZWQgfHxcbiAgICAgICAgICAgICAgICB0aGlzLl9ub0F1dGhvcml6YXRpb24gfHxcbiAgICAgICAgICAgICAgICB0aGlzLl9hbGxvd3NTdG9yaW5nQXV0aGVudGljYXRlZCgpKSAmJlxuICAgICAgICAgICAgLy8gdGhlIHJlc3BvbnNlIGVpdGhlcjpcbiAgICAgICAgICAgIC8vIGNvbnRhaW5zIGFuIEV4cGlyZXMgaGVhZGVyIGZpZWxkLCBvclxuICAgICAgICAgICAgKHRoaXMuX3Jlc0hlYWRlcnMuZXhwaXJlcyB8fFxuICAgICAgICAgICAgICAgIC8vIGNvbnRhaW5zIGEgbWF4LWFnZSByZXNwb25zZSBkaXJlY3RpdmUsIG9yXG4gICAgICAgICAgICAgICAgLy8gY29udGFpbnMgYSBzLW1heGFnZSByZXNwb25zZSBkaXJlY3RpdmUgYW5kIHRoZSBjYWNoZSBpcyBzaGFyZWQsIG9yXG4gICAgICAgICAgICAgICAgLy8gY29udGFpbnMgYSBwdWJsaWMgcmVzcG9uc2UgZGlyZWN0aXZlLlxuICAgICAgICAgICAgICAgIHRoaXMuX3Jlc2NjWydtYXgtYWdlJ10gfHxcbiAgICAgICAgICAgICAgICAodGhpcy5faXNTaGFyZWQgJiYgdGhpcy5fcmVzY2NbJ3MtbWF4YWdlJ10pIHx8XG4gICAgICAgICAgICAgICAgdGhpcy5fcmVzY2MucHVibGljIHx8XG4gICAgICAgICAgICAgICAgLy8gaGFzIGEgc3RhdHVzIGNvZGUgdGhhdCBpcyBkZWZpbmVkIGFzIGNhY2hlYWJsZSBieSBkZWZhdWx0XG4gICAgICAgICAgICAgICAgc3RhdHVzQ29kZUNhY2hlYWJsZUJ5RGVmYXVsdC5oYXModGhpcy5fc3RhdHVzKSlcbiAgICAgICAgKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0gdHJ1ZSBpZiBleHBpcmF0aW9uIGlzIGV4cGxpY2l0bHkgZGVmaW5lZC5cbiAgICAgKi9cbiAgICBfaGFzRXhwbGljaXRFeHBpcmF0aW9uKCkge1xuICAgICAgICAvLyA0LjIuMSBDYWxjdWxhdGluZyBGcmVzaG5lc3MgTGlmZXRpbWVcbiAgICAgICAgcmV0dXJuICEhKFxuICAgICAgICAgICAgKHRoaXMuX2lzU2hhcmVkICYmIHRoaXMuX3Jlc2NjWydzLW1heGFnZSddKSB8fFxuICAgICAgICAgICAgdGhpcy5fcmVzY2NbJ21heC1hZ2UnXSB8fFxuICAgICAgICAgICAgdGhpcy5fcmVzSGVhZGVycy5leHBpcmVzXG4gICAgICAgICk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogQHBhcmFtIHtIdHRwUmVxdWVzdH0gcmVxIC0gYSByZXF1ZXN0XG4gICAgICogQHRocm93cyB7RXJyb3J9IGlmIHRoZSBoZWFkZXJzIGFyZSBtaXNzaW5nLlxuICAgICAqL1xuICAgIF9hc3NlcnRSZXF1ZXN0SGFzSGVhZGVycyhyZXEpIHtcbiAgICAgICAgaWYgKCFyZXEgfHwgIXJlcS5oZWFkZXJzKSB7XG4gICAgICAgICAgICB0aHJvdyBFcnJvcignUmVxdWVzdCBoZWFkZXJzIG1pc3NpbmcnKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIENoZWNrcyBpZiB0aGUgcmVxdWVzdCBtYXRjaGVzIHRoZSBjYWNoZSBhbmQgY2FuIGJlIHNhdGlzZmllZCBmcm9tIHRoZSBjYWNoZSBpbW1lZGlhdGVseSxcbiAgICAgKiB3aXRob3V0IGhhdmluZyB0byBtYWtlIGEgcmVxdWVzdCB0byB0aGUgc2VydmVyLlxuICAgICAqXG4gICAgICogVGhpcyBkb2Vzbid0IHN1cHBvcnQgYHN0YWxlLXdoaWxlLXJldmFsaWRhdGVgLiBTZWUgYGV2YWx1YXRlUmVxdWVzdCgpYCBmb3IgYSBtb3JlIGNvbXBsZXRlIHNvbHV0aW9uLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtIdHRwUmVxdWVzdH0gcmVxIC0gVGhlIG5ldyBpbmNvbWluZyBIVFRQIHJlcXVlc3QuXG4gICAgICogQHJldHVybnMge2Jvb2xlYW59IGB0cnVlYGAgaWYgdGhlIGNhY2hlZCByZXNwb25zZSB1c2VkIHRvIGNvbnN0cnVjdCB0aGlzIGNhY2hlIHBvbGljeSBzYXRpc2ZpZXMgdGhlIHJlcXVlc3Qgd2l0aG91dCByZXZhbGlkYXRpb24uXG4gICAgICovXG4gICAgc2F0aXNmaWVzV2l0aG91dFJldmFsaWRhdGlvbihyZXEpIHtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gdGhpcy5ldmFsdWF0ZVJlcXVlc3QocmVxKTtcbiAgICAgICAgcmV0dXJuICFyZXN1bHQucmV2YWxpZGF0aW9uO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEBwYXJhbSB7e2hlYWRlcnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4sIHN5bmNocm9ub3VzOiBib29sZWFufXx1bmRlZmluZWR9IHJldmFsaWRhdGlvbiAtIFJldmFsaWRhdGlvbiBpbmZvcm1hdGlvbiwgaWYgYW55LlxuICAgICAqIEByZXR1cm5zIHt7cmVzcG9uc2U6IHtoZWFkZXJzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSwgcmV2YWxpZGF0aW9uOiB7aGVhZGVyczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiwgc3luY2hyb25vdXM6IGJvb2xlYW59fHVuZGVmaW5lZH19IEFuIG9iamVjdCB3aXRoIGEgY2FjaGVkIHJlc3BvbnNlIGhlYWRlcnMgYW5kIHJldmFsaWRhdGlvbiBpbmZvLlxuICAgICAqL1xuICAgIF9ldmFsdWF0ZVJlcXVlc3RIaXRSZXN1bHQocmV2YWxpZGF0aW9uKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICByZXNwb25zZToge1xuICAgICAgICAgICAgICAgIGhlYWRlcnM6IHRoaXMucmVzcG9uc2VIZWFkZXJzKCksXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgcmV2YWxpZGF0aW9uLFxuICAgICAgICB9O1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEBwYXJhbSB7SHR0cFJlcXVlc3R9IHJlcXVlc3QgLSBuZXcgaW5jb21pbmdcbiAgICAgKiBAcGFyYW0ge2Jvb2xlYW59IHN5bmNocm9ub3VzIC0gd2hldGhlciByZXZhbGlkYXRpb24gbXVzdCBiZSBzeW5jaHJvbm91cyAobm90IHMtdy1yKS5cbiAgICAgKiBAcmV0dXJucyB7e2hlYWRlcnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4sIHN5bmNocm9ub3VzOiBib29sZWFufX0gQW4gb2JqZWN0IHdpdGggcmV2YWxpZGF0aW9uIGhlYWRlcnMgYW5kIGEgc3luY2hyb25vdXMgZmxhZy5cbiAgICAgKi9cbiAgICBfZXZhbHVhdGVSZXF1ZXN0UmV2YWxpZGF0aW9uKHJlcXVlc3QsIHN5bmNocm9ub3VzKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBzeW5jaHJvbm91cyxcbiAgICAgICAgICAgIGhlYWRlcnM6IHRoaXMucmV2YWxpZGF0aW9uSGVhZGVycyhyZXF1ZXN0KSxcbiAgICAgICAgfTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBAcGFyYW0ge0h0dHBSZXF1ZXN0fSByZXF1ZXN0IC0gbmV3IGluY29taW5nXG4gICAgICogQHJldHVybnMge3tyZXNwb25zZTogdW5kZWZpbmVkLCByZXZhbGlkYXRpb246IHtoZWFkZXJzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+LCBzeW5jaHJvbm91czogYm9vbGVhbn19fSBBbiBvYmplY3QgaW5kaWNhdGluZyBubyBjYWNoZWQgcmVzcG9uc2UgYW5kIHJldmFsaWRhdGlvbiBkZXRhaWxzLlxuICAgICAqL1xuICAgIF9ldmFsdWF0ZVJlcXVlc3RNaXNzUmVzdWx0KHJlcXVlc3QpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHJlc3BvbnNlOiB1bmRlZmluZWQsXG4gICAgICAgICAgICByZXZhbGlkYXRpb246IHRoaXMuX2V2YWx1YXRlUmVxdWVzdFJldmFsaWRhdGlvbihyZXF1ZXN0LCB0cnVlKSxcbiAgICAgICAgfTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBDaGVja3MgaWYgdGhlIGdpdmVuIHJlcXVlc3QgbWF0Y2hlcyB0aGlzIGNhY2hlIGVudHJ5LCBhbmQgaG93IHRoZSBjYWNoZSBjYW4gYmUgdXNlZCB0byBzYXRpc2Z5IGl0LiBSZXR1cm5zIGFuIG9iamVjdCB3aXRoOlxuICAgICAqXG4gICAgICogYGBgXG4gICAgICoge1xuICAgICAqICAgICAvLyBJZiBkZWZpbmVkLCB5b3UgbXVzdCBzZW5kIGEgcmVxdWVzdCB0byB0aGUgc2VydmVyLlxuICAgICAqICAgICByZXZhbGlkYXRpb246IHtcbiAgICAgKiAgICAgICAgIGhlYWRlcnM6IHt9LCAvLyBIVFRQIGhlYWRlcnMgdG8gdXNlIHdoZW4gc2VuZGluZyB0aGUgcmV2YWxpZGF0aW9uIHJlc3BvbnNlXG4gICAgICogICAgICAgICAvLyBJZiB0cnVlLCB5b3UgTVVTVCB3YWl0IGZvciBhIHJlc3BvbnNlIGZyb20gdGhlIHNlcnZlciBiZWZvcmUgdXNpbmcgdGhlIGNhY2hlXG4gICAgICogICAgICAgICAvLyBJZiBmYWxzZSwgdGhpcyBpcyBzdGFsZS13aGlsZS1yZXZhbGlkYXRlLiBUaGUgY2FjaGUgaXMgc3RhbGUsIGJ1dCB5b3UgY2FuIHVzZSBpdCB3aGlsZSB5b3UgdXBkYXRlIGl0IGFzeW5jaHJvbm91c2x5LlxuICAgICAqICAgICAgICAgc3luY2hyb25vdXM6IGJvb2wsXG4gICAgICogICAgIH0sXG4gICAgICogICAgIC8vIElmIGRlZmluZWQsIHlvdSBjYW4gdXNlIHRoaXMgY2FjaGVkIHJlc3BvbnNlLlxuICAgICAqICAgICByZXNwb25zZToge1xuICAgICAqICAgICAgICAgaGVhZGVyczoge30sIC8vIFVwZGF0ZWQgY2FjaGVkIEhUVFAgaGVhZGVycyB5b3UgbXVzdCB1c2Ugd2hlbiByZXNwb25kaW5nIHRvIHRoZSBjbGllbnRcbiAgICAgKiAgICAgfSxcbiAgICAgKiB9XG4gICAgICogYGBgXG4gICAgICogQHBhcmFtIHtIdHRwUmVxdWVzdH0gcmVxIC0gbmV3IGluY29taW5nIEhUVFAgcmVxdWVzdFxuICAgICAqIEByZXR1cm5zIHt7cmVzcG9uc2U6IHtoZWFkZXJzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fXx1bmRlZmluZWQsIHJldmFsaWRhdGlvbjoge2hlYWRlcnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4sIHN5bmNocm9ub3VzOiBib29sZWFufXx1bmRlZmluZWR9fSBBbiBvYmplY3QgY29udGFpbmluZyBrZXlzOlxuICAgICAqICAgLSByZXZhbGlkYXRpb246IHsgaGVhZGVyczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiwgc3luY2hyb25vdXM6IGJvb2xlYW4gfSBTZXQgaWYgeW91IHNob3VsZCBzZW5kIHRoaXMgdG8gdGhlIG9yaWdpbiBzZXJ2ZXJcbiAgICAgKiAgIC0gcmVzcG9uc2U6IHsgaGVhZGVyczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiB9IFNldCBpZiB5b3UgY2FuIHJlc3BvbmQgdG8gdGhlIGNsaWVudCB3aXRoIHRoZXNlIGNhY2hlZCBoZWFkZXJzXG4gICAgICovXG4gICAgZXZhbHVhdGVSZXF1ZXN0KHJlcSkge1xuICAgICAgICB0aGlzLl9hc3NlcnRSZXF1ZXN0SGFzSGVhZGVycyhyZXEpO1xuXG4gICAgICAgIC8vIEluIGFsbCBjaXJjdW1zdGFuY2VzLCBhIGNhY2hlIE1VU1QgTk9UIGlnbm9yZSB0aGUgbXVzdC1yZXZhbGlkYXRlIGRpcmVjdGl2ZVxuICAgICAgICBpZiAodGhpcy5fcmVzY2NbJ211c3QtcmV2YWxpZGF0ZSddKSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5fZXZhbHVhdGVSZXF1ZXN0TWlzc1Jlc3VsdChyZXEpO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCF0aGlzLl9yZXF1ZXN0TWF0Y2hlcyhyZXEsIGZhbHNlKSkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuX2V2YWx1YXRlUmVxdWVzdE1pc3NSZXN1bHQocmVxKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFdoZW4gcHJlc2VudGVkIHdpdGggYSByZXF1ZXN0LCBhIGNhY2hlIE1VU1QgTk9UIHJldXNlIGEgc3RvcmVkIHJlc3BvbnNlLCB1bmxlc3M6XG4gICAgICAgIC8vIHRoZSBwcmVzZW50ZWQgcmVxdWVzdCBkb2VzIG5vdCBjb250YWluIHRoZSBuby1jYWNoZSBwcmFnbWEgKFNlY3Rpb24gNS40KSwgbm9yIHRoZSBuby1jYWNoZSBjYWNoZSBkaXJlY3RpdmUsXG4gICAgICAgIC8vIHVubGVzcyB0aGUgc3RvcmVkIHJlc3BvbnNlIGlzIHN1Y2Nlc3NmdWxseSB2YWxpZGF0ZWQgKFNlY3Rpb24gNC4zKSwgYW5kXG4gICAgICAgIGNvbnN0IHJlcXVlc3RDQyA9IHBhcnNlQ2FjaGVDb250cm9sKHJlcS5oZWFkZXJzWydjYWNoZS1jb250cm9sJ10pO1xuXG4gICAgICAgIGlmIChyZXF1ZXN0Q0NbJ25vLWNhY2hlJ10gfHwgL25vLWNhY2hlLy50ZXN0KHJlcS5oZWFkZXJzLnByYWdtYSkpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLl9ldmFsdWF0ZVJlcXVlc3RNaXNzUmVzdWx0KHJlcSk7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAocmVxdWVzdENDWydtYXgtYWdlJ10gJiYgdGhpcy5hZ2UoKSA+IHRvTnVtYmVyT3JaZXJvKHJlcXVlc3RDQ1snbWF4LWFnZSddKSkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuX2V2YWx1YXRlUmVxdWVzdE1pc3NSZXN1bHQocmVxKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChyZXF1ZXN0Q0NbJ21pbi1mcmVzaCddICYmIHRoaXMubWF4QWdlKCkgLSB0aGlzLmFnZSgpIDwgdG9OdW1iZXJPclplcm8ocmVxdWVzdENDWydtaW4tZnJlc2gnXSkpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLl9ldmFsdWF0ZVJlcXVlc3RNaXNzUmVzdWx0KHJlcSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyB0aGUgc3RvcmVkIHJlc3BvbnNlIGlzIGVpdGhlcjpcbiAgICAgICAgLy8gZnJlc2gsIG9yIGFsbG93ZWQgdG8gYmUgc2VydmVkIHN0YWxlXG4gICAgICAgIGlmICh0aGlzLnN0YWxlKCkpIHtcbiAgICAgICAgICAgIC8vIElmIGEgdmFsdWUgaXMgcHJlc2VudCwgdGhlbiB0aGUgY2xpZW50IGlzIHdpbGxpbmcgdG8gYWNjZXB0IGEgcmVzcG9uc2UgdGhhdCBoYXNcbiAgICAgICAgICAgIC8vIGV4Y2VlZGVkIGl0cyBmcmVzaG5lc3MgbGlmZXRpbWUgYnkgbm8gbW9yZSB0aGFuIHRoZSBzcGVjaWZpZWQgbnVtYmVyIG9mIHNlY29uZHNcbiAgICAgICAgICAgIGNvbnN0IGFsbG93c1N0YWxlV2l0aG91dFJldmFsaWRhdGlvbiA9ICdtYXgtc3RhbGUnIGluIHJlcXVlc3RDQyAmJlxuICAgICAgICAgICAgICAgICh0cnVlID09PSByZXF1ZXN0Q0NbJ21heC1zdGFsZSddIHx8IHJlcXVlc3RDQ1snbWF4LXN0YWxlJ10gPiB0aGlzLmFnZSgpIC0gdGhpcy5tYXhBZ2UoKSk7XG5cbiAgICAgICAgICAgIGlmIChhbGxvd3NTdGFsZVdpdGhvdXRSZXZhbGlkYXRpb24pIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5fZXZhbHVhdGVSZXF1ZXN0SGl0UmVzdWx0KHVuZGVmaW5lZCk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmICh0aGlzLnVzZVN0YWxlV2hpbGVSZXZhbGlkYXRlKCkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5fZXZhbHVhdGVSZXF1ZXN0SGl0UmVzdWx0KHRoaXMuX2V2YWx1YXRlUmVxdWVzdFJldmFsaWRhdGlvbihyZXEsIGZhbHNlKSk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIHJldHVybiB0aGlzLl9ldmFsdWF0ZVJlcXVlc3RNaXNzUmVzdWx0KHJlcSk7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gdGhpcy5fZXZhbHVhdGVSZXF1ZXN0SGl0UmVzdWx0KHVuZGVmaW5lZCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogQHBhcmFtIHtIdHRwUmVxdWVzdH0gcmVxIC0gY2hlY2sgaWYgdGhpcyBpcyBmb3IgdGhlIHNhbWUgY2FjaGUgZW50cnlcbiAgICAgKiBAcGFyYW0ge2Jvb2xlYW59IGFsbG93SGVhZE1ldGhvZCAtIGFsbG93IGEgSEVBRCBtZXRob2QgdG8gbWF0Y2guXG4gICAgICogQHJldHVybnMge2Jvb2xlYW59IGB0cnVlYCBpZiB0aGUgcmVxdWVzdCBtYXRjaGVzLlxuICAgICAqL1xuICAgIF9yZXF1ZXN0TWF0Y2hlcyhyZXEsIGFsbG93SGVhZE1ldGhvZCkge1xuICAgICAgICAvLyBUaGUgcHJlc2VudGVkIGVmZmVjdGl2ZSByZXF1ZXN0IFVSSSBhbmQgdGhhdCBvZiB0aGUgc3RvcmVkIHJlc3BvbnNlIG1hdGNoLCBhbmRcbiAgICAgICAgcmV0dXJuICEhKFxuICAgICAgICAgICAgKCF0aGlzLl91cmwgfHwgdGhpcy5fdXJsID09PSByZXEudXJsKSAmJlxuICAgICAgICAgICAgdGhpcy5faG9zdCA9PT0gcmVxLmhlYWRlcnMuaG9zdCAmJlxuICAgICAgICAgICAgLy8gdGhlIHJlcXVlc3QgbWV0aG9kIGFzc29jaWF0ZWQgd2l0aCB0aGUgc3RvcmVkIHJlc3BvbnNlIGFsbG93cyBpdCB0byBiZSB1c2VkIGZvciB0aGUgcHJlc2VudGVkIHJlcXVlc3QsIGFuZFxuICAgICAgICAgICAgKCFyZXEubWV0aG9kIHx8XG4gICAgICAgICAgICAgICAgdGhpcy5fbWV0aG9kID09PSByZXEubWV0aG9kIHx8XG4gICAgICAgICAgICAgICAgKGFsbG93SGVhZE1ldGhvZCAmJiAnSEVBRCcgPT09IHJlcS5tZXRob2QpKSAmJlxuICAgICAgICAgICAgLy8gc2VsZWN0aW5nIGhlYWRlciBmaWVsZHMgbm9taW5hdGVkIGJ5IHRoZSBzdG9yZWQgcmVzcG9uc2UgKGlmIGFueSkgbWF0Y2ggdGhvc2UgcHJlc2VudGVkLCBhbmRcbiAgICAgICAgICAgIHRoaXMuX3ZhcnlNYXRjaGVzKHJlcSlcbiAgICAgICAgKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBEZXRlcm1pbmVzIHdoZXRoZXIgc3RvcmluZyBhdXRoZW50aWNhdGVkIHJlc3BvbnNlcyBpcyBhbGxvd2VkLlxuICAgICAqIEByZXR1cm5zIHtib29sZWFufSBgdHJ1ZWAgaWYgYWxsb3dlZC5cbiAgICAgKi9cbiAgICBfYWxsb3dzU3RvcmluZ0F1dGhlbnRpY2F0ZWQoKSB7XG4gICAgICAgIC8vIGZvbGxvd2luZyBDYWNoZS1Db250cm9sIHJlc3BvbnNlIGRpcmVjdGl2ZXMgKFNlY3Rpb24gNS4yLjIpIGhhdmUgc3VjaCBhbiBlZmZlY3Q6IG11c3QtcmV2YWxpZGF0ZSwgcHVibGljLCBhbmQgcy1tYXhhZ2UuXG4gICAgICAgIHJldHVybiAhIShcbiAgICAgICAgICAgIHRoaXMuX3Jlc2NjWydtdXN0LXJldmFsaWRhdGUnXSB8fFxuICAgICAgICAgICAgdGhpcy5fcmVzY2MucHVibGljIHx8XG4gICAgICAgICAgICB0aGlzLl9yZXNjY1sncy1tYXhhZ2UnXVxuICAgICAgICApO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIENoZWNrcyB3aGV0aGVyIHRoZSBWYXJ5IGhlYWRlciBpbiB0aGUgcmVzcG9uc2UgbWF0Y2hlcyB0aGUgbmV3IHJlcXVlc3QuXG4gICAgICogQHBhcmFtIHtIdHRwUmVxdWVzdH0gcmVxIC0gaW5jb21pbmcgSFRUUCByZXF1ZXN0XG4gICAgICogQHJldHVybnMge2Jvb2xlYW59IGB0cnVlYCBpZiB0aGUgdmFyeSBoZWFkZXJzIG1hdGNoLlxuICAgICAqL1xuICAgIF92YXJ5TWF0Y2hlcyhyZXEpIHtcbiAgICAgICAgaWYgKCF0aGlzLl9yZXNIZWFkZXJzLnZhcnkpIHtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gQSBWYXJ5IGhlYWRlciBmaWVsZC12YWx1ZSBvZiBcIipcIiBhbHdheXMgZmFpbHMgdG8gbWF0Y2hcbiAgICAgICAgaWYgKHRoaXMuX3Jlc0hlYWRlcnMudmFyeSA9PT0gJyonKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBmaWVsZHMgPSB0aGlzLl9yZXNIZWFkZXJzLnZhcnlcbiAgICAgICAgICAgIC50cmltKClcbiAgICAgICAgICAgIC50b0xvd2VyQ2FzZSgpXG4gICAgICAgICAgICAuc3BsaXQoL1xccyosXFxzKi8pO1xuICAgICAgICBmb3IgKGNvbnN0IG5hbWUgb2YgZmllbGRzKSB7XG4gICAgICAgICAgICBpZiAocmVxLmhlYWRlcnNbbmFtZV0gIT09IHRoaXMuX3JlcUhlYWRlcnNbbmFtZV0pIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBDcmVhdGVzIGEgY29weSBvZiB0aGUgZ2l2ZW4gaGVhZGVycyB3aXRob3V0IGFueSBob3AtYnktaG9wIGhlYWRlcnMuXG4gICAgICogQHBhcmFtIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSBpbkhlYWRlcnMgLSBvbGQgaGVhZGVycyBmcm9tIHRoZSBjYWNoZWQgcmVzcG9uc2VcbiAgICAgKiBAcmV0dXJucyB7UmVjb3JkPHN0cmluZywgc3RyaW5nPn0gQSBuZXcgaGVhZGVycyBvYmplY3Qgd2l0aG91dCBob3AtYnktaG9wIGhlYWRlcnMuXG4gICAgICovXG4gICAgX2NvcHlXaXRob3V0SG9wQnlIb3BIZWFkZXJzKGluSGVhZGVycykge1xuICAgICAgICAvKiogQHR5cGUge1JlY29yZDxzdHJpbmcsIHN0cmluZz59ICovXG4gICAgICAgIGNvbnN0IGhlYWRlcnMgPSB7fTtcbiAgICAgICAgZm9yIChjb25zdCBuYW1lIGluIGluSGVhZGVycykge1xuICAgICAgICAgICAgaWYgKGhvcEJ5SG9wSGVhZGVyc1tuYW1lXSkgY29udGludWU7XG4gICAgICAgICAgICBoZWFkZXJzW25hbWVdID0gaW5IZWFkZXJzW25hbWVdO1xuICAgICAgICB9XG4gICAgICAgIC8vIDkuMS4gIENvbm5lY3Rpb25cbiAgICAgICAgaWYgKGluSGVhZGVycy5jb25uZWN0aW9uKSB7XG4gICAgICAgICAgICBjb25zdCB0b2tlbnMgPSBpbkhlYWRlcnMuY29ubmVjdGlvbi50cmltKCkuc3BsaXQoL1xccyosXFxzKi8pO1xuICAgICAgICAgICAgZm9yIChjb25zdCBuYW1lIG9mIHRva2Vucykge1xuICAgICAgICAgICAgICAgIGRlbGV0ZSBoZWFkZXJzW25hbWVdO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChoZWFkZXJzLndhcm5pbmcpIHtcbiAgICAgICAgICAgIGNvbnN0IHdhcm5pbmdzID0gaGVhZGVycy53YXJuaW5nLnNwbGl0KC8sLykuZmlsdGVyKHdhcm5pbmcgPT4ge1xuICAgICAgICAgICAgICAgIHJldHVybiAhL15cXHMqMVswLTldWzAtOV0vLnRlc3Qod2FybmluZyk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmICghd2FybmluZ3MubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgZGVsZXRlIGhlYWRlcnMud2FybmluZztcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgaGVhZGVycy53YXJuaW5nID0gd2FybmluZ3Muam9pbignLCcpLnRyaW0oKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gaGVhZGVycztcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBSZXR1cm5zIHRoZSByZXNwb25zZSBoZWFkZXJzIGFkanVzdGVkIGZvciBzZXJ2aW5nIHRoZSBjYWNoZWQgcmVzcG9uc2UuXG4gICAgICogUmVtb3ZlcyBob3AtYnktaG9wIGhlYWRlcnMgYW5kIHVwZGF0ZXMgdGhlIEFnZSBhbmQgRGF0ZSBoZWFkZXJzLlxuICAgICAqIEByZXR1cm5zIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSBUaGUgYWRqdXN0ZWQgcmVzcG9uc2UgaGVhZGVycy5cbiAgICAgKi9cbiAgICByZXNwb25zZUhlYWRlcnMoKSB7XG4gICAgICAgIGNvbnN0IGhlYWRlcnMgPSB0aGlzLl9jb3B5V2l0aG91dEhvcEJ5SG9wSGVhZGVycyh0aGlzLl9yZXNIZWFkZXJzKTtcbiAgICAgICAgY29uc3QgYWdlID0gdGhpcy5hZ2UoKTtcblxuICAgICAgICAvLyBBIGNhY2hlIFNIT1VMRCBnZW5lcmF0ZSAxMTMgd2FybmluZyBpZiBpdCBoZXVyaXN0aWNhbGx5IGNob3NlIGEgZnJlc2huZXNzXG4gICAgICAgIC8vIGxpZmV0aW1lIGdyZWF0ZXIgdGhhbiAyNCBob3VycyBhbmQgdGhlIHJlc3BvbnNlJ3MgYWdlIGlzIGdyZWF0ZXIgdGhhbiAyNCBob3Vycy5cbiAgICAgICAgaWYgKFxuICAgICAgICAgICAgYWdlID4gMzYwMCAqIDI0ICYmXG4gICAgICAgICAgICAhdGhpcy5faGFzRXhwbGljaXRFeHBpcmF0aW9uKCkgJiZcbiAgICAgICAgICAgIHRoaXMubWF4QWdlKCkgPiAzNjAwICogMjRcbiAgICAgICAgKSB7XG4gICAgICAgICAgICBoZWFkZXJzLndhcm5pbmcgPVxuICAgICAgICAgICAgICAgIChoZWFkZXJzLndhcm5pbmcgPyBgJHtoZWFkZXJzLndhcm5pbmd9LCBgIDogJycpICtcbiAgICAgICAgICAgICAgICAnMTEzIC0gXCJyZmM3MjM0IDUuNS40XCInO1xuICAgICAgICB9XG4gICAgICAgIGhlYWRlcnMuYWdlID0gYCR7TWF0aC5yb3VuZChhZ2UpfWA7XG4gICAgICAgIGhlYWRlcnMuZGF0ZSA9IG5ldyBEYXRlKHRoaXMubm93KCkpLnRvVVRDU3RyaW5nKCk7XG4gICAgICAgIHJldHVybiBoZWFkZXJzO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIFJldHVybnMgdGhlIERhdGUgaGVhZGVyIHZhbHVlIGZyb20gdGhlIHJlc3BvbnNlIG9yIHRoZSBjdXJyZW50IHRpbWUgaWYgaW52YWxpZC5cbiAgICAgKiBAcmV0dXJucyB7bnVtYmVyfSBUaW1lc3RhbXAgKGluIG1pbGxpc2Vjb25kcykgcmVwcmVzZW50aW5nIHRoZSBEYXRlIGhlYWRlciBvciByZXNwb25zZSB0aW1lLlxuICAgICAqL1xuICAgIGRhdGUoKSB7XG4gICAgICAgIGNvbnN0IHNlcnZlckRhdGUgPSBEYXRlLnBhcnNlKHRoaXMuX3Jlc0hlYWRlcnMuZGF0ZSk7XG4gICAgICAgIGlmIChpc0Zpbml0ZShzZXJ2ZXJEYXRlKSkge1xuICAgICAgICAgICAgcmV0dXJuIHNlcnZlckRhdGU7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMuX3Jlc3BvbnNlVGltZTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBWYWx1ZSBvZiB0aGUgQWdlIGhlYWRlciwgaW4gc2Vjb25kcywgdXBkYXRlZCBmb3IgdGhlIGN1cnJlbnQgdGltZS5cbiAgICAgKiBNYXkgYmUgZnJhY3Rpb25hbC5cbiAgICAgKiBAcmV0dXJucyB7bnVtYmVyfSBUaGUgYWdlIGluIHNlY29uZHMuXG4gICAgICovXG4gICAgYWdlKCkge1xuICAgICAgICBsZXQgYWdlID0gdGhpcy5fYWdlVmFsdWUoKTtcblxuICAgICAgICBjb25zdCByZXNpZGVudFRpbWUgPSAodGhpcy5ub3coKSAtIHRoaXMuX3Jlc3BvbnNlVGltZSkgLyAxMDAwO1xuICAgICAgICByZXR1cm4gYWdlICsgcmVzaWRlbnRUaW1lO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEByZXR1cm5zIHtudW1iZXJ9IFRoZSBBZ2UgaGVhZGVyIHZhbHVlIGFzIGEgbnVtYmVyLlxuICAgICAqL1xuICAgIF9hZ2VWYWx1ZSgpIHtcbiAgICAgICAgcmV0dXJuIHRvTnVtYmVyT3JaZXJvKHRoaXMuX3Jlc0hlYWRlcnMuYWdlKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBQb3NzaWJseSBvdXRkYXRlZCB2YWx1ZSBvZiBhcHBsaWNhYmxlIG1heC1hZ2UgKG9yIGhldXJpc3RpYyBlcXVpdmFsZW50KSBpbiBzZWNvbmRzLlxuICAgICAqIFRoaXMgY291bnRzIHNpbmNlIHJlc3BvbnNlJ3MgYERhdGVgLlxuICAgICAqXG4gICAgICogRm9yIGFuIHVwLXRvLWRhdGUgdmFsdWUsIHNlZSBgdGltZVRvTGl2ZSgpYC5cbiAgICAgKlxuICAgICAqIFJldHVybnMgdGhlIG1heGltdW0gYWdlIChmcmVzaG5lc3MgbGlmZXRpbWUpIG9mIHRoZSByZXNwb25zZSBpbiBzZWNvbmRzLlxuICAgICAqIEByZXR1cm5zIHtudW1iZXJ9IFRoZSBtYXgtYWdlIHZhbHVlIGluIHNlY29uZHMuXG4gICAgICovXG4gICAgbWF4QWdlKCkge1xuICAgICAgICBpZiAoIXRoaXMuc3RvcmFibGUoKSB8fCB0aGlzLl9yZXNjY1snbm8tY2FjaGUnXSkge1xuICAgICAgICAgICAgcmV0dXJuIDA7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBTaGFyZWQgcmVzcG9uc2VzIHdpdGggY29va2llcyBhcmUgY2FjaGVhYmxlIGFjY29yZGluZyB0byB0aGUgUkZDLCBidXQgSU1ITyBpdCdkIGJlIHVud2lzZSB0byBkbyBzbyBieSBkZWZhdWx0XG4gICAgICAgIC8vIHNvIHRoaXMgaW1wbGVtZW50YXRpb24gcmVxdWlyZXMgZXhwbGljaXQgb3B0LWluIHZpYSBwdWJsaWMgaGVhZGVyXG4gICAgICAgIGlmIChcbiAgICAgICAgICAgIHRoaXMuX2lzU2hhcmVkICYmXG4gICAgICAgICAgICAodGhpcy5fcmVzSGVhZGVyc1snc2V0LWNvb2tpZSddICYmXG4gICAgICAgICAgICAgICAgIXRoaXMuX3Jlc2NjLnB1YmxpYyAmJlxuICAgICAgICAgICAgICAgICF0aGlzLl9yZXNjYy5pbW11dGFibGUpXG4gICAgICAgICkge1xuICAgICAgICAgICAgcmV0dXJuIDA7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAodGhpcy5fcmVzSGVhZGVycy52YXJ5ID09PSAnKicpIHtcbiAgICAgICAgICAgIHJldHVybiAwO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKHRoaXMuX2lzU2hhcmVkKSB7XG4gICAgICAgICAgICBpZiAodGhpcy5fcmVzY2NbJ3Byb3h5LXJldmFsaWRhdGUnXSkge1xuICAgICAgICAgICAgICAgIHJldHVybiAwO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gaWYgYSByZXNwb25zZSBpbmNsdWRlcyB0aGUgcy1tYXhhZ2UgZGlyZWN0aXZlLCBhIHNoYXJlZCBjYWNoZSByZWNpcGllbnQgTVVTVCBpZ25vcmUgdGhlIEV4cGlyZXMgZmllbGQuXG4gICAgICAgICAgICBpZiAodGhpcy5fcmVzY2NbJ3MtbWF4YWdlJ10pIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdG9OdW1iZXJPclplcm8odGhpcy5fcmVzY2NbJ3MtbWF4YWdlJ10pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8gSWYgYSByZXNwb25zZSBpbmNsdWRlcyBhIENhY2hlLUNvbnRyb2wgZmllbGQgd2l0aCB0aGUgbWF4LWFnZSBkaXJlY3RpdmUsIGEgcmVjaXBpZW50IE1VU1QgaWdub3JlIHRoZSBFeHBpcmVzIGZpZWxkLlxuICAgICAgICBpZiAodGhpcy5fcmVzY2NbJ21heC1hZ2UnXSkge1xuICAgICAgICAgICAgcmV0dXJuIHRvTnVtYmVyT3JaZXJvKHRoaXMuX3Jlc2NjWydtYXgtYWdlJ10pO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgZGVmYXVsdE1pblR0bCA9IHRoaXMuX3Jlc2NjLmltbXV0YWJsZSA/IHRoaXMuX2ltbXV0YWJsZU1pblR0bCA6IDA7XG5cbiAgICAgICAgY29uc3Qgc2VydmVyRGF0ZSA9IHRoaXMuZGF0ZSgpO1xuICAgICAgICBpZiAodGhpcy5fcmVzSGVhZGVycy5leHBpcmVzKSB7XG4gICAgICAgICAgICBjb25zdCBleHBpcmVzID0gRGF0ZS5wYXJzZSh0aGlzLl9yZXNIZWFkZXJzLmV4cGlyZXMpO1xuICAgICAgICAgICAgLy8gQSBjYWNoZSByZWNpcGllbnQgTVVTVCBpbnRlcnByZXQgaW52YWxpZCBkYXRlIGZvcm1hdHMsIGVzcGVjaWFsbHkgdGhlIHZhbHVlIFwiMFwiLCBhcyByZXByZXNlbnRpbmcgYSB0aW1lIGluIHRoZSBwYXN0IChpLmUuLCBcImFscmVhZHkgZXhwaXJlZFwiKS5cbiAgICAgICAgICAgIGlmIChOdW1iZXIuaXNOYU4oZXhwaXJlcykgfHwgZXhwaXJlcyA8IHNlcnZlckRhdGUpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gMDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBNYXRoLm1heChkZWZhdWx0TWluVHRsLCAoZXhwaXJlcyAtIHNlcnZlckRhdGUpIC8gMTAwMCk7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAodGhpcy5fcmVzSGVhZGVyc1snbGFzdC1tb2RpZmllZCddKSB7XG4gICAgICAgICAgICBjb25zdCBsYXN0TW9kaWZpZWQgPSBEYXRlLnBhcnNlKHRoaXMuX3Jlc0hlYWRlcnNbJ2xhc3QtbW9kaWZpZWQnXSk7XG4gICAgICAgICAgICBpZiAoaXNGaW5pdGUobGFzdE1vZGlmaWVkKSAmJiBzZXJ2ZXJEYXRlID4gbGFzdE1vZGlmaWVkKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIE1hdGgubWF4KFxuICAgICAgICAgICAgICAgICAgICBkZWZhdWx0TWluVHRsLFxuICAgICAgICAgICAgICAgICAgICAoKHNlcnZlckRhdGUgLSBsYXN0TW9kaWZpZWQpIC8gMTAwMCkgKiB0aGlzLl9jYWNoZUhldXJpc3RpY1xuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gZGVmYXVsdE1pblR0bDtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBSZW1haW5pbmcgdGltZSB0aGlzIGNhY2hlIGVudHJ5IG1heSBiZSB1c2VmdWwgZm9yLCBpbiAqbWlsbGlzZWNvbmRzKi5cbiAgICAgKiBZb3UgY2FuIHVzZSB0aGlzIGFzIGFuIGV4cGlyYXRpb24gdGltZSBmb3IgeW91ciBjYWNoZSBzdG9yYWdlLlxuICAgICAqXG4gICAgICogUHJlZmVyIHRoaXMgbWV0aG9kIG92ZXIgYG1heEFnZSgpYCwgYmVjYXVzZSBpdCBpbmNsdWRlcyBvdGhlciBmYWN0b3JzIGxpa2UgYGFnZWAgYW5kIGBzdGFsZS13aGlsZS1yZXZhbGlkYXRlYC5cbiAgICAgKiBAcmV0dXJucyB7bnVtYmVyfSBUaW1lLXRvLWxpdmUgaW4gbWlsbGlzZWNvbmRzLlxuICAgICAqL1xuICAgIHRpbWVUb0xpdmUoKSB7XG4gICAgICAgIGNvbnN0IGFnZSA9IHRoaXMubWF4QWdlKCkgLSB0aGlzLmFnZSgpO1xuICAgICAgICBjb25zdCBzdGFsZUlmRXJyb3JBZ2UgPSBhZ2UgKyB0b051bWJlck9yWmVybyh0aGlzLl9yZXNjY1snc3RhbGUtaWYtZXJyb3InXSk7XG4gICAgICAgIGNvbnN0IHN0YWxlV2hpbGVSZXZhbGlkYXRlQWdlID0gYWdlICsgdG9OdW1iZXJPclplcm8odGhpcy5fcmVzY2NbJ3N0YWxlLXdoaWxlLXJldmFsaWRhdGUnXSk7XG4gICAgICAgIHJldHVybiBNYXRoLnJvdW5kKE1hdGgubWF4KDAsIGFnZSwgc3RhbGVJZkVycm9yQWdlLCBzdGFsZVdoaWxlUmV2YWxpZGF0ZUFnZSkgKiAxMDAwKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBJZiB0cnVlLCB0aGlzIGNhY2hlIGVudHJ5IGlzIHBhc3QgaXRzIGV4cGlyYXRpb24gZGF0ZS5cbiAgICAgKiBOb3RlIHRoYXQgc3RhbGUgY2FjaGUgbWF5IGJlIHVzZWZ1bCBzb21ldGltZXMsIHNlZSBgZXZhbHVhdGVSZXF1ZXN0KClgLlxuICAgICAqIEByZXR1cm5zIHtib29sZWFufSBgZmFsc2VgIGRvZXNuJ3QgbWVhbiBpdCdzIGZyZXNoIG5vciB1c2FibGVcbiAgICAgKi9cbiAgICBzdGFsZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubWF4QWdlKCkgPD0gdGhpcy5hZ2UoKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0gYHRydWVgIGlmIGBzdGFsZS1pZi1lcnJvcmAgY29uZGl0aW9uIGFsbG93cyB1c2Ugb2YgYSBzdGFsZSByZXNwb25zZS5cbiAgICAgKi9cbiAgICBfdXNlU3RhbGVJZkVycm9yKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5tYXhBZ2UoKSArIHRvTnVtYmVyT3JaZXJvKHRoaXMuX3Jlc2NjWydzdGFsZS1pZi1lcnJvciddKSA+IHRoaXMuYWdlKCk7XG4gICAgfVxuXG4gICAgLyoqIFNlZSBgZXZhbHVhdGVSZXF1ZXN0KClgIGZvciBhIG1vcmUgY29tcGxldGUgc29sdXRpb25cbiAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0gYHRydWVgIGlmIGBzdGFsZS13aGlsZS1yZXZhbGlkYXRlYCBpcyBjdXJyZW50bHkgYWxsb3dlZC5cbiAgICAgKi9cbiAgICB1c2VTdGFsZVdoaWxlUmV2YWxpZGF0ZSgpIHtcbiAgICAgICAgY29uc3Qgc3dyID0gdG9OdW1iZXJPclplcm8odGhpcy5fcmVzY2NbJ3N0YWxlLXdoaWxlLXJldmFsaWRhdGUnXSk7XG4gICAgICAgIHJldHVybiBzd3IgPiAwICYmIHRoaXMubWF4QWdlKCkgKyBzd3IgPiB0aGlzLmFnZSgpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIENyZWF0ZXMgYSBgQ2FjaGVQb2xpY3lgIGluc3RhbmNlIGZyb20gYSBzZXJpYWxpemVkIG9iamVjdC5cbiAgICAgKiBAcGFyYW0ge09iamVjdH0gb2JqIC0gVGhlIHNlcmlhbGl6ZWQgb2JqZWN0LlxuICAgICAqIEByZXR1cm5zIHtDYWNoZVBvbGljeX0gQSBuZXcgQ2FjaGVQb2xpY3kgaW5zdGFuY2UuXG4gICAgICovXG4gICAgc3RhdGljIGZyb21PYmplY3Qob2JqKSB7XG4gICAgICAgIHJldHVybiBuZXcgdGhpcyh1bmRlZmluZWQsIHVuZGVmaW5lZCwgeyBfZnJvbU9iamVjdDogb2JqIH0pO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEBwYXJhbSB7YW55fSBvYmogLSBUaGUgc2VyaWFsaXplZCBvYmplY3QuXG4gICAgICogQHRocm93cyB7RXJyb3J9IElmIGFscmVhZHkgaW5pdGlhbGl6ZWQgb3IgaWYgdGhlIG9iamVjdCBpcyBpbnZhbGlkLlxuICAgICAqL1xuICAgIF9mcm9tT2JqZWN0KG9iaikge1xuICAgICAgICBpZiAodGhpcy5fcmVzcG9uc2VUaW1lKSB0aHJvdyBFcnJvcignUmVpbml0aWFsaXplZCcpO1xuICAgICAgICBpZiAoIW9iaiB8fCBvYmoudiAhPT0gMSkgdGhyb3cgRXJyb3IoJ0ludmFsaWQgc2VyaWFsaXphdGlvbicpO1xuXG4gICAgICAgIHRoaXMuX3Jlc3BvbnNlVGltZSA9IG9iai50O1xuICAgICAgICB0aGlzLl9pc1NoYXJlZCA9IG9iai5zaDtcbiAgICAgICAgdGhpcy5fY2FjaGVIZXVyaXN0aWMgPSBvYmouY2g7XG4gICAgICAgIHRoaXMuX2ltbXV0YWJsZU1pblR0bCA9XG4gICAgICAgICAgICBvYmouaW1tICE9PSB1bmRlZmluZWQgPyBvYmouaW1tIDogMjQgKiAzNjAwICogMTAwMDtcbiAgICAgICAgdGhpcy5faWdub3JlQ2FyZ29DdWx0ID0gISFvYmouaWNjO1xuICAgICAgICB0aGlzLl9zdGF0dXMgPSBvYmouc3Q7XG4gICAgICAgIHRoaXMuX3Jlc0hlYWRlcnMgPSBvYmoucmVzaDtcbiAgICAgICAgdGhpcy5fcmVzY2MgPSBvYmoucmVzY2M7XG4gICAgICAgIHRoaXMuX21ldGhvZCA9IG9iai5tO1xuICAgICAgICB0aGlzLl91cmwgPSBvYmoudTtcbiAgICAgICAgdGhpcy5faG9zdCA9IG9iai5oO1xuICAgICAgICB0aGlzLl9ub0F1dGhvcml6YXRpb24gPSBvYmouYTtcbiAgICAgICAgdGhpcy5fcmVxSGVhZGVycyA9IG9iai5yZXFoO1xuICAgICAgICB0aGlzLl9yZXFjYyA9IG9iai5yZXFjYztcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBTZXJpYWxpemVzIHRoZSBgQ2FjaGVQb2xpY3lgIGluc3RhbmNlIGludG8gYSBKU09OLXNlcmlhbGl6YWJsZSBvYmplY3QuXG4gICAgICogQHJldHVybnMge09iamVjdH0gVGhlIHNlcmlhbGl6ZWQgb2JqZWN0LlxuICAgICAqL1xuICAgIHRvT2JqZWN0KCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdjogMSxcbiAgICAgICAgICAgIHQ6IHRoaXMuX3Jlc3BvbnNlVGltZSxcbiAgICAgICAgICAgIHNoOiB0aGlzLl9pc1NoYXJlZCxcbiAgICAgICAgICAgIGNoOiB0aGlzLl9jYWNoZUhldXJpc3RpYyxcbiAgICAgICAgICAgIGltbTogdGhpcy5faW1tdXRhYmxlTWluVHRsLFxuICAgICAgICAgICAgaWNjOiB0aGlzLl9pZ25vcmVDYXJnb0N1bHQsXG4gICAgICAgICAgICBzdDogdGhpcy5fc3RhdHVzLFxuICAgICAgICAgICAgcmVzaDogdGhpcy5fcmVzSGVhZGVycyxcbiAgICAgICAgICAgIHJlc2NjOiB0aGlzLl9yZXNjYyxcbiAgICAgICAgICAgIG06IHRoaXMuX21ldGhvZCxcbiAgICAgICAgICAgIHU6IHRoaXMuX3VybCxcbiAgICAgICAgICAgIGg6IHRoaXMuX2hvc3QsXG4gICAgICAgICAgICBhOiB0aGlzLl9ub0F1dGhvcml6YXRpb24sXG4gICAgICAgICAgICByZXFoOiB0aGlzLl9yZXFIZWFkZXJzLFxuICAgICAgICAgICAgcmVxY2M6IHRoaXMuX3JlcWNjLFxuICAgICAgICB9O1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEhlYWRlcnMgZm9yIHNlbmRpbmcgdG8gdGhlIG9yaWdpbiBzZXJ2ZXIgdG8gcmV2YWxpZGF0ZSBzdGFsZSByZXNwb25zZS5cbiAgICAgKiBBbGxvd3Mgc2VydmVyIHRvIHJldHVybiAzMDQgdG8gYWxsb3cgcmV1c2Ugb2YgdGhlIHByZXZpb3VzIHJlc3BvbnNlLlxuICAgICAqXG4gICAgICogSG9wIGJ5IGhvcCBoZWFkZXJzIGFyZSBhbHdheXMgc3RyaXBwZWQuXG4gICAgICogUmV2YWxpZGF0aW9uIGhlYWRlcnMgbWF5IGJlIGFkZGVkIG9yIHJlbW92ZWQsIGRlcGVuZGluZyBvbiByZXF1ZXN0LlxuICAgICAqIEBwYXJhbSB7SHR0cFJlcXVlc3R9IGluY29taW5nUmVxIC0gVGhlIGluY29taW5nIEhUVFAgcmVxdWVzdC5cbiAgICAgKiBAcmV0dXJucyB7UmVjb3JkPHN0cmluZywgc3RyaW5nPn0gVGhlIGhlYWRlcnMgZm9yIHRoZSByZXZhbGlkYXRpb24gcmVxdWVzdC5cbiAgICAgKi9cbiAgICByZXZhbGlkYXRpb25IZWFkZXJzKGluY29taW5nUmVxKSB7XG4gICAgICAgIHRoaXMuX2Fzc2VydFJlcXVlc3RIYXNIZWFkZXJzKGluY29taW5nUmVxKTtcbiAgICAgICAgY29uc3QgaGVhZGVycyA9IHRoaXMuX2NvcHlXaXRob3V0SG9wQnlIb3BIZWFkZXJzKGluY29taW5nUmVxLmhlYWRlcnMpO1xuXG4gICAgICAgIC8vIFRoaXMgaW1wbGVtZW50YXRpb24gZG9lcyBub3QgdW5kZXJzdGFuZCByYW5nZSByZXF1ZXN0c1xuICAgICAgICBkZWxldGUgaGVhZGVyc1snaWYtcmFuZ2UnXTtcblxuICAgICAgICBpZiAoIXRoaXMuX3JlcXVlc3RNYXRjaGVzKGluY29taW5nUmVxLCB0cnVlKSB8fCAhdGhpcy5zdG9yYWJsZSgpKSB7XG4gICAgICAgICAgICAvLyByZXZhbGlkYXRpb24gYWxsb3dlZCB2aWEgSEVBRFxuICAgICAgICAgICAgLy8gbm90IGZvciB0aGUgc2FtZSByZXNvdXJjZSwgb3Igd2Fzbid0IGFsbG93ZWQgdG8gYmUgY2FjaGVkIGFueXdheVxuICAgICAgICAgICAgZGVsZXRlIGhlYWRlcnNbJ2lmLW5vbmUtbWF0Y2gnXTtcbiAgICAgICAgICAgIGRlbGV0ZSBoZWFkZXJzWydpZi1tb2RpZmllZC1zaW5jZSddO1xuICAgICAgICAgICAgcmV0dXJuIGhlYWRlcnM7XG4gICAgICAgIH1cblxuICAgICAgICAvKiBNVVNUIHNlbmQgdGhhdCBlbnRpdHktdGFnIGluIGFueSBjYWNoZSB2YWxpZGF0aW9uIHJlcXVlc3QgKHVzaW5nIElmLU1hdGNoIG9yIElmLU5vbmUtTWF0Y2gpIGlmIGFuIGVudGl0eS10YWcgaGFzIGJlZW4gcHJvdmlkZWQgYnkgdGhlIG9yaWdpbiBzZXJ2ZXIuICovXG4gICAgICAgIGlmICh0aGlzLl9yZXNIZWFkZXJzLmV0YWcpIHtcbiAgICAgICAgICAgIGhlYWRlcnNbJ2lmLW5vbmUtbWF0Y2gnXSA9IGhlYWRlcnNbJ2lmLW5vbmUtbWF0Y2gnXVxuICAgICAgICAgICAgICAgID8gYCR7aGVhZGVyc1snaWYtbm9uZS1tYXRjaCddfSwgJHt0aGlzLl9yZXNIZWFkZXJzLmV0YWd9YFxuICAgICAgICAgICAgICAgIDogdGhpcy5fcmVzSGVhZGVycy5ldGFnO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gQ2xpZW50cyBNQVkgaXNzdWUgc2ltcGxlIChub24tc3VicmFuZ2UpIEdFVCByZXF1ZXN0cyB3aXRoIGVpdGhlciB3ZWFrIHZhbGlkYXRvcnMgb3Igc3Ryb25nIHZhbGlkYXRvcnMuIENsaWVudHMgTVVTVCBOT1QgdXNlIHdlYWsgdmFsaWRhdG9ycyBpbiBvdGhlciBmb3JtcyBvZiByZXF1ZXN0LlxuICAgICAgICBjb25zdCBmb3JiaWRzV2Vha1ZhbGlkYXRvcnMgPVxuICAgICAgICAgICAgaGVhZGVyc1snYWNjZXB0LXJhbmdlcyddIHx8XG4gICAgICAgICAgICBoZWFkZXJzWydpZi1tYXRjaCddIHx8XG4gICAgICAgICAgICBoZWFkZXJzWydpZi11bm1vZGlmaWVkLXNpbmNlJ10gfHxcbiAgICAgICAgICAgICh0aGlzLl9tZXRob2QgJiYgdGhpcy5fbWV0aG9kICE9ICdHRVQnKTtcblxuICAgICAgICAvKiBTSE9VTEQgc2VuZCB0aGUgTGFzdC1Nb2RpZmllZCB2YWx1ZSBpbiBub24tc3VicmFuZ2UgY2FjaGUgdmFsaWRhdGlvbiByZXF1ZXN0cyAodXNpbmcgSWYtTW9kaWZpZWQtU2luY2UpIGlmIG9ubHkgYSBMYXN0LU1vZGlmaWVkIHZhbHVlIGhhcyBiZWVuIHByb3ZpZGVkIGJ5IHRoZSBvcmlnaW4gc2VydmVyLlxuICAgICAgICBOb3RlOiBUaGlzIGltcGxlbWVudGF0aW9uIGRvZXMgbm90IHVuZGVyc3RhbmQgcGFydGlhbCByZXNwb25zZXMgKDIwNikgKi9cbiAgICAgICAgaWYgKGZvcmJpZHNXZWFrVmFsaWRhdG9ycykge1xuICAgICAgICAgICAgZGVsZXRlIGhlYWRlcnNbJ2lmLW1vZGlmaWVkLXNpbmNlJ107XG5cbiAgICAgICAgICAgIGlmIChoZWFkZXJzWydpZi1ub25lLW1hdGNoJ10pIHtcbiAgICAgICAgICAgICAgICBjb25zdCBldGFncyA9IGhlYWRlcnNbJ2lmLW5vbmUtbWF0Y2gnXVxuICAgICAgICAgICAgICAgICAgICAuc3BsaXQoLywvKVxuICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKGV0YWcgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuICEvXlxccypXXFwvLy50ZXN0KGV0YWcpO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBpZiAoIWV0YWdzLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgICAgICBkZWxldGUgaGVhZGVyc1snaWYtbm9uZS1tYXRjaCddO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGhlYWRlcnNbJ2lmLW5vbmUtbWF0Y2gnXSA9IGV0YWdzLmpvaW4oJywnKS50cmltKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9IGVsc2UgaWYgKFxuICAgICAgICAgICAgdGhpcy5fcmVzSGVhZGVyc1snbGFzdC1tb2RpZmllZCddICYmXG4gICAgICAgICAgICAhaGVhZGVyc1snaWYtbW9kaWZpZWQtc2luY2UnXVxuICAgICAgICApIHtcbiAgICAgICAgICAgIGhlYWRlcnNbJ2lmLW1vZGlmaWVkLXNpbmNlJ10gPSB0aGlzLl9yZXNIZWFkZXJzWydsYXN0LW1vZGlmaWVkJ107XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gaGVhZGVycztcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiBDcmVhdGVzIG5ldyBDYWNoZVBvbGljeSB3aXRoIGluZm9ybWF0aW9uIGNvbWJpbmVkIGZyb20gdGhlIHByZXZpZXdzIHJlc3BvbnNlLFxuICAgICAqIGFuZCB0aGUgbmV3IHJldmFsaWRhdGlvbiByZXNwb25zZS5cbiAgICAgKlxuICAgICAqIFJldHVybnMge3BvbGljeSwgbW9kaWZpZWR9IHdoZXJlIG1vZGlmaWVkIGlzIGEgYm9vbGVhbiBpbmRpY2F0aW5nXG4gICAgICogd2hldGhlciB0aGUgcmVzcG9uc2UgYm9keSBoYXMgYmVlbiBtb2RpZmllZCwgYW5kIG9sZCBjYWNoZWQgYm9keSBjYW4ndCBiZSB1c2VkLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtIdHRwUmVxdWVzdH0gcmVxdWVzdCAtIFRoZSBsYXRlc3QgSFRUUCByZXF1ZXN0IGFza2luZyBmb3IgdGhlIGNhY2hlZCBlbnRyeS5cbiAgICAgKiBAcGFyYW0ge0h0dHBSZXNwb25zZX0gcmVzcG9uc2UgLSBUaGUgbGF0ZXN0IHJldmFsaWRhdGlvbiBIVFRQIHJlc3BvbnNlIGZyb20gdGhlIG9yaWdpbiBzZXJ2ZXIuXG4gICAgICogQHJldHVybnMge3twb2xpY3k6IENhY2hlUG9saWN5LCBtb2RpZmllZDogYm9vbGVhbiwgbWF0Y2hlczogYm9vbGVhbn19IFRoZSB1cGRhdGVkIHBvbGljeSBhbmQgbW9kaWZpY2F0aW9uIHN0YXR1cy5cbiAgICAgKiBAdGhyb3dzIHtFcnJvcn0gSWYgdGhlIHJlc3BvbnNlIGhlYWRlcnMgYXJlIG1pc3NpbmcuXG4gICAgICovXG4gICAgcmV2YWxpZGF0ZWRQb2xpY3kocmVxdWVzdCwgcmVzcG9uc2UpIHtcbiAgICAgICAgdGhpcy5fYXNzZXJ0UmVxdWVzdEhhc0hlYWRlcnMocmVxdWVzdCk7XG5cbiAgICAgICAgaWYgKHRoaXMuX3VzZVN0YWxlSWZFcnJvcigpICYmIGlzRXJyb3JSZXNwb25zZShyZXNwb25zZSkpIHtcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICBwb2xpY3k6IHRoaXMsXG4gICAgICAgICAgICAgIG1vZGlmaWVkOiBmYWxzZSxcbiAgICAgICAgICAgICAgbWF0Y2hlczogdHJ1ZSxcbiAgICAgICAgICB9O1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCFyZXNwb25zZSB8fCAhcmVzcG9uc2UuaGVhZGVycykge1xuICAgICAgICAgICAgdGhyb3cgRXJyb3IoJ1Jlc3BvbnNlIGhlYWRlcnMgbWlzc2luZycpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gVGhlc2UgYXJlbid0IGdvaW5nIHRvIGJlIHN1cHBvcnRlZCBleGFjdGx5LCBzaW5jZSBvbmUgQ2FjaGVQb2xpY3kgb2JqZWN0XG4gICAgICAgIC8vIGRvZXNuJ3Qga25vdyBhYm91dCBhbGwgdGhlIG90aGVyIGNhY2hlZCBvYmplY3RzLlxuICAgICAgICBsZXQgbWF0Y2hlcyA9IGZhbHNlO1xuICAgICAgICBpZiAocmVzcG9uc2Uuc3RhdHVzICE9PSB1bmRlZmluZWQgJiYgcmVzcG9uc2Uuc3RhdHVzICE9IDMwNCkge1xuICAgICAgICAgICAgbWF0Y2hlcyA9IGZhbHNlO1xuICAgICAgICB9IGVsc2UgaWYgKFxuICAgICAgICAgICAgcmVzcG9uc2UuaGVhZGVycy5ldGFnICYmXG4gICAgICAgICAgICAhL15cXHMqV1xcLy8udGVzdChyZXNwb25zZS5oZWFkZXJzLmV0YWcpXG4gICAgICAgICkge1xuICAgICAgICAgICAgLy8gXCJBbGwgb2YgdGhlIHN0b3JlZCByZXNwb25zZXMgd2l0aCB0aGUgc2FtZSBzdHJvbmcgdmFsaWRhdG9yIGFyZSBzZWxlY3RlZC5cbiAgICAgICAgICAgIC8vIElmIG5vbmUgb2YgdGhlIHN0b3JlZCByZXNwb25zZXMgY29udGFpbiB0aGUgc2FtZSBzdHJvbmcgdmFsaWRhdG9yLFxuICAgICAgICAgICAgLy8gdGhlbiB0aGUgY2FjaGUgTVVTVCBOT1QgdXNlIHRoZSBuZXcgcmVzcG9uc2UgdG8gdXBkYXRlIGFueSBzdG9yZWQgcmVzcG9uc2VzLlwiXG4gICAgICAgICAgICBtYXRjaGVzID1cbiAgICAgICAgICAgICAgICB0aGlzLl9yZXNIZWFkZXJzLmV0YWcgJiZcbiAgICAgICAgICAgICAgICB0aGlzLl9yZXNIZWFkZXJzLmV0YWcucmVwbGFjZSgvXlxccypXXFwvLywgJycpID09PVxuICAgICAgICAgICAgICAgICAgICByZXNwb25zZS5oZWFkZXJzLmV0YWc7XG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy5fcmVzSGVhZGVycy5ldGFnICYmIHJlc3BvbnNlLmhlYWRlcnMuZXRhZykge1xuICAgICAgICAgICAgLy8gXCJJZiB0aGUgbmV3IHJlc3BvbnNlIGNvbnRhaW5zIGEgd2VhayB2YWxpZGF0b3IgYW5kIHRoYXQgdmFsaWRhdG9yIGNvcnJlc3BvbmRzXG4gICAgICAgICAgICAvLyB0byBvbmUgb2YgdGhlIGNhY2hlJ3Mgc3RvcmVkIHJlc3BvbnNlcyxcbiAgICAgICAgICAgIC8vIHRoZW4gdGhlIG1vc3QgcmVjZW50IG9mIHRob3NlIG1hdGNoaW5nIHN0b3JlZCByZXNwb25zZXMgaXMgc2VsZWN0ZWQgZm9yIHVwZGF0ZS5cIlxuICAgICAgICAgICAgbWF0Y2hlcyA9XG4gICAgICAgICAgICAgICAgdGhpcy5fcmVzSGVhZGVycy5ldGFnLnJlcGxhY2UoL15cXHMqV1xcLy8sICcnKSA9PT1cbiAgICAgICAgICAgICAgICByZXNwb25zZS5oZWFkZXJzLmV0YWcucmVwbGFjZSgvXlxccypXXFwvLywgJycpO1xuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuX3Jlc0hlYWRlcnNbJ2xhc3QtbW9kaWZpZWQnXSkge1xuICAgICAgICAgICAgbWF0Y2hlcyA9XG4gICAgICAgICAgICAgICAgdGhpcy5fcmVzSGVhZGVyc1snbGFzdC1tb2RpZmllZCddID09PVxuICAgICAgICAgICAgICAgIHJlc3BvbnNlLmhlYWRlcnNbJ2xhc3QtbW9kaWZpZWQnXTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIElmIHRoZSBuZXcgcmVzcG9uc2UgZG9lcyBub3QgaW5jbHVkZSBhbnkgZm9ybSBvZiB2YWxpZGF0b3IgKHN1Y2ggYXMgaW4gdGhlIGNhc2Ugd2hlcmVcbiAgICAgICAgICAgIC8vIGEgY2xpZW50IGdlbmVyYXRlcyBhbiBJZi1Nb2RpZmllZC1TaW5jZSByZXF1ZXN0IGZyb20gYSBzb3VyY2Ugb3RoZXIgdGhhbiB0aGUgTGFzdC1Nb2RpZmllZFxuICAgICAgICAgICAgLy8gcmVzcG9uc2UgaGVhZGVyIGZpZWxkKSwgYW5kIHRoZXJlIGlzIG9ubHkgb25lIHN0b3JlZCByZXNwb25zZSwgYW5kIHRoYXQgc3RvcmVkIHJlc3BvbnNlIGFsc29cbiAgICAgICAgICAgIC8vIGxhY2tzIGEgdmFsaWRhdG9yLCB0aGVuIHRoYXQgc3RvcmVkIHJlc3BvbnNlIGlzIHNlbGVjdGVkIGZvciB1cGRhdGUuXG4gICAgICAgICAgICBpZiAoXG4gICAgICAgICAgICAgICAgIXRoaXMuX3Jlc0hlYWRlcnMuZXRhZyAmJlxuICAgICAgICAgICAgICAgICF0aGlzLl9yZXNIZWFkZXJzWydsYXN0LW1vZGlmaWVkJ10gJiZcbiAgICAgICAgICAgICAgICAhcmVzcG9uc2UuaGVhZGVycy5ldGFnICYmXG4gICAgICAgICAgICAgICAgIXJlc3BvbnNlLmhlYWRlcnNbJ2xhc3QtbW9kaWZpZWQnXVxuICAgICAgICAgICAgKSB7XG4gICAgICAgICAgICAgICAgbWF0Y2hlcyA9IHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBvcHRpb25zQ29weSA9IHtcbiAgICAgICAgICAgIHNoYXJlZDogdGhpcy5faXNTaGFyZWQsXG4gICAgICAgICAgICBjYWNoZUhldXJpc3RpYzogdGhpcy5fY2FjaGVIZXVyaXN0aWMsXG4gICAgICAgICAgICBpbW11dGFibGVNaW5UaW1lVG9MaXZlOiB0aGlzLl9pbW11dGFibGVNaW5UdGwsXG4gICAgICAgICAgICBpZ25vcmVDYXJnb0N1bHQ6IHRoaXMuX2lnbm9yZUNhcmdvQ3VsdCxcbiAgICAgICAgfTtcblxuICAgICAgICBpZiAoIW1hdGNoZXMpIHtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgcG9saWN5OiBuZXcgdGhpcy5jb25zdHJ1Y3RvcihyZXF1ZXN0LCByZXNwb25zZSwgb3B0aW9uc0NvcHkpLFxuICAgICAgICAgICAgICAgIC8vIENsaWVudCByZWNlaXZpbmcgMzA0IHdpdGhvdXQgYm9keSwgZXZlbiBpZiBpdCdzIGludmFsaWQvbWlzbWF0Y2hlZCBoYXMgbm8gb3B0aW9uXG4gICAgICAgICAgICAgICAgLy8gYnV0IHRvIHJldXNlIGEgY2FjaGVkIGJvZHkuIFdlIGRvbid0IGhhdmUgYSBnb29kIHdheSB0byB0ZWxsIGNsaWVudHMgdG8gZG9cbiAgICAgICAgICAgICAgICAvLyBlcnJvciByZWNvdmVyeSBpbiBzdWNoIGNhc2UuXG4gICAgICAgICAgICAgICAgbW9kaWZpZWQ6IHJlc3BvbnNlLnN0YXR1cyAhPSAzMDQsXG4gICAgICAgICAgICAgICAgbWF0Y2hlczogZmFsc2UsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gdXNlIG90aGVyIGhlYWRlciBmaWVsZHMgcHJvdmlkZWQgaW4gdGhlIDMwNCAoTm90IE1vZGlmaWVkKSByZXNwb25zZSB0byByZXBsYWNlIGFsbCBpbnN0YW5jZXNcbiAgICAgICAgLy8gb2YgdGhlIGNvcnJlc3BvbmRpbmcgaGVhZGVyIGZpZWxkcyBpbiB0aGUgc3RvcmVkIHJlc3BvbnNlLlxuICAgICAgICBjb25zdCBoZWFkZXJzID0ge307XG4gICAgICAgIGZvciAoY29uc3QgayBpbiB0aGlzLl9yZXNIZWFkZXJzKSB7XG4gICAgICAgICAgICBoZWFkZXJzW2tdID1cbiAgICAgICAgICAgICAgICBrIGluIHJlc3BvbnNlLmhlYWRlcnMgJiYgIWV4Y2x1ZGVkRnJvbVJldmFsaWRhdGlvblVwZGF0ZVtrXVxuICAgICAgICAgICAgICAgICAgICA/IHJlc3BvbnNlLmhlYWRlcnNba11cbiAgICAgICAgICAgICAgICAgICAgOiB0aGlzLl9yZXNIZWFkZXJzW2tdO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgbmV3UmVzcG9uc2UgPSBPYmplY3QuYXNzaWduKHt9LCByZXNwb25zZSwge1xuICAgICAgICAgICAgc3RhdHVzOiB0aGlzLl9zdGF0dXMsXG4gICAgICAgICAgICBtZXRob2Q6IHRoaXMuX21ldGhvZCxcbiAgICAgICAgICAgIGhlYWRlcnMsXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgcG9saWN5OiBuZXcgdGhpcy5jb25zdHJ1Y3RvcihyZXF1ZXN0LCBuZXdSZXNwb25zZSwgb3B0aW9uc0NvcHkpLFxuICAgICAgICAgICAgbW9kaWZpZWQ6IGZhbHNlLFxuICAgICAgICAgICAgbWF0Y2hlczogdHJ1ZSxcbiAgICAgICAgfTtcbiAgICB9XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/http-cache-semantics/index.js\n");

/***/ })

};
;