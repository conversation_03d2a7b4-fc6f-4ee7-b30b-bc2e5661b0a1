/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/apps/appdetails/[appId]/page";
exports.ids = ["app/apps/appdetails/[appId]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage&page=%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage&appPaths=%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage&pagePath=private-next-app-dir%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage&page=%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage&appPaths=%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage&pagePath=private-next-app-dir%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'apps',\n        {\n        children: [\n        'appdetails',\n        {\n        children: [\n        '[appId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/apps/appdetails/[appId]/page.js */ \"(rsc)/./src/app/apps/appdetails/[appId]/page.js\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\appdetails\\\\[appId]\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\appdetails\\\\[appId]\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/apps/appdetails/[appId]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/apps/appdetails/[appId]/page\",\n        pathname: \"/apps/appdetails/[appId]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage&page=%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage&appPaths=%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage&pagePath=private-next-app-dir%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CComponents%5C%5Cnavbar%5C%5Cnavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CReduxLayout%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CComponents%5C%5Cnavbar%5C%5Cnavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CReduxLayout%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/Components/navbar/navbar.jsx */ \"(ssr)/./src/app/Components/navbar/navbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/ReduxLayout/layout.js */ \"(ssr)/./src/app/ReduxLayout/layout.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Cnode_modules%5C%5C%40fortawesome%5C%5Cfontawesome-svg-core%5C%5Cstyles.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CComponents%5C%5Cnavbar%5C%5Cnavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5CReduxLayout%5C%5Clayout.js%22%2C%22ids%22%3A%5B%22ReduxProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Capps%5C%5Cappdetails%5C%5C%5BappId%5D%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Capps%5C%5Cappdetails%5C%5C%5BappId%5D%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/apps/appdetails/[appId]/page.js */ \"(ssr)/./src/app/apps/appdetails/[appId]/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0JodXNoYW4lMjBwYXRpbCU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q0FuY2hvcmluZyU1QyU1Q0FwayUyMG1pcnJvciU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FwcHMlNUMlNUNhcHBkZXRhaWxzJTVDJTVDJTVCYXBwSWQlNUQlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4TEFBcUoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLz9jZGY5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQmh1c2hhbiBwYXRpbFxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXEFuY2hvcmluZ1xcXFxBcGsgbWlycm9yXFxcXHNyY1xcXFxhcHBcXFxcYXBwc1xcXFxhcHBkZXRhaWxzXFxcXFthcHBJZF1cXFxccGFnZS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CBhushan%20patil%5C%5COneDrive%5C%5CDesktop%5C%5CAnchoring%5C%5CApk%20mirror%5C%5Csrc%5C%5Capp%5C%5Capps%5C%5Cappdetails%5C%5C%5BappId%5D%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/Ads.jsx":
/*!*************************!*\
  !*** ./src/app/Ads.jsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Ads = ({ slot = \"\", className })=>{\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [insSize, setInsSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const insRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setUrl(window.location.hostname);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAd = ()=>{\n            try {\n                const adsGoogle = window.adsbygoogle || [];\n                adsGoogle.push({});\n            } catch (err) {}\n        };\n        const adScript = document.createElement(\"script\");\n        adScript.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8916641928046583`;\n        adScript.async = true;\n        adScript.onload = initializeAd;\n        document.body.appendChild(adScript);\n        const checkInsSize = setTimeout(()=>{\n            if (insRef.current) {\n                const { offsetWidth, offsetHeight } = insRef.current;\n                setInsSize({\n                    width: offsetWidth,\n                    height: offsetHeight\n                });\n            }\n        }, 1500); // Delay by 1.5 seconds to allow ad to load\n        return ()=>clearTimeout(checkInsSize);\n    }, [\n        slot\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n            className: \"adsbygoogle\",\n            style: {\n                \"display\": \"block\"\n            },\n            \"data-ad-client\": \"ca-pub-8916641928046583\",\n            \"data-ad-slot\": \"8457249210\",\n            \"data-ad-format\": \"auto\",\n            \"data-full-width-responsive\": \"true\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Ads.jsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Ads.jsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Ads);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Ads.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/Components/navbar/navbar.jsx":
/*!**********************************************!*\
  !*** ./src/app/Components/navbar/navbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Navbar = ()=>{\n    const pathName = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [searchTerm, setSearchTerm] = useState(\"\");\n    const [showSearch, setShowSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSearch(!pathName.includes(\"/app-search\"));\n    }, [\n        pathName\n    ]);\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    // const onHandleChange = (e) => {\n    //   setSearchTerm(e.target.value);\n    // };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 left-0 right-0 z-10 bg-white shadow\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"bg-white border-gray-200 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/\",\n                        className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"self-center text-2xl font-semibold whitespace-nowrap dark:text-white\",\n                            children: \"APKExplorer\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex md:order-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            \"aria-controls\": \"navbar-search\",\n                            \"aria-expanded\": isMenuOpen,\n                            className: \"grid place-items-center md:hidden text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5\",\n                            onClick: toggleMenu,\n                            children: [\n                                isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    \"aria-hidden\": \"true\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        stroke: \"currentColor\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    \"aria-hidden\": \"true\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        stroke: \"currentColor\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M3 6h14M3 10h14m-7 4h7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Toggle navigation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-full md:flex ml-auto md:w-auto md:order-1 ${isMenuOpen ? \"block\" : \"hidden\"}`,\n                        id: \"navbar-search\",\n                        onClick: toggleMenu,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"flex flex-col p-4 md:p-0 mt-4 font-medium border text-transform: uppercase border-gray-100 rounded-lg bg-gray-50 md:space-x-6 lg:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-white dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700\n                    ${pathName === \"/\" && \"md:text-blue-700 decoration-blue-700\"}\n                 `,\n                                        \"aria-current\": \"page\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/apps\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700\n                     ${pathName.includes(\"/apps\") && \"md:text-blue-700 focus:ring-violet-300 decoration-blue-700\"}\n                  `,\n                                        children: \"Android Apps\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/games\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 ${pathName.includes(\"/games\") && \"md:text-blue-700 decoration-blue-700\"}`,\n                                        children: \"Android Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/app-search\",\n                                        className: `block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:text-white md:hover:bg-transparent md:hover:text-blue-700 md:p-0 md:dark:hover:text-blue-500 dark:text-white dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 ${pathName.includes(\"/app-search\") && \"md:text-blue-700 decoration-blue-700\"}`,\n                                        children: \"Search Apps & Games\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Components\\\\navbar\\\\navbar.jsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Components/navbar/navbar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/Constant/staticData.js":
/*!****************************************!*\
  !*** ./src/app/Constant/staticData.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   androidApps: () => (/* binding */ androidApps),\n/* harmony export */   androidGames: () => (/* binding */ androidGames)\n/* harmony export */ });\nconst androidApps = [\n    {\n        icon: \"fa-solid fa-paintbrush\",\n        name: \"Art & Design\",\n        category: \"art-design\"\n    },\n    {\n        icon: \"fa-solid fa-car\",\n        name: \"Auto & Vehicles\",\n        category: \"auto-vehicles\"\n    },\n    {\n        icon: \"fa-solid fa-person-dress\",\n        name: \"Beauty\",\n        category: \"beauty\"\n    },\n    {\n        icon: \"fa-solid fa-book\",\n        name: \"Books & Reference\",\n        category: \"books-reference\"\n    },\n    {\n        icon: \"fa-solid fa-briefcase\",\n        name: \"Business\",\n        category: \"business\"\n    },\n    {\n        icon: \"fa-solid fa-paintbrush\",\n        name: \"Comics\",\n        category: \"comics\"\n    },\n    {\n        icon: \"fa-regular fa-comments\",\n        name: \"Communication\",\n        category: \"communication\"\n    },\n    {\n        icon: \"fa-solid fa-heart\",\n        name: \"Dating\",\n        category: \"dating\"\n    },\n    {\n        icon: \"fa-solid fa-graduation-cap\",\n        name: \"Education\",\n        category: \"education\"\n    },\n    {\n        icon: \"fa-solid fa-video\",\n        name: \"Entertainment\",\n        category: \"entertainment\"\n    },\n    {\n        icon: \"fa-regular fa-money-bill-1\",\n        name: \"Finance\",\n        category: \"finance\"\n    },\n    {\n        icon: \"fa-solid fa-utensils\",\n        name: \"Food & Drink\",\n        category: \"food-drink\"\n    },\n    {\n        icon: \"fa-solid fa-heart-pulse\",\n        name: \"Health & Fitness\",\n        category: \"health-fitness\"\n    },\n    {\n        icon: \"fa-solid fa-house-chimney\",\n        name: \"House & Home\",\n        category: \"house-home\"\n    },\n    {\n        icon: \"fa-regular fa-file-code\",\n        name: \"Libraries & Demo\",\n        category: \"libraries-demo\"\n    },\n    {\n        icon: \"fa-solid fa-person\",\n        name: \"Lifestyle\",\n        category: \"lifestyle\"\n    },\n    {\n        icon: \"fa-solid fa-suitcase-medical\",\n        name: \"Medical\",\n        category: \"medical\"\n    },\n    {\n        icon: \"fa-solid fa-headphones-simple\",\n        name: \"Music & Audio\",\n        category: \"music-audio\"\n    },\n    {\n        icon: \"fa-regular fa-newspaper\",\n        name: \"News & Magazines\",\n        category: \"news-magazines\"\n    },\n    {\n        icon: \"fa-solid fa-user\",\n        name: \"Personalization\",\n        category: \"personalization\"\n    },\n    {\n        icon: \"fa-solid fa-camera-retro\",\n        name: \"Photography\",\n        category: \"photography\"\n    },\n    {\n        icon: \"fa-solid fa-gears\",\n        name: \"Productivity\",\n        category: \"productivity\"\n    },\n    {\n        icon: \"fa-solid fa-cart-shopping\",\n        name: \"Shopping\",\n        category: \"shopping\"\n    },\n    {\n        icon: \"fa-solid fa-earth-americas\",\n        name: \"Social\",\n        category: \"social\"\n    },\n    {\n        icon: \"fa-regular fa-futbol\",\n        name: \"Sports\",\n        category: \"sports\"\n    },\n    {\n        icon: \"fa-solid fa-wrench\",\n        name: \"Tools\",\n        category: \"tools\"\n    },\n    {\n        icon: \"fa-solid fa-signs-post\",\n        name: \"Travel & Local\",\n        category: \"travel-local\"\n    },\n    {\n        icon: \"fa-regular fa-sun\",\n        name: \"Weather\",\n        category: \"weather\"\n    }\n];\nconst androidGames = [\n    {\n        icon: \"fa-solid fa-gamepad\",\n        name: \"Action\",\n        category: \"action\"\n    },\n    {\n        icon: \"fa-solid fa-chess-knight\",\n        name: \"Board\",\n        category: \"board\"\n    },\n    {\n        icon: \"fa-solid fa-mug-hot\",\n        name: \"Casual\",\n        category: \"casual\"\n    },\n    {\n        icon: \"fa-solid fa-puzzle-piece\",\n        name: \"Puzzle\",\n        category: \"puzzle\"\n    },\n    {\n        icon: \"fa-solid fa-clipboard-question\",\n        name: \"Simulation\",\n        category: \"simulation\"\n    },\n    {\n        icon: \"fa-regular fa-circle-question\",\n        name: \"Trivia\",\n        category: \"trivia\"\n    },\n    {\n        icon: \"fa-brands fa-space-awesome\",\n        name: \"Adventure\",\n        category: \"adventure\"\n    },\n    {\n        icon: \"fa-regular fa-heart\",\n        name: \"Card\",\n        category: \"card\"\n    },\n    {\n        icon: \"fa-solid fa-car\",\n        name: \"Racing\",\n        category: \"racing\"\n    },\n    {\n        icon: \"fa-regular fa-futbol\",\n        name: \"Sports\",\n        category: \"sports\"\n    },\n    {\n        icon: \"fa-solid fa-pen-to-square\",\n        name: \"Word\",\n        category: \"word\"\n    },\n    {\n        icon: \"fa-solid fa-trophy\",\n        name: \"Arcade\",\n        category: \"arcade\"\n    },\n    {\n        icon: \"fa-regular fa-square\",\n        name: \"Casino\",\n        category: \"casino\"\n    },\n    {\n        icon: \"fa-solid fa-music\",\n        name: \"Music\",\n        category: \"music\"\n    },\n    {\n        icon: \"fa-solid fa-users\",\n        name: \"Role Playing\",\n        category: \"role-playing\"\n    },\n    {\n        icon: \"fa-regular fa-lightbulb\",\n        name: \"Strategy\",\n        category: \"strategy\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Constant/staticData.js\n");

/***/ }),

/***/ "(ssr)/./src/app/Loading.jsx":
/*!*****************************!*\
  !*** ./src/app/Loading.jsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst LoadingComponent = ({ length, md = 2, lg })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `grid grid-cols-1 md:grid-cols-${md} lg:grid-cols-${lg} gap-4 p-4`,\n        children: Array.from({\n            length: length\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse hover:bg-gray-100 p-1 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md bg-slate-200 h-20 w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                            lineNumber: 10,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2.5 bg-gray-200 rounded-full  w-48 my-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2 bg-gray-200 rounded-full  w-48 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2 bg-gray-200 rounded-full  w-48 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2.5 bg-gray-200 rounded-full  w-32 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                            lineNumber: 11,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, undefined)\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\Loading.jsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingComponent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Loading.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/ReduxLayout/appDetails.jsx":
/*!********************************************!*\
  !*** ./src/app/ReduxLayout/appDetails.jsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"(ssr)/./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"(ssr)/./node_modules/@fortawesome/free-solid-svg-icons/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _SideBar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../SideBar */ \"(ssr)/./src/app/SideBar.jsx\");\n/* harmony import */ var _redux_features_appSlice__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../redux/features/appSlice */ \"(ssr)/./src/app/redux/features/appSlice.js\");\n/* harmony import */ var _Ads__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Ads */ \"(ssr)/./src/app/Ads.jsx\");\n/* harmony import */ var _SkeletonDetails__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../SkeletonDetails */ \"(ssr)/./src/app/SkeletonDetails.jsx\");\n/* harmony import */ var _utils_crossLinking__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../utils/crossLinking */ \"(ssr)/./src/app/utils/crossLinking.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nconst AppDetails = ({ appId, name, categories })=>{\n    const [openIndex, setOpenIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [selectedVersion, setSelectedVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [foundCategory, setFoundCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [contentHeight, setContentHeight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"90px\");\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { loading, error } = (0,react_redux__WEBPACK_IMPORTED_MODULE_10__.useSelector)((state)=>state.app);\n    const MAX_LENGTH = 900;\n    const isAdsServe = JSON.parse(\"true\");\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_10__.useDispatch)();\n    const appDetails = (0,react_redux__WEBPACK_IMPORTED_MODULE_10__.useSelector)((state)=>state.app.appDetail.app?.appDetails);\n    const combinedScreenshots = [\n        appDetails?.headerImage,\n        appDetails?.video,\n        ...Array.isArray(appDetails?.screenshots) ? appDetails.screenshots : []\n    ].filter(Boolean);\n    const handlePrev = ()=>{\n        setActiveIndex((prevIndex)=>prevIndex === 0 ? combinedScreenshots.length - 1 : prevIndex - 1);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (contentRef.current) {\n            setContentHeight(isExpanded ? `${contentRef.current.scrollHeight}px` : \"90px\");\n        }\n    }, [\n        isExpanded\n    ]);\n    const handleNext = ()=>{\n        setActiveIndex((prevIndex)=>prevIndex === combinedScreenshots.length - 1 ? 0 : prevIndex + 1);\n    };\n    const toggleContent = ()=>{\n        setIsExpanded(!isExpanded);\n    };\n    const contentLength = appDetails?.description.length;\n    const shouldShowLess = contentLength > MAX_LENGTH;\n    const appVersions = (0,react_redux__WEBPACK_IMPORTED_MODULE_10__.useSelector)((state)=>state.app.appDetail.app?.appVersions);\n    const recentlyUpdatedApps = (0,react_redux__WEBPACK_IMPORTED_MODULE_10__.useSelector)((state)=>state.app.appDetail.app?.recentlyUpdatedApps);\n    const similarApps = (0,react_redux__WEBPACK_IMPORTED_MODULE_10__.useSelector)((state)=>state.app.appDetail.app?.similarApps);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        dispatch((0,_redux_features_appSlice__WEBPACK_IMPORTED_MODULE_6__.getAppDetails)(appId));\n    }, [\n        dispatch,\n        appId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const latestVersionData = appVersions?.versions[1];\n        setSelectedVersion(latestVersionData ? latestVersionData : appVersions?.versions[0]);\n        const category = categories.find((item)=>item.name === appVersions?.category);\n        setFoundCategory(category);\n    }, [\n        appVersions\n    ]);\n    const indexOfLastItem = currentPage * 5;\n    const indexOfFirstItem = indexOfLastItem - 5;\n    const currentItems = appVersions?.versions?.filter((version)=>!version.latestVersion).slice(indexOfFirstItem, indexOfLastItem);\n    const toggleDetails = (index)=>{\n        setOpenIndex(openIndex === index ? null : index);\n    };\n    const pagination = (pageNumber)=>{\n        setCurrentPage(pageNumber);\n        setOpenIndex(null);\n    };\n    const handleVersionDownload = (version)=>{\n        setSelectedVersion(version);\n        setOpenIndex(null);\n    };\n    const formatInstalls = (installs)=>{\n        if (installs >= 1000000000) {\n            return Math.floor(installs / 1000000000) + \"B+\";\n        } else if (installs >= 1000000) {\n            return Math.floor(installs / 1000000) + \"M+\";\n        } else if (installs >= 1000) {\n            return Math.floor(installs / 1000) + \"K+\";\n        } else {\n            return installs;\n        }\n    };\n    const formatRatingsAndReviews = (ratings)=>{\n        if (ratings >= 1000000000) {\n            return (Math.floor(ratings / 10000000) / 100).toFixed(1) + \"B+\";\n        } else if (ratings >= 1000000) {\n            return (Math.floor(ratings / 10000) / 100).toFixed(1) + \"M+\";\n        } else if (ratings >= 1000) {\n            return Math.floor(ratings / 1000) + \"K+\";\n        } else {\n            return ratings;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: appDetails && appVersions && !loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"metadata\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: appDetails.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"lg:container flex flex-col items-center justify-between mt-0.5 mx-5 sm:mx-0 md:mx-20 lg:mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto max-w-screen-xl\",\n                        children: [\n                            isAdsServe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Ads__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                slot: 19,\n                                className: \"mb-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                lineNumber: 147,\n                                columnNumber: 30\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \" w-full md:px-3.5 justify-center flex flex-col lg:flex-row\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:w-4/6 xl:w-4/6 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3.5 pl-2.5 min-h-5 bg-white rounded-md shadow-md\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-1 p-3 text-sm font-normal\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-[10px] sm:text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                href: \"/\",\n                                                                children: \"Home\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \"\\xa0/\\xa0\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                href: `${name}`,\n                                                                children: [\n                                                                    \" \",\n                                                                    name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \"\\xa0/\\xa0\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                href: `/${name}/${foundCategory?.category}`,\n                                                                children: appVersions.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \"\\xa0/ \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-slate-500\",\n                                                                children: appDetails.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 113\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3.5 \",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-5 pt-5 bg-white rounded-md shadow-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-3.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-2xl text-center md:text-left\",\n                                                                children: [\n                                                                    appDetails.title,\n                                                                    \" APK\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3.5 w-full flex-col flex md:flex-row\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \" justify-center items-center mb-3 md:mb-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"rounded-3xl\",\n                                                                                src: appDetails.icon,\n                                                                                alt: `${appDetails.title} icon`,\n                                                                                width: 176,\n                                                                                height: 176,\n                                                                                priority: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 169,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                            lineNumber: 168,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center p-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-yellow-400 inline\",\n                                                                                    \"aria-hidden\": \"true\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 22 20\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                        lineNumber: 186,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                    lineNumber: 179,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        \" \",\n                                                                                        appDetails.scoreText,\n                                                                                        \" / 5\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                    lineNumber: 188,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-400\",\n                                                                                    children: [\n                                                                                        \" \",\n                                                                                        formatRatingsAndReviews(appDetails.ratings),\n                                                                                        \" \",\n                                                                                        \"Ratings\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                    lineNumber: 189,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                            lineNumber: 178,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full md:w-4/6 flex justify-center items-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-x-1 gap-y-1 sm:gap-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"uppercase text-[10px] sm:text-sm text-center text-slate-400 sm:text-right truncate\",\n                                                                                children: \"CURRENT VERSION\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 198,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"md:col-span-1 col-span-1 text-[10px] sm:text-sm text-center sm:text-left truncate mb-3 sm:mb-0\",\n                                                                                children: selectedVersion?.versionNumber\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 201,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"uppercase text-[10px] sm:text-sm text-center text-slate-400 sm:text-right truncate\",\n                                                                                children: \"date published\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 204,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"md:col-span-1 col-span-1 text-[10px] sm:text-sm text-center sm:text-left truncate mb-3 sm:mb-0\",\n                                                                                children: selectedVersion?.updated\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 207,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"uppercase text-[10px] sm:text-sm text-center text-slate-400 sm:text-right truncate\",\n                                                                                children: \"FILE SIZE\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 210,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"md:col-span-1 col-span-1 text-[10px] sm:text-sm text-center sm:text-left truncate mb-3 sm:mb-0\",\n                                                                                children: selectedVersion?.size\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 213,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"uppercase text-[10px] sm:text-sm text-center text-slate-400 sm:text-right truncate\",\n                                                                                children: \"PACKAGE ID\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 216,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"md:col-span-1 col-span-1 text-[10px] sm:text-sm text-center sm:text-left truncate mb-3 sm:mb-0\",\n                                                                                children: appVersions.appId\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 219,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"uppercase text-[10px] sm:text-sm text-center text-slate-400 sm:text-right truncate\",\n                                                                                children: \"DOWNLOADS\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 222,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"md:col-span-1 col-span-1 text-[10px] sm:text-sm text-center sm:text-left truncate mb-3 sm:mb-0\",\n                                                                                children: formatInstalls(appDetails.maxInstalls)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 225,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"uppercase text-[10px] sm:text-sm text-center text-slate-400 sm:text-right truncate\",\n                                                                                children: \"CATEGORY\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 229,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"md:col-span-1 col-span-1 text-[10px] sm:text-sm text-center sm:text-left truncate mb-3 sm:mb-0\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                    href: `/${name}`,\n                                                                                    prefetch: false,\n                                                                                    children: [\n                                                                                        \"Android \",\n                                                                                        name\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                    lineNumber: 233,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 232,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"uppercase text-[10px] sm:text-sm text-center text-slate-400 sm:text-right truncate\",\n                                                                                children: \"GENRE\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 238,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \" md:col-span-1 col-span-1 text-[10px] sm:text-sm text-center sm:text-left truncate mb-3 sm:mb-0\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                    href: `/${name}/${foundCategory?.category}`,\n                                                                                    prefetch: false,\n                                                                                    children: appVersions.category\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                    lineNumber: 242,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 241,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"-mx-5 px-1.5 py-2 bg-neutral-100 rounded-b-md\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-4 flex flex-col sm:flex-row gap-3 justify-center items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                        href: `/${name}/appdetails/${appVersions.appId}/${selectedVersion?.versionNumber?.split(\" \")[0]}#download`,\n                                                                        // target=\"_blank\"\n                                                                        className: \"px-6 py-3 flex items-center justify-between gap-2 text-xs sm:text-sm md:text-base bg-slate-900 text-white uppercase rounded-md hover:bg-slate-700\",\n                                                                        prefetch: false,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                                className: \"pr-1\",\n                                                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faDownload\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 261,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            \"DOWNLOAD APK\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    (0,_utils_crossLinking__WEBPACK_IMPORTED_MODULE_9__.isCrossLinkingEnabled)() && (()=>{\n                                                                        const aiToolsURL = (0,_utils_crossLinking__WEBPACK_IMPORTED_MODULE_9__.generateAIToolsURL)({\n                                                                            appId: appVersions?.appId,\n                                                                            category: appVersions?.category,\n                                                                            title: appDetails?.title\n                                                                        });\n                                                                        const anchorText = (0,_utils_crossLinking__WEBPACK_IMPORTED_MODULE_9__.generateAnchorText)(\"ai-tools\", {\n                                                                            category: appVersions?.category\n                                                                        });\n                                                                        if (!aiToolsURL) return null;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            href: aiToolsURL,\n                                                                            target: \"_blank\",\n                                                                            rel: \"noopener noreferrer\",\n                                                                            className: \"px-6 py-3 flex items-center justify-between gap-2 text-xs sm:text-sm md:text-base bg-blue-600 text-white uppercase rounded-md hover:bg-blue-700 transition-colors\",\n                                                                            onClick: ()=>(0,_utils_crossLinking__WEBPACK_IMPORTED_MODULE_9__.trackCrossLinkClick)(\"apk-site\", \"ai-tools\", {\n                                                                                    appId: appVersions?.appId,\n                                                                                    title: appDetails?.title\n                                                                                }),\n                                                                            title: `Discover AI tools related to ${appDetails?.title || \"this app\"}`,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4\",\n                                                                                    fill: \"none\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                        lineNumber: 291,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                    lineNumber: 290,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                anchorText\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                            lineNumber: 279,\n                                                                            columnNumber: 31\n                                                                        }, undefined);\n                                                                    })()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            isAdsServe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Ads__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                slot: 20,\n                                                className: \"mt-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 34\n                                            }, undefined),\n                                            currentItems?.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-5 \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"mt-7 text-xl font-normal\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"APK\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            \" Version History\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-5\",\n                                                        children: currentItems?.map((version, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-2.5 py-4 px-2.5 bg-white rounded-md shadow-md transform origin-center transition duration-200 ease-out\",\n                                                                        onClick: ()=>toggleDetails(index),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"mt-0.5 mr-2.5 pl-1.5 \",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                                    icon: openIndex !== index ? _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faAnglesDown : _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faAnglesUp\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                    lineNumber: 315,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 314,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"p-1.5 rounded-md font-bold bg-gray-100\",\n                                                                                children: [\n                                                                                    \"v\",\n                                                                                    version.versionNumber.split(\" \")[0]\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 323,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"p-1 float-right px-4 mr-3.5 bg-slate-900 text-white uppercase rounded-md hover:bg-slate-700\",\n                                                                                children: \"VIEW\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 326,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    openIndex === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-4 border-t rounded-md border-gray-300 bg-white shadow-md transition-all duration-900 ease-in-out\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full bg-gray-100 p-2.5 mb-2.5 px-3.5 flex-col flex md:flex-row transition-all duration-300 ease-in-out\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"md:w-4/6 px-3.5 \",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-x-1 gap-y-1 sm:gap-y-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \" text-xs text-slate-400 md:text-right\",\n                                                                                                children: \"VERSION\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                lineNumber: 335,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"md:col-span-1 col-span-1 leading-4 text-xs sm:text-sm truncate\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                                    href: \"\",\n                                                                                                    children: version.versionNumber\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                    lineNumber: 339,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                lineNumber: 338,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-slate-400 md:text-right\",\n                                                                                                children: \"SIZE\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                lineNumber: 343,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"md:col-span-1 col-span-1 leading-4 text-xs sm:text-sm truncate\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                                    href: \"\",\n                                                                                                    children: version.size\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                    lineNumber: 347,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                lineNumber: 346,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-slate-400 md:text-right\",\n                                                                                                children: \"RELEASE DATE\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                lineNumber: 349,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"md:col-span-1 col-span-1 leading-9 text-xs sm:text-sm truncate\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                                    href: \"\",\n                                                                                                    children: version.updated\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                    lineNumber: 353,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                lineNumber: 352,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-slate-400 md:text-right\",\n                                                                                                children: \"REQUIREMENT\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                lineNumber: 355,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"md:col-span-1 col-span-1 leading-9 text-xs sm:text-sm truncate\",\n                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                                    href: \"\",\n                                                                                                    children: version.minimum.split(\"(\")[0]\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                    lineNumber: 359,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                lineNumber: 358,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                        lineNumber: 334,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                    lineNumber: 333,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleVersionDownload(version),\n                                                                                    className: \"md:w-4/6 text-center md:mt-10 my-4\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                        href: `/apps/appdetails/${appVersions.appId}#${version.versionNumber.split(\" \")[0]}`,\n                                                                                        // target=\"_blank\"\n                                                                                        className: \"px-6 py-3 bg-slate-900 text-white uppercase rounded-md  hover:bg-slate-700   \",\n                                                                                        prefetch: false,\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                                                className: \"pr-1\",\n                                                                                                icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faDownload\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                                lineNumber: 376,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            \"DOWNLOAD \",\n                                                                                            version.size\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                        lineNumber: 369,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                    lineNumber: 365,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 27\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap justify-center sm:justify-between gap-2 sm:gap-0 text-xl items-center \",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-5\",\n                                                                children: [\n                                                                    \"Page\",\n                                                                    \" \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"underline\",\n                                                                        children: [\n                                                                            \" \",\n                                                                            currentPage === 1 ? 1 : 2\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    \"Out Of \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"underline\",\n                                                                        children: 2\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                        lineNumber: 396,\n                                                                        columnNumber: 34\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-5\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>pagination(currentPage - 1),\n                                                                        disabled: currentPage === 1,\n                                                                        className: `flex-row  items-center justify-center px-5 mx-2 h-11 leading-tight rounded-s-lg text-gray-500 border border-gray-300 hover:bg-gray-100 ${currentPage === 1 ? \"bg-gray-100 \" : \"bg-white hover:text-gray-700\"}`,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faAnglesLeft\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>pagination(currentPage + 1),\n                                                                        disabled: currentPage === Math.ceil(appVersions.versions.filter((version)=>!version.latestVersion).length / 5),\n                                                                        className: `flex-row items-center justify-center px-5 h-11 leading-tight rounded-e-lg text-gray-500  border border-gray-300 hover:bg-gray-100 ${currentPage === Math.ceil(appVersions.versions.filter((version)=>!version.latestVersion).length / 5) ? \"bg-gray-100 \" : \"bg-white hover:text-gray-700\"}`,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_2__.FontAwesomeIcon, {\n                                                                            icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_11__.faAnglesRight\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"mt-7 text-xl font-normal\",\n                                                children: [\n                                                    \"There is \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"NO APK\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 32\n                                                    }, undefined),\n                                                    \" Version History\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            isAdsServe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"block sm:hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Ads__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    slot: 22,\n                                                    className: \"mb-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"my-5 p-5 bg-white rounded-md shadow-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"mb-2.5 text-base font-normal text-slate-500 uppercase tracking-wider\",\n                                                        children: [\n                                                            appDetails.title,\n                                                            \" screenShots\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        id: \"indicators-carousel\",\n                                                        className: \"relative w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative h-56 overflow-hidden rounded-lg md:h-96\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex transition-transform duration-700 ease-in-out\",\n                                                                    style: {\n                                                                        transform: `translateX(-${activeIndex * 100}%)`\n                                                                    },\n                                                                    children: combinedScreenshots.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full flex-shrink-0\",\n                                                                            \"data-carousel-item\": index === activeIndex ? \"active\" : \"\",\n                                                                            children: item === appDetails.video ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                                                                className: \"object-contain w-full h-full py-2 bg-gray-100\",\n                                                                                src: item,\n                                                                                title: \"YouTube video\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 460,\n                                                                                columnNumber: 33\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"rounded-sm block w-full\",\n                                                                                src: item,\n                                                                                alt: `Slide ${index + 1}`,\n                                                                                width: 176,\n                                                                                height: 176,\n                                                                                priority: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                                lineNumber: 466,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, index, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 29\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute z-5 flex -translate-x-1/2 space-x-3 bottom-5 left-1/2\",\n                                                                children: combinedScreenshots.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setActiveIndex(index),\n                                                                        className: `w-3 h-3 rounded-full ${index === activeIndex ? \"bg-slate-900\" : \"bg-slate-400\"}`,\n                                                                        \"aria-label\": `Slide ${index + 1}`\n                                                                    }, index, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 27\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handlePrev,\n                                                                className: \"absolute top-0 left-0 z-5 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none\",\n                                                                \"data-carousel-prev\": true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center justify-center w-10 h-10 rounded-full bg-slate-900 dark:bg-gray-800/30 group-hover:bg-slate-700 dark:group-hover:bg-gray-800/60 group-focus:ring-4 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                        className: \"fa-solid text-white fa-chevron-left\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                        lineNumber: 498,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleNext,\n                                                                className: \"absolute top-0 right-0 z-5 flex items-center justify-center h-full px-4 cursor-pointer group focus:outline-none\",\n                                                                \"data-carousel-next\": true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center justify-center w-10 h-10 rounded-full bg-slate-900 dark:bg-gray-800/30 group-hover:bg-slate-700 dark:group-hover:bg-gray-800/60 group-focus:ring-4 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                        className: \"fa-solid text-white fa-chevron-right\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"my-5 p-5 bg-white rounded-md shadow-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"mb-2.5 text-base font-normal text-slate-500 uppercase tracking-wider\",\n                                                        children: [\n                                                            \"Overview of \",\n                                                            appDetails?.title\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        ref: contentRef,\n                                                        style: {\n                                                            maxHeight: contentHeight,\n                                                            overflow: \"hidden\"\n                                                        },\n                                                        className: \"transition-max-height duration-500 ease-in-out\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"whitespace-pre-wrap\",\n                                                            children: appDetails.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    shouldShowLess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: toggleContent,\n                                                        className: \"flex justify-center align-center w-full text-white font-medium text-sm mt-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center justify-center w-10 h-10 rounded-full bg-slate-900 hover:bg-slate-600 dark:bg-gray-800/30 group-hover:bg-slate-600 dark:group-hover:bg-gray-800/60 group-focus:ring-4 group-focus:ring-white dark:group-focus:ring-gray-800/70 group-focus:outline-none\",\n                                                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fa-solid fa-angle-up\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 33\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fa-solid fa-angle-down\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                                        className: \" sm:w-auto lg:w-2/6 lg:px-3.5 \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideBar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sideappDetails: similarApps,\n                                                header: \"SIMILAR APPS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            isAdsServe && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden sm:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Ads__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    slot: 22,\n                                                    className: \"mb-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SideBar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sideappDetails: recentlyUpdatedApps,\n                                                header: \"RECENTLY UPDATED APPS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto max-w-screen-xl flex items-center justify-center h-screen\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                lineNumber: 565,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SkeletonDetails__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\appDetails.jsx\",\n                lineNumber: 565,\n                columnNumber: 130\n            }, undefined)\n        }, void 0, false)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppDetails);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/ReduxLayout/appDetails.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/ReduxLayout/layout.js":
/*!***************************************!*\
  !*** ./src/app/ReduxLayout/layout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReduxProvider: () => (/* binding */ ReduxProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _redux_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../redux/store */ \"(ssr)/./src/app/redux/store.js\");\n/* __next_internal_client_entry_do_not_use__ ReduxProvider auto */ \n\n\nfunction ReduxProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_2__.Provider, {\n        store: _redux_store__WEBPACK_IMPORTED_MODULE_1__.store,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\ReduxLayout\\\\layout.js\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1JlZHV4TGF5b3V0L2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDdUM7QUFDQTtBQUVoQyxTQUFTRSxjQUFjLEVBQUVDLFFBQVEsRUFBRTtJQUN4QyxxQkFBTyw4REFBQ0gsaURBQVFBO1FBQUNDLE9BQU9BLCtDQUFLQTtrQkFBR0U7Ozs7OztBQUNsQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Fway1taXJyb3IvLi9zcmMvYXBwL1JlZHV4TGF5b3V0L2xheW91dC5qcz8xYzE1Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHsgUHJvdmlkZXIgfSBmcm9tIFwicmVhY3QtcmVkdXhcIjtcbmltcG9ydCB7IHN0b3JlIH0gZnJvbSBcIi4uL3JlZHV4L3N0b3JlXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBSZWR1eFByb3ZpZGVyKHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gPFByb3ZpZGVyIHN0b3JlPXtzdG9yZX0+e2NoaWxkcmVufTwvUHJvdmlkZXI+O1xufVxuIl0sIm5hbWVzIjpbIlByb3ZpZGVyIiwic3RvcmUiLCJSZWR1eFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/ReduxLayout/layout.js\n");

/***/ }),

/***/ "(ssr)/./src/app/SideBar.jsx":
/*!*****************************!*\
  !*** ./src/app/SideBar.jsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Loading__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Loading */ \"(ssr)/./src/app/Loading.jsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst SideBar = ({ sideappDetails, header = \"RECENTLY UPDATED APPS\", isLoading })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-3.5 p-5 bg-white rounded-md shadow-md \",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"mb-2.5 font-normal text-slate-500 tracking-wider uppercase\",\n                children: header\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loading__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                length: 5,\n                md: 1\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid sm:grid-cols-2 lg:grid-cols-1\",\n                children: sideappDetails?.map((appDetails)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: `/apps/appdetails/${appDetails.appId}`,\n                        target: \"_blank\",\n                        prefetch: false,\n                        className: \"mt-2.5 p-1.5 hover:bg-gray-100 p-1 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \" grid grid-cols-4 lg:grid-cols-3 xl:grid-cols-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    width: 75,\n                                    height: 75,\n                                    className: \"rounded-2xl\",\n                                    src: appDetails.icon,\n                                    alt: `${appDetails.title} Icon`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 17\n                                }, undefined),\n                                header === \"RECENTLY UPDATED APPS\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-2 col-span-2 \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium truncate\",\n                                            title: appDetails.title,\n                                            children: appDetails.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-400 tracking-wider\",\n                                            children: [\n                                                \"VERSION \",\n                                                appDetails.latestVersion?.split(\" \")[0]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-400 uppercase tracking-wider\",\n                                            children: appDetails.updated\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-yellow-400 me-1\",\n                                                    \"aria-hidden\": \"true\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 22 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-slate-400 dark:text-slate-400\",\n                                                    children: appDetails.scoreText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-2 col-span-2 \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium truncate\",\n                                            title: appDetails.title,\n                                            children: appDetails.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-400 tracking-wider uppercase\",\n                                            children: appDetails.developer\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-yellow-400 me-1\",\n                                                    \"aria-hidden\": \"true\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 22 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M20.924 7.625a1.523 1.523 0 0 0-1.238-1.044l-5.051-.734-2.259-4.577a1.534 1.534 0 0 0-2.752 0L7.365 5.847l-5.051.734A1.535 1.535 0 0 0 1.463 9.2l3.656 3.563-.863 5.031a1.532 1.532 0 0 0 2.226 1.616L11 17.033l4.518 2.375a1.534 1.534 0 0 0 2.226-1.617l-.863-5.03L20.537 9.2a1.523 1.523 0 0 0 .387-1.575Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-bold text-slate-400 dark:text-slate-400\",\n                                                    children: appDetails.scoreText\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                            lineNumber: 28,\n                            columnNumber: 15\n                        }, undefined)\n                    }, appDetails.appId, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                        lineNumber: 21,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SideBar.jsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SideBar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/SideBar.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/SkeletonDetails.jsx":
/*!*************************************!*\
  !*** ./src/app/SkeletonDetails.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Loading__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Loading */ \"(ssr)/./src/app/Loading.jsx\");\n\n\n\nconst SkeletonDetails = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"lg:container flex flex-col items-center justify-between mt-0.5 mx-5 sm:mx-0 md:mx-20 lg:mx-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"lg:container flex flex-col items-center justify-between mt-0.5 mx-5 sm:mx-0 md:mx-20 lg:mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto max-w-screen-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full justify-center flex flex-wrap\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"w-full xl:w-4/6 mt-4 relative px-0 xl:px-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3.5 p-5 bg-white rounded-lg shadow-md flex flex-col\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-1 xl:grid-cols-1 gap-4 p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            role: \"status\",\n                                            className: \"space-y-8 animate-pulse md:space-y-0 md:space-x-8 rtl:space-x-reverse md:flex md:items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center w-full h-48 bg-gray-300 rounded sm:w-96 dark:bg-gray-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-10 h-10 text-gray-200 dark:text-gray-600\",\n                                                        \"aria-hidden\": \"true\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 18\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M18 0H2a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Zm-5.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm4.376 10.481A1 1 0 0 1 16 15H4a1 1 0 0 1-.895-1.447l3.5-7A1 1 0 0 1 7.468 6a.965.965 0 0 1 .9.5l2.775 4.757 1.546-1.887a1 1 0 0 1 1.618.1l2.541 4a1 1 0 0 1 .028 1.011Z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                            lineNumber: 18,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                        lineNumber: 17,\n                                                        columnNumber: 45\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                    lineNumber: 16,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-2.5 bg-gray-200 rounded-full dark:bg-gray-700 w-48 mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                            lineNumber: 22,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-2 bg-gray-200 rounded-full dark:bg-gray-700 max-w-[480px] mb-2.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                            lineNumber: 23,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-2 bg-gray-200 rounded-full dark:bg-gray-700 mb-2.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                            lineNumber: 24,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-2 bg-gray-200 rounded-full dark:bg-gray-700 max-w-[440px] mb-2.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                            lineNumber: 25,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-2 bg-gray-200 rounded-full dark:bg-gray-700 max-w-[460px] mb-2.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                            lineNumber: 26,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-2 bg-gray-200 rounded-full dark:bg-gray-700 max-w-[360px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                            lineNumber: 27,\n                                                            columnNumber: 45\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                    lineNumber: 21,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Loading...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                    lineNumber: 29,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                            lineNumber: 15,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                        lineNumber: 13,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-5 p-5 bg-white rounded-md shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        id: \"indicators-carousel\",\n                                        className: \"relative w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            role: \"status\",\n                                            className: \"flex items-center justify-center h-56  bg-gray-300 rounded-lg animate-pulse dark:bg-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-10 h-10 text-gray-200 dark:text-gray-600\",\n                                                    \"aria-hidden\": \"true\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 16 20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.98 2.98 0 0 0 .13 5H5Z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                            lineNumber: 40,\n                                                            columnNumber: 45\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M14.066 0H7v5a2 2 0 0 1-2 2H0v11a1.97 1.97 0 0 0 1.934 2h12.132A1.97 1.97 0 0 0 16 18V2a1.97 1.97 0 0 0-1.934-2ZM9 13a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2Zm4 .382a1 1 0 0 1-1.447.894L10 13v-2l1.553-1.276a1 1 0 0 1 1.447.894v2.764Z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                            lineNumber: 41,\n                                                            columnNumber: 45\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 41\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Loading...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 41\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-5 p-5 bg-white rounded-md shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        role: \"status\",\n                                        className: \" p-4 space-y-4 border border-gray-200 divide-y divide-gray-200 rounded shadow animate-pulse dark:divide-gray-700 md:p-6 dark:border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                                lineNumber: 51,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                                lineNumber: 52,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                        lineNumber: 50,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                        lineNumber: 54,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                                lineNumber: 58,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                                lineNumber: 59,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                        lineNumber: 57,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                                lineNumber: 65,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                                lineNumber: 66,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                                lineNumber: 72,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                                lineNumber: 73,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                                lineNumber: 79,\n                                                                columnNumber: 45\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                                lineNumber: 80,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Loading...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                            lineNumber: 10,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                            className: \" sidebar-container w-full xl:w-2/6 xl:px-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-3.5 p-5 bg-white rounded-md shadow-md \",\n                                    children: [\n                                        \"  \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loading__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            length: 4,\n                                            md: 1,\n                                            lg: 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 90\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-3.5 p-5 bg-white rounded-md shadow-md \",\n                                    children: [\n                                        \"  \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loading__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            length: 4,\n                                            md: 1,\n                                            lg: 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 90\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                            lineNumber: 88,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                    lineNumber: 9,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n                lineNumber: 8,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n            lineNumber: 7,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\SkeletonDetails.jsx\",\n        lineNumber: 6,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SkeletonDetails);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL1NrZWxldG9uRGV0YWlscy5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQjtBQUNlO0FBRXpDLE1BQU1FLGtCQUFrQjtJQUNwQixxQkFDSSw4REFBQ0M7UUFBS0MsV0FBVTtrQkFDWiw0RUFBQ0M7WUFBSUQsV0FBVTtzQkFDWCw0RUFBQ0M7Z0JBQUlELFdBQVU7MEJBQ1gsNEVBQUNDO29CQUFJRCxXQUFVOztzQ0FDWCw4REFBQ0Q7NEJBQUtDLFdBQVU7OzhDQUNaLDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FFWCw0RUFBQ0M7d0NBQUlELFdBQVU7a0RBRVgsNEVBQUNDOzRDQUFJQyxNQUFLOzRDQUFTRixXQUFVOzs4REFDekIsOERBQUNDO29EQUFJRCxXQUFVOzhEQUNYLDRFQUFDRzt3REFBSUgsV0FBVTt3REFBNkNJLGVBQVk7d0RBQU9DLE9BQU07d0RBQTZCQyxNQUFLO3dEQUFlQyxTQUFRO2tFQUMxSSw0RUFBQ0M7NERBQUtDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OERBR2hCLDhEQUFDUjtvREFBSUQsV0FBVTs7c0VBQ1gsOERBQUNDOzREQUFJRCxXQUFVOzs7Ozs7c0VBQ2YsOERBQUNDOzREQUFJRCxXQUFVOzs7Ozs7c0VBQ2YsOERBQUNDOzREQUFJRCxXQUFVOzs7Ozs7c0VBQ2YsOERBQUNDOzREQUFJRCxXQUFVOzs7Ozs7c0VBQ2YsOERBQUNDOzREQUFJRCxXQUFVOzs7Ozs7c0VBQ2YsOERBQUNDOzREQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7OERBRW5CLDhEQUFDVTtvREFBS1YsV0FBVTs4REFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJdEMsOERBQUNDO29DQUFJRCxXQUFVOzhDQUVYLDRFQUFDQzt3Q0FBSVUsSUFBRzt3Q0FBc0JYLFdBQVU7a0RBR3BDLDRFQUFDQzs0Q0FBSUMsTUFBSzs0Q0FBU0YsV0FBVTs7OERBQ3pCLDhEQUFDRztvREFBSUgsV0FBVTtvREFBNkNJLGVBQVk7b0RBQU9DLE9BQU07b0RBQTZCQyxNQUFLO29EQUFlQyxTQUFROztzRUFDMUksOERBQUNDOzREQUFLQyxHQUFFOzs7Ozs7c0VBQ1IsOERBQUNEOzREQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7OERBRVosOERBQUNDO29EQUFLVixXQUFVOzhEQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUl0Qyw4REFBQ0M7b0NBQUlELFdBQVU7OENBQ1gsNEVBQUNDO3dDQUFJQyxNQUFLO3dDQUFTRixXQUFVOzswREFDekIsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFDWCw4REFBQ0M7OzBFQUNHLDhEQUFDQTtnRUFBSUQsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDQztnRUFBSUQsV0FBVTs7Ozs7Ozs7Ozs7O2tFQUVuQiw4REFBQ0M7d0RBQUlELFdBQVU7Ozs7Ozs7Ozs7OzswREFFbkIsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFDWCw4REFBQ0M7OzBFQUNHLDhEQUFDQTtnRUFBSUQsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDQztnRUFBSUQsV0FBVTs7Ozs7Ozs7Ozs7O2tFQUVuQiw4REFBQ0M7d0RBQUlELFdBQVU7Ozs7Ozs7Ozs7OzswREFFbkIsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFDWCw4REFBQ0M7OzBFQUNHLDhEQUFDQTtnRUFBSUQsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDQztnRUFBSUQsV0FBVTs7Ozs7Ozs7Ozs7O2tFQUVuQiw4REFBQ0M7d0RBQUlELFdBQVU7Ozs7Ozs7Ozs7OzswREFFbkIsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFDWCw4REFBQ0M7OzBFQUNHLDhEQUFDQTtnRUFBSUQsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDQztnRUFBSUQsV0FBVTs7Ozs7Ozs7Ozs7O2tFQUVuQiw4REFBQ0M7d0RBQUlELFdBQVU7Ozs7Ozs7Ozs7OzswREFFbkIsOERBQUNDO2dEQUFJRCxXQUFVOztrRUFDWCw4REFBQ0M7OzBFQUNHLDhEQUFDQTtnRUFBSUQsV0FBVTs7Ozs7OzBFQUNmLDhEQUFDQztnRUFBSUQsV0FBVTs7Ozs7Ozs7Ozs7O2tFQUVuQiw4REFBQ0M7d0RBQUlELFdBQVU7Ozs7Ozs7Ozs7OzswREFFbkIsOERBQUNVO2dEQUFLVixXQUFVOzBEQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJdEMsOERBQUNZOzRCQUFNWixXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7O3dDQUE0QztzREFBRSw4REFBQ0gsZ0RBQWdCQTs0Q0FBQ2dCLFFBQVE7NENBQUdDLElBQUk7NENBQUdDLElBQUk7Ozs7Ozs7Ozs7Ozs4Q0FFckcsOERBQUNkO29DQUFJRCxXQUFVOzt3Q0FBNEM7c0RBQUUsOERBQUNILGdEQUFnQkE7NENBQUNnQixRQUFROzRDQUFHQyxJQUFJOzRDQUFHQyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPakk7QUFFQSxpRUFBZWpCLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vc3JjL2FwcC9Ta2VsZXRvbkRldGFpbHMuanN4PzA1NjQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IExvYWRpbmdDb21wb25lbnQgZnJvbSAnLi9Mb2FkaW5nJztcclxuXHJcbmNvbnN0IFNrZWxldG9uRGV0YWlscyA9ICgpID0+IHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwibGc6Y29udGFpbmVyIGZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtdC0wLjUgbXgtNSBzbTpteC0wIG1kOm14LTIwIGxnOm14LWF1dG9cIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb250YWluZXIgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG10LTAuNSBteC01IHNtOm14LTAgbWQ6bXgtMjAgbGc6bXgtYXV0b1wiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBtYXgtdy1zY3JlZW4teGxcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBqdXN0aWZ5LWNlbnRlciBmbGV4IGZsZXgtd3JhcFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJ3LWZ1bGwgeGw6dy00LzYgbXQtNCByZWxhdGl2ZSBweC0wIHhsOnB4LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMy41IHAtNSBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1tZCBmbGV4IGZsZXgtY29sXCI+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMSB4bDpncmlkLWNvbHMtMSBnYXAtNCBwLTRcIj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgcm9sZT1cInN0YXR1c1wiIGNsYXNzTmFtZT1cInNwYWNlLXktOCBhbmltYXRlLXB1bHNlIG1kOnNwYWNlLXktMCBtZDpzcGFjZS14LTggcnRsOnNwYWNlLXgtcmV2ZXJzZSBtZDpmbGV4IG1kOml0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3LWZ1bGwgaC00OCBiZy1ncmF5LTMwMCByb3VuZGVkIHNtOnctOTYgZGFyazpiZy1ncmF5LTcwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0xMCBoLTEwIHRleHQtZ3JheS0yMDAgZGFyazp0ZXh0LWdyYXktNjAwXCIgYXJpYS1oaWRkZW49XCJ0cnVlXCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDE4XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMTggMEgyYTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNmEyIDIgMCAwIDAgMi0yVjJhMiAyIDAgMCAwLTItMlptLTUuNSA0YTEuNSAxLjUgMCAxIDEgMCAzIDEuNSAxLjUgMCAwIDEgMC0zWm00LjM3NiAxMC40ODFBMSAxIDAgMCAxIDE2IDE1SDRhMSAxIDAgMCAxLS44OTUtMS40NDdsMy41LTdBMSAxIDAgMCAxIDcuNDY4IDZhLjk2NS45NjUgMCAwIDEgLjkuNWwyLjc3NSA0Ljc1NyAxLjU0Ni0xLjg4N2ExIDEgMCAwIDEgMS42MTguMWwyLjU0MSA0YTEgMSAwIDAgMSAuMDI4IDEuMDExWlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTIuNSBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgZGFyazpiZy1ncmF5LTcwMCB3LTQ4IG1iLTRcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMiBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgZGFyazpiZy1ncmF5LTcwMCBtYXgtdy1bNDgwcHhdIG1iLTIuNVwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0yIGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBkYXJrOmJnLWdyYXktNzAwIG1iLTIuNVwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0yIGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBkYXJrOmJnLWdyYXktNzAwIG1heC13LVs0NDBweF0gbWItMi41XCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTIgYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIGRhcms6YmctZ3JheS03MDAgbWF4LXctWzQ2MHB4XSBtYi0yLjVcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMiBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgZGFyazpiZy1ncmF5LTcwMCBtYXgtdy1bMzYwcHhdXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHlcIj5Mb2FkaW5nLi4uPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteS01IHAtNSBiZy13aGl0ZSByb3VuZGVkLW1kIHNoYWRvdy1tZFwiPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGlkPVwiaW5kaWNhdG9ycy1jYXJvdXNlbFwiIGNsYXNzTmFtZT1cInJlbGF0aXZlIHctZnVsbFwiPlxyXG5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgcm9sZT1cInN0YXR1c1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtNTYgIGJnLWdyYXktMzAwIHJvdW5kZWQtbGcgYW5pbWF0ZS1wdWxzZSBkYXJrOmJnLWdyYXktNzAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMTAgaC0xMCB0ZXh0LWdyYXktMjAwIGRhcms6dGV4dC1ncmF5LTYwMFwiIGFyaWEtaGlkZGVuPVwidHJ1ZVwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAxNiAyMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNNSA1Vi4xM2EyLjk2IDIuOTYgMCAwIDAtMS4yOTMuNzQ5TC44NzkgMy43MDdBMi45OCAyLjk4IDAgMCAwIC4xMyA1SDVaXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTE0LjA2NiAwSDd2NWEyIDIgMCAwIDEtMiAySDB2MTFhMS45NyAxLjk3IDAgMCAwIDEuOTM0IDJoMTIuMTMyQTEuOTcgMS45NyAwIDAgMCAxNiAxOFYyYTEuOTcgMS45NyAwIDAgMC0xLjkzNC0yWk05IDEzYTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0ydi0yYTIgMiAwIDAgMSAyLTJoMmEyIDIgMCAwIDEgMiAydjJabTQgLjM4MmExIDEgMCAwIDEtMS40NDcuODk0TDEwIDEzdi0ybDEuNTUzLTEuMjc2YTEgMSAwIDAgMSAxLjQ0Ny44OTR2Mi43NjRaXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3Itb25seVwiPkxvYWRpbmcuLi48L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm15LTUgcC01IGJnLXdoaXRlIHJvdW5kZWQtbWQgc2hhZG93LW1kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiByb2xlPVwic3RhdHVzXCIgY2xhc3NOYW1lPVwiIHAtNCBzcGFjZS15LTQgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDAgcm91bmRlZCBzaGFkb3cgYW5pbWF0ZS1wdWxzZSBkYXJrOmRpdmlkZS1ncmF5LTcwMCBtZDpwLTYgZGFyazpib3JkZXItZ3JheS03MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTIuNSBiZy1ncmF5LTMwMCByb3VuZGVkLWZ1bGwgZGFyazpiZy1ncmF5LTYwMCB3LTI0IG1iLTIuNVwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zMiBoLTIgYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIGRhcms6YmctZ3JheS03MDBcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTIuNSBiZy1ncmF5LTMwMCByb3VuZGVkLWZ1bGwgZGFyazpiZy1ncmF5LTcwMCB3LTEyXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwdC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0yLjUgYmctZ3JheS0zMDAgcm91bmRlZC1mdWxsIGRhcms6YmctZ3JheS02MDAgdy0yNCBtYi0yLjVcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMzIgaC0yIGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBkYXJrOmJnLWdyYXktNzAwXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0yLjUgYmctZ3JheS0zMDAgcm91bmRlZC1mdWxsIGRhcms6YmctZ3JheS03MDAgdy0xMlwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcHQtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMi41IGJnLWdyYXktMzAwIHJvdW5kZWQtZnVsbCBkYXJrOmJnLWdyYXktNjAwIHctMjQgbWItMi41XCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMyIGgtMiBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgZGFyazpiZy1ncmF5LTcwMFwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMi41IGJnLWdyYXktMzAwIHJvdW5kZWQtZnVsbCBkYXJrOmJnLWdyYXktNzAwIHctMTJcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB0LTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTIuNSBiZy1ncmF5LTMwMCByb3VuZGVkLWZ1bGwgZGFyazpiZy1ncmF5LTYwMCB3LTI0IG1iLTIuNVwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zMiBoLTIgYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIGRhcms6YmctZ3JheS03MDBcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTIuNSBiZy1ncmF5LTMwMCByb3VuZGVkLWZ1bGwgZGFyazpiZy1ncmF5LTcwMCB3LTEyXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwdC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0yLjUgYmctZ3JheS0zMDAgcm91bmRlZC1mdWxsIGRhcms6YmctZ3JheS02MDAgdy0yNCBtYi0yLjVcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMzIgaC0yIGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBkYXJrOmJnLWdyYXktNzAwXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0yLjUgYmctZ3JheS0zMDAgcm91bmRlZC1mdWxsIGRhcms6YmctZ3JheS03MDAgdy0xMlwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3Itb25seVwiPkxvYWRpbmcuLi48L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9tYWluPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8YXNpZGUgY2xhc3NOYW1lPVwiIHNpZGViYXItY29udGFpbmVyIHctZnVsbCB4bDp3LTIvNiB4bDpweC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm15LTMuNSBwLTUgYmctd2hpdGUgcm91bmRlZC1tZCBzaGFkb3ctbWQgXCI+ICA8TG9hZGluZ0NvbXBvbmVudCBsZW5ndGg9ezR9IG1kPXsxfSBsZz17MX0gLz48L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm15LTMuNSBwLTUgYmctd2hpdGUgcm91bmRlZC1tZCBzaGFkb3ctbWQgXCI+ICA8TG9hZGluZ0NvbXBvbmVudCBsZW5ndGg9ezR9IG1kPXsxfSBsZz17MX0gLz48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9hc2lkZT5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L21haW4+XHJcbiAgICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgU2tlbGV0b25EZXRhaWxzO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMb2FkaW5nQ29tcG9uZW50IiwiU2tlbGV0b25EZXRhaWxzIiwibWFpbiIsImNsYXNzTmFtZSIsImRpdiIsInJvbGUiLCJzdmciLCJhcmlhLWhpZGRlbiIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJwYXRoIiwiZCIsInNwYW4iLCJpZCIsImFzaWRlIiwibGVuZ3RoIiwibWQiLCJsZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/SkeletonDetails.jsx\n");

/***/ }),

/***/ "(ssr)/./src/app/apps/appdetails/[appId]/page.js":
/*!*************************************************!*\
  !*** ./src/app/apps/appdetails/[appId]/page.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_Constant_staticData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/Constant/staticData */ \"(ssr)/./src/app/Constant/staticData.js\");\n/* harmony import */ var _app_ReduxLayout_appDetails__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/ReduxLayout/appDetails */ \"(ssr)/./src/app/ReduxLayout/appDetails.jsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// import AppDetails from \"@/app/appDetails\";\n\nconst Page = ({ params })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ReduxLayout_appDetails__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            appId: params.appId,\n            name: \"apps\",\n            categories: _app_Constant_staticData__WEBPACK_IMPORTED_MODULE_1__.androidApps\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\apps\\\\appdetails\\\\[appId]\\\\page.js\",\n            lineNumber: 10,\n            columnNumber: 4\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Page);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2FwcHMvYXBwZGV0YWlscy9bYXBwSWRdL3BhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDd0Q7QUFDRjtBQUN0RCw2Q0FBNkM7QUFDbkI7QUFFMUIsTUFBTUcsT0FBTyxDQUFDLEVBQUNDLE1BQU0sRUFBQztJQUNwQixxQkFDRTtrQkFDRCw0RUFBQ0gsbUVBQVVBO1lBQUNJLE9BQU9ELE9BQU9DLEtBQUs7WUFBRUMsTUFBSztZQUFPQyxZQUFZUCxpRUFBV0E7Ozs7Ozs7QUFJdkU7QUFFQSxpRUFBZUcsSUFBSUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Fway1taXJyb3IvLi9zcmMvYXBwL2FwcHMvYXBwZGV0YWlscy9bYXBwSWRdL3BhZ2UuanM/NjE3MCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuaW1wb3J0IHsgYW5kcm9pZEFwcHMgfSBmcm9tIFwiQC9hcHAvQ29uc3RhbnQvc3RhdGljRGF0YVwiO1xyXG5pbXBvcnQgQXBwRGV0YWlscyBmcm9tIFwiQC9hcHAvUmVkdXhMYXlvdXQvYXBwRGV0YWlsc1wiO1xyXG4vLyBpbXBvcnQgQXBwRGV0YWlscyBmcm9tIFwiQC9hcHAvYXBwRGV0YWlsc1wiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcblxyXG5jb25zdCBQYWdlID0gKHtwYXJhbXN9KSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgIDxBcHBEZXRhaWxzIGFwcElkPXtwYXJhbXMuYXBwSWR9IG5hbWU9XCJhcHBzXCIgY2F0ZWdvcmllcz17YW5kcm9pZEFwcHN9Lz5cclxuICAgIDwvPlxyXG5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgUGFnZTtcclxuIl0sIm5hbWVzIjpbImFuZHJvaWRBcHBzIiwiQXBwRGV0YWlscyIsIlJlYWN0IiwiUGFnZSIsInBhcmFtcyIsImFwcElkIiwibmFtZSIsImNhdGVnb3JpZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/apps/appdetails/[appId]/page.js\n");

/***/ }),

/***/ "(ssr)/./src/app/redux/features/appSlice.js":
/*!********************************************!*\
  !*** ./src/app/redux/features/appSlice.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAppDetails: () => (/* binding */ getAppDetails)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n\nconst getAppDetails = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)(\"app/getAppDetails\", async (appId, { rejectWithValue })=>{\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(`/api/app_by_name_id?appId=${appId}`);\n        if (response && response.status === 200) {\n            return response.data;\n        }\n    } catch (error) {\n        return rejectWithValue(\"App Version not available\");\n    }\n});\nconst initialState = {\n    loading: false,\n    appDetail: [],\n    error: null\n};\nconst appSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: \"app\",\n    initialState,\n    reducers: {},\n    extraReducers: (builder)=>{\n        builder.addCase(getAppDetails.pending, (state)=>{\n            state.loading = true;\n            state.error = null;\n        }).addCase(getAppDetails.fulfilled, (state, action)=>{\n            state.loading = false;\n            state.appDetail = action.payload;\n            state.error = null;\n        }).addCase(getAppDetails.rejected, (state, action)=>{\n            state.loading = false;\n            state.error = action.payload;\n        });\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (appSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/redux/features/appSlice.js\n");

/***/ }),

/***/ "(ssr)/./src/app/redux/store.js":
/*!********************************!*\
  !*** ./src/app/redux/store.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: () => (/* binding */ store)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var _features_appSlice__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./features/appSlice */ \"(ssr)/./src/app/redux/features/appSlice.js\");\n\n\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_1__.configureStore)({\n    reducer: {\n        app: _features_appSlice__WEBPACK_IMPORTED_MODULE_0__[\"default\"]\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3JlZHV4L3N0b3JlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpRDtBQUNQO0FBRW5DLE1BQU1FLFFBQVFGLGdFQUFjQSxDQUFDO0lBQ2xDRyxTQUFTO1FBQ1BDLEtBQU1ILDBEQUFRQTtJQUNoQjtBQUNGLEdBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vc3JjL2FwcC9yZWR1eC9zdG9yZS5qcz9jZjA2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbmZpZ3VyZVN0b3JlIH0gZnJvbSAnQHJlZHV4anMvdG9vbGtpdCdcclxuaW1wb3J0IGFwcFNsaWNlIGZyb20gJy4vZmVhdHVyZXMvYXBwU2xpY2UnXHJcblxyXG5leHBvcnQgY29uc3Qgc3RvcmUgPSBjb25maWd1cmVTdG9yZSh7XHJcbiAgcmVkdWNlcjoge1xyXG4gICAgYXBwIDogYXBwU2xpY2VcclxuICB9LFxyXG59KSJdLCJuYW1lcyI6WyJjb25maWd1cmVTdG9yZSIsImFwcFNsaWNlIiwic3RvcmUiLCJyZWR1Y2VyIiwiYXBwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/redux/store.js\n");

/***/ }),

/***/ "(ssr)/./src/app/utils/appMappingService.js":
/*!********************************************!*\
  !*** ./src/app/utils/appMappingService.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_AI_MAPPINGS: () => (/* binding */ APP_AI_MAPPINGS),\n/* harmony export */   generateSmartAIToolsURL: () => (/* binding */ generateSmartAIToolsURL),\n/* harmony export */   getBestAICategory: () => (/* binding */ getBestAICategory),\n/* harmony export */   getMatchingConfidence: () => (/* binding */ getMatchingConfidence),\n/* harmony export */   getRelevantAICategories: () => (/* binding */ getRelevantAICategories)\n/* harmony export */ });\n/**\n * Intelligent App-to-AI-Tools Mapping Service\n * Provides smart matching between APK apps and AI tools\n */ /**\n * Enhanced app-to-AI-tool mappings with keyword matching\n */ const APP_AI_MAPPINGS = {\n    // Direct app name mappings\n    direct: {\n        \"whatsapp\": [\n            \"social-media\",\n            \"communication\",\n            \"chatbot\"\n        ],\n        \"telegram\": [\n            \"social-media\",\n            \"communication\",\n            \"chatbot\"\n        ],\n        \"instagram\": [\n            \"social-media\",\n            \"image-generation\",\n            \"content-creation\"\n        ],\n        \"tiktok\": [\n            \"video-generation\",\n            \"social-media\",\n            \"content-creation\"\n        ],\n        \"youtube\": [\n            \"video-generation\",\n            \"content-creation\",\n            \"transcription\"\n        ],\n        \"spotify\": [\n            \"music\",\n            \"audio\",\n            \"podcast\"\n        ],\n        \"netflix\": [\n            \"video-generation\",\n            \"entertainment\",\n            \"content-creation\"\n        ],\n        \"canva\": [\n            \"graphic-design\",\n            \"image-generation\",\n            \"presentation\"\n        ],\n        \"photoshop\": [\n            \"image-generation\",\n            \"art\",\n            \"graphic-design\"\n        ],\n        \"lightroom\": [\n            \"image-generation\",\n            \"photography\",\n            \"art\"\n        ],\n        \"figma\": [\n            \"graphic-design\",\n            \"design\",\n            \"prototyping\"\n        ],\n        \"notion\": [\n            \"productivity\",\n            \"note-taking\",\n            \"organization\"\n        ],\n        \"slack\": [\n            \"communication\",\n            \"productivity\",\n            \"team-collaboration\"\n        ],\n        \"discord\": [\n            \"communication\",\n            \"social-media\",\n            \"gaming\"\n        ],\n        \"zoom\": [\n            \"communication\",\n            \"video-conferencing\",\n            \"productivity\"\n        ],\n        \"teams\": [\n            \"communication\",\n            \"productivity\",\n            \"collaboration\"\n        ],\n        \"gmail\": [\n            \"email-generator\",\n            \"communication\",\n            \"productivity\"\n        ],\n        \"outlook\": [\n            \"email-generator\",\n            \"communication\",\n            \"productivity\"\n        ],\n        \"chrome\": [\n            \"productivity\",\n            \"web-browsing\",\n            \"research\"\n        ],\n        \"firefox\": [\n            \"productivity\",\n            \"web-browsing\",\n            \"research\"\n        ],\n        \"office\": [\n            \"productivity\",\n            \"document-generation\",\n            \"presentation\"\n        ],\n        \"word\": [\n            \"writing-generator\",\n            \"document-generation\",\n            \"productivity\"\n        ],\n        \"excel\": [\n            \"data-analysis\",\n            \"productivity\",\n            \"automation\"\n        ],\n        \"powerpoint\": [\n            \"presentation\",\n            \"graphic-design\",\n            \"productivity\"\n        ],\n        \"adobe\": [\n            \"graphic-design\",\n            \"image-generation\",\n            \"video-generation\"\n        ],\n        \"camera\": [\n            \"image-generation\",\n            \"photography\",\n            \"art\"\n        ],\n        \"gallery\": [\n            \"image-generation\",\n            \"photography\",\n            \"organization\"\n        ],\n        \"music\": [\n            \"music\",\n            \"audio\",\n            \"entertainment\"\n        ],\n        \"video\": [\n            \"video-generation\",\n            \"entertainment\",\n            \"editing\"\n        ],\n        \"calculator\": [\n            \"productivity\",\n            \"math\",\n            \"automation\"\n        ],\n        \"calendar\": [\n            \"productivity\",\n            \"scheduling\",\n            \"organization\"\n        ],\n        \"notes\": [\n            \"note-taking\",\n            \"productivity\",\n            \"writing-generator\"\n        ],\n        \"weather\": [\n            \"lifestyle\",\n            \"productivity\",\n            \"information\"\n        ],\n        \"maps\": [\n            \"navigation\",\n            \"travel\",\n            \"productivity\"\n        ],\n        \"translator\": [\n            \"translation\",\n            \"language\",\n            \"communication\"\n        ],\n        \"keyboard\": [\n            \"productivity\",\n            \"typing\",\n            \"communication\"\n        ],\n        \"launcher\": [\n            \"productivity\",\n            \"customization\",\n            \"automation\"\n        ],\n        \"vpn\": [\n            \"security\",\n            \"privacy\",\n            \"productivity\"\n        ],\n        \"antivirus\": [\n            \"security\",\n            \"privacy\",\n            \"productivity\"\n        ],\n        \"cleaner\": [\n            \"productivity\",\n            \"optimization\",\n            \"automation\"\n        ],\n        \"battery\": [\n            \"productivity\",\n            \"optimization\",\n            \"monitoring\"\n        ],\n        \"file\": [\n            \"productivity\",\n            \"organization\",\n            \"automation\"\n        ],\n        \"download\": [\n            \"productivity\",\n            \"file-management\",\n            \"automation\"\n        ]\n    },\n    // Category-based mappings\n    category: {\n        \"communication\": [\n            \"social-media\",\n            \"email-generator\",\n            \"chatbot\",\n            \"translation\"\n        ],\n        \"social\": [\n            \"social-media\",\n            \"content-creation\",\n            \"image-generation\"\n        ],\n        \"productivity\": [\n            \"productivity\",\n            \"automation\",\n            \"organization\",\n            \"note-taking\"\n        ],\n        \"business\": [\n            \"business\",\n            \"productivity\",\n            \"automation\",\n            \"finace\"\n        ],\n        \"education\": [\n            \"education\",\n            \"research\",\n            \"writing-generator\",\n            \"translation\"\n        ],\n        \"entertainment\": [\n            \"video-generation\",\n            \"music\",\n            \"content-creation\",\n            \"gaming\"\n        ],\n        \"art-design\": [\n            \"art\",\n            \"graphic-design\",\n            \"image-generation\",\n            \"avatar\"\n        ],\n        \"photography\": [\n            \"image-generation\",\n            \"art\",\n            \"graphic-design\",\n            \"avatar\"\n        ],\n        \"music-audio\": [\n            \"music\",\n            \"audio\",\n            \"podcast\",\n            \"transcription\"\n        ],\n        \"video-players\": [\n            \"video-generation\",\n            \"entertainment\",\n            \"transcription\"\n        ],\n        \"news-magazines\": [\n            \"blog-generator\",\n            \"writing-generator\",\n            \"research\"\n        ],\n        \"books-reference\": [\n            \"education\",\n            \"research\",\n            \"writing-generator\",\n            \"translation\"\n        ],\n        \"health-fitness\": [\n            \"health\",\n            \"lifestyle\",\n            \"monitoring\",\n            \"coaching\"\n        ],\n        \"lifestyle\": [\n            \"lifestyle\",\n            \"health\",\n            \"organization\",\n            \"coaching\"\n        ],\n        \"travel-local\": [\n            \"travel\",\n            \"lifestyle\",\n            \"translation\",\n            \"navigation\"\n        ],\n        \"shopping\": [\n            \"e-commerce\",\n            \"recommendation\",\n            \"price-comparison\"\n        ],\n        \"finance\": [\n            \"finace\",\n            \"business\",\n            \"automation\",\n            \"analysis\"\n        ],\n        \"tools\": [\n            \"productivity\",\n            \"automation\",\n            \"developer-tools\",\n            \"optimization\"\n        ],\n        \"personalization\": [\n            \"avatar\",\n            \"customization\",\n            \"art\",\n            \"graphic-design\"\n        ],\n        \"weather\": [\n            \"lifestyle\",\n            \"information\",\n            \"prediction\"\n        ],\n        \"maps-navigation\": [\n            \"navigation\",\n            \"travel\",\n            \"productivity\"\n        ],\n        \"auto-vehicles\": [\n            \"automation\",\n            \"monitoring\",\n            \"productivity\"\n        ],\n        \"dating\": [\n            \"social-media\",\n            \"communication\",\n            \"lifestyle\"\n        ],\n        \"food-drink\": [\n            \"lifestyle\",\n            \"recommendation\",\n            \"health\"\n        ],\n        \"house-home\": [\n            \"lifestyle\",\n            \"automation\",\n            \"monitoring\"\n        ],\n        \"libraries-demo\": [\n            \"developer-tools\",\n            \"education\",\n            \"productivity\"\n        ],\n        \"medical\": [\n            \"health\",\n            \"monitoring\",\n            \"analysis\",\n            \"research\"\n        ],\n        \"parenting\": [\n            \"education\",\n            \"lifestyle\",\n            \"monitoring\"\n        ],\n        \"sports\": [\n            \"lifestyle\",\n            \"health\",\n            \"monitoring\",\n            \"analysis\"\n        ],\n        \"beauty\": [\n            \"lifestyle\",\n            \"image-generation\",\n            \"recommendation\"\n        ]\n    },\n    // Keyword-based mappings for app descriptions/titles\n    keywords: {\n        \"chat\": [\n            \"chatbot\",\n            \"communication\",\n            \"social-media\"\n        ],\n        \"message\": [\n            \"communication\",\n            \"social-media\",\n            \"email-generator\"\n        ],\n        \"photo\": [\n            \"image-generation\",\n            \"photography\",\n            \"art\"\n        ],\n        \"camera\": [\n            \"image-generation\",\n            \"photography\",\n            \"art\"\n        ],\n        \"video\": [\n            \"video-generation\",\n            \"entertainment\",\n            \"content-creation\"\n        ],\n        \"music\": [\n            \"music\",\n            \"audio\",\n            \"entertainment\"\n        ],\n        \"edit\": [\n            \"image-generation\",\n            \"video-generation\",\n            \"content-creation\"\n        ],\n        \"design\": [\n            \"graphic-design\",\n            \"art\",\n            \"image-generation\"\n        ],\n        \"write\": [\n            \"writing-generator\",\n            \"blog-generator\",\n            \"productivity\"\n        ],\n        \"note\": [\n            \"note-taking\",\n            \"productivity\",\n            \"organization\"\n        ],\n        \"task\": [\n            \"productivity\",\n            \"organization\",\n            \"automation\"\n        ],\n        \"calendar\": [\n            \"productivity\",\n            \"scheduling\",\n            \"organization\"\n        ],\n        \"email\": [\n            \"email-generator\",\n            \"communication\",\n            \"productivity\"\n        ],\n        \"translate\": [\n            \"translation\",\n            \"language\",\n            \"communication\"\n        ],\n        \"learn\": [\n            \"education\",\n            \"research\",\n            \"skill-development\"\n        ],\n        \"fitness\": [\n            \"health\",\n            \"lifestyle\",\n            \"monitoring\"\n        ],\n        \"health\": [\n            \"health\",\n            \"monitoring\",\n            \"analysis\"\n        ],\n        \"game\": [\n            \"gaming\",\n            \"entertainment\",\n            \"content-creation\"\n        ],\n        \"social\": [\n            \"social-media\",\n            \"communication\",\n            \"content-creation\"\n        ],\n        \"business\": [\n            \"business\",\n            \"productivity\",\n            \"finace\"\n        ],\n        \"finance\": [\n            \"finace\",\n            \"business\",\n            \"analysis\"\n        ],\n        \"shop\": [\n            \"e-commerce\",\n            \"recommendation\",\n            \"price-comparison\"\n        ],\n        \"travel\": [\n            \"travel\",\n            \"lifestyle\",\n            \"navigation\"\n        ],\n        \"news\": [\n            \"blog-generator\",\n            \"research\",\n            \"information\"\n        ],\n        \"weather\": [\n            \"lifestyle\",\n            \"information\",\n            \"prediction\"\n        ],\n        \"security\": [\n            \"security\",\n            \"privacy\",\n            \"monitoring\"\n        ],\n        \"clean\": [\n            \"productivity\",\n            \"optimization\",\n            \"automation\"\n        ],\n        \"optimize\": [\n            \"productivity\",\n            \"optimization\",\n            \"automation\"\n        ],\n        \"backup\": [\n            \"productivity\",\n            \"automation\",\n            \"security\"\n        ],\n        \"scan\": [\n            \"productivity\",\n            \"security\",\n            \"analysis\"\n        ],\n        \"voice\": [\n            \"audio\",\n            \"transcription\",\n            \"communication\"\n        ],\n        \"speech\": [\n            \"audio\",\n            \"transcription\",\n            \"communication\"\n        ],\n        \"ai\": [\n            \"artificial-intelligence\",\n            \"automation\",\n            \"productivity\"\n        ],\n        \"smart\": [\n            \"automation\",\n            \"productivity\",\n            \"artificial-intelligence\"\n        ],\n        \"auto\": [\n            \"automation\",\n            \"productivity\",\n            \"optimization\"\n        ]\n    }\n};\n/**\n * Get relevant AI tool categories for an app\n */ const getRelevantAICategories = (appDetails)=>{\n    const categories = new Set();\n    if (!appDetails) return [\n        \"productivity\"\n    ];\n    const appId = (appDetails.appId || \"\").toLowerCase();\n    const title = (appDetails.title || \"\").toLowerCase();\n    const category = (appDetails.category || \"\").toLowerCase();\n    const description = (appDetails.description || \"\").toLowerCase();\n    // Check direct app mappings\n    for (const [appName, aiCategories] of Object.entries(APP_AI_MAPPINGS.direct)){\n        if (appId.includes(appName) || title.includes(appName)) {\n            aiCategories.forEach((cat)=>categories.add(cat));\n        }\n    }\n    // Check category mappings\n    const normalizedCategory = category.replace(/[^a-z0-9]/g, \"-\");\n    if (APP_AI_MAPPINGS.category[normalizedCategory]) {\n        APP_AI_MAPPINGS.category[normalizedCategory].forEach((cat)=>categories.add(cat));\n    }\n    // Check keyword mappings\n    const searchText = `${title} ${description}`.toLowerCase();\n    for (const [keyword, aiCategories] of Object.entries(APP_AI_MAPPINGS.keywords)){\n        if (searchText.includes(keyword)) {\n            aiCategories.forEach((cat)=>categories.add(cat));\n        }\n    }\n    // Return array with most relevant first, fallback to productivity\n    const result = Array.from(categories);\n    return result.length > 0 ? result : [\n        \"productivity\"\n    ];\n};\n/**\n * Get confidence score for app-to-AI-tool matching\n */ const getMatchingConfidence = (appDetails, aiCategory)=>{\n    const relevantCategories = getRelevantAICategories(appDetails);\n    const index = relevantCategories.indexOf(aiCategory);\n    if (index === -1) return 0;\n    if (index === 0) return 1.0;\n    if (index === 1) return 0.8;\n    if (index === 2) return 0.6;\n    return 0.4;\n};\n/**\n * Get the best AI tool category for an app\n */ const getBestAICategory = (appDetails)=>{\n    const categories = getRelevantAICategories(appDetails);\n    return categories[0] || \"productivity\";\n};\n/**\n * Generate smart AI tools URL with best matching category\n */ const generateSmartAIToolsURL = (appDetails, baseURL)=>{\n    const bestCategory = getBestAICategory(appDetails);\n    const appId = appDetails?.appId || \"\";\n    const appTitle = appDetails?.title || \"\";\n    return `${baseURL}/tools/${bestCategory}?ref=apk&app=${encodeURIComponent(appId)}&title=${encodeURIComponent(appTitle)}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/utils/appMappingService.js\n");

/***/ }),

/***/ "(ssr)/./src/app/utils/crossLinking.js":
/*!***************************************!*\
  !*** ./src/app/utils/crossLinking.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CATEGORY_MAPPING: () => (/* binding */ CATEGORY_MAPPING),\n/* harmony export */   DOMAINS: () => (/* binding */ DOMAINS),\n/* harmony export */   generateAIToolsURL: () => (/* binding */ generateAIToolsURL),\n/* harmony export */   generateAPKURL: () => (/* binding */ generateAPKURL),\n/* harmony export */   generateAnchorText: () => (/* binding */ generateAnchorText),\n/* harmony export */   getRelevantAICategories: () => (/* binding */ getRelevantAICategories),\n/* harmony export */   isCrossLinkingEnabled: () => (/* binding */ isCrossLinkingEnabled),\n/* harmony export */   trackCrossLinkClick: () => (/* binding */ trackCrossLinkClick)\n/* harmony export */ });\n/* harmony import */ var _appMappingService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./appMappingService */ \"(ssr)/./src/app/utils/appMappingService.js\");\n/**\n * Cross-linking utilities for SEO-friendly navigation between APK and AI Tools sites\n */ \n// Domain configuration\nconst DOMAINS = {\n    APK_SITE: \"https://apk-mirror.com/\" || 0,\n    AI_TOOLS_SITE: \"https://instadownloader.app\" || 0\n};\n// Check if cross-linking is enabled\nconst isCrossLinkingEnabled = ()=>{\n    return \"true\" === \"true\";\n};\n/**\n * Category mapping between APK categories and AI tool categories\n */ const CATEGORY_MAPPING = {\n    // APK categories to AI tool categories\n    \"productivity\": [\n        \"productivity\",\n        \"business\",\n        \"automation\"\n    ],\n    \"communication\": [\n        \"social-media\",\n        \"email-generator\",\n        \"communication\"\n    ],\n    \"education\": [\n        \"education\",\n        \"research\",\n        \"writing-generator\"\n    ],\n    \"business\": [\n        \"business\",\n        \"productivity\",\n        \"finace\",\n        \"startup\"\n    ],\n    \"art-design\": [\n        \"art\",\n        \"graphic-design\",\n        \"image-generation\",\n        \"avatar\"\n    ],\n    \"photography\": [\n        \"image-generation\",\n        \"art\",\n        \"graphic-design\"\n    ],\n    \"entertainment\": [\n        \"video-generation\",\n        \"music\",\n        \"anime\"\n    ],\n    \"social\": [\n        \"social-media\",\n        \"marketing\"\n    ],\n    \"tools\": [\n        \"productivity\",\n        \"automation\",\n        \"developer-tools\"\n    ],\n    \"lifestyle\": [\n        \"lifestyle\",\n        \"health\"\n    ],\n    \"health-fitness\": [\n        \"health\",\n        \"lifestyle\"\n    ],\n    \"music-audio\": [\n        \"music\",\n        \"audio\"\n    ],\n    \"video-players\": [\n        \"video-generation\",\n        \"video-editing\"\n    ],\n    \"news-magazines\": [\n        \"blog-generator\",\n        \"writing-generator\"\n    ],\n    \"shopping\": [\n        \"e-commerce\",\n        \"marketing\"\n    ],\n    \"travel-local\": [\n        \"travel\",\n        \"lifestyle\"\n    ],\n    \"finance\": [\n        \"finace\",\n        \"business\"\n    ],\n    \"auto-vehicles\": [\n        \"automation\"\n    ],\n    \"dating\": [\n        \"social-media\"\n    ],\n    \"food-drink\": [\n        \"lifestyle\"\n    ],\n    \"house-home\": [\n        \"lifestyle\"\n    ],\n    \"libraries-demo\": [\n        \"developer-tools\"\n    ],\n    \"medical\": [\n        \"health\"\n    ],\n    \"parenting\": [\n        \"education\"\n    ],\n    \"personalization\": [\n        \"avatar\",\n        \"customization\"\n    ],\n    \"sports\": [\n        \"lifestyle\"\n    ],\n    \"weather\": [\n        \"lifestyle\"\n    ]\n};\n/**\n * Get relevant AI tool categories for an APK category\n */ const getRelevantAICategories = (apkCategory)=>{\n    if (!apkCategory) return [\n        \"productivity\"\n    ]; // Default fallback\n    const normalizedCategory = apkCategory.toLowerCase().replace(/[^a-z0-9]/g, \"-\");\n    return CATEGORY_MAPPING[normalizedCategory] || [\n        \"productivity\"\n    ];\n};\n/**\n * Generate AI tools discovery URL for an app (Enhanced with smart mapping)\n */ const generateAIToolsURL = (appDetails)=>{\n    if (!isCrossLinkingEnabled()) return null;\n    const baseURL = DOMAINS.AI_TOOLS_SITE;\n    // Use smart mapping service for better matching\n    return (0,_appMappingService__WEBPACK_IMPORTED_MODULE_0__.generateSmartAIToolsURL)(appDetails, baseURL);\n};\n/**\n * Generate APK download URL for an AI tool\n */ const generateAPKURL = (toolDetails)=>{\n    if (!isCrossLinkingEnabled()) return null;\n    const baseURL = DOMAINS.APK_SITE;\n    // Try to find matching app based on tool name or category\n    const toolName = toolDetails?.title?.toLowerCase() || \"\";\n    const toolCategory = toolDetails?.subCategory?.toLowerCase() || \"\";\n    // Common app mappings for AI tools\n    const APP_MAPPINGS = {\n        \"chatgpt\": \"com.openai.chatgpt\",\n        \"claude\": \"com.anthropic.claude\",\n        \"gemini\": \"com.google.android.apps.bard\",\n        \"copilot\": \"com.microsoft.copilot\",\n        \"midjourney\": \"com.midjourney.android\",\n        \"canva\": \"com.canva.editor\",\n        \"photoshop\": \"com.adobe.photoshop.express\",\n        \"lightroom\": \"com.adobe.lrmobile\",\n        \"figma\": \"com.figma.mirror\",\n        \"notion\": \"notion.id\",\n        \"slack\": \"com.Slack\",\n        \"discord\": \"com.discord\",\n        \"zoom\": \"us.zoom.videomeetings\",\n        \"teams\": \"com.microsoft.teams\",\n        \"telegram\": \"org.telegram.messenger\",\n        \"whatsapp\": \"com.whatsapp\",\n        \"instagram\": \"com.instagram.android\",\n        \"tiktok\": \"com.zhiliaoapp.musically\",\n        \"youtube\": \"com.google.android.youtube\",\n        \"spotify\": \"com.spotify.music\",\n        \"netflix\": \"com.netflix.mediaclient\"\n    };\n    // Check for direct app mapping\n    for (const [keyword, appId] of Object.entries(APP_MAPPINGS)){\n        if (toolName.includes(keyword)) {\n            return `${baseURL}/apps/appdetails/${appId}?ref=ai-tools&tool=${encodeURIComponent(toolDetails?.appId || \"\")}`;\n        }\n    }\n    // Category-based fallback\n    const categoryMappings = {\n        \"social-media\": \"/apps/communication\",\n        \"productivity\": \"/apps/productivity\",\n        \"image-generation\": \"/apps/art-design\",\n        \"video-generation\": \"/apps/video-players\",\n        \"music\": \"/apps/music-audio\",\n        \"business\": \"/apps/business\",\n        \"education\": \"/apps/education\",\n        \"health\": \"/apps/health-fitness\"\n    };\n    const categoryPath = categoryMappings[toolCategory];\n    if (categoryPath) {\n        return `${baseURL}${categoryPath}?ref=ai-tools&tool=${encodeURIComponent(toolDetails?.appId || \"\")}`;\n    }\n    // Ultimate fallback to main apps page\n    return `${baseURL}/apps?ref=ai-tools&tool=${encodeURIComponent(toolDetails?.appId || \"\")}`;\n};\n/**\n * Generate SEO-friendly anchor text for cross-links\n */ const generateAnchorText = (type, details)=>{\n    if (type === \"ai-tools\") {\n        const category = details?.category || \"productivity\";\n        return `Discover AI ${category.charAt(0).toUpperCase() + category.slice(1)} Tools`;\n    } else if (type === \"apk-download\") {\n        const toolName = details?.title || \"Related Apps\";\n        return `Download ${toolName} APK & Similar Apps`;\n    }\n    return \"Explore More\";\n};\n/**\n * Track cross-link clicks for analytics\n */ const trackCrossLinkClick = (source, target, details)=>{\n    if (false) {}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/utils/crossLinking.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8ebd5d55d030\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZTk5YSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhlYmQ1ZDU1ZDAzMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/Components/navbar/navbar.jsx":
/*!**********************************************!*\
  !*** ./src/app/Components/navbar/navbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring\Apk mirror\src\app\Components\navbar\navbar.jsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/ReduxLayout/layout.js":
/*!***************************************!*\
  !*** ./src/app/ReduxLayout/layout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReduxProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring\Apk mirror\src\app\ReduxLayout\layout.js#ReduxProvider`);


/***/ }),

/***/ "(rsc)/./src/app/apps/appdetails/[appId]/page.js":
/*!*************************************************!*\
  !*** ./src/app/apps/appdetails/[appId]/page.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\Anchoring\Apk mirror\src\app\apps\appdetails\[appId]\page.js#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _fortawesome_fontawesome_svg_core_styles_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/fontawesome-svg-core/styles.css */ \"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/styles.css\");\n/* harmony import */ var _fortawesome_fontawesome_svg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/fontawesome-svg-core */ \"(rsc)/./node_modules/@fortawesome/fontawesome-svg-core/index.mjs\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _Components_navbar_navbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Components/navbar/navbar */ \"(rsc)/./src/app/Components/navbar/navbar.jsx\");\n/* harmony import */ var _ReduxLayout_layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ReduxLayout/layout */ \"(rsc)/./src/app/ReduxLayout/layout.js\");\n\n\n\n\n\n_fortawesome_fontawesome_svg_core__WEBPACK_IMPORTED_MODULE_2__.config.autoAddCss = false;\n\n\n\nconst metadata = {\n    title: \"APKExplorer - Fast Android APK Downloader\",\n    description: \"APKExplorer is your go-to source for downloading Android APKs quickly and securely. Discover a vast library of apps, explore different versions, and stay updated with the latest releases.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        async: true,\n                        src: \"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8916641928046583\",\n                        crossOrigin: \"anonymous\",\n                        \"data-adtest\": \"on\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: \"https://cdn.jsdelivr.net/npm/@fingerprintjs/fingerprintjs@3/dist/fp.min.js\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        async: true,\n                        src: \"https://www.googletagmanager.com/gtag/js?id=G-TW5T46HGBD\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        id: \"google-analytics\",\n                        strategy: \"afterInteractive\",\n                        children: `\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n            gtag('js', new Date());\n            gtag('config', 'G-TW5T46HGBD');\n          `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n                (function(c,l,a,r,i,t,y){\n                    c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };\n                    t=l.createElement(r);\n                    t.async=1;\n                    t.src=\"https://www.clarity.ms/tag/\"+i;\n                    y=l.getElementsByTagName(r)[0];\n                    y.parentNode.insertBefore(t,y);\n                })(window, document, \"clarity\", \"script\", \"oa4v7ql0yx\");`\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ReduxLayout_layout__WEBPACK_IMPORTED_MODULE_6__.ReduxProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_navbar_navbar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Anchoring\\\\Apk mirror\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBU01BO0FBVGdEO0FBQ0s7QUFDMUI7QUFDakNDLHFFQUFNQSxDQUFDRSxVQUFVLEdBQUc7QUFFRztBQUN5QjtBQUNLO0FBSTlDLE1BQU1HLFdBQVc7SUFDdEJDLE9BQU87SUFDUEMsYUFDRTtBQUNKLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBRTtJQUM3QyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSzs7MEJBQ1QsOERBQUNDOztrQ0FDQyw4REFBQ0M7d0JBQ0NDLEtBQUk7d0JBQ0pDLE1BQUs7Ozs7OztrQ0FFUCw4REFBQ0M7d0JBQU9DLEtBQUs7d0JBQUNDLEtBQUk7d0JBQ2hCQyxhQUFZO3dCQUFZQyxlQUFZOzs7Ozs7a0NBQ3RDLDhEQUFDSjt3QkFBT0UsS0FBSTs7Ozs7O2tDQUNaLDhEQUFDRjt3QkFBT0MsS0FBSzt3QkFBQ0MsS0FBSTs7Ozs7O2tDQUNsQiw4REFBQ2pCLG1EQUFNQTt3QkFBQ29CLElBQUc7d0JBQW1CQyxVQUFTO2tDQUNwQyxDQUFDOzs7OztVQUtGLENBQUM7Ozs7OztrQ0FFSCw4REFBQ047d0JBQ0NPLHlCQUF5Qjs0QkFDdkJDLFFBQVEsQ0FBQzs7Ozs7Ozs7d0VBUW1ELENBQUM7d0JBQy9EOzs7Ozs7Ozs7Ozs7MEJBR0osOERBQUNDO2dCQUFLQyxXQUFXM0IsOEpBQWU7MEJBQzlCLDRFQUFDSyw4REFBYUE7O3NDQUNaLDhEQUFDRCxpRUFBTUE7Ozs7O3dCQUNOTTs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vc3JjL2FwcC9sYXlvdXQuanM/NWIxOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgXCJAZm9ydGF3ZXNvbWUvZm9udGF3ZXNvbWUtc3ZnLWNvcmUvc3R5bGVzLmNzc1wiO1xuaW1wb3J0IHsgY29uZmlnIH0gZnJvbSBcIkBmb3J0YXdlc29tZS9mb250YXdlc29tZS1zdmctY29yZVwiO1xuaW1wb3J0IFNjcmlwdCBmcm9tIFwibmV4dC9zY3JpcHRcIjtcbmNvbmZpZy5hdXRvQWRkQ3NzID0gZmFsc2U7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5pbXBvcnQgTmF2YmFyIGZyb20gXCIuL0NvbXBvbmVudHMvbmF2YmFyL25hdmJhclwiO1xuaW1wb3J0IHsgUmVkdXhQcm92aWRlciB9IGZyb20gXCIuL1JlZHV4TGF5b3V0L2xheW91dFwiO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQVBLRXhwbG9yZXIgLSBGYXN0IEFuZHJvaWQgQVBLIERvd25sb2FkZXJcIixcbiAgZGVzY3JpcHRpb246XG4gICAgXCJBUEtFeHBsb3JlciBpcyB5b3VyIGdvLXRvIHNvdXJjZSBmb3IgZG93bmxvYWRpbmcgQW5kcm9pZCBBUEtzIHF1aWNrbHkgYW5kIHNlY3VyZWx5LiBEaXNjb3ZlciBhIHZhc3QgbGlicmFyeSBvZiBhcHBzLCBleHBsb3JlIGRpZmZlcmVudCB2ZXJzaW9ucywgYW5kIHN0YXkgdXBkYXRlZCB3aXRoIHRoZSBsYXRlc3QgcmVsZWFzZXMuXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGhlYWQ+XG4gICAgICAgIDxsaW5rXG4gICAgICAgICAgcmVsPVwic3R5bGVzaGVldFwiXG4gICAgICAgICAgaHJlZj1cImh0dHBzOi8vY2RuanMuY2xvdWRmbGFyZS5jb20vYWpheC9saWJzL2ZvbnQtYXdlc29tZS82LjUuMi9jc3MvYWxsLm1pbi5jc3NcIlxuICAgICAgICAvPlxuICAgICAgICA8c2NyaXB0IGFzeW5jIHNyYz1cImh0dHBzOi8vcGFnZWFkMi5nb29nbGVzeW5kaWNhdGlvbi5jb20vcGFnZWFkL2pzL2Fkc2J5Z29vZ2xlLmpzP2NsaWVudD1jYS1wdWItODkxNjY0MTkyODA0NjU4M1wiXG4gICAgICAgICAgY3Jvc3NPcmlnaW49XCJhbm9ueW1vdXNcIiBkYXRhLWFkdGVzdD1cIm9uXCI+PC9zY3JpcHQ+XG4gICAgICAgIDxzY3JpcHQgc3JjPVwiaHR0cHM6Ly9jZG4uanNkZWxpdnIubmV0L25wbS9AZmluZ2VycHJpbnRqcy9maW5nZXJwcmludGpzQDMvZGlzdC9mcC5taW4uanNcIj48L3NjcmlwdD5cbiAgICAgICAgPHNjcmlwdCBhc3luYyBzcmM9XCJodHRwczovL3d3dy5nb29nbGV0YWdtYW5hZ2VyLmNvbS9ndGFnL2pzP2lkPUctVFc1VDQ2SEdCRFwiPjwvc2NyaXB0PlxuICAgICAgICA8U2NyaXB0IGlkPVwiZ29vZ2xlLWFuYWx5dGljc1wiIHN0cmF0ZWd5PVwiYWZ0ZXJJbnRlcmFjdGl2ZVwiPlxuICAgICAgICAgIHtgXG4gICAgICAgICAgICB3aW5kb3cuZGF0YUxheWVyID0gd2luZG93LmRhdGFMYXllciB8fCBbXTtcbiAgICAgICAgICAgIGZ1bmN0aW9uIGd0YWcoKXtkYXRhTGF5ZXIucHVzaChhcmd1bWVudHMpO31cbiAgICAgICAgICAgIGd0YWcoJ2pzJywgbmV3IERhdGUoKSk7XG4gICAgICAgICAgICBndGFnKCdjb25maWcnLCAnRy1UVzVUNDZIR0JEJyk7XG4gICAgICAgICAgYH1cbiAgICAgICAgPC9TY3JpcHQ+XG4gICAgICAgIDxzY3JpcHRcbiAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17e1xuICAgICAgICAgICAgX19odG1sOiBgXG4gICAgICAgICAgICAgICAgKGZ1bmN0aW9uKGMsbCxhLHIsaSx0LHkpe1xuICAgICAgICAgICAgICAgICAgICBjW2FdID0gY1thXSB8fCBmdW5jdGlvbiAoKSB7IChjW2FdLnEgPSBjW2FdLnEgfHwgW10pLnB1c2goYXJndW1lbnRzKSB9O1xuICAgICAgICAgICAgICAgICAgICB0PWwuY3JlYXRlRWxlbWVudChyKTtcbiAgICAgICAgICAgICAgICAgICAgdC5hc3luYz0xO1xuICAgICAgICAgICAgICAgICAgICB0LnNyYz1cImh0dHBzOi8vd3d3LmNsYXJpdHkubXMvdGFnL1wiK2k7XG4gICAgICAgICAgICAgICAgICAgIHk9bC5nZXRFbGVtZW50c0J5VGFnTmFtZShyKVswXTtcbiAgICAgICAgICAgICAgICAgICAgeS5wYXJlbnROb2RlLmluc2VydEJlZm9yZSh0LHkpO1xuICAgICAgICAgICAgICAgIH0pKHdpbmRvdywgZG9jdW1lbnQsIFwiY2xhcml0eVwiLCBcInNjcmlwdFwiLCBcIm9hNHY3cWwweXhcIik7YCxcbiAgICAgICAgICB9fVxuICAgICAgICAvPlxuICAgICAgPC9oZWFkPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8UmVkdXhQcm92aWRlcj5cbiAgICAgICAgICA8TmF2YmFyIC8+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L1JlZHV4UHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufSJdLCJuYW1lcyI6WyJpbnRlciIsImNvbmZpZyIsIlNjcmlwdCIsImF1dG9BZGRDc3MiLCJOYXZiYXIiLCJSZWR1eFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJoZWFkIiwibGluayIsInJlbCIsImhyZWYiLCJzY3JpcHQiLCJhc3luYyIsInNyYyIsImNyb3NzT3JpZ2luIiwiZGF0YS1hZHRlc3QiLCJpZCIsInN0cmF0ZWd5IiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/debug","vendor-chunks/ms","vendor-chunks/@fortawesome","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@reduxjs","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/reselect","vendor-chunks/follow-redirects","vendor-chunks/redux","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/use-sync-external-store","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/redux-thunk","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/prop-types","vendor-chunks/react-is","vendor-chunks/object-assign"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage&page=%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage&appPaths=%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage&pagePath=private-next-app-dir%2Fapps%2Fappdetails%2F%5BappId%5D%2Fpage.js&appDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CBhushan%20patil%5COneDrive%5CDesktop%5CAnchoring%5CApk%20mirror&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();