"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jss";
exports.ids = ["vendor-chunks/jss"];
exports.modules = {

/***/ "(ssr)/./node_modules/jss/dist/jss.esm.js":
/*!******************************************!*\
  !*** ./node_modules/jss/dist/jss.esm.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RuleList: () => (/* binding */ RuleList),\n/* harmony export */   SheetsManager: () => (/* binding */ SheetsManager),\n/* harmony export */   SheetsRegistry: () => (/* binding */ SheetsRegistry),\n/* harmony export */   create: () => (/* binding */ createJss),\n/* harmony export */   createGenerateId: () => (/* binding */ createGenerateId),\n/* harmony export */   createRule: () => (/* binding */ createRule),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getDynamicStyles: () => (/* binding */ getDynamicStyles),\n/* harmony export */   hasCSSTOMSupport: () => (/* binding */ hasCSSTOMSupport),\n/* harmony export */   sheets: () => (/* binding */ sheets),\n/* harmony export */   toCssValue: () => (/* binding */ toCssValue)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var is_in_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! is-in-browser */ \"(ssr)/./node_modules/is-in-browser/dist/module.js\");\n/* harmony import */ var tiny_warning__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tiny-warning */ \"(ssr)/./node_modules/tiny-warning/dist/tiny-warning.esm.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/assertThisInitialized */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n\n\n\n\n\n\n\nvar plainObjectConstrurctor = {}.constructor;\nfunction cloneStyle(style) {\n    if (style == null || typeof style !== \"object\") return style;\n    if (Array.isArray(style)) return style.map(cloneStyle);\n    if (style.constructor !== plainObjectConstrurctor) return style;\n    var newStyle = {};\n    for(var name in style){\n        newStyle[name] = cloneStyle(style[name]);\n    }\n    return newStyle;\n}\n/**\n * Create a rule instance.\n */ function createRule(name, decl, options) {\n    if (name === void 0) {\n        name = \"unnamed\";\n    }\n    var jss = options.jss;\n    var declCopy = cloneStyle(decl);\n    var rule = jss.plugins.onCreateRule(name, declCopy, options);\n    if (rule) return rule; // It is an at-rule and it has no instance.\n    if (name[0] === \"@\") {\n         true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, \"[JSS] Unknown rule \" + name) : 0;\n    }\n    return null;\n}\nvar join = function join(value, by) {\n    var result = \"\";\n    for(var i = 0; i < value.length; i++){\n        // Remove !important from the value, it will be readded later.\n        if (value[i] === \"!important\") break;\n        if (result) result += by;\n        result += value[i];\n    }\n    return result;\n};\n/**\n * Converts JSS array value to a CSS string.\n *\n * `margin: [['5px', '10px']]` > `margin: 5px 10px;`\n * `border: ['1px', '2px']` > `border: 1px, 2px;`\n * `margin: [['5px', '10px'], '!important']` > `margin: 5px 10px !important;`\n * `color: ['red', !important]` > `color: red !important;`\n */ var toCssValue = function toCssValue(value) {\n    if (!Array.isArray(value)) return value;\n    var cssValue = \"\"; // Support space separated values via `[['5px', '10px']]`.\n    if (Array.isArray(value[0])) {\n        for(var i = 0; i < value.length; i++){\n            if (value[i] === \"!important\") break;\n            if (cssValue) cssValue += \", \";\n            cssValue += join(value[i], \" \");\n        }\n    } else cssValue = join(value, \", \"); // Add !important, because it was ignored.\n    if (value[value.length - 1] === \"!important\") {\n        cssValue += \" !important\";\n    }\n    return cssValue;\n};\nfunction getWhitespaceSymbols(options) {\n    if (options && options.format === false) {\n        return {\n            linebreak: \"\",\n            space: \"\"\n        };\n    }\n    return {\n        linebreak: \"\\n\",\n        space: \" \"\n    };\n}\n/**\n * Indent a string.\n * http://jsperf.com/array-join-vs-for\n */ function indentStr(str, indent) {\n    var result = \"\";\n    for(var index = 0; index < indent; index++){\n        result += \"  \";\n    }\n    return result + str;\n}\n/**\n * Converts a Rule to CSS string.\n */ function toCss(selector, style, options) {\n    if (options === void 0) {\n        options = {};\n    }\n    var result = \"\";\n    if (!style) return result;\n    var _options = options, _options$indent = _options.indent, indent = _options$indent === void 0 ? 0 : _options$indent;\n    var fallbacks = style.fallbacks;\n    if (options.format === false) {\n        indent = -Infinity;\n    }\n    var _getWhitespaceSymbols = getWhitespaceSymbols(options), linebreak = _getWhitespaceSymbols.linebreak, space = _getWhitespaceSymbols.space;\n    if (selector) indent++; // Apply fallbacks first.\n    if (fallbacks) {\n        // Array syntax {fallbacks: [{prop: value}]}\n        if (Array.isArray(fallbacks)) {\n            for(var index = 0; index < fallbacks.length; index++){\n                var fallback = fallbacks[index];\n                for(var prop in fallback){\n                    var value = fallback[prop];\n                    if (value != null) {\n                        if (result) result += linebreak;\n                        result += indentStr(prop + \":\" + space + toCssValue(value) + \";\", indent);\n                    }\n                }\n            }\n        } else {\n            // Object syntax {fallbacks: {prop: value}}\n            for(var _prop in fallbacks){\n                var _value = fallbacks[_prop];\n                if (_value != null) {\n                    if (result) result += linebreak;\n                    result += indentStr(_prop + \":\" + space + toCssValue(_value) + \";\", indent);\n                }\n            }\n        }\n    }\n    for(var _prop2 in style){\n        var _value2 = style[_prop2];\n        if (_value2 != null && _prop2 !== \"fallbacks\") {\n            if (result) result += linebreak;\n            result += indentStr(_prop2 + \":\" + space + toCssValue(_value2) + \";\", indent);\n        }\n    } // Allow empty style in this case, because properties will be added dynamically.\n    if (!result && !options.allowEmpty) return result; // When rule is being stringified before selector was defined.\n    if (!selector) return result;\n    indent--;\n    if (result) result = \"\" + linebreak + result + linebreak;\n    return indentStr(\"\" + selector + space + \"{\" + result, indent) + indentStr(\"}\", indent);\n}\nvar escapeRegex = /([[\\].#*$><+~=|^:(),\"'`\\s])/g;\nvar nativeEscape = typeof CSS !== \"undefined\" && CSS.escape;\nvar escape = function(str) {\n    return nativeEscape ? nativeEscape(str) : str.replace(escapeRegex, \"\\\\$1\");\n};\nvar BaseStyleRule = /*#__PURE__*/ function() {\n    function BaseStyleRule(key, style, options) {\n        this.type = \"style\";\n        this.isProcessed = false;\n        var sheet = options.sheet, Renderer = options.Renderer;\n        this.key = key;\n        this.options = options;\n        this.style = style;\n        if (sheet) this.renderer = sheet.renderer;\n        else if (Renderer) this.renderer = new Renderer();\n    }\n    /**\n   * Get or set a style property.\n   */ var _proto = BaseStyleRule.prototype;\n    _proto.prop = function prop(name, value, options) {\n        // It's a getter.\n        if (value === undefined) return this.style[name]; // Don't do anything if the value has not changed.\n        var force = options ? options.force : false;\n        if (!force && this.style[name] === value) return this;\n        var newValue = value;\n        if (!options || options.process !== false) {\n            newValue = this.options.jss.plugins.onChangeValue(value, name, this);\n        }\n        var isEmpty = newValue == null || newValue === false;\n        var isDefined = name in this.style; // Value is empty and wasn't defined before.\n        if (isEmpty && !isDefined && !force) return this; // We are going to remove this value.\n        var remove = isEmpty && isDefined;\n        if (remove) delete this.style[name];\n        else this.style[name] = newValue; // Renderable is defined if StyleSheet option `link` is true.\n        if (this.renderable && this.renderer) {\n            if (remove) this.renderer.removeProperty(this.renderable, name);\n            else this.renderer.setProperty(this.renderable, name, newValue);\n            return this;\n        }\n        var sheet = this.options.sheet;\n        if (sheet && sheet.attached) {\n             true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, '[JSS] Rule is not linked. Missing sheet option \"link: true\".') : 0;\n        }\n        return this;\n    };\n    return BaseStyleRule;\n}();\nvar StyleRule = /*#__PURE__*/ function(_BaseStyleRule) {\n    (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(StyleRule, _BaseStyleRule);\n    function StyleRule(key, style, options) {\n        var _this;\n        _this = _BaseStyleRule.call(this, key, style, options) || this;\n        var selector = options.selector, scoped = options.scoped, sheet = options.sheet, generateId = options.generateId;\n        if (selector) {\n            _this.selectorText = selector;\n        } else if (scoped !== false) {\n            _this.id = generateId((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_4__[\"default\"])((0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_this)), sheet);\n            _this.selectorText = \".\" + escape(_this.id);\n        }\n        return _this;\n    }\n    /**\n   * Set selector string.\n   * Attention: use this with caution. Most browsers didn't implement\n   * selectorText setter, so this may result in rerendering of entire Style Sheet.\n   */ var _proto2 = StyleRule.prototype;\n    /**\n   * Apply rule to an element inline.\n   */ _proto2.applyTo = function applyTo(renderable) {\n        var renderer = this.renderer;\n        if (renderer) {\n            var json = this.toJSON();\n            for(var prop in json){\n                renderer.setProperty(renderable, prop, json[prop]);\n            }\n        }\n        return this;\n    } /**\n   * Returns JSON representation of the rule.\n   * Fallbacks are not supported.\n   * Useful for inline styles.\n   */ ;\n    _proto2.toJSON = function toJSON() {\n        var json = {};\n        for(var prop in this.style){\n            var value = this.style[prop];\n            if (typeof value !== \"object\") json[prop] = value;\n            else if (Array.isArray(value)) json[prop] = toCssValue(value);\n        }\n        return json;\n    } /**\n   * Generates a CSS string.\n   */ ;\n    _proto2.toString = function toString(options) {\n        var sheet = this.options.sheet;\n        var link = sheet ? sheet.options.link : false;\n        var opts = link ? (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n            allowEmpty: true\n        }) : options;\n        return toCss(this.selectorText, this.style, opts);\n    };\n    (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(StyleRule, [\n        {\n            key: \"selector\",\n            set: function set(selector) {\n                if (selector === this.selectorText) return;\n                this.selectorText = selector;\n                var renderer = this.renderer, renderable = this.renderable;\n                if (!renderable || !renderer) return;\n                var hasChanged = renderer.setSelector(renderable, selector); // If selector setter is not implemented, rerender the rule.\n                if (!hasChanged) {\n                    renderer.replaceRule(renderable, this);\n                }\n            },\n            get: function get() {\n                return this.selectorText;\n            }\n        }\n    ]);\n    return StyleRule;\n}(BaseStyleRule);\nvar pluginStyleRule = {\n    onCreateRule: function onCreateRule(key, style, options) {\n        if (key[0] === \"@\" || options.parent && options.parent.type === \"keyframes\") {\n            return null;\n        }\n        return new StyleRule(key, style, options);\n    }\n};\nvar defaultToStringOptions = {\n    indent: 1,\n    children: true\n};\nvar atRegExp = /@([\\w-]+)/;\n/**\n * Conditional rule for @media, @supports\n */ var ConditionalRule = /*#__PURE__*/ function() {\n    function ConditionalRule(key, styles, options) {\n        this.type = \"conditional\";\n        this.isProcessed = false;\n        this.key = key;\n        var atMatch = key.match(atRegExp);\n        this.at = atMatch ? atMatch[1] : \"unknown\"; // Key might contain a unique suffix in case the `name` passed by user was duplicate.\n        this.query = options.name || \"@\" + this.at;\n        this.options = options;\n        this.rules = new RuleList((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n            parent: this\n        }));\n        for(var name in styles){\n            this.rules.add(name, styles[name]);\n        }\n        this.rules.process();\n    }\n    /**\n   * Get a rule.\n   */ var _proto = ConditionalRule.prototype;\n    _proto.getRule = function getRule(name) {\n        return this.rules.get(name);\n    } /**\n   * Get index of a rule.\n   */ ;\n    _proto.indexOf = function indexOf(rule) {\n        return this.rules.indexOf(rule);\n    } /**\n   * Create and register rule, run plugins.\n   */ ;\n    _proto.addRule = function addRule(name, style, options) {\n        var rule = this.rules.add(name, style, options);\n        if (!rule) return null;\n        this.options.jss.plugins.onProcessRule(rule);\n        return rule;\n    } /**\n   * Replace rule, run plugins.\n   */ ;\n    _proto.replaceRule = function replaceRule(name, style, options) {\n        var newRule = this.rules.replace(name, style, options);\n        if (newRule) this.options.jss.plugins.onProcessRule(newRule);\n        return newRule;\n    } /**\n   * Generates a CSS string.\n   */ ;\n    _proto.toString = function toString(options) {\n        if (options === void 0) {\n            options = defaultToStringOptions;\n        }\n        var _getWhitespaceSymbols = getWhitespaceSymbols(options), linebreak = _getWhitespaceSymbols.linebreak;\n        if (options.indent == null) options.indent = defaultToStringOptions.indent;\n        if (options.children == null) options.children = defaultToStringOptions.children;\n        if (options.children === false) {\n            return this.query + \" {}\";\n        }\n        var children = this.rules.toString(options);\n        return children ? this.query + \" {\" + linebreak + children + linebreak + \"}\" : \"\";\n    };\n    return ConditionalRule;\n}();\nvar keyRegExp = /@container|@media|@supports\\s+/;\nvar pluginConditionalRule = {\n    onCreateRule: function onCreateRule(key, styles, options) {\n        return keyRegExp.test(key) ? new ConditionalRule(key, styles, options) : null;\n    }\n};\nvar defaultToStringOptions$1 = {\n    indent: 1,\n    children: true\n};\nvar nameRegExp = /@keyframes\\s+([\\w-]+)/;\n/**\n * Rule for @keyframes\n */ var KeyframesRule = /*#__PURE__*/ function() {\n    function KeyframesRule(key, frames, options) {\n        this.type = \"keyframes\";\n        this.at = \"@keyframes\";\n        this.isProcessed = false;\n        var nameMatch = key.match(nameRegExp);\n        if (nameMatch && nameMatch[1]) {\n            this.name = nameMatch[1];\n        } else {\n            this.name = \"noname\";\n             true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, \"[JSS] Bad keyframes name \" + key) : 0;\n        }\n        this.key = this.type + \"-\" + this.name;\n        this.options = options;\n        var scoped = options.scoped, sheet = options.sheet, generateId = options.generateId;\n        this.id = scoped === false ? this.name : escape(generateId(this, sheet));\n        this.rules = new RuleList((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n            parent: this\n        }));\n        for(var name in frames){\n            this.rules.add(name, frames[name], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n                parent: this\n            }));\n        }\n        this.rules.process();\n    }\n    /**\n   * Generates a CSS string.\n   */ var _proto = KeyframesRule.prototype;\n    _proto.toString = function toString(options) {\n        if (options === void 0) {\n            options = defaultToStringOptions$1;\n        }\n        var _getWhitespaceSymbols = getWhitespaceSymbols(options), linebreak = _getWhitespaceSymbols.linebreak;\n        if (options.indent == null) options.indent = defaultToStringOptions$1.indent;\n        if (options.children == null) options.children = defaultToStringOptions$1.children;\n        if (options.children === false) {\n            return this.at + \" \" + this.id + \" {}\";\n        }\n        var children = this.rules.toString(options);\n        if (children) children = \"\" + linebreak + children + linebreak;\n        return this.at + \" \" + this.id + \" {\" + children + \"}\";\n    };\n    return KeyframesRule;\n}();\nvar keyRegExp$1 = /@keyframes\\s+/;\nvar refRegExp = /\\$([\\w-]+)/g;\nvar findReferencedKeyframe = function findReferencedKeyframe(val, keyframes) {\n    if (typeof val === \"string\") {\n        return val.replace(refRegExp, function(match, name) {\n            if (name in keyframes) {\n                return keyframes[name];\n            }\n             true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, '[JSS] Referenced keyframes rule \"' + name + '\" is not defined.') : 0;\n            return match;\n        });\n    }\n    return val;\n};\n/**\n * Replace the reference for a animation name.\n */ var replaceRef = function replaceRef(style, prop, keyframes) {\n    var value = style[prop];\n    var refKeyframe = findReferencedKeyframe(value, keyframes);\n    if (refKeyframe !== value) {\n        style[prop] = refKeyframe;\n    }\n};\nvar pluginKeyframesRule = {\n    onCreateRule: function onCreateRule(key, frames, options) {\n        return typeof key === \"string\" && keyRegExp$1.test(key) ? new KeyframesRule(key, frames, options) : null;\n    },\n    // Animation name ref replacer.\n    onProcessStyle: function onProcessStyle(style, rule, sheet) {\n        if (rule.type !== \"style\" || !sheet) return style;\n        if (\"animation-name\" in style) replaceRef(style, \"animation-name\", sheet.keyframes);\n        if (\"animation\" in style) replaceRef(style, \"animation\", sheet.keyframes);\n        return style;\n    },\n    onChangeValue: function onChangeValue(val, prop, rule) {\n        var sheet = rule.options.sheet;\n        if (!sheet) {\n            return val;\n        }\n        switch(prop){\n            case \"animation\":\n                return findReferencedKeyframe(val, sheet.keyframes);\n            case \"animation-name\":\n                return findReferencedKeyframe(val, sheet.keyframes);\n            default:\n                return val;\n        }\n    }\n};\nvar KeyframeRule = /*#__PURE__*/ function(_BaseStyleRule) {\n    (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(KeyframeRule, _BaseStyleRule);\n    function KeyframeRule() {\n        return _BaseStyleRule.apply(this, arguments) || this;\n    }\n    var _proto = KeyframeRule.prototype;\n    /**\n   * Generates a CSS string.\n   */ _proto.toString = function toString(options) {\n        var sheet = this.options.sheet;\n        var link = sheet ? sheet.options.link : false;\n        var opts = link ? (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n            allowEmpty: true\n        }) : options;\n        return toCss(this.key, this.style, opts);\n    };\n    return KeyframeRule;\n}(BaseStyleRule);\nvar pluginKeyframeRule = {\n    onCreateRule: function onCreateRule(key, style, options) {\n        if (options.parent && options.parent.type === \"keyframes\") {\n            return new KeyframeRule(key, style, options);\n        }\n        return null;\n    }\n};\nvar FontFaceRule = /*#__PURE__*/ function() {\n    function FontFaceRule(key, style, options) {\n        this.type = \"font-face\";\n        this.at = \"@font-face\";\n        this.isProcessed = false;\n        this.key = key;\n        this.style = style;\n        this.options = options;\n    }\n    /**\n   * Generates a CSS string.\n   */ var _proto = FontFaceRule.prototype;\n    _proto.toString = function toString(options) {\n        var _getWhitespaceSymbols = getWhitespaceSymbols(options), linebreak = _getWhitespaceSymbols.linebreak;\n        if (Array.isArray(this.style)) {\n            var str = \"\";\n            for(var index = 0; index < this.style.length; index++){\n                str += toCss(this.at, this.style[index]);\n                if (this.style[index + 1]) str += linebreak;\n            }\n            return str;\n        }\n        return toCss(this.at, this.style, options);\n    };\n    return FontFaceRule;\n}();\nvar keyRegExp$2 = /@font-face/;\nvar pluginFontFaceRule = {\n    onCreateRule: function onCreateRule(key, style, options) {\n        return keyRegExp$2.test(key) ? new FontFaceRule(key, style, options) : null;\n    }\n};\nvar ViewportRule = /*#__PURE__*/ function() {\n    function ViewportRule(key, style, options) {\n        this.type = \"viewport\";\n        this.at = \"@viewport\";\n        this.isProcessed = false;\n        this.key = key;\n        this.style = style;\n        this.options = options;\n    }\n    /**\n   * Generates a CSS string.\n   */ var _proto = ViewportRule.prototype;\n    _proto.toString = function toString(options) {\n        return toCss(this.key, this.style, options);\n    };\n    return ViewportRule;\n}();\nvar pluginViewportRule = {\n    onCreateRule: function onCreateRule(key, style, options) {\n        return key === \"@viewport\" || key === \"@-ms-viewport\" ? new ViewportRule(key, style, options) : null;\n    }\n};\nvar SimpleRule = /*#__PURE__*/ function() {\n    function SimpleRule(key, value, options) {\n        this.type = \"simple\";\n        this.isProcessed = false;\n        this.key = key;\n        this.value = value;\n        this.options = options;\n    }\n    /**\n   * Generates a CSS string.\n   */ // eslint-disable-next-line no-unused-vars\n    var _proto = SimpleRule.prototype;\n    _proto.toString = function toString(options) {\n        if (Array.isArray(this.value)) {\n            var str = \"\";\n            for(var index = 0; index < this.value.length; index++){\n                str += this.key + \" \" + this.value[index] + \";\";\n                if (this.value[index + 1]) str += \"\\n\";\n            }\n            return str;\n        }\n        return this.key + \" \" + this.value + \";\";\n    };\n    return SimpleRule;\n}();\nvar keysMap = {\n    \"@charset\": true,\n    \"@import\": true,\n    \"@namespace\": true\n};\nvar pluginSimpleRule = {\n    onCreateRule: function onCreateRule(key, value, options) {\n        return key in keysMap ? new SimpleRule(key, value, options) : null;\n    }\n};\nvar plugins = [\n    pluginStyleRule,\n    pluginConditionalRule,\n    pluginKeyframesRule,\n    pluginKeyframeRule,\n    pluginFontFaceRule,\n    pluginViewportRule,\n    pluginSimpleRule\n];\nvar defaultUpdateOptions = {\n    process: true\n};\nvar forceUpdateOptions = {\n    force: true,\n    process: true\n};\nvar RuleList = /*#__PURE__*/ function() {\n    // Rules registry for access by .get() method.\n    // It contains the same rule registered by name and by selector.\n    // Original styles object.\n    // Used to ensure correct rules order.\n    function RuleList(options) {\n        this.map = {};\n        this.raw = {};\n        this.index = [];\n        this.counter = 0;\n        this.options = options;\n        this.classes = options.classes;\n        this.keyframes = options.keyframes;\n    }\n    /**\n   * Create and register rule.\n   *\n   * Will not render after Style Sheet was rendered the first time.\n   */ var _proto = RuleList.prototype;\n    _proto.add = function add(name, decl, ruleOptions) {\n        var _this$options = this.options, parent = _this$options.parent, sheet = _this$options.sheet, jss = _this$options.jss, Renderer = _this$options.Renderer, generateId = _this$options.generateId, scoped = _this$options.scoped;\n        var options = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            classes: this.classes,\n            parent: parent,\n            sheet: sheet,\n            jss: jss,\n            Renderer: Renderer,\n            generateId: generateId,\n            scoped: scoped,\n            name: name,\n            keyframes: this.keyframes,\n            selector: undefined\n        }, ruleOptions); // When user uses .createStyleSheet(), duplicate names are not possible, but\n        // `sheet.addRule()` opens the door for any duplicate rule name. When this happens\n        // we need to make the key unique within this RuleList instance scope.\n        var key = name;\n        if (name in this.raw) {\n            key = name + \"-d\" + this.counter++;\n        } // We need to save the original decl before creating the rule\n        // because cache plugin needs to use it as a key to return a cached rule.\n        this.raw[key] = decl;\n        if (key in this.classes) {\n            // E.g. rules inside of @media container\n            options.selector = \".\" + escape(this.classes[key]);\n        }\n        var rule = createRule(key, decl, options);\n        if (!rule) return null;\n        this.register(rule);\n        var index = options.index === undefined ? this.index.length : options.index;\n        this.index.splice(index, 0, rule);\n        return rule;\n    } /**\n   * Replace rule.\n   * Create a new rule and remove old one instead of overwriting\n   * because we want to invoke onCreateRule hook to make plugins work.\n   */ ;\n    _proto.replace = function replace(name, decl, ruleOptions) {\n        var oldRule = this.get(name);\n        var oldIndex = this.index.indexOf(oldRule);\n        if (oldRule) {\n            this.remove(oldRule);\n        }\n        var options = ruleOptions;\n        if (oldIndex !== -1) options = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, ruleOptions, {\n            index: oldIndex\n        });\n        return this.add(name, decl, options);\n    } /**\n   * Get a rule by name or selector.\n   */ ;\n    _proto.get = function get(nameOrSelector) {\n        return this.map[nameOrSelector];\n    } /**\n   * Delete a rule.\n   */ ;\n    _proto.remove = function remove(rule) {\n        this.unregister(rule);\n        delete this.raw[rule.key];\n        this.index.splice(this.index.indexOf(rule), 1);\n    } /**\n   * Get index of a rule.\n   */ ;\n    _proto.indexOf = function indexOf(rule) {\n        return this.index.indexOf(rule);\n    } /**\n   * Run `onProcessRule()` plugins on every rule.\n   */ ;\n    _proto.process = function process() {\n        var plugins = this.options.jss.plugins; // We need to clone array because if we modify the index somewhere else during a loop\n        // we end up with very hard-to-track-down side effects.\n        this.index.slice(0).forEach(plugins.onProcessRule, plugins);\n    } /**\n   * Register a rule in `.map`, `.classes` and `.keyframes` maps.\n   */ ;\n    _proto.register = function register(rule) {\n        this.map[rule.key] = rule;\n        if (rule instanceof StyleRule) {\n            this.map[rule.selector] = rule;\n            if (rule.id) this.classes[rule.key] = rule.id;\n        } else if (rule instanceof KeyframesRule && this.keyframes) {\n            this.keyframes[rule.name] = rule.id;\n        }\n    } /**\n   * Unregister a rule.\n   */ ;\n    _proto.unregister = function unregister(rule) {\n        delete this.map[rule.key];\n        if (rule instanceof StyleRule) {\n            delete this.map[rule.selector];\n            delete this.classes[rule.key];\n        } else if (rule instanceof KeyframesRule) {\n            delete this.keyframes[rule.name];\n        }\n    } /**\n   * Update the function values with a new data.\n   */ ;\n    _proto.update = function update() {\n        var name;\n        var data;\n        var options;\n        if (typeof (arguments.length <= 0 ? undefined : arguments[0]) === \"string\") {\n            name = arguments.length <= 0 ? undefined : arguments[0];\n            data = arguments.length <= 1 ? undefined : arguments[1];\n            options = arguments.length <= 2 ? undefined : arguments[2];\n        } else {\n            data = arguments.length <= 0 ? undefined : arguments[0];\n            options = arguments.length <= 1 ? undefined : arguments[1];\n            name = null;\n        }\n        if (name) {\n            this.updateOne(this.get(name), data, options);\n        } else {\n            for(var index = 0; index < this.index.length; index++){\n                this.updateOne(this.index[index], data, options);\n            }\n        }\n    } /**\n   * Execute plugins, update rule props.\n   */ ;\n    _proto.updateOne = function updateOne(rule, data, options) {\n        if (options === void 0) {\n            options = defaultUpdateOptions;\n        }\n        var _this$options2 = this.options, plugins = _this$options2.jss.plugins, sheet = _this$options2.sheet; // It is a rules container like for e.g. ConditionalRule.\n        if (rule.rules instanceof RuleList) {\n            rule.rules.update(data, options);\n            return;\n        }\n        var style = rule.style;\n        plugins.onUpdate(data, rule, sheet, options); // We rely on a new `style` ref in case it was mutated during onUpdate hook.\n        if (options.process && style && style !== rule.style) {\n            // We need to run the plugins in case new `style` relies on syntax plugins.\n            plugins.onProcessStyle(rule.style, rule, sheet); // Update and add props.\n            for(var prop in rule.style){\n                var nextValue = rule.style[prop];\n                var prevValue = style[prop]; // We need to use `force: true` because `rule.style` has been updated during onUpdate hook, so `rule.prop()` will not update the CSSOM rule.\n                // We do this comparison to avoid unneeded `rule.prop()` calls, since we have the old `style` object here.\n                if (nextValue !== prevValue) {\n                    rule.prop(prop, nextValue, forceUpdateOptions);\n                }\n            } // Remove props.\n            for(var _prop in style){\n                var _nextValue = rule.style[_prop];\n                var _prevValue = style[_prop]; // We need to use `force: true` because `rule.style` has been updated during onUpdate hook, so `rule.prop()` will not update the CSSOM rule.\n                // We do this comparison to avoid unneeded `rule.prop()` calls, since we have the old `style` object here.\n                if (_nextValue == null && _nextValue !== _prevValue) {\n                    rule.prop(_prop, null, forceUpdateOptions);\n                }\n            }\n        }\n    } /**\n   * Convert rules to a CSS string.\n   */ ;\n    _proto.toString = function toString(options) {\n        var str = \"\";\n        var sheet = this.options.sheet;\n        var link = sheet ? sheet.options.link : false;\n        var _getWhitespaceSymbols = getWhitespaceSymbols(options), linebreak = _getWhitespaceSymbols.linebreak;\n        for(var index = 0; index < this.index.length; index++){\n            var rule = this.index[index];\n            var css = rule.toString(options); // No need to render an empty rule.\n            if (!css && !link) continue;\n            if (str) str += linebreak;\n            str += css;\n        }\n        return str;\n    };\n    return RuleList;\n}();\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(styles, options) {\n        this.attached = false;\n        this.deployed = false;\n        this.classes = {};\n        this.keyframes = {};\n        this.options = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n            sheet: this,\n            parent: this,\n            classes: this.classes,\n            keyframes: this.keyframes\n        });\n        if (options.Renderer) {\n            this.renderer = new options.Renderer(this);\n        }\n        this.rules = new RuleList(this.options);\n        for(var name in styles){\n            this.rules.add(name, styles[name]);\n        }\n        this.rules.process();\n    }\n    /**\n   * Attach renderable to the render tree.\n   */ var _proto = StyleSheet.prototype;\n    _proto.attach = function attach() {\n        if (this.attached) return this;\n        if (this.renderer) this.renderer.attach();\n        this.attached = true; // Order is important, because we can't use insertRule API if style element is not attached.\n        if (!this.deployed) this.deploy();\n        return this;\n    } /**\n   * Remove renderable from render tree.\n   */ ;\n    _proto.detach = function detach() {\n        if (!this.attached) return this;\n        if (this.renderer) this.renderer.detach();\n        this.attached = false;\n        return this;\n    } /**\n   * Add a rule to the current stylesheet.\n   * Will insert a rule also after the stylesheet has been rendered first time.\n   */ ;\n    _proto.addRule = function addRule(name, decl, options) {\n        var queue = this.queue; // Plugins can create rules.\n        // In order to preserve the right order, we need to queue all `.addRule` calls,\n        // which happen after the first `rules.add()` call.\n        if (this.attached && !queue) this.queue = [];\n        var rule = this.rules.add(name, decl, options);\n        if (!rule) return null;\n        this.options.jss.plugins.onProcessRule(rule);\n        if (this.attached) {\n            if (!this.deployed) return rule; // Don't insert rule directly if there is no stringified version yet.\n            // It will be inserted all together when .attach is called.\n            if (queue) queue.push(rule);\n            else {\n                this.insertRule(rule);\n                if (this.queue) {\n                    this.queue.forEach(this.insertRule, this);\n                    this.queue = undefined;\n                }\n            }\n            return rule;\n        } // We can't add rules to a detached style node.\n        // We will redeploy the sheet once user will attach it.\n        this.deployed = false;\n        return rule;\n    } /**\n   * Replace a rule in the current stylesheet.\n   */ ;\n    _proto.replaceRule = function replaceRule(nameOrSelector, decl, options) {\n        var oldRule = this.rules.get(nameOrSelector);\n        if (!oldRule) return this.addRule(nameOrSelector, decl, options);\n        var newRule = this.rules.replace(nameOrSelector, decl, options);\n        if (newRule) {\n            this.options.jss.plugins.onProcessRule(newRule);\n        }\n        if (this.attached) {\n            if (!this.deployed) return newRule; // Don't replace / delete rule directly if there is no stringified version yet.\n            // It will be inserted all together when .attach is called.\n            if (this.renderer) {\n                if (!newRule) {\n                    this.renderer.deleteRule(oldRule);\n                } else if (oldRule.renderable) {\n                    this.renderer.replaceRule(oldRule.renderable, newRule);\n                }\n            }\n            return newRule;\n        } // We can't replace rules to a detached style node.\n        // We will redeploy the sheet once user will attach it.\n        this.deployed = false;\n        return newRule;\n    } /**\n   * Insert rule into the StyleSheet\n   */ ;\n    _proto.insertRule = function insertRule(rule) {\n        if (this.renderer) {\n            this.renderer.insertRule(rule);\n        }\n    } /**\n   * Create and add rules.\n   * Will render also after Style Sheet was rendered the first time.\n   */ ;\n    _proto.addRules = function addRules(styles, options) {\n        var added = [];\n        for(var name in styles){\n            var rule = this.addRule(name, styles[name], options);\n            if (rule) added.push(rule);\n        }\n        return added;\n    } /**\n   * Get a rule by name or selector.\n   */ ;\n    _proto.getRule = function getRule(nameOrSelector) {\n        return this.rules.get(nameOrSelector);\n    } /**\n   * Delete a rule by name.\n   * Returns `true`: if rule has been deleted from the DOM.\n   */ ;\n    _proto.deleteRule = function deleteRule(name) {\n        var rule = typeof name === \"object\" ? name : this.rules.get(name);\n        if (!rule || // Style sheet was created without link: true and attached, in this case we\n        // won't be able to remove the CSS rule from the DOM.\n        this.attached && !rule.renderable) {\n            return false;\n        }\n        this.rules.remove(rule);\n        if (this.attached && rule.renderable && this.renderer) {\n            return this.renderer.deleteRule(rule.renderable);\n        }\n        return true;\n    } /**\n   * Get index of a rule.\n   */ ;\n    _proto.indexOf = function indexOf(rule) {\n        return this.rules.indexOf(rule);\n    } /**\n   * Deploy pure CSS string to a renderable.\n   */ ;\n    _proto.deploy = function deploy() {\n        if (this.renderer) this.renderer.deploy();\n        this.deployed = true;\n        return this;\n    } /**\n   * Update the function values with a new data.\n   */ ;\n    _proto.update = function update() {\n        var _this$rules;\n        (_this$rules = this.rules).update.apply(_this$rules, arguments);\n        return this;\n    } /**\n   * Updates a single rule.\n   */ ;\n    _proto.updateOne = function updateOne(rule, data, options) {\n        this.rules.updateOne(rule, data, options);\n        return this;\n    } /**\n   * Convert rules to a CSS string.\n   */ ;\n    _proto.toString = function toString(options) {\n        return this.rules.toString(options);\n    };\n    return StyleSheet;\n}();\nvar PluginsRegistry = /*#__PURE__*/ function() {\n    function PluginsRegistry() {\n        this.plugins = {\n            internal: [],\n            external: []\n        };\n        this.registry = {};\n    }\n    var _proto = PluginsRegistry.prototype;\n    /**\n   * Call `onCreateRule` hooks and return an object if returned by a hook.\n   */ _proto.onCreateRule = function onCreateRule(name, decl, options) {\n        for(var i = 0; i < this.registry.onCreateRule.length; i++){\n            var rule = this.registry.onCreateRule[i](name, decl, options);\n            if (rule) return rule;\n        }\n        return null;\n    } /**\n   * Call `onProcessRule` hooks.\n   */ ;\n    _proto.onProcessRule = function onProcessRule(rule) {\n        if (rule.isProcessed) return;\n        var sheet = rule.options.sheet;\n        for(var i = 0; i < this.registry.onProcessRule.length; i++){\n            this.registry.onProcessRule[i](rule, sheet);\n        }\n        if (rule.style) this.onProcessStyle(rule.style, rule, sheet);\n        rule.isProcessed = true;\n    } /**\n   * Call `onProcessStyle` hooks.\n   */ ;\n    _proto.onProcessStyle = function onProcessStyle(style, rule, sheet) {\n        for(var i = 0; i < this.registry.onProcessStyle.length; i++){\n            rule.style = this.registry.onProcessStyle[i](rule.style, rule, sheet);\n        }\n    } /**\n   * Call `onProcessSheet` hooks.\n   */ ;\n    _proto.onProcessSheet = function onProcessSheet(sheet) {\n        for(var i = 0; i < this.registry.onProcessSheet.length; i++){\n            this.registry.onProcessSheet[i](sheet);\n        }\n    } /**\n   * Call `onUpdate` hooks.\n   */ ;\n    _proto.onUpdate = function onUpdate(data, rule, sheet, options) {\n        for(var i = 0; i < this.registry.onUpdate.length; i++){\n            this.registry.onUpdate[i](data, rule, sheet, options);\n        }\n    } /**\n   * Call `onChangeValue` hooks.\n   */ ;\n    _proto.onChangeValue = function onChangeValue(value, prop, rule) {\n        var processedValue = value;\n        for(var i = 0; i < this.registry.onChangeValue.length; i++){\n            processedValue = this.registry.onChangeValue[i](processedValue, prop, rule);\n        }\n        return processedValue;\n    } /**\n   * Register a plugin.\n   */ ;\n    _proto.use = function use(newPlugin, options) {\n        if (options === void 0) {\n            options = {\n                queue: \"external\"\n            };\n        }\n        var plugins = this.plugins[options.queue]; // Avoids applying same plugin twice, at least based on ref.\n        if (plugins.indexOf(newPlugin) !== -1) {\n            return;\n        }\n        plugins.push(newPlugin);\n        this.registry = [].concat(this.plugins.external, this.plugins.internal).reduce(function(registry, plugin) {\n            for(var name in plugin){\n                if (name in registry) {\n                    registry[name].push(plugin[name]);\n                } else {\n                     true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, '[JSS] Unknown hook \"' + name + '\".') : 0;\n                }\n            }\n            return registry;\n        }, {\n            onCreateRule: [],\n            onProcessRule: [],\n            onProcessStyle: [],\n            onProcessSheet: [],\n            onChangeValue: [],\n            onUpdate: []\n        });\n    };\n    return PluginsRegistry;\n}();\n/**\n * Sheets registry to access all instances in one place.\n */ var SheetsRegistry = /*#__PURE__*/ function() {\n    function SheetsRegistry() {\n        this.registry = [];\n    }\n    var _proto = SheetsRegistry.prototype;\n    /**\n   * Register a Style Sheet.\n   */ _proto.add = function add(sheet) {\n        var registry = this.registry;\n        var index = sheet.options.index;\n        if (registry.indexOf(sheet) !== -1) return;\n        if (registry.length === 0 || index >= this.index) {\n            registry.push(sheet);\n            return;\n        } // Find a position.\n        for(var i = 0; i < registry.length; i++){\n            if (registry[i].options.index > index) {\n                registry.splice(i, 0, sheet);\n                return;\n            }\n        }\n    } /**\n   * Reset the registry.\n   */ ;\n    _proto.reset = function reset() {\n        this.registry = [];\n    } /**\n   * Remove a Style Sheet.\n   */ ;\n    _proto.remove = function remove(sheet) {\n        var index = this.registry.indexOf(sheet);\n        this.registry.splice(index, 1);\n    } /**\n   * Convert all attached sheets to a CSS string.\n   */ ;\n    _proto.toString = function toString(_temp) {\n        var _ref = _temp === void 0 ? {} : _temp, attached = _ref.attached, options = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_ref, [\n            \"attached\"\n        ]);\n        var _getWhitespaceSymbols = getWhitespaceSymbols(options), linebreak = _getWhitespaceSymbols.linebreak;\n        var css = \"\";\n        for(var i = 0; i < this.registry.length; i++){\n            var sheet = this.registry[i];\n            if (attached != null && sheet.attached !== attached) {\n                continue;\n            }\n            if (css) css += linebreak;\n            css += sheet.toString(options);\n        }\n        return css;\n    };\n    (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(SheetsRegistry, [\n        {\n            key: \"index\",\n            /**\n     * Current highest index number.\n     */ get: function get() {\n                return this.registry.length === 0 ? 0 : this.registry[this.registry.length - 1].options.index;\n            }\n        }\n    ]);\n    return SheetsRegistry;\n}();\n/**\n * This is a global sheets registry. Only DomRenderer will add sheets to it.\n * On the server one should use an own SheetsRegistry instance and add the\n * sheets to it, because you need to make sure to create a new registry for\n * each request in order to not leak sheets across requests.\n */ var sheets = new SheetsRegistry();\n/* eslint-disable */ /**\n * Now that `globalThis` is available on most platforms\n * (https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/globalThis#browser_compatibility)\n * we check for `globalThis` first. `globalThis` is necessary for jss\n * to run in Agoric's secure version of JavaScript (SES). Under SES,\n * `globalThis` exists, but `window`, `self`, and `Function('return\n * this')()` are all undefined for security reasons.\n *\n * https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\n */ var globalThis$1 = typeof globalThis !== \"undefined\" ? globalThis :  false ? 0 : typeof self !== \"undefined\" && self.Math === Math ? self : Function(\"return this\")();\nvar ns = \"2f1acc6c3a606b082e5eef5e54414ffb\";\nif (globalThis$1[ns] == null) globalThis$1[ns] = 0; // Bundle may contain multiple JSS versions at the same time. In order to identify\n// the current version with just one short number and use it for classes generation\n// we use a counter. Also it is more accurate, because user can manually reevaluate\n// the module.\nvar moduleId = globalThis$1[ns]++;\nvar maxRules = 1e10;\n/**\n * Returns a function which generates unique class names based on counters.\n * When new generator function is created, rule counter is reseted.\n * We need to reset the rule counter for SSR for each request.\n */ var createGenerateId = function createGenerateId(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    var ruleCounter = 0;\n    var generateId = function generateId(rule, sheet) {\n        ruleCounter += 1;\n        if (ruleCounter > maxRules) {\n             true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, \"[JSS] You might have a memory leak. Rule counter is at \" + ruleCounter + \".\") : 0;\n        }\n        var jssId = \"\";\n        var prefix = \"\";\n        if (sheet) {\n            if (sheet.options.classNamePrefix) {\n                prefix = sheet.options.classNamePrefix;\n            }\n            if (sheet.options.jss.id != null) {\n                jssId = String(sheet.options.jss.id);\n            }\n        }\n        if (options.minify) {\n            // Using \"c\" because a number can't be the first char in a class name.\n            return \"\" + (prefix || \"c\") + moduleId + jssId + ruleCounter;\n        }\n        return prefix + rule.key + \"-\" + moduleId + (jssId ? \"-\" + jssId : \"\") + \"-\" + ruleCounter;\n    };\n    return generateId;\n};\n/**\n * Cache the value from the first time a function is called.\n */ var memoize = function memoize(fn) {\n    var value;\n    return function() {\n        if (!value) value = fn();\n        return value;\n    };\n};\n/**\n * Get a style property value.\n */ var getPropertyValue = function getPropertyValue(cssRule, prop) {\n    try {\n        // Support CSSTOM.\n        if (cssRule.attributeStyleMap) {\n            return cssRule.attributeStyleMap.get(prop);\n        }\n        return cssRule.style.getPropertyValue(prop);\n    } catch (err) {\n        // IE may throw if property is unknown.\n        return \"\";\n    }\n};\n/**\n * Set a style property.\n */ var setProperty = function setProperty(cssRule, prop, value) {\n    try {\n        var cssValue = value;\n        if (Array.isArray(value)) {\n            cssValue = toCssValue(value);\n        } // Support CSSTOM.\n        if (cssRule.attributeStyleMap) {\n            cssRule.attributeStyleMap.set(prop, cssValue);\n        } else {\n            var indexOfImportantFlag = cssValue ? cssValue.indexOf(\"!important\") : -1;\n            var cssValueWithoutImportantFlag = indexOfImportantFlag > -1 ? cssValue.substr(0, indexOfImportantFlag - 1) : cssValue;\n            cssRule.style.setProperty(prop, cssValueWithoutImportantFlag, indexOfImportantFlag > -1 ? \"important\" : \"\");\n        }\n    } catch (err) {\n        // IE may throw if property is unknown.\n        return false;\n    }\n    return true;\n};\n/**\n * Remove a style property.\n */ var removeProperty = function removeProperty(cssRule, prop) {\n    try {\n        // Support CSSTOM.\n        if (cssRule.attributeStyleMap) {\n            cssRule.attributeStyleMap.delete(prop);\n        } else {\n            cssRule.style.removeProperty(prop);\n        }\n    } catch (err) {\n         true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, '[JSS] DOMException \"' + err.message + '\" was thrown. Tried to remove property \"' + prop + '\".') : 0;\n    }\n};\n/**\n * Set the selector.\n */ var setSelector = function setSelector(cssRule, selectorText) {\n    cssRule.selectorText = selectorText; // Return false if setter was not successful.\n    // Currently works in chrome only.\n    return cssRule.selectorText === selectorText;\n};\n/**\n * Gets the `head` element upon the first call and caches it.\n * We assume it can't be null.\n */ var getHead = memoize(function() {\n    return document.querySelector(\"head\");\n});\n/**\n * Find attached sheet with an index higher than the passed one.\n */ function findHigherSheet(registry, options) {\n    for(var i = 0; i < registry.length; i++){\n        var sheet = registry[i];\n        if (sheet.attached && sheet.options.index > options.index && sheet.options.insertionPoint === options.insertionPoint) {\n            return sheet;\n        }\n    }\n    return null;\n}\n/**\n * Find attached sheet with the highest index.\n */ function findHighestSheet(registry, options) {\n    for(var i = registry.length - 1; i >= 0; i--){\n        var sheet = registry[i];\n        if (sheet.attached && sheet.options.insertionPoint === options.insertionPoint) {\n            return sheet;\n        }\n    }\n    return null;\n}\n/**\n * Find a comment with \"jss\" inside.\n */ function findCommentNode(text) {\n    var head = getHead();\n    for(var i = 0; i < head.childNodes.length; i++){\n        var node = head.childNodes[i];\n        if (node.nodeType === 8 && node.nodeValue.trim() === text) {\n            return node;\n        }\n    }\n    return null;\n}\n/**\n * Find a node before which we can insert the sheet.\n */ function findPrevNode(options) {\n    var registry = sheets.registry;\n    if (registry.length > 0) {\n        // Try to insert before the next higher sheet.\n        var sheet = findHigherSheet(registry, options);\n        if (sheet && sheet.renderer) {\n            return {\n                parent: sheet.renderer.element.parentNode,\n                node: sheet.renderer.element\n            };\n        } // Otherwise insert after the last attached.\n        sheet = findHighestSheet(registry, options);\n        if (sheet && sheet.renderer) {\n            return {\n                parent: sheet.renderer.element.parentNode,\n                node: sheet.renderer.element.nextSibling\n            };\n        }\n    } // Try to find a comment placeholder if registry is empty.\n    var insertionPoint = options.insertionPoint;\n    if (insertionPoint && typeof insertionPoint === \"string\") {\n        var comment = findCommentNode(insertionPoint);\n        if (comment) {\n            return {\n                parent: comment.parentNode,\n                node: comment.nextSibling\n            };\n        } // If user specifies an insertion point and it can't be found in the document -\n        // bad specificity issues may appear.\n         true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, '[JSS] Insertion point \"' + insertionPoint + '\" not found.') : 0;\n    }\n    return false;\n}\n/**\n * Insert style element into the DOM.\n */ function insertStyle(style, options) {\n    var insertionPoint = options.insertionPoint;\n    var nextNode = findPrevNode(options);\n    if (nextNode !== false && nextNode.parent) {\n        nextNode.parent.insertBefore(style, nextNode.node);\n        return;\n    } // Works with iframes and any node types.\n    if (insertionPoint && typeof insertionPoint.nodeType === \"number\") {\n        var insertionPointElement = insertionPoint;\n        var parentNode = insertionPointElement.parentNode;\n        if (parentNode) parentNode.insertBefore(style, insertionPointElement.nextSibling);\n        else  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, \"[JSS] Insertion point is not in the DOM.\") : 0;\n        return;\n    }\n    getHead().appendChild(style);\n}\n/**\n * Read jss nonce setting from the page if the user has set it.\n */ var getNonce = memoize(function() {\n    var node = document.querySelector('meta[property=\"csp-nonce\"]');\n    return node ? node.getAttribute(\"content\") : null;\n});\nvar _insertRule = function insertRule(container, rule, index) {\n    try {\n        if (\"insertRule\" in container) {\n            container.insertRule(rule, index);\n        } else if (\"appendRule\" in container) {\n            container.appendRule(rule);\n        }\n    } catch (err) {\n         true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, \"[JSS] \" + err.message) : 0;\n        return false;\n    }\n    return container.cssRules[index];\n};\nvar getValidRuleInsertionIndex = function getValidRuleInsertionIndex(container, index) {\n    var maxIndex = container.cssRules.length; // In case previous insertion fails, passed index might be wrong\n    if (index === undefined || index > maxIndex) {\n        // eslint-disable-next-line no-param-reassign\n        return maxIndex;\n    }\n    return index;\n};\nvar createStyle = function createStyle() {\n    var el = document.createElement(\"style\"); // Without it, IE will have a broken source order specificity if we\n    // insert rules after we insert the style tag.\n    // It seems to kick-off the source order specificity algorithm.\n    el.textContent = \"\\n\";\n    return el;\n};\nvar DomRenderer = /*#__PURE__*/ function() {\n    // Will be empty if link: true option is not set, because\n    // it is only for use together with insertRule API.\n    function DomRenderer(sheet) {\n        this.getPropertyValue = getPropertyValue;\n        this.setProperty = setProperty;\n        this.removeProperty = removeProperty;\n        this.setSelector = setSelector;\n        this.hasInsertedRules = false;\n        this.cssRules = [];\n        // There is no sheet when the renderer is used from a standalone StyleRule.\n        if (sheet) sheets.add(sheet);\n        this.sheet = sheet;\n        var _ref = this.sheet ? this.sheet.options : {}, media = _ref.media, meta = _ref.meta, element = _ref.element;\n        this.element = element || createStyle();\n        this.element.setAttribute(\"data-jss\", \"\");\n        if (media) this.element.setAttribute(\"media\", media);\n        if (meta) this.element.setAttribute(\"data-meta\", meta);\n        var nonce = getNonce();\n        if (nonce) this.element.setAttribute(\"nonce\", nonce);\n    }\n    /**\n   * Insert style element into render tree.\n   */ var _proto = DomRenderer.prototype;\n    _proto.attach = function attach() {\n        // In the case the element node is external and it is already in the DOM.\n        if (this.element.parentNode || !this.sheet) return;\n        insertStyle(this.element, this.sheet.options); // When rules are inserted using `insertRule` API, after `sheet.detach().attach()`\n        // most browsers create a new CSSStyleSheet, except of all IEs.\n        var deployed = Boolean(this.sheet && this.sheet.deployed);\n        if (this.hasInsertedRules && deployed) {\n            this.hasInsertedRules = false;\n            this.deploy();\n        }\n    } /**\n   * Remove style element from render tree.\n   */ ;\n    _proto.detach = function detach() {\n        if (!this.sheet) return;\n        var parentNode = this.element.parentNode;\n        if (parentNode) parentNode.removeChild(this.element); // In the most browsers, rules inserted using insertRule() API will be lost when style element is removed.\n        // Though IE will keep them and we need a consistent behavior.\n        if (this.sheet.options.link) {\n            this.cssRules = [];\n            this.element.textContent = \"\\n\";\n        }\n    } /**\n   * Inject CSS string into element.\n   */ ;\n    _proto.deploy = function deploy() {\n        var sheet = this.sheet;\n        if (!sheet) return;\n        if (sheet.options.link) {\n            this.insertRules(sheet.rules);\n            return;\n        }\n        this.element.textContent = \"\\n\" + sheet.toString() + \"\\n\";\n    } /**\n   * Insert RuleList into an element.\n   */ ;\n    _proto.insertRules = function insertRules(rules, nativeParent) {\n        for(var i = 0; i < rules.index.length; i++){\n            this.insertRule(rules.index[i], i, nativeParent);\n        }\n    } /**\n   * Insert a rule into element.\n   */ ;\n    _proto.insertRule = function insertRule(rule, index, nativeParent) {\n        if (nativeParent === void 0) {\n            nativeParent = this.element.sheet;\n        }\n        if (rule.rules) {\n            var parent = rule;\n            var latestNativeParent = nativeParent;\n            if (rule.type === \"conditional\" || rule.type === \"keyframes\") {\n                var _insertionIndex = getValidRuleInsertionIndex(nativeParent, index); // We need to render the container without children first.\n                latestNativeParent = _insertRule(nativeParent, parent.toString({\n                    children: false\n                }), _insertionIndex);\n                if (latestNativeParent === false) {\n                    return false;\n                }\n                this.refCssRule(rule, _insertionIndex, latestNativeParent);\n            }\n            this.insertRules(parent.rules, latestNativeParent);\n            return latestNativeParent;\n        }\n        var ruleStr = rule.toString();\n        if (!ruleStr) return false;\n        var insertionIndex = getValidRuleInsertionIndex(nativeParent, index);\n        var nativeRule = _insertRule(nativeParent, ruleStr, insertionIndex);\n        if (nativeRule === false) {\n            return false;\n        }\n        this.hasInsertedRules = true;\n        this.refCssRule(rule, insertionIndex, nativeRule);\n        return nativeRule;\n    };\n    _proto.refCssRule = function refCssRule(rule, index, cssRule) {\n        rule.renderable = cssRule; // We only want to reference the top level rules, deleteRule API doesn't support removing nested rules\n        // like rules inside media queries or keyframes\n        if (rule.options.parent instanceof StyleSheet) {\n            this.cssRules.splice(index, 0, cssRule);\n        }\n    } /**\n   * Delete a rule.\n   */ ;\n    _proto.deleteRule = function deleteRule(cssRule) {\n        var sheet = this.element.sheet;\n        var index = this.indexOf(cssRule);\n        if (index === -1) return false;\n        sheet.deleteRule(index);\n        this.cssRules.splice(index, 1);\n        return true;\n    } /**\n   * Get index of a CSS Rule.\n   */ ;\n    _proto.indexOf = function indexOf(cssRule) {\n        return this.cssRules.indexOf(cssRule);\n    } /**\n   * Generate a new CSS rule and replace the existing one.\n   */ ;\n    _proto.replaceRule = function replaceRule(cssRule, rule) {\n        var index = this.indexOf(cssRule);\n        if (index === -1) return false;\n        this.element.sheet.deleteRule(index);\n        this.cssRules.splice(index, 1);\n        return this.insertRule(rule, index);\n    } /**\n   * Get all rules elements.\n   */ ;\n    _proto.getRules = function getRules() {\n        return this.element.sheet.cssRules;\n    };\n    return DomRenderer;\n}();\nvar instanceCounter = 0;\nvar Jss = /*#__PURE__*/ function() {\n    function Jss(options) {\n        this.id = instanceCounter++;\n        this.version = \"10.10.0\";\n        this.plugins = new PluginsRegistry();\n        this.options = {\n            id: {\n                minify: false\n            },\n            createGenerateId: createGenerateId,\n            Renderer: is_in_browser__WEBPACK_IMPORTED_MODULE_1__[\"default\"] ? DomRenderer : null,\n            plugins: []\n        };\n        this.generateId = createGenerateId({\n            minify: false\n        });\n        for(var i = 0; i < plugins.length; i++){\n            this.plugins.use(plugins[i], {\n                queue: \"internal\"\n            });\n        }\n        this.setup(options);\n    }\n    /**\n   * Prepares various options, applies plugins.\n   * Should not be used twice on the same instance, because there is no plugins\n   * deduplication logic.\n   */ var _proto = Jss.prototype;\n    _proto.setup = function setup(options) {\n        if (options === void 0) {\n            options = {};\n        }\n        if (options.createGenerateId) {\n            this.options.createGenerateId = options.createGenerateId;\n        }\n        if (options.id) {\n            this.options.id = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, this.options.id, options.id);\n        }\n        if (options.createGenerateId || options.id) {\n            this.generateId = this.options.createGenerateId(this.options.id);\n        }\n        if (options.insertionPoint != null) this.options.insertionPoint = options.insertionPoint;\n        if (\"Renderer\" in options) {\n            this.options.Renderer = options.Renderer;\n        } // eslint-disable-next-line prefer-spread\n        if (options.plugins) this.use.apply(this, options.plugins);\n        return this;\n    } /**\n   * Create a Style Sheet.\n   */ ;\n    _proto.createStyleSheet = function createStyleSheet(styles, options) {\n        if (options === void 0) {\n            options = {};\n        }\n        var _options = options, index = _options.index;\n        if (typeof index !== \"number\") {\n            index = sheets.index === 0 ? 0 : sheets.index + 1;\n        }\n        var sheet = new StyleSheet(styles, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n            jss: this,\n            generateId: options.generateId || this.generateId,\n            insertionPoint: this.options.insertionPoint,\n            Renderer: this.options.Renderer,\n            index: index\n        }));\n        this.plugins.onProcessSheet(sheet);\n        return sheet;\n    } /**\n   * Detach the Style Sheet and remove it from the registry.\n   */ ;\n    _proto.removeStyleSheet = function removeStyleSheet(sheet) {\n        sheet.detach();\n        sheets.remove(sheet);\n        return this;\n    } /**\n   * Create a rule without a Style Sheet.\n   * [Deprecated] will be removed in the next major version.\n   */ ;\n    _proto.createRule = function createRule$1(name, style, options) {\n        if (style === void 0) {\n            style = {};\n        }\n        if (options === void 0) {\n            options = {};\n        }\n        // Enable rule without name for inline styles.\n        if (typeof name === \"object\") {\n            return this.createRule(undefined, name, style);\n        }\n        var ruleOptions = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, options, {\n            name: name,\n            jss: this,\n            Renderer: this.options.Renderer\n        });\n        if (!ruleOptions.generateId) ruleOptions.generateId = this.generateId;\n        if (!ruleOptions.classes) ruleOptions.classes = {};\n        if (!ruleOptions.keyframes) ruleOptions.keyframes = {};\n        var rule = createRule(name, style, ruleOptions);\n        if (rule) this.plugins.onProcessRule(rule);\n        return rule;\n    } /**\n   * Register plugin. Passed function will be invoked with a rule instance.\n   */ ;\n    _proto.use = function use() {\n        var _this = this;\n        for(var _len = arguments.length, plugins = new Array(_len), _key = 0; _key < _len; _key++){\n            plugins[_key] = arguments[_key];\n        }\n        plugins.forEach(function(plugin) {\n            _this.plugins.use(plugin);\n        });\n        return this;\n    };\n    return Jss;\n}();\nvar createJss = function createJss(options) {\n    return new Jss(options);\n};\n/**\n * SheetsManager is like a WeakMap which is designed to count StyleSheet\n * instances and attach/detach automatically.\n * Used in react-jss.\n */ var SheetsManager = /*#__PURE__*/ function() {\n    function SheetsManager() {\n        this.length = 0;\n        this.sheets = new WeakMap();\n    }\n    var _proto = SheetsManager.prototype;\n    _proto.get = function get(key) {\n        var entry = this.sheets.get(key);\n        return entry && entry.sheet;\n    };\n    _proto.add = function add(key, sheet) {\n        if (this.sheets.has(key)) return;\n        this.length++;\n        this.sheets.set(key, {\n            sheet: sheet,\n            refs: 0\n        });\n    };\n    _proto.manage = function manage(key) {\n        var entry = this.sheets.get(key);\n        if (entry) {\n            if (entry.refs === 0) {\n                entry.sheet.attach();\n            }\n            entry.refs++;\n            return entry.sheet;\n        }\n        (0,tiny_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, \"[JSS] SheetsManager: can't find sheet to manage\");\n        return undefined;\n    };\n    _proto.unmanage = function unmanage(key) {\n        var entry = this.sheets.get(key);\n        if (entry) {\n            if (entry.refs > 0) {\n                entry.refs--;\n                if (entry.refs === 0) entry.sheet.detach();\n            }\n        } else {\n            (0,tiny_warning__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(false, \"SheetsManager: can't find sheet to unmanage\");\n        }\n    };\n    (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(SheetsManager, [\n        {\n            key: \"size\",\n            get: function get() {\n                return this.length;\n            }\n        }\n    ]);\n    return SheetsManager;\n}();\n/**\n* Export a constant indicating if this browser has CSSTOM support.\n* https://developers.google.com/web/updates/2018/03/cssom\n*/ var hasCSSTOMSupport = typeof CSS === \"object\" && CSS != null && \"number\" in CSS;\n/**\n * Extracts a styles object with only props that contain function values.\n */ function getDynamicStyles(styles) {\n    var to = null;\n    for(var key in styles){\n        var value = styles[key];\n        var type = typeof value;\n        if (type === \"function\") {\n            if (!to) to = {};\n            to[key] = value;\n        } else if (type === \"object\" && value !== null && !Array.isArray(value)) {\n            var extracted = getDynamicStyles(value);\n            if (extracted) {\n                if (!to) to = {};\n                to[key] = extracted;\n            }\n        }\n    }\n    return to;\n}\n/**\n * A better abstraction over CSS.\n *\n * @copyright Oleg Isonen (Slobodskoi) / Isonen 2014-present\n * @website https://github.com/cssinjs/jss\n * @license MIT\n */ var index = createJss();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (index);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jss/dist/jss.esm.js\n");

/***/ })

};
;