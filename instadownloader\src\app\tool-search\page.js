"use client";
import React, { useState } from "react";
import Category from "../components/reuseable/Category";
import LoadingComponent from "../Loading";
import Card from "../components/reuseable/Card";
import { getToolsBySearch } from "../util/dataFetch";

const Search = () => {
    const [searchName, setSearchName] = useState("");
    const [isLoading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchToggle, setSearchToggle] = useState(false);
    const [SearchResults, setSearchResults] = useState([]);

    const handleSearch = async (event) => {
        event.preventDefault();
        setError(null);

        if (!searchName.trim()) {
            setError('Please enter a search term.');
            return;
        }

        setLoading(true);

        try {
            setSearchToggle(true);
            const tools = await getToolsBySearch(searchName);
            setSearchResults(tools);
        } catch (err) {
            console.error('Search error:', err);
            setError('An error occurred while searching. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <main className="lg:container flex flex-col items-center justify-between mx-5 mt-0.5 sm:mx-0 md:mx-20 lg:mx-auto px-5 py-5">
            <div className="container mx-auto">
                <div className="flex flex-col mb-8">
                    <h1 className="text-2xl md:text-4xl font-bold mb-2 md:mb-6 text-center">
                        Search Tools
                    </h1>
                    <div className="px-5 py-5">
                        <form
                            onSubmit={handleSearch}
                            id="app-search-form"
                            className="flex flex-col sm:flex-row justify-center items-center w-full gap-2"
                        >
                            <div className="space-y-2">
                                <input
                                    type="search"
                                    name="search"
                                    className="w-full sm:w-96 p-3 text-black border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 mr-1 dark:border-gray-600 dark:text-black dark:focus:ring-blue-500 dark:focus:border-blue-500 transition duration-300"
                                    value={searchName}
                                    onChange={(e) => setSearchName(e.target.value)}
                                    placeholder="Type tool name here (e.g., ChatGPT, DALL-E)"
                                />
                                <button
                                    type="submit"
                                    className="w-full sm:w-auto px-6 py-3 text-center bg-slate-900 text-white rounded-lg hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-500 transition duration-300"
                                >
                                    Search Tools
                                </button>
                                {error && (
                                    <p className="text-red-600 text-sm mt-2 bg-red-100 border border-red-400 p-2 rounded-lg dark:bg-red-900 dark:border-red-600 dark:text-red-300">
                                        {error}
                                    </p>
                                )}
                            </div>

                        </form>
                        <div className="flex flex-col justify-center gap-2 px-5 py-5">
                            <h2 className="lg:text-42px text-2xl md:text-28px text-22px text-primary-p-blue-500 font-bold mb-0 text-center">
                                Explore 1000+ AI Tools
                            </h2>
                        </div>
                    </div>
                </div>
                <div>
                    {searchToggle ? (
                        <div className="p-5 bg-white rounded-md shadow-lg mb-5">
                            <div className="flex justify-between">
                                <h1 className="uppercase mb-2.5 text-lg font-normal tracking text-gray-600">
                                    Search Results for "{searchName}"
                                </h1>
                                <span
                                    onClick={() => setSearchToggle(false)}
                                    className="cursor-pointer text-2xl"
                                >
                                    <i className="fa-regular fa-circle-xmark" />
                                </span>
                            </div>

                            {isLoading ? (
                                <LoadingComponent length={6} lg={2} />
                            ) : (
                                <Card tools={SearchResults} grid={2} searchToggle={searchToggle} errorMsg={`No results found for "${searchName}". Please try a different search term.`} />
                            )}
                        </div>
                    ) : (
                        <div className="w-full">
                            <Category />
                        </div>
                    )}
                </div>
            </div>
        </main>
    );
};

export default Search;
