"use client";
import React from 'react';
import Link from 'next/link';
import { 
  generateAIToolsURL, 
  generateAnchorText, 
  trackCrossLinkClick,
  isCrossLinkingEnabled 
} from '../utils/crossLinking';

/**
 * SEO-friendly "Discover AI Tools" button component
 * Links from APK app pages to relevant AI tools
 */
const DiscoverAIToolsButton = ({ 
  appDetails, 
  variant = 'primary',
  size = 'medium',
  className = '',
  showIcon = true 
}) => {
  // Don't render if cross-linking is disabled
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const aiToolsURL = generateAIToolsURL(appDetails);
  const anchorText = generateAnchorText('ai-tools', appDetails);

  // Don't render if no URL could be generated
  if (!aiToolsURL) {
    return null;
  }

  const handleClick = () => {
    trackCrossLinkClick('apk-site', 'ai-tools', appDetails);
  };

  // Style variants
  const variants = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white',
    outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white',
    gradient: 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white'
  };

  // Size variants
  const sizes = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-6 py-3 text-base',
    large: 'px-8 py-4 text-lg'
  };

  const baseClasses = `
    inline-flex items-center justify-center gap-2 
    font-medium rounded-lg transition-all duration-200 
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
    transform hover:scale-105 active:scale-95
    ${variants[variant]} 
    ${sizes[size]}
    ${className}
  `;

  return (
    <Link
      href={aiToolsURL}
      onClick={handleClick}
      className={baseClasses}
      target="_blank"
      rel="noopener noreferrer"
      title={`Discover AI tools related to ${appDetails?.title || 'this app'}`}
    >
      {showIcon && (
        <svg 
          className="w-5 h-5" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M13 10V3L4 14h7v7l9-11h-7z" 
          />
        </svg>
      )}
      <span>{anchorText}</span>
      <svg 
        className="w-4 h-4" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" 
        />
      </svg>
    </Link>
  );
};

/**
 * Compact version for sidebars or smaller spaces
 */
export const DiscoverAIToolsCompact = ({ appDetails, className = '' }) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const aiToolsURL = generateAIToolsURL(appDetails);
  
  if (!aiToolsURL) {
    return null;
  }

  const handleClick = () => {
    trackCrossLinkClick('apk-site', 'ai-tools', appDetails);
  };

  return (
    <Link
      href={aiToolsURL}
      onClick={handleClick}
      className={`
        inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 
        hover:underline transition-colors duration-200 ${className}
      `}
      target="_blank"
      rel="noopener noreferrer"
      title="Discover related AI tools"
    >
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
      AI Tools
    </Link>
  );
};

/**
 * Banner version for prominent placement
 */
export const DiscoverAIToolsBanner = ({ appDetails, className = '' }) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const aiToolsURL = generateAIToolsURL(appDetails);
  const anchorText = generateAnchorText('ai-tools', appDetails);
  
  if (!aiToolsURL) {
    return null;
  }

  const handleClick = () => {
    trackCrossLinkClick('apk-site', 'ai-tools', appDetails);
  };

  return (
    <div className={`bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            Boost Your Productivity
          </h3>
          <p className="text-gray-600 text-sm">
            Discover AI tools that complement {appDetails?.title || 'this app'}
          </p>
        </div>
        <Link
          href={aiToolsURL}
          onClick={handleClick}
          className="
            bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg 
            font-medium transition-all duration-200 transform hover:scale-105
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
          "
          target="_blank"
          rel="noopener noreferrer"
          title={anchorText}
        >
          {anchorText}
        </Link>
      </div>
    </div>
  );
};

export default DiscoverAIToolsButton;
