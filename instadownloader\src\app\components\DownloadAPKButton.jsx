"use client";
import React from 'react';
import Link from 'next/link';
import { 
  generateAPKURL, 
  generateAnchorText, 
  trackCrossLinkClick,
  isCrossLinkingEnabled 
} from '../utils/crossLinking';

/**
 * SEO-friendly "Download APK Versions" button component
 * Links from AI tool pages to relevant APK downloads
 */
const DownloadAPKButton = ({ 
  toolDetails, 
  variant = 'primary',
  size = 'medium',
  className = '',
  showIcon = true 
}) => {
  // Don't render if cross-linking is disabled
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const apkURL = generateAPKURL(toolDetails);
  const anchorText = generateAnchorText('apk-download', toolDetails);

  // Don't render if no URL could be generated
  if (!apkURL) {
    return null;
  }

  const handleClick = () => {
    trackCrossLinkClick('ai-tools', 'apk-site', toolDetails);
  };

  // Style variants
  const variants = {
    primary: 'bg-green-600 hover:bg-green-700 text-white',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white',
    outline: 'border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white',
    gradient: 'bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white'
  };

  // Size variants
  const sizes = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-6 py-3 text-base',
    large: 'px-8 py-4 text-lg'
  };

  const baseClasses = `
    inline-flex items-center justify-center gap-2 
    font-medium rounded-lg transition-all duration-200 
    focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2
    transform hover:scale-105 active:scale-95
    ${variants[variant]} 
    ${sizes[size]}
    ${className}
  `;

  return (
    <Link
      href={apkURL}
      onClick={handleClick}
      className={baseClasses}
      target="_blank"
      rel="noopener noreferrer"
      title={`Download APK versions of ${toolDetails?.title || 'related apps'}`}
    >
      {showIcon && (
        <svg 
          className="w-5 h-5" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
          />
        </svg>
      )}
      <span>{anchorText}</span>
      <svg 
        className="w-4 h-4" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" 
        />
      </svg>
    </Link>
  );
};

/**
 * Compact version for sidebars or smaller spaces
 */
export const DownloadAPKCompact = ({ toolDetails, className = '' }) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const apkURL = generateAPKURL(toolDetails);
  
  if (!apkURL) {
    return null;
  }

  const handleClick = () => {
    trackCrossLinkClick('ai-tools', 'apk-site', toolDetails);
  };

  return (
    <Link
      href={apkURL}
      onClick={handleClick}
      className={`
        inline-flex items-center gap-1 text-sm text-green-600 hover:text-green-800 
        hover:underline transition-colors duration-200 ${className}
      `}
      target="_blank"
      rel="noopener noreferrer"
      title="Download APK versions"
    >
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      APK Download
    </Link>
  );
};

/**
 * Banner version for prominent placement
 */
export const DownloadAPKBanner = ({ toolDetails, className = '' }) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const apkURL = generateAPKURL(toolDetails);
  const anchorText = generateAnchorText('apk-download', toolDetails);
  
  if (!apkURL) {
    return null;
  }

  const handleClick = () => {
    trackCrossLinkClick('ai-tools', 'apk-site', toolDetails);
  };

  return (
    <div className={`bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            Get Mobile Access
          </h3>
          <p className="text-gray-600 text-sm">
            Download {toolDetails?.title || 'related apps'} and similar mobile apps
          </p>
        </div>
        <Link
          href={apkURL}
          onClick={handleClick}
          className="
            bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg 
            font-medium transition-all duration-200 transform hover:scale-105
            focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2
          "
          target="_blank"
          rel="noopener noreferrer"
          title={anchorText}
        >
          {anchorText}
        </Link>
      </div>
    </div>
  );
};

/**
 * Card version with more detailed information
 */
export const DownloadAPKCard = ({ toolDetails, className = '' }) => {
  if (!isCrossLinkingEnabled()) {
    return null;
  }

  const apkURL = generateAPKURL(toolDetails);
  
  if (!apkURL) {
    return null;
  }

  const handleClick = () => {
    trackCrossLinkClick('ai-tools', 'apk-site', toolDetails);
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow ${className}`}>
      <div className="flex items-start gap-4">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Mobile App Available
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            Access {toolDetails?.title || 'this tool'} and similar functionality on your Android device. 
            Download APK files for the latest and previous versions.
          </p>
          <Link
            href={apkURL}
            onClick={handleClick}
            className="
              inline-flex items-center gap-2 bg-green-600 hover:bg-green-700 
              text-white px-4 py-2 rounded-lg font-medium transition-colors
            "
            target="_blank"
            rel="noopener noreferrer"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Download APK
          </Link>
        </div>
      </div>
    </div>
  );
};

export default DownloadAPKButton;
