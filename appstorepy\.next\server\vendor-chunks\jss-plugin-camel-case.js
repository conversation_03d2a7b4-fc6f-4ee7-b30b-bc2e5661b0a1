"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jss-plugin-camel-case";
exports.ids = ["vendor-chunks/jss-plugin-camel-case"];
exports.modules = {

/***/ "(ssr)/./node_modules/jss-plugin-camel-case/dist/jss-plugin-camel-case.esm.js":
/*!******************************************************************************!*\
  !*** ./node_modules/jss-plugin-camel-case/dist/jss-plugin-camel-case.esm.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var hyphenate_style_name__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hyphenate-style-name */ \"(ssr)/./node_modules/hyphenate-style-name/index.js\");\n\n/**\n * Convert camel cased property names to dash separated.\n */ function convertCase(style) {\n    var converted = {};\n    for(var prop in style){\n        var key = prop.indexOf(\"--\") === 0 ? prop : (0,hyphenate_style_name__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(prop);\n        converted[key] = style[prop];\n    }\n    if (style.fallbacks) {\n        if (Array.isArray(style.fallbacks)) converted.fallbacks = style.fallbacks.map(convertCase);\n        else converted.fallbacks = convertCase(style.fallbacks);\n    }\n    return converted;\n}\n/**\n * Allow camel cased property names by converting them back to dasherized.\n */ function camelCase() {\n    function onProcessStyle(style) {\n        if (Array.isArray(style)) {\n            // Handle rules like @font-face, which can have multiple styles in an array\n            for(var index = 0; index < style.length; index++){\n                style[index] = convertCase(style[index]);\n            }\n            return style;\n        }\n        return convertCase(style);\n    }\n    function onChangeValue(value, prop, rule) {\n        if (prop.indexOf(\"--\") === 0) {\n            return value;\n        }\n        var hyphenatedProp = (0,hyphenate_style_name__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(prop); // There was no camel case in place\n        if (prop === hyphenatedProp) return value;\n        rule.prop(hyphenatedProp, value); // Core will ignore that property value we set the proper one above.\n        return null;\n    }\n    return {\n        onProcessStyle: onProcessStyle,\n        onChangeValue: onChangeValue\n    };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (camelCase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jss-plugin-camel-case/dist/jss-plugin-camel-case.esm.js\n");

/***/ })

};
;