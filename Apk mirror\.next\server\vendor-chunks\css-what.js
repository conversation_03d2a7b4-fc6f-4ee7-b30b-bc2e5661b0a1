"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/css-what";
exports.ids = ["vendor-chunks/css-what"];
exports.modules = {

/***/ "(rsc)/./node_modules/css-what/lib/es/parse.js":
/*!***********************************************!*\
  !*** ./node_modules/css-what/lib/es/parse.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isTraversal: () => (/* binding */ isTraversal),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/css-what/lib/es/types.js\");\n\nconst reName = /^[^\\\\#]?(?:\\\\(?:[\\da-f]{1,6}\\s?|.)|[\\w\\-\\u00b0-\\uFFFF])+/;\nconst reEscape = /\\\\([\\da-f]{1,6}\\s?|(\\s)|.)/gi;\nconst actionTypes = new Map([\n    [126 /* Tilde */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Element],\n    [94 /* Circumflex */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Start],\n    [36 /* Dollar */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.End],\n    [42 /* Asterisk */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Any],\n    [33 /* ExclamationMark */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Not],\n    [124 /* Pipe */, _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Hyphen],\n]);\n// Pseudos, whose data property is parsed as well.\nconst unpackPseudos = new Set([\n    \"has\",\n    \"not\",\n    \"matches\",\n    \"is\",\n    \"where\",\n    \"host\",\n    \"host-context\",\n]);\n/**\n * Checks whether a specific selector is a traversal.\n * This is useful eg. in swapping the order of elements that\n * are not traversals.\n *\n * @param selector Selector to check.\n */\nfunction isTraversal(selector) {\n    switch (selector.type) {\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Adjacent:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Child:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Parent:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Sibling:\n        case _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.ColumnCombinator:\n            return true;\n        default:\n            return false;\n    }\n}\nconst stripQuotesFromPseudos = new Set([\"contains\", \"icontains\"]);\n// Unescape function taken from https://github.com/jquery/sizzle/blob/master/src/sizzle.js#L152\nfunction funescape(_, escaped, escapedWhitespace) {\n    const high = parseInt(escaped, 16) - 0x10000;\n    // NaN means non-codepoint\n    return high !== high || escapedWhitespace\n        ? escaped\n        : high < 0\n            ? // BMP codepoint\n                String.fromCharCode(high + 0x10000)\n            : // Supplemental Plane codepoint (surrogate pair)\n                String.fromCharCode((high >> 10) | 0xd800, (high & 0x3ff) | 0xdc00);\n}\nfunction unescapeCSS(str) {\n    return str.replace(reEscape, funescape);\n}\nfunction isQuote(c) {\n    return c === 39 /* SingleQuote */ || c === 34 /* DoubleQuote */;\n}\nfunction isWhitespace(c) {\n    return (c === 32 /* Space */ ||\n        c === 9 /* Tab */ ||\n        c === 10 /* NewLine */ ||\n        c === 12 /* FormFeed */ ||\n        c === 13 /* CarriageReturn */);\n}\n/**\n * Parses `selector`, optionally with the passed `options`.\n *\n * @param selector Selector to parse.\n * @param options Options for parsing.\n * @returns Returns a two-dimensional array.\n * The first dimension represents selectors separated by commas (eg. `sub1, sub2`),\n * the second contains the relevant tokens for that selector.\n */\nfunction parse(selector) {\n    const subselects = [];\n    const endIndex = parseSelector(subselects, `${selector}`, 0);\n    if (endIndex < selector.length) {\n        throw new Error(`Unmatched selector: ${selector.slice(endIndex)}`);\n    }\n    return subselects;\n}\nfunction parseSelector(subselects, selector, selectorIndex) {\n    let tokens = [];\n    function getName(offset) {\n        const match = selector.slice(selectorIndex + offset).match(reName);\n        if (!match) {\n            throw new Error(`Expected name, found ${selector.slice(selectorIndex)}`);\n        }\n        const [name] = match;\n        selectorIndex += offset + name.length;\n        return unescapeCSS(name);\n    }\n    function stripWhitespace(offset) {\n        selectorIndex += offset;\n        while (selectorIndex < selector.length &&\n            isWhitespace(selector.charCodeAt(selectorIndex))) {\n            selectorIndex++;\n        }\n    }\n    function readValueWithParenthesis() {\n        selectorIndex += 1;\n        const start = selectorIndex;\n        let counter = 1;\n        for (; counter > 0 && selectorIndex < selector.length; selectorIndex++) {\n            if (selector.charCodeAt(selectorIndex) ===\n                40 /* LeftParenthesis */ &&\n                !isEscaped(selectorIndex)) {\n                counter++;\n            }\n            else if (selector.charCodeAt(selectorIndex) ===\n                41 /* RightParenthesis */ &&\n                !isEscaped(selectorIndex)) {\n                counter--;\n            }\n        }\n        if (counter) {\n            throw new Error(\"Parenthesis not matched\");\n        }\n        return unescapeCSS(selector.slice(start, selectorIndex - 1));\n    }\n    function isEscaped(pos) {\n        let slashCount = 0;\n        while (selector.charCodeAt(--pos) === 92 /* BackSlash */)\n            slashCount++;\n        return (slashCount & 1) === 1;\n    }\n    function ensureNotTraversal() {\n        if (tokens.length > 0 && isTraversal(tokens[tokens.length - 1])) {\n            throw new Error(\"Did not expect successive traversals.\");\n        }\n    }\n    function addTraversal(type) {\n        if (tokens.length > 0 &&\n            tokens[tokens.length - 1].type === _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant) {\n            tokens[tokens.length - 1].type = type;\n            return;\n        }\n        ensureNotTraversal();\n        tokens.push({ type });\n    }\n    function addSpecialAttribute(name, action) {\n        tokens.push({\n            type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Attribute,\n            name,\n            action,\n            value: getName(1),\n            namespace: null,\n            ignoreCase: \"quirks\",\n        });\n    }\n    /**\n     * We have finished parsing the current part of the selector.\n     *\n     * Remove descendant tokens at the end if they exist,\n     * and return the last index, so that parsing can be\n     * picked up from here.\n     */\n    function finalizeSubselector() {\n        if (tokens.length &&\n            tokens[tokens.length - 1].type === _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant) {\n            tokens.pop();\n        }\n        if (tokens.length === 0) {\n            throw new Error(\"Empty sub-selector\");\n        }\n        subselects.push(tokens);\n    }\n    stripWhitespace(0);\n    if (selector.length === selectorIndex) {\n        return selectorIndex;\n    }\n    loop: while (selectorIndex < selector.length) {\n        const firstChar = selector.charCodeAt(selectorIndex);\n        switch (firstChar) {\n            // Whitespace\n            case 32 /* Space */:\n            case 9 /* Tab */:\n            case 10 /* NewLine */:\n            case 12 /* FormFeed */:\n            case 13 /* CarriageReturn */: {\n                if (tokens.length === 0 ||\n                    tokens[0].type !== _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant) {\n                    ensureNotTraversal();\n                    tokens.push({ type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Descendant });\n                }\n                stripWhitespace(1);\n                break;\n            }\n            // Traversals\n            case 62 /* GreaterThan */: {\n                addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Child);\n                stripWhitespace(1);\n                break;\n            }\n            case 60 /* LessThan */: {\n                addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Parent);\n                stripWhitespace(1);\n                break;\n            }\n            case 126 /* Tilde */: {\n                addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Sibling);\n                stripWhitespace(1);\n                break;\n            }\n            case 43 /* Plus */: {\n                addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Adjacent);\n                stripWhitespace(1);\n                break;\n            }\n            // Special attribute selectors: .class, #id\n            case 46 /* Period */: {\n                addSpecialAttribute(\"class\", _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Element);\n                break;\n            }\n            case 35 /* Hash */: {\n                addSpecialAttribute(\"id\", _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Equals);\n                break;\n            }\n            case 91 /* LeftSquareBracket */: {\n                stripWhitespace(1);\n                // Determine attribute name and namespace\n                let name;\n                let namespace = null;\n                if (selector.charCodeAt(selectorIndex) === 124 /* Pipe */) {\n                    // Equivalent to no namespace\n                    name = getName(1);\n                }\n                else if (selector.startsWith(\"*|\", selectorIndex)) {\n                    namespace = \"*\";\n                    name = getName(2);\n                }\n                else {\n                    name = getName(0);\n                    if (selector.charCodeAt(selectorIndex) === 124 /* Pipe */ &&\n                        selector.charCodeAt(selectorIndex + 1) !==\n                            61 /* Equal */) {\n                        namespace = name;\n                        name = getName(1);\n                    }\n                }\n                stripWhitespace(0);\n                // Determine comparison operation\n                let action = _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Exists;\n                const possibleAction = actionTypes.get(selector.charCodeAt(selectorIndex));\n                if (possibleAction) {\n                    action = possibleAction;\n                    if (selector.charCodeAt(selectorIndex + 1) !==\n                        61 /* Equal */) {\n                        throw new Error(\"Expected `=`\");\n                    }\n                    stripWhitespace(2);\n                }\n                else if (selector.charCodeAt(selectorIndex) === 61 /* Equal */) {\n                    action = _types__WEBPACK_IMPORTED_MODULE_0__.AttributeAction.Equals;\n                    stripWhitespace(1);\n                }\n                // Determine value\n                let value = \"\";\n                let ignoreCase = null;\n                if (action !== \"exists\") {\n                    if (isQuote(selector.charCodeAt(selectorIndex))) {\n                        const quote = selector.charCodeAt(selectorIndex);\n                        let sectionEnd = selectorIndex + 1;\n                        while (sectionEnd < selector.length &&\n                            (selector.charCodeAt(sectionEnd) !== quote ||\n                                isEscaped(sectionEnd))) {\n                            sectionEnd += 1;\n                        }\n                        if (selector.charCodeAt(sectionEnd) !== quote) {\n                            throw new Error(\"Attribute value didn't end\");\n                        }\n                        value = unescapeCSS(selector.slice(selectorIndex + 1, sectionEnd));\n                        selectorIndex = sectionEnd + 1;\n                    }\n                    else {\n                        const valueStart = selectorIndex;\n                        while (selectorIndex < selector.length &&\n                            ((!isWhitespace(selector.charCodeAt(selectorIndex)) &&\n                                selector.charCodeAt(selectorIndex) !==\n                                    93 /* RightSquareBracket */) ||\n                                isEscaped(selectorIndex))) {\n                            selectorIndex += 1;\n                        }\n                        value = unescapeCSS(selector.slice(valueStart, selectorIndex));\n                    }\n                    stripWhitespace(0);\n                    // See if we have a force ignore flag\n                    const forceIgnore = selector.charCodeAt(selectorIndex) | 0x20;\n                    // If the forceIgnore flag is set (either `i` or `s`), use that value\n                    if (forceIgnore === 115 /* LowerS */) {\n                        ignoreCase = false;\n                        stripWhitespace(1);\n                    }\n                    else if (forceIgnore === 105 /* LowerI */) {\n                        ignoreCase = true;\n                        stripWhitespace(1);\n                    }\n                }\n                if (selector.charCodeAt(selectorIndex) !==\n                    93 /* RightSquareBracket */) {\n                    throw new Error(\"Attribute selector didn't terminate\");\n                }\n                selectorIndex += 1;\n                const attributeSelector = {\n                    type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Attribute,\n                    name,\n                    action,\n                    value,\n                    namespace,\n                    ignoreCase,\n                };\n                tokens.push(attributeSelector);\n                break;\n            }\n            case 58 /* Colon */: {\n                if (selector.charCodeAt(selectorIndex + 1) === 58 /* Colon */) {\n                    tokens.push({\n                        type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.PseudoElement,\n                        name: getName(2).toLowerCase(),\n                        data: selector.charCodeAt(selectorIndex) ===\n                            40 /* LeftParenthesis */\n                            ? readValueWithParenthesis()\n                            : null,\n                    });\n                    continue;\n                }\n                const name = getName(1).toLowerCase();\n                let data = null;\n                if (selector.charCodeAt(selectorIndex) ===\n                    40 /* LeftParenthesis */) {\n                    if (unpackPseudos.has(name)) {\n                        if (isQuote(selector.charCodeAt(selectorIndex + 1))) {\n                            throw new Error(`Pseudo-selector ${name} cannot be quoted`);\n                        }\n                        data = [];\n                        selectorIndex = parseSelector(data, selector, selectorIndex + 1);\n                        if (selector.charCodeAt(selectorIndex) !==\n                            41 /* RightParenthesis */) {\n                            throw new Error(`Missing closing parenthesis in :${name} (${selector})`);\n                        }\n                        selectorIndex += 1;\n                    }\n                    else {\n                        data = readValueWithParenthesis();\n                        if (stripQuotesFromPseudos.has(name)) {\n                            const quot = data.charCodeAt(0);\n                            if (quot === data.charCodeAt(data.length - 1) &&\n                                isQuote(quot)) {\n                                data = data.slice(1, -1);\n                            }\n                        }\n                        data = unescapeCSS(data);\n                    }\n                }\n                tokens.push({ type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Pseudo, name, data });\n                break;\n            }\n            case 44 /* Comma */: {\n                finalizeSubselector();\n                tokens = [];\n                stripWhitespace(1);\n                break;\n            }\n            default: {\n                if (selector.startsWith(\"/*\", selectorIndex)) {\n                    const endIndex = selector.indexOf(\"*/\", selectorIndex + 2);\n                    if (endIndex < 0) {\n                        throw new Error(\"Comment was not terminated\");\n                    }\n                    selectorIndex = endIndex + 2;\n                    // Remove leading whitespace\n                    if (tokens.length === 0) {\n                        stripWhitespace(0);\n                    }\n                    break;\n                }\n                let namespace = null;\n                let name;\n                if (firstChar === 42 /* Asterisk */) {\n                    selectorIndex += 1;\n                    name = \"*\";\n                }\n                else if (firstChar === 124 /* Pipe */) {\n                    name = \"\";\n                    if (selector.charCodeAt(selectorIndex + 1) === 124 /* Pipe */) {\n                        addTraversal(_types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.ColumnCombinator);\n                        stripWhitespace(2);\n                        break;\n                    }\n                }\n                else if (reName.test(selector.slice(selectorIndex))) {\n                    name = getName(0);\n                }\n                else {\n                    break loop;\n                }\n                if (selector.charCodeAt(selectorIndex) === 124 /* Pipe */ &&\n                    selector.charCodeAt(selectorIndex + 1) !== 124 /* Pipe */) {\n                    namespace = name;\n                    if (selector.charCodeAt(selectorIndex + 1) ===\n                        42 /* Asterisk */) {\n                        name = \"*\";\n                        selectorIndex += 2;\n                    }\n                    else {\n                        name = getName(1);\n                    }\n                }\n                tokens.push(name === \"*\"\n                    ? { type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Universal, namespace }\n                    : { type: _types__WEBPACK_IMPORTED_MODULE_0__.SelectorType.Tag, name, namespace });\n            }\n        }\n    }\n    finalizeSubselector();\n    return selectorIndex;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/css-what/lib/es/parse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/css-what/lib/es/types.js":
/*!***********************************************!*\
  !*** ./node_modules/css-what/lib/es/types.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AttributeAction: () => (/* binding */ AttributeAction),\n/* harmony export */   IgnoreCaseMode: () => (/* binding */ IgnoreCaseMode),\n/* harmony export */   SelectorType: () => (/* binding */ SelectorType)\n/* harmony export */ });\nvar SelectorType;\n(function (SelectorType) {\n    SelectorType[\"Attribute\"] = \"attribute\";\n    SelectorType[\"Pseudo\"] = \"pseudo\";\n    SelectorType[\"PseudoElement\"] = \"pseudo-element\";\n    SelectorType[\"Tag\"] = \"tag\";\n    SelectorType[\"Universal\"] = \"universal\";\n    // Traversals\n    SelectorType[\"Adjacent\"] = \"adjacent\";\n    SelectorType[\"Child\"] = \"child\";\n    SelectorType[\"Descendant\"] = \"descendant\";\n    SelectorType[\"Parent\"] = \"parent\";\n    SelectorType[\"Sibling\"] = \"sibling\";\n    SelectorType[\"ColumnCombinator\"] = \"column-combinator\";\n})(SelectorType || (SelectorType = {}));\n/**\n * Modes for ignore case.\n *\n * This could be updated to an enum, and the object is\n * the current stand-in that will allow code to be updated\n * without big changes.\n */\nconst IgnoreCaseMode = {\n    Unknown: null,\n    QuirksMode: \"quirks\",\n    IgnoreCase: true,\n    CaseSensitive: false,\n};\nvar AttributeAction;\n(function (AttributeAction) {\n    AttributeAction[\"Any\"] = \"any\";\n    AttributeAction[\"Element\"] = \"element\";\n    AttributeAction[\"End\"] = \"end\";\n    AttributeAction[\"Equals\"] = \"equals\";\n    AttributeAction[\"Exists\"] = \"exists\";\n    AttributeAction[\"Hyphen\"] = \"hyphen\";\n    AttributeAction[\"Not\"] = \"not\";\n    AttributeAction[\"Start\"] = \"start\";\n})(AttributeAction || (AttributeAction = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/css-what/lib/es/types.js\n");

/***/ })

};
;