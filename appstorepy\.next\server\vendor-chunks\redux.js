"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/redux";
exports.ids = ["vendor-chunks/redux"];
exports.modules = {

/***/ "(ssr)/./node_modules/redux/dist/redux.mjs":
/*!*******************************************!*\
  !*** ./node_modules/redux/dist/redux.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __DO_NOT_USE__ActionTypes: () => (/* binding */ actionTypes_default),\n/* harmony export */   applyMiddleware: () => (/* binding */ applyMiddleware),\n/* harmony export */   bindActionCreators: () => (/* binding */ bindActionCreators),\n/* harmony export */   combineReducers: () => (/* binding */ combineReducers),\n/* harmony export */   compose: () => (/* binding */ compose),\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   isAction: () => (/* binding */ isAction),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   legacy_createStore: () => (/* binding */ legacy_createStore)\n/* harmony export */ });\n// src/utils/formatProdErrorMessage.ts\nfunction formatProdErrorMessage(code) {\n    return `Minified Redux error #${code}; visit https://redux.js.org/Errors?code=${code} for the full message or use the non-minified dev environment for full errors. `;\n}\n// src/utils/symbol-observable.ts\nvar $$observable = /* @__PURE__ */ (()=>typeof Symbol === \"function\" && Symbol.observable || \"@@observable\")();\nvar symbol_observable_default = $$observable;\n// src/utils/actionTypes.ts\nvar randomString = ()=>Math.random().toString(36).substring(7).split(\"\").join(\".\");\nvar ActionTypes = {\n    INIT: `@@redux/INIT${randomString()}`,\n    REPLACE: `@@redux/REPLACE${randomString()}`,\n    PROBE_UNKNOWN_ACTION: ()=>`@@redux/PROBE_UNKNOWN_ACTION${randomString()}`\n};\nvar actionTypes_default = ActionTypes;\n// src/utils/isPlainObject.ts\nfunction isPlainObject(obj) {\n    if (typeof obj !== \"object\" || obj === null) return false;\n    let proto = obj;\n    while(Object.getPrototypeOf(proto) !== null){\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(obj) === proto || Object.getPrototypeOf(obj) === null;\n}\n// src/utils/kindOf.ts\nfunction miniKindOf(val) {\n    if (val === void 0) return \"undefined\";\n    if (val === null) return \"null\";\n    const type = typeof val;\n    switch(type){\n        case \"boolean\":\n        case \"string\":\n        case \"number\":\n        case \"symbol\":\n        case \"function\":\n            {\n                return type;\n            }\n    }\n    if (Array.isArray(val)) return \"array\";\n    if (isDate(val)) return \"date\";\n    if (isError(val)) return \"error\";\n    const constructorName = ctorName(val);\n    switch(constructorName){\n        case \"Symbol\":\n        case \"Promise\":\n        case \"WeakMap\":\n        case \"WeakSet\":\n        case \"Map\":\n        case \"Set\":\n            return constructorName;\n    }\n    return Object.prototype.toString.call(val).slice(8, -1).toLowerCase().replace(/\\s/g, \"\");\n}\nfunction ctorName(val) {\n    return typeof val.constructor === \"function\" ? val.constructor.name : null;\n}\nfunction isError(val) {\n    return val instanceof Error || typeof val.message === \"string\" && val.constructor && typeof val.constructor.stackTraceLimit === \"number\";\n}\nfunction isDate(val) {\n    if (val instanceof Date) return true;\n    return typeof val.toDateString === \"function\" && typeof val.getDate === \"function\" && typeof val.setDate === \"function\";\n}\nfunction kindOf(val) {\n    let typeOfVal = typeof val;\n    if (true) {\n        typeOfVal = miniKindOf(val);\n    }\n    return typeOfVal;\n}\n// src/createStore.ts\nfunction createStore(reducer, preloadedState, enhancer) {\n    if (typeof reducer !== \"function\") {\n        throw new Error( false ? 0 : `Expected the root reducer to be a function. Instead, received: '${kindOf(reducer)}'`);\n    }\n    if (typeof preloadedState === \"function\" && typeof enhancer === \"function\" || typeof enhancer === \"function\" && typeof arguments[3] === \"function\") {\n        throw new Error( false ? 0 : \"It looks like you are passing several store enhancers to createStore(). This is not supported. Instead, compose them together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.\");\n    }\n    if (typeof preloadedState === \"function\" && typeof enhancer === \"undefined\") {\n        enhancer = preloadedState;\n        preloadedState = void 0;\n    }\n    if (typeof enhancer !== \"undefined\") {\n        if (typeof enhancer !== \"function\") {\n            throw new Error( false ? 0 : `Expected the enhancer to be a function. Instead, received: '${kindOf(enhancer)}'`);\n        }\n        return enhancer(createStore)(reducer, preloadedState);\n    }\n    let currentReducer = reducer;\n    let currentState = preloadedState;\n    let currentListeners = /* @__PURE__ */ new Map();\n    let nextListeners = currentListeners;\n    let listenerIdCounter = 0;\n    let isDispatching = false;\n    function ensureCanMutateNextListeners() {\n        if (nextListeners === currentListeners) {\n            nextListeners = /* @__PURE__ */ new Map();\n            currentListeners.forEach((listener, key)=>{\n                nextListeners.set(key, listener);\n            });\n        }\n    }\n    function getState() {\n        if (isDispatching) {\n            throw new Error( false ? 0 : \"You may not call store.getState() while the reducer is executing. The reducer has already received the state as an argument. Pass it down from the top reducer instead of reading it from the store.\");\n        }\n        return currentState;\n    }\n    function subscribe(listener) {\n        if (typeof listener !== \"function\") {\n            throw new Error( false ? 0 : `Expected the listener to be a function. Instead, received: '${kindOf(listener)}'`);\n        }\n        if (isDispatching) {\n            throw new Error( false ? 0 : \"You may not call store.subscribe() while the reducer is executing. If you would like to be notified after the store has been updated, subscribe from a component and invoke store.getState() in the callback to access the latest state. See https://redux.js.org/api/store#subscribelistener for more details.\");\n        }\n        let isSubscribed = true;\n        ensureCanMutateNextListeners();\n        const listenerId = listenerIdCounter++;\n        nextListeners.set(listenerId, listener);\n        return function unsubscribe() {\n            if (!isSubscribed) {\n                return;\n            }\n            if (isDispatching) {\n                throw new Error( false ? 0 : \"You may not unsubscribe from a store listener while the reducer is executing. See https://redux.js.org/api/store#subscribelistener for more details.\");\n            }\n            isSubscribed = false;\n            ensureCanMutateNextListeners();\n            nextListeners.delete(listenerId);\n            currentListeners = null;\n        };\n    }\n    function dispatch(action) {\n        if (!isPlainObject(action)) {\n            throw new Error( false ? 0 : `Actions must be plain objects. Instead, the actual type was: '${kindOf(action)}'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.`);\n        }\n        if (typeof action.type === \"undefined\") {\n            throw new Error( false ? 0 : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n        }\n        if (typeof action.type !== \"string\") {\n            throw new Error( false ? 0 : `Action \"type\" property must be a string. Instead, the actual type was: '${kindOf(action.type)}'. Value was: '${action.type}' (stringified)`);\n        }\n        if (isDispatching) {\n            throw new Error( false ? 0 : \"Reducers may not dispatch actions.\");\n        }\n        try {\n            isDispatching = true;\n            currentState = currentReducer(currentState, action);\n        } finally{\n            isDispatching = false;\n        }\n        const listeners = currentListeners = nextListeners;\n        listeners.forEach((listener)=>{\n            listener();\n        });\n        return action;\n    }\n    function replaceReducer(nextReducer) {\n        if (typeof nextReducer !== \"function\") {\n            throw new Error( false ? 0 : `Expected the nextReducer to be a function. Instead, received: '${kindOf(nextReducer)}`);\n        }\n        currentReducer = nextReducer;\n        dispatch({\n            type: actionTypes_default.REPLACE\n        });\n    }\n    function observable() {\n        const outerSubscribe = subscribe;\n        return {\n            /**\n       * The minimal observable subscription method.\n       * @param observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */ subscribe (observer) {\n                if (typeof observer !== \"object\" || observer === null) {\n                    throw new Error( false ? 0 : `Expected the observer to be an object. Instead, received: '${kindOf(observer)}'`);\n                }\n                function observeState() {\n                    const observerAsObserver = observer;\n                    if (observerAsObserver.next) {\n                        observerAsObserver.next(getState());\n                    }\n                }\n                observeState();\n                const unsubscribe = outerSubscribe(observeState);\n                return {\n                    unsubscribe\n                };\n            },\n            [symbol_observable_default] () {\n                return this;\n            }\n        };\n    }\n    dispatch({\n        type: actionTypes_default.INIT\n    });\n    const store = {\n        dispatch,\n        subscribe,\n        getState,\n        replaceReducer,\n        [symbol_observable_default]: observable\n    };\n    return store;\n}\nfunction legacy_createStore(reducer, preloadedState, enhancer) {\n    return createStore(reducer, preloadedState, enhancer);\n}\n// src/utils/warning.ts\nfunction warning(message) {\n    if (typeof console !== \"undefined\" && typeof console.error === \"function\") {\n        console.error(message);\n    }\n    try {\n        throw new Error(message);\n    } catch (e) {}\n}\n// src/combineReducers.ts\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n    const reducerKeys = Object.keys(reducers);\n    const argumentName = action && action.type === actionTypes_default.INIT ? \"preloadedState argument passed to createStore\" : \"previous state received by the reducer\";\n    if (reducerKeys.length === 0) {\n        return \"Store does not have a valid reducer. Make sure the argument passed to combineReducers is an object whose values are reducers.\";\n    }\n    if (!isPlainObject(inputState)) {\n        return `The ${argumentName} has unexpected type of \"${kindOf(inputState)}\". Expected argument to be an object with the following keys: \"${reducerKeys.join('\", \"')}\"`;\n    }\n    const unexpectedKeys = Object.keys(inputState).filter((key)=>!reducers.hasOwnProperty(key) && !unexpectedKeyCache[key]);\n    unexpectedKeys.forEach((key)=>{\n        unexpectedKeyCache[key] = true;\n    });\n    if (action && action.type === actionTypes_default.REPLACE) return;\n    if (unexpectedKeys.length > 0) {\n        return `Unexpected ${unexpectedKeys.length > 1 ? \"keys\" : \"key\"} \"${unexpectedKeys.join('\", \"')}\" found in ${argumentName}. Expected to find one of the known reducer keys instead: \"${reducerKeys.join('\", \"')}\". Unexpected keys will be ignored.`;\n    }\n}\nfunction assertReducerShape(reducers) {\n    Object.keys(reducers).forEach((key)=>{\n        const reducer = reducers[key];\n        const initialState = reducer(void 0, {\n            type: actionTypes_default.INIT\n        });\n        if (typeof initialState === \"undefined\") {\n            throw new Error( false ? 0 : `The slice reducer for key \"${key}\" returned undefined during initialization. If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);\n        }\n        if (typeof reducer(void 0, {\n            type: actionTypes_default.PROBE_UNKNOWN_ACTION()\n        }) === \"undefined\") {\n            throw new Error( false ? 0 : `The slice reducer for key \"${key}\" returned undefined when probed with a random type. Don't try to handle '${actionTypes_default.INIT}' or other actions in \"redux/*\" namespace. They are considered private. Instead, you must return the current state for any unknown actions, unless it is undefined, in which case you must return the initial state, regardless of the action type. The initial state may not be undefined, but can be null.`);\n        }\n    });\n}\nfunction combineReducers(reducers) {\n    const reducerKeys = Object.keys(reducers);\n    const finalReducers = {};\n    for(let i = 0; i < reducerKeys.length; i++){\n        const key = reducerKeys[i];\n        if (true) {\n            if (typeof reducers[key] === \"undefined\") {\n                warning(`No reducer provided for key \"${key}\"`);\n            }\n        }\n        if (typeof reducers[key] === \"function\") {\n            finalReducers[key] = reducers[key];\n        }\n    }\n    const finalReducerKeys = Object.keys(finalReducers);\n    let unexpectedKeyCache;\n    if (true) {\n        unexpectedKeyCache = {};\n    }\n    let shapeAssertionError;\n    try {\n        assertReducerShape(finalReducers);\n    } catch (e) {\n        shapeAssertionError = e;\n    }\n    return function combination(state = {}, action) {\n        if (shapeAssertionError) {\n            throw shapeAssertionError;\n        }\n        if (true) {\n            const warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n            if (warningMessage) {\n                warning(warningMessage);\n            }\n        }\n        let hasChanged = false;\n        const nextState = {};\n        for(let i = 0; i < finalReducerKeys.length; i++){\n            const key = finalReducerKeys[i];\n            const reducer = finalReducers[key];\n            const previousStateForKey = state[key];\n            const nextStateForKey = reducer(previousStateForKey, action);\n            if (typeof nextStateForKey === \"undefined\") {\n                const actionType = action && action.type;\n                throw new Error( false ? 0 : `When called with an action of type ${actionType ? `\"${String(actionType)}\"` : \"(unknown type)\"}, the slice reducer for key \"${key}\" returned undefined. To ignore an action, you must explicitly return the previous state. If you want this reducer to hold no value, you can return null instead of undefined.`);\n            }\n            nextState[key] = nextStateForKey;\n            hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n        }\n        hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n        return hasChanged ? nextState : state;\n    };\n}\n// src/bindActionCreators.ts\nfunction bindActionCreator(actionCreator, dispatch) {\n    return function(...args) {\n        return dispatch(actionCreator.apply(this, args));\n    };\n}\nfunction bindActionCreators(actionCreators, dispatch) {\n    if (typeof actionCreators === \"function\") {\n        return bindActionCreator(actionCreators, dispatch);\n    }\n    if (typeof actionCreators !== \"object\" || actionCreators === null) {\n        throw new Error( false ? 0 : `bindActionCreators expected an object or a function, but instead received: '${kindOf(actionCreators)}'. Did you write \"import ActionCreators from\" instead of \"import * as ActionCreators from\"?`);\n    }\n    const boundActionCreators = {};\n    for(const key in actionCreators){\n        const actionCreator = actionCreators[key];\n        if (typeof actionCreator === \"function\") {\n            boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n        }\n    }\n    return boundActionCreators;\n}\n// src/compose.ts\nfunction compose(...funcs) {\n    if (funcs.length === 0) {\n        return (arg)=>arg;\n    }\n    if (funcs.length === 1) {\n        return funcs[0];\n    }\n    return funcs.reduce((a, b)=>(...args)=>a(b(...args)));\n}\n// src/applyMiddleware.ts\nfunction applyMiddleware(...middlewares) {\n    return (createStore2)=>(reducer, preloadedState)=>{\n            const store = createStore2(reducer, preloadedState);\n            let dispatch = ()=>{\n                throw new Error( false ? 0 : \"Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.\");\n            };\n            const middlewareAPI = {\n                getState: store.getState,\n                dispatch: (action, ...args)=>dispatch(action, ...args)\n            };\n            const chain = middlewares.map((middleware)=>middleware(middlewareAPI));\n            dispatch = compose(...chain)(store.dispatch);\n            return {\n                ...store,\n                dispatch\n            };\n        };\n}\n// src/utils/isAction.ts\nfunction isAction(action) {\n    return isPlainObject(action) && \"type\" in action && typeof action.type === \"string\";\n}\n //# sourceMappingURL=redux.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVkdXgvZGlzdC9yZWR1eC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUEsc0NBQXNDO0FBQ3RDLFNBQVNBLHVCQUF1QkMsSUFBSTtJQUNsQyxPQUFPLENBQUMsc0JBQXNCLEVBQUVBLEtBQUsseUNBQXlDLEVBQUVBLEtBQUssK0VBQStFLENBQUM7QUFDdks7QUFFQSxpQ0FBaUM7QUFDakMsSUFBSUMsZUFBK0IsYUFBSCxHQUFJLEtBQU0sT0FBT0MsV0FBVyxjQUFjQSxPQUFPQyxVQUFVLElBQUksY0FBYTtBQUM1RyxJQUFJQyw0QkFBNEJIO0FBRWhDLDJCQUEyQjtBQUMzQixJQUFJSSxlQUFlLElBQU1DLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHQyxLQUFLLENBQUMsSUFBSUMsSUFBSSxDQUFDO0FBQ2hGLElBQUlDLGNBQWM7SUFDaEJDLE1BQU0sQ0FBQyxZQUFZLEVBQWtCUixlQUFlLENBQUM7SUFDckRTLFNBQVMsQ0FBQyxlQUFlLEVBQWtCVCxlQUFlLENBQUM7SUFDM0RVLHNCQUFzQixJQUFNLENBQUMsNEJBQTRCLEVBQUVWLGVBQWUsQ0FBQztBQUM3RTtBQUNBLElBQUlXLHNCQUFzQko7QUFFMUIsNkJBQTZCO0FBQzdCLFNBQVNLLGNBQWNDLEdBQUc7SUFDeEIsSUFBSSxPQUFPQSxRQUFRLFlBQVlBLFFBQVEsTUFDckMsT0FBTztJQUNULElBQUlDLFFBQVFEO0lBQ1osTUFBT0UsT0FBT0MsY0FBYyxDQUFDRixXQUFXLEtBQU07UUFDNUNBLFFBQVFDLE9BQU9DLGNBQWMsQ0FBQ0Y7SUFDaEM7SUFDQSxPQUFPQyxPQUFPQyxjQUFjLENBQUNILFNBQVNDLFNBQVNDLE9BQU9DLGNBQWMsQ0FBQ0gsU0FBUztBQUNoRjtBQUVBLHNCQUFzQjtBQUN0QixTQUFTSSxXQUFXQyxHQUFHO0lBQ3JCLElBQUlBLFFBQVEsS0FBSyxHQUNmLE9BQU87SUFDVCxJQUFJQSxRQUFRLE1BQ1YsT0FBTztJQUNULE1BQU1DLE9BQU8sT0FBT0Q7SUFDcEIsT0FBUUM7UUFDTixLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztZQUFZO2dCQUNmLE9BQU9BO1lBQ1Q7SUFDRjtJQUNBLElBQUlDLE1BQU1DLE9BQU8sQ0FBQ0gsTUFDaEIsT0FBTztJQUNULElBQUlJLE9BQU9KLE1BQ1QsT0FBTztJQUNULElBQUlLLFFBQVFMLE1BQ1YsT0FBTztJQUNULE1BQU1NLGtCQUFrQkMsU0FBU1A7SUFDakMsT0FBUU07UUFDTixLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7WUFDSCxPQUFPQTtJQUNYO0lBQ0EsT0FBT1QsT0FBT1csU0FBUyxDQUFDdkIsUUFBUSxDQUFDd0IsSUFBSSxDQUFDVCxLQUFLVSxLQUFLLENBQUMsR0FBRyxDQUFDLEdBQUdDLFdBQVcsR0FBR0MsT0FBTyxDQUFDLE9BQU87QUFDdkY7QUFDQSxTQUFTTCxTQUFTUCxHQUFHO0lBQ25CLE9BQU8sT0FBT0EsSUFBSWEsV0FBVyxLQUFLLGFBQWFiLElBQUlhLFdBQVcsQ0FBQ0MsSUFBSSxHQUFHO0FBQ3hFO0FBQ0EsU0FBU1QsUUFBUUwsR0FBRztJQUNsQixPQUFPQSxlQUFlZSxTQUFTLE9BQU9mLElBQUlnQixPQUFPLEtBQUssWUFBWWhCLElBQUlhLFdBQVcsSUFBSSxPQUFPYixJQUFJYSxXQUFXLENBQUNJLGVBQWUsS0FBSztBQUNsSTtBQUNBLFNBQVNiLE9BQU9KLEdBQUc7SUFDakIsSUFBSUEsZUFBZWtCLE1BQ2pCLE9BQU87SUFDVCxPQUFPLE9BQU9sQixJQUFJbUIsWUFBWSxLQUFLLGNBQWMsT0FBT25CLElBQUlvQixPQUFPLEtBQUssY0FBYyxPQUFPcEIsSUFBSXFCLE9BQU8sS0FBSztBQUMvRztBQUNBLFNBQVNDLE9BQU90QixHQUFHO0lBQ2pCLElBQUl1QixZQUFZLE9BQU92QjtJQUN2QixJQUFJd0IsSUFBcUMsRUFBRTtRQUN6Q0QsWUFBWXhCLFdBQVdDO0lBQ3pCO0lBQ0EsT0FBT3VCO0FBQ1Q7QUFFQSxxQkFBcUI7QUFDckIsU0FBU0UsWUFBWUMsT0FBTyxFQUFFQyxjQUFjLEVBQUVDLFFBQVE7SUFDcEQsSUFBSSxPQUFPRixZQUFZLFlBQVk7UUFDakMsTUFBTSxJQUFJWCxNQUFNUyxNQUFxQyxHQUFHaEQsQ0FBeUIsR0FBRyxDQUFDLGdFQUFnRSxFQUFFOEMsT0FBT0ksU0FBUyxDQUFDLENBQUM7SUFDM0s7SUFDQSxJQUFJLE9BQU9DLG1CQUFtQixjQUFjLE9BQU9DLGFBQWEsY0FBYyxPQUFPQSxhQUFhLGNBQWMsT0FBT0MsU0FBUyxDQUFDLEVBQUUsS0FBSyxZQUFZO1FBQ2xKLE1BQU0sSUFBSWQsTUFBTVMsTUFBcUMsR0FBR2hELENBQXlCLEdBQUc7SUFDdEY7SUFDQSxJQUFJLE9BQU9tRCxtQkFBbUIsY0FBYyxPQUFPQyxhQUFhLGFBQWE7UUFDM0VBLFdBQVdEO1FBQ1hBLGlCQUFpQixLQUFLO0lBQ3hCO0lBQ0EsSUFBSSxPQUFPQyxhQUFhLGFBQWE7UUFDbkMsSUFBSSxPQUFPQSxhQUFhLFlBQVk7WUFDbEMsTUFBTSxJQUFJYixNQUFNUyxNQUFxQyxHQUFHaEQsQ0FBeUIsR0FBRyxDQUFDLDREQUE0RCxFQUFFOEMsT0FBT00sVUFBVSxDQUFDLENBQUM7UUFDeEs7UUFDQSxPQUFPQSxTQUFTSCxhQUFhQyxTQUFTQztJQUN4QztJQUNBLElBQUlHLGlCQUFpQko7SUFDckIsSUFBSUssZUFBZUo7SUFDbkIsSUFBSUssbUJBQW1CLGFBQWEsR0FBRyxJQUFJQztJQUMzQyxJQUFJQyxnQkFBZ0JGO0lBQ3BCLElBQUlHLG9CQUFvQjtJQUN4QixJQUFJQyxnQkFBZ0I7SUFDcEIsU0FBU0M7UUFDUCxJQUFJSCxrQkFBa0JGLGtCQUFrQjtZQUN0Q0UsZ0JBQWdCLGFBQWEsR0FBRyxJQUFJRDtZQUNwQ0QsaUJBQWlCTSxPQUFPLENBQUMsQ0FBQ0MsVUFBVUM7Z0JBQ2xDTixjQUFjTyxHQUFHLENBQUNELEtBQUtEO1lBQ3pCO1FBQ0Y7SUFDRjtJQUNBLFNBQVNHO1FBQ1AsSUFBSU4sZUFBZTtZQUNqQixNQUFNLElBQUlyQixNQUFNUyxNQUFxQyxHQUFHaEQsQ0FBeUIsR0FBRztRQUN0RjtRQUNBLE9BQU91RDtJQUNUO0lBQ0EsU0FBU1ksVUFBVUosUUFBUTtRQUN6QixJQUFJLE9BQU9BLGFBQWEsWUFBWTtZQUNsQyxNQUFNLElBQUl4QixNQUFNUyxNQUFxQyxHQUFHaEQsQ0FBeUIsR0FBRyxDQUFDLDREQUE0RCxFQUFFOEMsT0FBT2lCLFVBQVUsQ0FBQyxDQUFDO1FBQ3hLO1FBQ0EsSUFBSUgsZUFBZTtZQUNqQixNQUFNLElBQUlyQixNQUFNUyxNQUFxQyxHQUFHaEQsQ0FBeUIsR0FBRztRQUN0RjtRQUNBLElBQUlvRSxlQUFlO1FBQ25CUDtRQUNBLE1BQU1RLGFBQWFWO1FBQ25CRCxjQUFjTyxHQUFHLENBQUNJLFlBQVlOO1FBQzlCLE9BQU8sU0FBU087WUFDZCxJQUFJLENBQUNGLGNBQWM7Z0JBQ2pCO1lBQ0Y7WUFDQSxJQUFJUixlQUFlO2dCQUNqQixNQUFNLElBQUlyQixNQUFNUyxNQUFxQyxHQUFHaEQsQ0FBeUIsR0FBRztZQUN0RjtZQUNBb0UsZUFBZTtZQUNmUDtZQUNBSCxjQUFjYSxNQUFNLENBQUNGO1lBQ3JCYixtQkFBbUI7UUFDckI7SUFDRjtJQUNBLFNBQVNnQixTQUFTQyxNQUFNO1FBQ3RCLElBQUksQ0FBQ3ZELGNBQWN1RCxTQUFTO1lBQzFCLE1BQU0sSUFBSWxDLE1BQU1TLE1BQXFDLEdBQUdoRCxDQUF5QixHQUFHLENBQUMsOERBQThELEVBQUU4QyxPQUFPMkIsUUFBUSwwVUFBMFUsQ0FBQztRQUNqZjtRQUNBLElBQUksT0FBT0EsT0FBT2hELElBQUksS0FBSyxhQUFhO1lBQ3RDLE1BQU0sSUFBSWMsTUFBTVMsTUFBcUMsR0FBR2hELENBQXlCLEdBQUc7UUFDdEY7UUFDQSxJQUFJLE9BQU95RSxPQUFPaEQsSUFBSSxLQUFLLFVBQVU7WUFDbkMsTUFBTSxJQUFJYyxNQUFNUyxNQUFxQyxHQUFHaEQsQ0FBMEIsR0FBRyxDQUFDLHdFQUF3RSxFQUFFOEMsT0FBTzJCLE9BQU9oRCxJQUFJLEVBQUUsZUFBZSxFQUFFZ0QsT0FBT2hELElBQUksQ0FBQyxlQUFlLENBQUM7UUFDbk87UUFDQSxJQUFJbUMsZUFBZTtZQUNqQixNQUFNLElBQUlyQixNQUFNUyxNQUFxQyxHQUFHaEQsQ0FBeUIsR0FBRztRQUN0RjtRQUNBLElBQUk7WUFDRjRELGdCQUFnQjtZQUNoQkwsZUFBZUQsZUFBZUMsY0FBY2tCO1FBQzlDLFNBQVU7WUFDUmIsZ0JBQWdCO1FBQ2xCO1FBQ0EsTUFBTWMsWUFBWWxCLG1CQUFtQkU7UUFDckNnQixVQUFVWixPQUFPLENBQUMsQ0FBQ0M7WUFDakJBO1FBQ0Y7UUFDQSxPQUFPVTtJQUNUO0lBQ0EsU0FBU0UsZUFBZUMsV0FBVztRQUNqQyxJQUFJLE9BQU9BLGdCQUFnQixZQUFZO1lBQ3JDLE1BQU0sSUFBSXJDLE1BQU1TLE1BQXFDLEdBQUdoRCxDQUEwQixHQUFHLENBQUMsK0RBQStELEVBQUU4QyxPQUFPOEIsYUFBYSxDQUFDO1FBQzlLO1FBQ0F0QixpQkFBaUJzQjtRQUNqQkosU0FBUztZQUNQL0MsTUFBTVIsb0JBQW9CRixPQUFPO1FBQ25DO0lBQ0Y7SUFDQSxTQUFTWDtRQUNQLE1BQU15RSxpQkFBaUJWO1FBQ3ZCLE9BQU87WUFDTDs7Ozs7OztPQU9DLEdBQ0RBLFdBQVVXLFFBQVE7Z0JBQ2hCLElBQUksT0FBT0EsYUFBYSxZQUFZQSxhQUFhLE1BQU07b0JBQ3JELE1BQU0sSUFBSXZDLE1BQU1TLE1BQXFDLEdBQUdoRCxDQUEwQixHQUFHLENBQUMsMkRBQTJELEVBQUU4QyxPQUFPZ0MsVUFBVSxDQUFDLENBQUM7Z0JBQ3hLO2dCQUNBLFNBQVNDO29CQUNQLE1BQU1DLHFCQUFxQkY7b0JBQzNCLElBQUlFLG1CQUFtQkMsSUFBSSxFQUFFO3dCQUMzQkQsbUJBQW1CQyxJQUFJLENBQUNmO29CQUMxQjtnQkFDRjtnQkFDQWE7Z0JBQ0EsTUFBTVQsY0FBY08sZUFBZUU7Z0JBQ25DLE9BQU87b0JBQ0xUO2dCQUNGO1lBQ0Y7WUFDQSxDQUFDakUsMEJBQTBCO2dCQUN6QixPQUFPLElBQUk7WUFDYjtRQUNGO0lBQ0Y7SUFDQW1FLFNBQVM7UUFDUC9DLE1BQU1SLG9CQUFvQkgsSUFBSTtJQUNoQztJQUNBLE1BQU1vRSxRQUFRO1FBQ1pWO1FBQ0FMO1FBQ0FEO1FBQ0FTO1FBQ0EsQ0FBQ3RFLDBCQUEwQixFQUFFRDtJQUMvQjtJQUNBLE9BQU84RTtBQUNUO0FBQ0EsU0FBU0MsbUJBQW1CakMsT0FBTyxFQUFFQyxjQUFjLEVBQUVDLFFBQVE7SUFDM0QsT0FBT0gsWUFBWUMsU0FBU0MsZ0JBQWdCQztBQUM5QztBQUVBLHVCQUF1QjtBQUN2QixTQUFTZ0MsUUFBUTVDLE9BQU87SUFDdEIsSUFBSSxPQUFPNkMsWUFBWSxlQUFlLE9BQU9BLFFBQVFDLEtBQUssS0FBSyxZQUFZO1FBQ3pFRCxRQUFRQyxLQUFLLENBQUM5QztJQUNoQjtJQUNBLElBQUk7UUFDRixNQUFNLElBQUlELE1BQU1DO0lBQ2xCLEVBQUUsT0FBTytDLEdBQUcsQ0FDWjtBQUNGO0FBRUEseUJBQXlCO0FBQ3pCLFNBQVNDLHNDQUFzQ0MsVUFBVSxFQUFFQyxRQUFRLEVBQUVqQixNQUFNLEVBQUVrQixrQkFBa0I7SUFDN0YsTUFBTUMsY0FBY3ZFLE9BQU93RSxJQUFJLENBQUNIO0lBQ2hDLE1BQU1JLGVBQWVyQixVQUFVQSxPQUFPaEQsSUFBSSxLQUFLUixvQkFBb0JILElBQUksR0FBRyxrREFBa0Q7SUFDNUgsSUFBSThFLFlBQVlHLE1BQU0sS0FBSyxHQUFHO1FBQzVCLE9BQU87SUFDVDtJQUNBLElBQUksQ0FBQzdFLGNBQWN1RSxhQUFhO1FBQzlCLE9BQU8sQ0FBQyxJQUFJLEVBQUVLLGFBQWEseUJBQXlCLEVBQUVoRCxPQUFPMkMsWUFBWSwrREFBK0QsRUFBRUcsWUFBWWhGLElBQUksQ0FBQyxRQUFRLENBQUMsQ0FBQztJQUN2SztJQUNBLE1BQU1vRixpQkFBaUIzRSxPQUFPd0UsSUFBSSxDQUFDSixZQUFZUSxNQUFNLENBQUMsQ0FBQ2pDLE1BQVEsQ0FBQzBCLFNBQVNRLGNBQWMsQ0FBQ2xDLFFBQVEsQ0FBQzJCLGtCQUFrQixDQUFDM0IsSUFBSTtJQUN4SGdDLGVBQWVsQyxPQUFPLENBQUMsQ0FBQ0U7UUFDdEIyQixrQkFBa0IsQ0FBQzNCLElBQUksR0FBRztJQUM1QjtJQUNBLElBQUlTLFVBQVVBLE9BQU9oRCxJQUFJLEtBQUtSLG9CQUFvQkYsT0FBTyxFQUN2RDtJQUNGLElBQUlpRixlQUFlRCxNQUFNLEdBQUcsR0FBRztRQUM3QixPQUFPLENBQUMsV0FBVyxFQUFFQyxlQUFlRCxNQUFNLEdBQUcsSUFBSSxTQUFTLE1BQU0sRUFBRSxFQUFFQyxlQUFlcEYsSUFBSSxDQUFDLFFBQVEsV0FBVyxFQUFFa0YsYUFBYSwyREFBMkQsRUFBRUYsWUFBWWhGLElBQUksQ0FBQyxRQUFRLG1DQUFtQyxDQUFDO0lBQ3RQO0FBQ0Y7QUFDQSxTQUFTdUYsbUJBQW1CVCxRQUFRO0lBQ2xDckUsT0FBT3dFLElBQUksQ0FBQ0gsVUFBVTVCLE9BQU8sQ0FBQyxDQUFDRTtRQUM3QixNQUFNZCxVQUFVd0MsUUFBUSxDQUFDMUIsSUFBSTtRQUM3QixNQUFNb0MsZUFBZWxELFFBQVEsS0FBSyxHQUFHO1lBQ25DekIsTUFBTVIsb0JBQW9CSCxJQUFJO1FBQ2hDO1FBQ0EsSUFBSSxPQUFPc0YsaUJBQWlCLGFBQWE7WUFDdkMsTUFBTSxJQUFJN0QsTUFBTVMsTUFBcUMsR0FBR2hELENBQTBCLEdBQUcsQ0FBQywyQkFBMkIsRUFBRWdFLElBQUksNFFBQTRRLENBQUM7UUFDdFk7UUFDQSxJQUFJLE9BQU9kLFFBQVEsS0FBSyxHQUFHO1lBQ3pCekIsTUFBTVIsb0JBQW9CRCxvQkFBb0I7UUFDaEQsT0FBTyxhQUFhO1lBQ2xCLE1BQU0sSUFBSXVCLE1BQU1TLE1BQXFDLEdBQUdoRCxDQUEwQixHQUFHLENBQUMsMkJBQTJCLEVBQUVnRSxJQUFJLDBFQUEwRSxFQUFFL0Msb0JBQW9CSCxJQUFJLENBQUMsNFNBQTRTLENBQUM7UUFDM2dCO0lBQ0Y7QUFDRjtBQUNBLFNBQVN1RixnQkFBZ0JYLFFBQVE7SUFDL0IsTUFBTUUsY0FBY3ZFLE9BQU93RSxJQUFJLENBQUNIO0lBQ2hDLE1BQU1ZLGdCQUFnQixDQUFDO0lBQ3ZCLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJWCxZQUFZRyxNQUFNLEVBQUVRLElBQUs7UUFDM0MsTUFBTXZDLE1BQU00QixXQUFXLENBQUNXLEVBQUU7UUFDMUIsSUFBSXZELElBQXFDLEVBQUU7WUFDekMsSUFBSSxPQUFPMEMsUUFBUSxDQUFDMUIsSUFBSSxLQUFLLGFBQWE7Z0JBQ3hDb0IsUUFBUSxDQUFDLDZCQUE2QixFQUFFcEIsSUFBSSxDQUFDLENBQUM7WUFDaEQ7UUFDRjtRQUNBLElBQUksT0FBTzBCLFFBQVEsQ0FBQzFCLElBQUksS0FBSyxZQUFZO1lBQ3ZDc0MsYUFBYSxDQUFDdEMsSUFBSSxHQUFHMEIsUUFBUSxDQUFDMUIsSUFBSTtRQUNwQztJQUNGO0lBQ0EsTUFBTXdDLG1CQUFtQm5GLE9BQU93RSxJQUFJLENBQUNTO0lBQ3JDLElBQUlYO0lBQ0osSUFBSTNDLElBQXFDLEVBQUU7UUFDekMyQyxxQkFBcUIsQ0FBQztJQUN4QjtJQUNBLElBQUljO0lBQ0osSUFBSTtRQUNGTixtQkFBbUJHO0lBQ3JCLEVBQUUsT0FBT2YsR0FBRztRQUNWa0Isc0JBQXNCbEI7SUFDeEI7SUFDQSxPQUFPLFNBQVNtQixZQUFZQyxRQUFRLENBQUMsQ0FBQyxFQUFFbEMsTUFBTTtRQUM1QyxJQUFJZ0MscUJBQXFCO1lBQ3ZCLE1BQU1BO1FBQ1I7UUFDQSxJQUFJekQsSUFBcUMsRUFBRTtZQUN6QyxNQUFNNEQsaUJBQWlCcEIsc0NBQXNDbUIsT0FBT0wsZUFBZTdCLFFBQVFrQjtZQUMzRixJQUFJaUIsZ0JBQWdCO2dCQUNsQnhCLFFBQVF3QjtZQUNWO1FBQ0Y7UUFDQSxJQUFJQyxhQUFhO1FBQ2pCLE1BQU1DLFlBQVksQ0FBQztRQUNuQixJQUFLLElBQUlQLElBQUksR0FBR0EsSUFBSUMsaUJBQWlCVCxNQUFNLEVBQUVRLElBQUs7WUFDaEQsTUFBTXZDLE1BQU13QyxnQkFBZ0IsQ0FBQ0QsRUFBRTtZQUMvQixNQUFNckQsVUFBVW9ELGFBQWEsQ0FBQ3RDLElBQUk7WUFDbEMsTUFBTStDLHNCQUFzQkosS0FBSyxDQUFDM0MsSUFBSTtZQUN0QyxNQUFNZ0Qsa0JBQWtCOUQsUUFBUTZELHFCQUFxQnRDO1lBQ3JELElBQUksT0FBT3VDLG9CQUFvQixhQUFhO2dCQUMxQyxNQUFNQyxhQUFheEMsVUFBVUEsT0FBT2hELElBQUk7Z0JBQ3hDLE1BQU0sSUFBSWMsTUFBTVMsTUFBcUMsR0FBR2hELENBQTBCLEdBQUcsQ0FBQyxtQ0FBbUMsRUFBRWlILGFBQWEsQ0FBQyxDQUFDLEVBQUVDLE9BQU9ELFlBQVksQ0FBQyxDQUFDLEdBQUcsaUJBQWlCLDZCQUE2QixFQUFFakQsSUFBSSw4S0FBOEssQ0FBQztZQUN6WTtZQUNBOEMsU0FBUyxDQUFDOUMsSUFBSSxHQUFHZ0Q7WUFDakJILGFBQWFBLGNBQWNHLG9CQUFvQkQ7UUFDakQ7UUFDQUYsYUFBYUEsY0FBY0wsaUJBQWlCVCxNQUFNLEtBQUsxRSxPQUFPd0UsSUFBSSxDQUFDYyxPQUFPWixNQUFNO1FBQ2hGLE9BQU9jLGFBQWFDLFlBQVlIO0lBQ2xDO0FBQ0Y7QUFFQSw0QkFBNEI7QUFDNUIsU0FBU1Esa0JBQWtCQyxhQUFhLEVBQUU1QyxRQUFRO0lBQ2hELE9BQU8sU0FBUyxHQUFHNkMsSUFBSTtRQUNyQixPQUFPN0MsU0FBUzRDLGNBQWNFLEtBQUssQ0FBQyxJQUFJLEVBQUVEO0lBQzVDO0FBQ0Y7QUFDQSxTQUFTRSxtQkFBbUJDLGNBQWMsRUFBRWhELFFBQVE7SUFDbEQsSUFBSSxPQUFPZ0QsbUJBQW1CLFlBQVk7UUFDeEMsT0FBT0wsa0JBQWtCSyxnQkFBZ0JoRDtJQUMzQztJQUNBLElBQUksT0FBT2dELG1CQUFtQixZQUFZQSxtQkFBbUIsTUFBTTtRQUNqRSxNQUFNLElBQUlqRixNQUFNUyxNQUFxQyxHQUFHaEQsQ0FBMEIsR0FBRyxDQUFDLDRFQUE0RSxFQUFFOEMsT0FBTzBFLGdCQUFnQiwyRkFBMkYsQ0FBQztJQUN6UjtJQUNBLE1BQU1DLHNCQUFzQixDQUFDO0lBQzdCLElBQUssTUFBTXpELE9BQU93RCxlQUFnQjtRQUNoQyxNQUFNSixnQkFBZ0JJLGNBQWMsQ0FBQ3hELElBQUk7UUFDekMsSUFBSSxPQUFPb0Qsa0JBQWtCLFlBQVk7WUFDdkNLLG1CQUFtQixDQUFDekQsSUFBSSxHQUFHbUQsa0JBQWtCQyxlQUFlNUM7UUFDOUQ7SUFDRjtJQUNBLE9BQU9pRDtBQUNUO0FBRUEsaUJBQWlCO0FBQ2pCLFNBQVNDLFFBQVEsR0FBR0MsS0FBSztJQUN2QixJQUFJQSxNQUFNNUIsTUFBTSxLQUFLLEdBQUc7UUFDdEIsT0FBTyxDQUFDNkIsTUFBUUE7SUFDbEI7SUFDQSxJQUFJRCxNQUFNNUIsTUFBTSxLQUFLLEdBQUc7UUFDdEIsT0FBTzRCLEtBQUssQ0FBQyxFQUFFO0lBQ2pCO0lBQ0EsT0FBT0EsTUFBTUUsTUFBTSxDQUFDLENBQUNDLEdBQUdDLElBQU0sQ0FBQyxHQUFHVixPQUFTUyxFQUFFQyxLQUFLVjtBQUNwRDtBQUVBLHlCQUF5QjtBQUN6QixTQUFTVyxnQkFBZ0IsR0FBR0MsV0FBVztJQUNyQyxPQUFPLENBQUNDLGVBQWlCLENBQUNoRixTQUFTQztZQUNqQyxNQUFNK0IsUUFBUWdELGFBQWFoRixTQUFTQztZQUNwQyxJQUFJcUIsV0FBVztnQkFDYixNQUFNLElBQUlqQyxNQUFNUyxNQUFxQyxHQUFHaEQsQ0FBMEIsR0FBRztZQUN2RjtZQUNBLE1BQU1tSSxnQkFBZ0I7Z0JBQ3BCakUsVUFBVWdCLE1BQU1oQixRQUFRO2dCQUN4Qk0sVUFBVSxDQUFDQyxRQUFRLEdBQUc0QyxPQUFTN0MsU0FBU0MsV0FBVzRDO1lBQ3JEO1lBQ0EsTUFBTWUsUUFBUUgsWUFBWUksR0FBRyxDQUFDLENBQUNDLGFBQWVBLFdBQVdIO1lBQ3pEM0QsV0FBV2tELFdBQVdVLE9BQU9sRCxNQUFNVixRQUFRO1lBQzNDLE9BQU87Z0JBQ0wsR0FBR1UsS0FBSztnQkFDUlY7WUFDRjtRQUNGO0FBQ0Y7QUFFQSx3QkFBd0I7QUFDeEIsU0FBUytELFNBQVM5RCxNQUFNO0lBQ3RCLE9BQU92RCxjQUFjdUQsV0FBVyxVQUFVQSxVQUFVLE9BQU9BLE9BQU9oRCxJQUFJLEtBQUs7QUFDN0U7QUFXRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL3JlZHV4L2Rpc3QvcmVkdXgubWpzP2E0MzMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3V0aWxzL2Zvcm1hdFByb2RFcnJvck1lc3NhZ2UudHNcbmZ1bmN0aW9uIGZvcm1hdFByb2RFcnJvck1lc3NhZ2UoY29kZSkge1xuICByZXR1cm4gYE1pbmlmaWVkIFJlZHV4IGVycm9yICMke2NvZGV9OyB2aXNpdCBodHRwczovL3JlZHV4LmpzLm9yZy9FcnJvcnM/Y29kZT0ke2NvZGV9IGZvciB0aGUgZnVsbCBtZXNzYWdlIG9yIHVzZSB0aGUgbm9uLW1pbmlmaWVkIGRldiBlbnZpcm9ubWVudCBmb3IgZnVsbCBlcnJvcnMuIGA7XG59XG5cbi8vIHNyYy91dGlscy9zeW1ib2wtb2JzZXJ2YWJsZS50c1xudmFyICQkb2JzZXJ2YWJsZSA9IC8qIEBfX1BVUkVfXyAqLyAoKCkgPT4gdHlwZW9mIFN5bWJvbCA9PT0gXCJmdW5jdGlvblwiICYmIFN5bWJvbC5vYnNlcnZhYmxlIHx8IFwiQEBvYnNlcnZhYmxlXCIpKCk7XG52YXIgc3ltYm9sX29ic2VydmFibGVfZGVmYXVsdCA9ICQkb2JzZXJ2YWJsZTtcblxuLy8gc3JjL3V0aWxzL2FjdGlvblR5cGVzLnRzXG52YXIgcmFuZG9tU3RyaW5nID0gKCkgPT4gTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDcpLnNwbGl0KFwiXCIpLmpvaW4oXCIuXCIpO1xudmFyIEFjdGlvblR5cGVzID0ge1xuICBJTklUOiBgQEByZWR1eC9JTklUJHsvKiBAX19QVVJFX18gKi8gcmFuZG9tU3RyaW5nKCl9YCxcbiAgUkVQTEFDRTogYEBAcmVkdXgvUkVQTEFDRSR7LyogQF9fUFVSRV9fICovIHJhbmRvbVN0cmluZygpfWAsXG4gIFBST0JFX1VOS05PV05fQUNUSU9OOiAoKSA9PiBgQEByZWR1eC9QUk9CRV9VTktOT1dOX0FDVElPTiR7cmFuZG9tU3RyaW5nKCl9YFxufTtcbnZhciBhY3Rpb25UeXBlc19kZWZhdWx0ID0gQWN0aW9uVHlwZXM7XG5cbi8vIHNyYy91dGlscy9pc1BsYWluT2JqZWN0LnRzXG5mdW5jdGlvbiBpc1BsYWluT2JqZWN0KG9iaikge1xuICBpZiAodHlwZW9mIG9iaiAhPT0gXCJvYmplY3RcIiB8fCBvYmogPT09IG51bGwpXG4gICAgcmV0dXJuIGZhbHNlO1xuICBsZXQgcHJvdG8gPSBvYmo7XG4gIHdoaWxlIChPYmplY3QuZ2V0UHJvdG90eXBlT2YocHJvdG8pICE9PSBudWxsKSB7XG4gICAgcHJvdG8gPSBPYmplY3QuZ2V0UHJvdG90eXBlT2YocHJvdG8pO1xuICB9XG4gIHJldHVybiBPYmplY3QuZ2V0UHJvdG90eXBlT2Yob2JqKSA9PT0gcHJvdG8gfHwgT2JqZWN0LmdldFByb3RvdHlwZU9mKG9iaikgPT09IG51bGw7XG59XG5cbi8vIHNyYy91dGlscy9raW5kT2YudHNcbmZ1bmN0aW9uIG1pbmlLaW5kT2YodmFsKSB7XG4gIGlmICh2YWwgPT09IHZvaWQgMClcbiAgICByZXR1cm4gXCJ1bmRlZmluZWRcIjtcbiAgaWYgKHZhbCA9PT0gbnVsbClcbiAgICByZXR1cm4gXCJudWxsXCI7XG4gIGNvbnN0IHR5cGUgPSB0eXBlb2YgdmFsO1xuICBzd2l0Y2ggKHR5cGUpIHtcbiAgICBjYXNlIFwiYm9vbGVhblwiOlxuICAgIGNhc2UgXCJzdHJpbmdcIjpcbiAgICBjYXNlIFwibnVtYmVyXCI6XG4gICAgY2FzZSBcInN5bWJvbFwiOlxuICAgIGNhc2UgXCJmdW5jdGlvblwiOiB7XG4gICAgICByZXR1cm4gdHlwZTtcbiAgICB9XG4gIH1cbiAgaWYgKEFycmF5LmlzQXJyYXkodmFsKSlcbiAgICByZXR1cm4gXCJhcnJheVwiO1xuICBpZiAoaXNEYXRlKHZhbCkpXG4gICAgcmV0dXJuIFwiZGF0ZVwiO1xuICBpZiAoaXNFcnJvcih2YWwpKVxuICAgIHJldHVybiBcImVycm9yXCI7XG4gIGNvbnN0IGNvbnN0cnVjdG9yTmFtZSA9IGN0b3JOYW1lKHZhbCk7XG4gIHN3aXRjaCAoY29uc3RydWN0b3JOYW1lKSB7XG4gICAgY2FzZSBcIlN5bWJvbFwiOlxuICAgIGNhc2UgXCJQcm9taXNlXCI6XG4gICAgY2FzZSBcIldlYWtNYXBcIjpcbiAgICBjYXNlIFwiV2Vha1NldFwiOlxuICAgIGNhc2UgXCJNYXBcIjpcbiAgICBjYXNlIFwiU2V0XCI6XG4gICAgICByZXR1cm4gY29uc3RydWN0b3JOYW1lO1xuICB9XG4gIHJldHVybiBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwodmFsKS5zbGljZSg4LCAtMSkudG9Mb3dlckNhc2UoKS5yZXBsYWNlKC9cXHMvZywgXCJcIik7XG59XG5mdW5jdGlvbiBjdG9yTmFtZSh2YWwpIHtcbiAgcmV0dXJuIHR5cGVvZiB2YWwuY29uc3RydWN0b3IgPT09IFwiZnVuY3Rpb25cIiA/IHZhbC5jb25zdHJ1Y3Rvci5uYW1lIDogbnVsbDtcbn1cbmZ1bmN0aW9uIGlzRXJyb3IodmFsKSB7XG4gIHJldHVybiB2YWwgaW5zdGFuY2VvZiBFcnJvciB8fCB0eXBlb2YgdmFsLm1lc3NhZ2UgPT09IFwic3RyaW5nXCIgJiYgdmFsLmNvbnN0cnVjdG9yICYmIHR5cGVvZiB2YWwuY29uc3RydWN0b3Iuc3RhY2tUcmFjZUxpbWl0ID09PSBcIm51bWJlclwiO1xufVxuZnVuY3Rpb24gaXNEYXRlKHZhbCkge1xuICBpZiAodmFsIGluc3RhbmNlb2YgRGF0ZSlcbiAgICByZXR1cm4gdHJ1ZTtcbiAgcmV0dXJuIHR5cGVvZiB2YWwudG9EYXRlU3RyaW5nID09PSBcImZ1bmN0aW9uXCIgJiYgdHlwZW9mIHZhbC5nZXREYXRlID09PSBcImZ1bmN0aW9uXCIgJiYgdHlwZW9mIHZhbC5zZXREYXRlID09PSBcImZ1bmN0aW9uXCI7XG59XG5mdW5jdGlvbiBraW5kT2YodmFsKSB7XG4gIGxldCB0eXBlT2ZWYWwgPSB0eXBlb2YgdmFsO1xuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgdHlwZU9mVmFsID0gbWluaUtpbmRPZih2YWwpO1xuICB9XG4gIHJldHVybiB0eXBlT2ZWYWw7XG59XG5cbi8vIHNyYy9jcmVhdGVTdG9yZS50c1xuZnVuY3Rpb24gY3JlYXRlU3RvcmUocmVkdWNlciwgcHJlbG9hZGVkU3RhdGUsIGVuaGFuY2VyKSB7XG4gIGlmICh0eXBlb2YgcmVkdWNlciAhPT0gXCJmdW5jdGlvblwiKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIiA/IGZvcm1hdFByb2RFcnJvck1lc3NhZ2UoMikgOiBgRXhwZWN0ZWQgdGhlIHJvb3QgcmVkdWNlciB0byBiZSBhIGZ1bmN0aW9uLiBJbnN0ZWFkLCByZWNlaXZlZDogJyR7a2luZE9mKHJlZHVjZXIpfSdgKTtcbiAgfVxuICBpZiAodHlwZW9mIHByZWxvYWRlZFN0YXRlID09PSBcImZ1bmN0aW9uXCIgJiYgdHlwZW9mIGVuaGFuY2VyID09PSBcImZ1bmN0aW9uXCIgfHwgdHlwZW9mIGVuaGFuY2VyID09PSBcImZ1bmN0aW9uXCIgJiYgdHlwZW9mIGFyZ3VtZW50c1szXSA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIiA/IGZvcm1hdFByb2RFcnJvck1lc3NhZ2UoMCkgOiBcIkl0IGxvb2tzIGxpa2UgeW91IGFyZSBwYXNzaW5nIHNldmVyYWwgc3RvcmUgZW5oYW5jZXJzIHRvIGNyZWF0ZVN0b3JlKCkuIFRoaXMgaXMgbm90IHN1cHBvcnRlZC4gSW5zdGVhZCwgY29tcG9zZSB0aGVtIHRvZ2V0aGVyIHRvIGEgc2luZ2xlIGZ1bmN0aW9uLiBTZWUgaHR0cHM6Ly9yZWR1eC5qcy5vcmcvdHV0b3JpYWxzL2Z1bmRhbWVudGFscy9wYXJ0LTQtc3RvcmUjY3JlYXRpbmctYS1zdG9yZS13aXRoLWVuaGFuY2VycyBmb3IgYW4gZXhhbXBsZS5cIik7XG4gIH1cbiAgaWYgKHR5cGVvZiBwcmVsb2FkZWRTdGF0ZSA9PT0gXCJmdW5jdGlvblwiICYmIHR5cGVvZiBlbmhhbmNlciA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgIGVuaGFuY2VyID0gcHJlbG9hZGVkU3RhdGU7XG4gICAgcHJlbG9hZGVkU3RhdGUgPSB2b2lkIDA7XG4gIH1cbiAgaWYgKHR5cGVvZiBlbmhhbmNlciAhPT0gXCJ1bmRlZmluZWRcIikge1xuICAgIGlmICh0eXBlb2YgZW5oYW5jZXIgIT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIiA/IGZvcm1hdFByb2RFcnJvck1lc3NhZ2UoMSkgOiBgRXhwZWN0ZWQgdGhlIGVuaGFuY2VyIHRvIGJlIGEgZnVuY3Rpb24uIEluc3RlYWQsIHJlY2VpdmVkOiAnJHtraW5kT2YoZW5oYW5jZXIpfSdgKTtcbiAgICB9XG4gICAgcmV0dXJuIGVuaGFuY2VyKGNyZWF0ZVN0b3JlKShyZWR1Y2VyLCBwcmVsb2FkZWRTdGF0ZSk7XG4gIH1cbiAgbGV0IGN1cnJlbnRSZWR1Y2VyID0gcmVkdWNlcjtcbiAgbGV0IGN1cnJlbnRTdGF0ZSA9IHByZWxvYWRlZFN0YXRlO1xuICBsZXQgY3VycmVudExpc3RlbmVycyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKCk7XG4gIGxldCBuZXh0TGlzdGVuZXJzID0gY3VycmVudExpc3RlbmVycztcbiAgbGV0IGxpc3RlbmVySWRDb3VudGVyID0gMDtcbiAgbGV0IGlzRGlzcGF0Y2hpbmcgPSBmYWxzZTtcbiAgZnVuY3Rpb24gZW5zdXJlQ2FuTXV0YXRlTmV4dExpc3RlbmVycygpIHtcbiAgICBpZiAobmV4dExpc3RlbmVycyA9PT0gY3VycmVudExpc3RlbmVycykge1xuICAgICAgbmV4dExpc3RlbmVycyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKCk7XG4gICAgICBjdXJyZW50TGlzdGVuZXJzLmZvckVhY2goKGxpc3RlbmVyLCBrZXkpID0+IHtcbiAgICAgICAgbmV4dExpc3RlbmVycy5zZXQoa2V5LCBsaXN0ZW5lcik7XG4gICAgICB9KTtcbiAgICB9XG4gIH1cbiAgZnVuY3Rpb24gZ2V0U3RhdGUoKSB7XG4gICAgaWYgKGlzRGlzcGF0Y2hpbmcpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIgPyBmb3JtYXRQcm9kRXJyb3JNZXNzYWdlKDMpIDogXCJZb3UgbWF5IG5vdCBjYWxsIHN0b3JlLmdldFN0YXRlKCkgd2hpbGUgdGhlIHJlZHVjZXIgaXMgZXhlY3V0aW5nLiBUaGUgcmVkdWNlciBoYXMgYWxyZWFkeSByZWNlaXZlZCB0aGUgc3RhdGUgYXMgYW4gYXJndW1lbnQuIFBhc3MgaXQgZG93biBmcm9tIHRoZSB0b3AgcmVkdWNlciBpbnN0ZWFkIG9mIHJlYWRpbmcgaXQgZnJvbSB0aGUgc3RvcmUuXCIpO1xuICAgIH1cbiAgICByZXR1cm4gY3VycmVudFN0YXRlO1xuICB9XG4gIGZ1bmN0aW9uIHN1YnNjcmliZShsaXN0ZW5lcikge1xuICAgIGlmICh0eXBlb2YgbGlzdGVuZXIgIT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIiA/IGZvcm1hdFByb2RFcnJvck1lc3NhZ2UoNCkgOiBgRXhwZWN0ZWQgdGhlIGxpc3RlbmVyIHRvIGJlIGEgZnVuY3Rpb24uIEluc3RlYWQsIHJlY2VpdmVkOiAnJHtraW5kT2YobGlzdGVuZXIpfSdgKTtcbiAgICB9XG4gICAgaWYgKGlzRGlzcGF0Y2hpbmcpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIgPyBmb3JtYXRQcm9kRXJyb3JNZXNzYWdlKDUpIDogXCJZb3UgbWF5IG5vdCBjYWxsIHN0b3JlLnN1YnNjcmliZSgpIHdoaWxlIHRoZSByZWR1Y2VyIGlzIGV4ZWN1dGluZy4gSWYgeW91IHdvdWxkIGxpa2UgdG8gYmUgbm90aWZpZWQgYWZ0ZXIgdGhlIHN0b3JlIGhhcyBiZWVuIHVwZGF0ZWQsIHN1YnNjcmliZSBmcm9tIGEgY29tcG9uZW50IGFuZCBpbnZva2Ugc3RvcmUuZ2V0U3RhdGUoKSBpbiB0aGUgY2FsbGJhY2sgdG8gYWNjZXNzIHRoZSBsYXRlc3Qgc3RhdGUuIFNlZSBodHRwczovL3JlZHV4LmpzLm9yZy9hcGkvc3RvcmUjc3Vic2NyaWJlbGlzdGVuZXIgZm9yIG1vcmUgZGV0YWlscy5cIik7XG4gICAgfVxuICAgIGxldCBpc1N1YnNjcmliZWQgPSB0cnVlO1xuICAgIGVuc3VyZUNhbk11dGF0ZU5leHRMaXN0ZW5lcnMoKTtcbiAgICBjb25zdCBsaXN0ZW5lcklkID0gbGlzdGVuZXJJZENvdW50ZXIrKztcbiAgICBuZXh0TGlzdGVuZXJzLnNldChsaXN0ZW5lcklkLCBsaXN0ZW5lcik7XG4gICAgcmV0dXJuIGZ1bmN0aW9uIHVuc3Vic2NyaWJlKCkge1xuICAgICAgaWYgKCFpc1N1YnNjcmliZWQpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgaWYgKGlzRGlzcGF0Y2hpbmcpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIiA/IGZvcm1hdFByb2RFcnJvck1lc3NhZ2UoNikgOiBcIllvdSBtYXkgbm90IHVuc3Vic2NyaWJlIGZyb20gYSBzdG9yZSBsaXN0ZW5lciB3aGlsZSB0aGUgcmVkdWNlciBpcyBleGVjdXRpbmcuIFNlZSBodHRwczovL3JlZHV4LmpzLm9yZy9hcGkvc3RvcmUjc3Vic2NyaWJlbGlzdGVuZXIgZm9yIG1vcmUgZGV0YWlscy5cIik7XG4gICAgICB9XG4gICAgICBpc1N1YnNjcmliZWQgPSBmYWxzZTtcbiAgICAgIGVuc3VyZUNhbk11dGF0ZU5leHRMaXN0ZW5lcnMoKTtcbiAgICAgIG5leHRMaXN0ZW5lcnMuZGVsZXRlKGxpc3RlbmVySWQpO1xuICAgICAgY3VycmVudExpc3RlbmVycyA9IG51bGw7XG4gICAgfTtcbiAgfVxuICBmdW5jdGlvbiBkaXNwYXRjaChhY3Rpb24pIHtcbiAgICBpZiAoIWlzUGxhaW5PYmplY3QoYWN0aW9uKSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIiA/IGZvcm1hdFByb2RFcnJvck1lc3NhZ2UoNykgOiBgQWN0aW9ucyBtdXN0IGJlIHBsYWluIG9iamVjdHMuIEluc3RlYWQsIHRoZSBhY3R1YWwgdHlwZSB3YXM6ICcke2tpbmRPZihhY3Rpb24pfScuIFlvdSBtYXkgbmVlZCB0byBhZGQgbWlkZGxld2FyZSB0byB5b3VyIHN0b3JlIHNldHVwIHRvIGhhbmRsZSBkaXNwYXRjaGluZyBvdGhlciB2YWx1ZXMsIHN1Y2ggYXMgJ3JlZHV4LXRodW5rJyB0byBoYW5kbGUgZGlzcGF0Y2hpbmcgZnVuY3Rpb25zLiBTZWUgaHR0cHM6Ly9yZWR1eC5qcy5vcmcvdHV0b3JpYWxzL2Z1bmRhbWVudGFscy9wYXJ0LTQtc3RvcmUjbWlkZGxld2FyZSBhbmQgaHR0cHM6Ly9yZWR1eC5qcy5vcmcvdHV0b3JpYWxzL2Z1bmRhbWVudGFscy9wYXJ0LTYtYXN5bmMtbG9naWMjdXNpbmctdGhlLXJlZHV4LXRodW5rLW1pZGRsZXdhcmUgZm9yIGV4YW1wbGVzLmApO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIGFjdGlvbi50eXBlID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiID8gZm9ybWF0UHJvZEVycm9yTWVzc2FnZSg4KSA6ICdBY3Rpb25zIG1heSBub3QgaGF2ZSBhbiB1bmRlZmluZWQgXCJ0eXBlXCIgcHJvcGVydHkuIFlvdSBtYXkgaGF2ZSBtaXNzcGVsbGVkIGFuIGFjdGlvbiB0eXBlIHN0cmluZyBjb25zdGFudC4nKTtcbiAgICB9XG4gICAgaWYgKHR5cGVvZiBhY3Rpb24udHlwZSAhPT0gXCJzdHJpbmdcIikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIiA/IGZvcm1hdFByb2RFcnJvck1lc3NhZ2UoMTcpIDogYEFjdGlvbiBcInR5cGVcIiBwcm9wZXJ0eSBtdXN0IGJlIGEgc3RyaW5nLiBJbnN0ZWFkLCB0aGUgYWN0dWFsIHR5cGUgd2FzOiAnJHtraW5kT2YoYWN0aW9uLnR5cGUpfScuIFZhbHVlIHdhczogJyR7YWN0aW9uLnR5cGV9JyAoc3RyaW5naWZpZWQpYCk7XG4gICAgfVxuICAgIGlmIChpc0Rpc3BhdGNoaW5nKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiID8gZm9ybWF0UHJvZEVycm9yTWVzc2FnZSg5KSA6IFwiUmVkdWNlcnMgbWF5IG5vdCBkaXNwYXRjaCBhY3Rpb25zLlwiKTtcbiAgICB9XG4gICAgdHJ5IHtcbiAgICAgIGlzRGlzcGF0Y2hpbmcgPSB0cnVlO1xuICAgICAgY3VycmVudFN0YXRlID0gY3VycmVudFJlZHVjZXIoY3VycmVudFN0YXRlLCBhY3Rpb24pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBpc0Rpc3BhdGNoaW5nID0gZmFsc2U7XG4gICAgfVxuICAgIGNvbnN0IGxpc3RlbmVycyA9IGN1cnJlbnRMaXN0ZW5lcnMgPSBuZXh0TGlzdGVuZXJzO1xuICAgIGxpc3RlbmVycy5mb3JFYWNoKChsaXN0ZW5lcikgPT4ge1xuICAgICAgbGlzdGVuZXIoKTtcbiAgICB9KTtcbiAgICByZXR1cm4gYWN0aW9uO1xuICB9XG4gIGZ1bmN0aW9uIHJlcGxhY2VSZWR1Y2VyKG5leHRSZWR1Y2VyKSB7XG4gICAgaWYgKHR5cGVvZiBuZXh0UmVkdWNlciAhPT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiID8gZm9ybWF0UHJvZEVycm9yTWVzc2FnZSgxMCkgOiBgRXhwZWN0ZWQgdGhlIG5leHRSZWR1Y2VyIHRvIGJlIGEgZnVuY3Rpb24uIEluc3RlYWQsIHJlY2VpdmVkOiAnJHtraW5kT2YobmV4dFJlZHVjZXIpfWApO1xuICAgIH1cbiAgICBjdXJyZW50UmVkdWNlciA9IG5leHRSZWR1Y2VyO1xuICAgIGRpc3BhdGNoKHtcbiAgICAgIHR5cGU6IGFjdGlvblR5cGVzX2RlZmF1bHQuUkVQTEFDRVxuICAgIH0pO1xuICB9XG4gIGZ1bmN0aW9uIG9ic2VydmFibGUoKSB7XG4gICAgY29uc3Qgb3V0ZXJTdWJzY3JpYmUgPSBzdWJzY3JpYmU7XG4gICAgcmV0dXJuIHtcbiAgICAgIC8qKlxuICAgICAgICogVGhlIG1pbmltYWwgb2JzZXJ2YWJsZSBzdWJzY3JpcHRpb24gbWV0aG9kLlxuICAgICAgICogQHBhcmFtIG9ic2VydmVyIEFueSBvYmplY3QgdGhhdCBjYW4gYmUgdXNlZCBhcyBhbiBvYnNlcnZlci5cbiAgICAgICAqIFRoZSBvYnNlcnZlciBvYmplY3Qgc2hvdWxkIGhhdmUgYSBgbmV4dGAgbWV0aG9kLlxuICAgICAgICogQHJldHVybnMgQW4gb2JqZWN0IHdpdGggYW4gYHVuc3Vic2NyaWJlYCBtZXRob2QgdGhhdCBjYW5cbiAgICAgICAqIGJlIHVzZWQgdG8gdW5zdWJzY3JpYmUgdGhlIG9ic2VydmFibGUgZnJvbSB0aGUgc3RvcmUsIGFuZCBwcmV2ZW50IGZ1cnRoZXJcbiAgICAgICAqIGVtaXNzaW9uIG9mIHZhbHVlcyBmcm9tIHRoZSBvYnNlcnZhYmxlLlxuICAgICAgICovXG4gICAgICBzdWJzY3JpYmUob2JzZXJ2ZXIpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBvYnNlcnZlciAhPT0gXCJvYmplY3RcIiB8fCBvYnNlcnZlciA9PT0gbnVsbCkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIgPyBmb3JtYXRQcm9kRXJyb3JNZXNzYWdlKDExKSA6IGBFeHBlY3RlZCB0aGUgb2JzZXJ2ZXIgdG8gYmUgYW4gb2JqZWN0LiBJbnN0ZWFkLCByZWNlaXZlZDogJyR7a2luZE9mKG9ic2VydmVyKX0nYCk7XG4gICAgICAgIH1cbiAgICAgICAgZnVuY3Rpb24gb2JzZXJ2ZVN0YXRlKCkge1xuICAgICAgICAgIGNvbnN0IG9ic2VydmVyQXNPYnNlcnZlciA9IG9ic2VydmVyO1xuICAgICAgICAgIGlmIChvYnNlcnZlckFzT2JzZXJ2ZXIubmV4dCkge1xuICAgICAgICAgICAgb2JzZXJ2ZXJBc09ic2VydmVyLm5leHQoZ2V0U3RhdGUoKSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIG9ic2VydmVTdGF0ZSgpO1xuICAgICAgICBjb25zdCB1bnN1YnNjcmliZSA9IG91dGVyU3Vic2NyaWJlKG9ic2VydmVTdGF0ZSk7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgdW5zdWJzY3JpYmVcbiAgICAgICAgfTtcbiAgICAgIH0sXG4gICAgICBbc3ltYm9sX29ic2VydmFibGVfZGVmYXVsdF0oKSB7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgICAgfVxuICAgIH07XG4gIH1cbiAgZGlzcGF0Y2goe1xuICAgIHR5cGU6IGFjdGlvblR5cGVzX2RlZmF1bHQuSU5JVFxuICB9KTtcbiAgY29uc3Qgc3RvcmUgPSB7XG4gICAgZGlzcGF0Y2gsXG4gICAgc3Vic2NyaWJlLFxuICAgIGdldFN0YXRlLFxuICAgIHJlcGxhY2VSZWR1Y2VyLFxuICAgIFtzeW1ib2xfb2JzZXJ2YWJsZV9kZWZhdWx0XTogb2JzZXJ2YWJsZVxuICB9O1xuICByZXR1cm4gc3RvcmU7XG59XG5mdW5jdGlvbiBsZWdhY3lfY3JlYXRlU3RvcmUocmVkdWNlciwgcHJlbG9hZGVkU3RhdGUsIGVuaGFuY2VyKSB7XG4gIHJldHVybiBjcmVhdGVTdG9yZShyZWR1Y2VyLCBwcmVsb2FkZWRTdGF0ZSwgZW5oYW5jZXIpO1xufVxuXG4vLyBzcmMvdXRpbHMvd2FybmluZy50c1xuZnVuY3Rpb24gd2FybmluZyhtZXNzYWdlKSB7XG4gIGlmICh0eXBlb2YgY29uc29sZSAhPT0gXCJ1bmRlZmluZWRcIiAmJiB0eXBlb2YgY29uc29sZS5lcnJvciA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgY29uc29sZS5lcnJvcihtZXNzYWdlKTtcbiAgfVxuICB0cnkge1xuICAgIHRocm93IG5ldyBFcnJvcihtZXNzYWdlKTtcbiAgfSBjYXRjaCAoZSkge1xuICB9XG59XG5cbi8vIHNyYy9jb21iaW5lUmVkdWNlcnMudHNcbmZ1bmN0aW9uIGdldFVuZXhwZWN0ZWRTdGF0ZVNoYXBlV2FybmluZ01lc3NhZ2UoaW5wdXRTdGF0ZSwgcmVkdWNlcnMsIGFjdGlvbiwgdW5leHBlY3RlZEtleUNhY2hlKSB7XG4gIGNvbnN0IHJlZHVjZXJLZXlzID0gT2JqZWN0LmtleXMocmVkdWNlcnMpO1xuICBjb25zdCBhcmd1bWVudE5hbWUgPSBhY3Rpb24gJiYgYWN0aW9uLnR5cGUgPT09IGFjdGlvblR5cGVzX2RlZmF1bHQuSU5JVCA/IFwicHJlbG9hZGVkU3RhdGUgYXJndW1lbnQgcGFzc2VkIHRvIGNyZWF0ZVN0b3JlXCIgOiBcInByZXZpb3VzIHN0YXRlIHJlY2VpdmVkIGJ5IHRoZSByZWR1Y2VyXCI7XG4gIGlmIChyZWR1Y2VyS2V5cy5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4gXCJTdG9yZSBkb2VzIG5vdCBoYXZlIGEgdmFsaWQgcmVkdWNlci4gTWFrZSBzdXJlIHRoZSBhcmd1bWVudCBwYXNzZWQgdG8gY29tYmluZVJlZHVjZXJzIGlzIGFuIG9iamVjdCB3aG9zZSB2YWx1ZXMgYXJlIHJlZHVjZXJzLlwiO1xuICB9XG4gIGlmICghaXNQbGFpbk9iamVjdChpbnB1dFN0YXRlKSkge1xuICAgIHJldHVybiBgVGhlICR7YXJndW1lbnROYW1lfSBoYXMgdW5leHBlY3RlZCB0eXBlIG9mIFwiJHtraW5kT2YoaW5wdXRTdGF0ZSl9XCIuIEV4cGVjdGVkIGFyZ3VtZW50IHRvIGJlIGFuIG9iamVjdCB3aXRoIHRoZSBmb2xsb3dpbmcga2V5czogXCIke3JlZHVjZXJLZXlzLmpvaW4oJ1wiLCBcIicpfVwiYDtcbiAgfVxuICBjb25zdCB1bmV4cGVjdGVkS2V5cyA9IE9iamVjdC5rZXlzKGlucHV0U3RhdGUpLmZpbHRlcigoa2V5KSA9PiAhcmVkdWNlcnMuaGFzT3duUHJvcGVydHkoa2V5KSAmJiAhdW5leHBlY3RlZEtleUNhY2hlW2tleV0pO1xuICB1bmV4cGVjdGVkS2V5cy5mb3JFYWNoKChrZXkpID0+IHtcbiAgICB1bmV4cGVjdGVkS2V5Q2FjaGVba2V5XSA9IHRydWU7XG4gIH0pO1xuICBpZiAoYWN0aW9uICYmIGFjdGlvbi50eXBlID09PSBhY3Rpb25UeXBlc19kZWZhdWx0LlJFUExBQ0UpXG4gICAgcmV0dXJuO1xuICBpZiAodW5leHBlY3RlZEtleXMubGVuZ3RoID4gMCkge1xuICAgIHJldHVybiBgVW5leHBlY3RlZCAke3VuZXhwZWN0ZWRLZXlzLmxlbmd0aCA+IDEgPyBcImtleXNcIiA6IFwia2V5XCJ9IFwiJHt1bmV4cGVjdGVkS2V5cy5qb2luKCdcIiwgXCInKX1cIiBmb3VuZCBpbiAke2FyZ3VtZW50TmFtZX0uIEV4cGVjdGVkIHRvIGZpbmQgb25lIG9mIHRoZSBrbm93biByZWR1Y2VyIGtleXMgaW5zdGVhZDogXCIke3JlZHVjZXJLZXlzLmpvaW4oJ1wiLCBcIicpfVwiLiBVbmV4cGVjdGVkIGtleXMgd2lsbCBiZSBpZ25vcmVkLmA7XG4gIH1cbn1cbmZ1bmN0aW9uIGFzc2VydFJlZHVjZXJTaGFwZShyZWR1Y2Vycykge1xuICBPYmplY3Qua2V5cyhyZWR1Y2VycykuZm9yRWFjaCgoa2V5KSA9PiB7XG4gICAgY29uc3QgcmVkdWNlciA9IHJlZHVjZXJzW2tleV07XG4gICAgY29uc3QgaW5pdGlhbFN0YXRlID0gcmVkdWNlcih2b2lkIDAsIHtcbiAgICAgIHR5cGU6IGFjdGlvblR5cGVzX2RlZmF1bHQuSU5JVFxuICAgIH0pO1xuICAgIGlmICh0eXBlb2YgaW5pdGlhbFN0YXRlID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiID8gZm9ybWF0UHJvZEVycm9yTWVzc2FnZSgxMikgOiBgVGhlIHNsaWNlIHJlZHVjZXIgZm9yIGtleSBcIiR7a2V5fVwiIHJldHVybmVkIHVuZGVmaW5lZCBkdXJpbmcgaW5pdGlhbGl6YXRpb24uIElmIHRoZSBzdGF0ZSBwYXNzZWQgdG8gdGhlIHJlZHVjZXIgaXMgdW5kZWZpbmVkLCB5b3UgbXVzdCBleHBsaWNpdGx5IHJldHVybiB0aGUgaW5pdGlhbCBzdGF0ZS4gVGhlIGluaXRpYWwgc3RhdGUgbWF5IG5vdCBiZSB1bmRlZmluZWQuIElmIHlvdSBkb24ndCB3YW50IHRvIHNldCBhIHZhbHVlIGZvciB0aGlzIHJlZHVjZXIsIHlvdSBjYW4gdXNlIG51bGwgaW5zdGVhZCBvZiB1bmRlZmluZWQuYCk7XG4gICAgfVxuICAgIGlmICh0eXBlb2YgcmVkdWNlcih2b2lkIDAsIHtcbiAgICAgIHR5cGU6IGFjdGlvblR5cGVzX2RlZmF1bHQuUFJPQkVfVU5LTk9XTl9BQ1RJT04oKVxuICAgIH0pID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiID8gZm9ybWF0UHJvZEVycm9yTWVzc2FnZSgxMykgOiBgVGhlIHNsaWNlIHJlZHVjZXIgZm9yIGtleSBcIiR7a2V5fVwiIHJldHVybmVkIHVuZGVmaW5lZCB3aGVuIHByb2JlZCB3aXRoIGEgcmFuZG9tIHR5cGUuIERvbid0IHRyeSB0byBoYW5kbGUgJyR7YWN0aW9uVHlwZXNfZGVmYXVsdC5JTklUfScgb3Igb3RoZXIgYWN0aW9ucyBpbiBcInJlZHV4LypcIiBuYW1lc3BhY2UuIFRoZXkgYXJlIGNvbnNpZGVyZWQgcHJpdmF0ZS4gSW5zdGVhZCwgeW91IG11c3QgcmV0dXJuIHRoZSBjdXJyZW50IHN0YXRlIGZvciBhbnkgdW5rbm93biBhY3Rpb25zLCB1bmxlc3MgaXQgaXMgdW5kZWZpbmVkLCBpbiB3aGljaCBjYXNlIHlvdSBtdXN0IHJldHVybiB0aGUgaW5pdGlhbCBzdGF0ZSwgcmVnYXJkbGVzcyBvZiB0aGUgYWN0aW9uIHR5cGUuIFRoZSBpbml0aWFsIHN0YXRlIG1heSBub3QgYmUgdW5kZWZpbmVkLCBidXQgY2FuIGJlIG51bGwuYCk7XG4gICAgfVxuICB9KTtcbn1cbmZ1bmN0aW9uIGNvbWJpbmVSZWR1Y2VycyhyZWR1Y2Vycykge1xuICBjb25zdCByZWR1Y2VyS2V5cyA9IE9iamVjdC5rZXlzKHJlZHVjZXJzKTtcbiAgY29uc3QgZmluYWxSZWR1Y2VycyA9IHt9O1xuICBmb3IgKGxldCBpID0gMDsgaSA8IHJlZHVjZXJLZXlzLmxlbmd0aDsgaSsrKSB7XG4gICAgY29uc3Qga2V5ID0gcmVkdWNlcktleXNbaV07XG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgICAgaWYgKHR5cGVvZiByZWR1Y2Vyc1trZXldID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgIHdhcm5pbmcoYE5vIHJlZHVjZXIgcHJvdmlkZWQgZm9yIGtleSBcIiR7a2V5fVwiYCk7XG4gICAgICB9XG4gICAgfVxuICAgIGlmICh0eXBlb2YgcmVkdWNlcnNba2V5XSA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICBmaW5hbFJlZHVjZXJzW2tleV0gPSByZWR1Y2Vyc1trZXldO1xuICAgIH1cbiAgfVxuICBjb25zdCBmaW5hbFJlZHVjZXJLZXlzID0gT2JqZWN0LmtleXMoZmluYWxSZWR1Y2Vycyk7XG4gIGxldCB1bmV4cGVjdGVkS2V5Q2FjaGU7XG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICB1bmV4cGVjdGVkS2V5Q2FjaGUgPSB7fTtcbiAgfVxuICBsZXQgc2hhcGVBc3NlcnRpb25FcnJvcjtcbiAgdHJ5IHtcbiAgICBhc3NlcnRSZWR1Y2VyU2hhcGUoZmluYWxSZWR1Y2Vycyk7XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICBzaGFwZUFzc2VydGlvbkVycm9yID0gZTtcbiAgfVxuICByZXR1cm4gZnVuY3Rpb24gY29tYmluYXRpb24oc3RhdGUgPSB7fSwgYWN0aW9uKSB7XG4gICAgaWYgKHNoYXBlQXNzZXJ0aW9uRXJyb3IpIHtcbiAgICAgIHRocm93IHNoYXBlQXNzZXJ0aW9uRXJyb3I7XG4gICAgfVxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICAgIGNvbnN0IHdhcm5pbmdNZXNzYWdlID0gZ2V0VW5leHBlY3RlZFN0YXRlU2hhcGVXYXJuaW5nTWVzc2FnZShzdGF0ZSwgZmluYWxSZWR1Y2VycywgYWN0aW9uLCB1bmV4cGVjdGVkS2V5Q2FjaGUpO1xuICAgICAgaWYgKHdhcm5pbmdNZXNzYWdlKSB7XG4gICAgICAgIHdhcm5pbmcod2FybmluZ01lc3NhZ2UpO1xuICAgICAgfVxuICAgIH1cbiAgICBsZXQgaGFzQ2hhbmdlZCA9IGZhbHNlO1xuICAgIGNvbnN0IG5leHRTdGF0ZSA9IHt9O1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZmluYWxSZWR1Y2VyS2V5cy5sZW5ndGg7IGkrKykge1xuICAgICAgY29uc3Qga2V5ID0gZmluYWxSZWR1Y2VyS2V5c1tpXTtcbiAgICAgIGNvbnN0IHJlZHVjZXIgPSBmaW5hbFJlZHVjZXJzW2tleV07XG4gICAgICBjb25zdCBwcmV2aW91c1N0YXRlRm9yS2V5ID0gc3RhdGVba2V5XTtcbiAgICAgIGNvbnN0IG5leHRTdGF0ZUZvcktleSA9IHJlZHVjZXIocHJldmlvdXNTdGF0ZUZvcktleSwgYWN0aW9uKTtcbiAgICAgIGlmICh0eXBlb2YgbmV4dFN0YXRlRm9yS2V5ID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgIGNvbnN0IGFjdGlvblR5cGUgPSBhY3Rpb24gJiYgYWN0aW9uLnR5cGU7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIgPyBmb3JtYXRQcm9kRXJyb3JNZXNzYWdlKDE0KSA6IGBXaGVuIGNhbGxlZCB3aXRoIGFuIGFjdGlvbiBvZiB0eXBlICR7YWN0aW9uVHlwZSA/IGBcIiR7U3RyaW5nKGFjdGlvblR5cGUpfVwiYCA6IFwiKHVua25vd24gdHlwZSlcIn0sIHRoZSBzbGljZSByZWR1Y2VyIGZvciBrZXkgXCIke2tleX1cIiByZXR1cm5lZCB1bmRlZmluZWQuIFRvIGlnbm9yZSBhbiBhY3Rpb24sIHlvdSBtdXN0IGV4cGxpY2l0bHkgcmV0dXJuIHRoZSBwcmV2aW91cyBzdGF0ZS4gSWYgeW91IHdhbnQgdGhpcyByZWR1Y2VyIHRvIGhvbGQgbm8gdmFsdWUsIHlvdSBjYW4gcmV0dXJuIG51bGwgaW5zdGVhZCBvZiB1bmRlZmluZWQuYCk7XG4gICAgICB9XG4gICAgICBuZXh0U3RhdGVba2V5XSA9IG5leHRTdGF0ZUZvcktleTtcbiAgICAgIGhhc0NoYW5nZWQgPSBoYXNDaGFuZ2VkIHx8IG5leHRTdGF0ZUZvcktleSAhPT0gcHJldmlvdXNTdGF0ZUZvcktleTtcbiAgICB9XG4gICAgaGFzQ2hhbmdlZCA9IGhhc0NoYW5nZWQgfHwgZmluYWxSZWR1Y2VyS2V5cy5sZW5ndGggIT09IE9iamVjdC5rZXlzKHN0YXRlKS5sZW5ndGg7XG4gICAgcmV0dXJuIGhhc0NoYW5nZWQgPyBuZXh0U3RhdGUgOiBzdGF0ZTtcbiAgfTtcbn1cblxuLy8gc3JjL2JpbmRBY3Rpb25DcmVhdG9ycy50c1xuZnVuY3Rpb24gYmluZEFjdGlvbkNyZWF0b3IoYWN0aW9uQ3JlYXRvciwgZGlzcGF0Y2gpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKC4uLmFyZ3MpIHtcbiAgICByZXR1cm4gZGlzcGF0Y2goYWN0aW9uQ3JlYXRvci5hcHBseSh0aGlzLCBhcmdzKSk7XG4gIH07XG59XG5mdW5jdGlvbiBiaW5kQWN0aW9uQ3JlYXRvcnMoYWN0aW9uQ3JlYXRvcnMsIGRpc3BhdGNoKSB7XG4gIGlmICh0eXBlb2YgYWN0aW9uQ3JlYXRvcnMgPT09IFwiZnVuY3Rpb25cIikge1xuICAgIHJldHVybiBiaW5kQWN0aW9uQ3JlYXRvcihhY3Rpb25DcmVhdG9ycywgZGlzcGF0Y2gpO1xuICB9XG4gIGlmICh0eXBlb2YgYWN0aW9uQ3JlYXRvcnMgIT09IFwib2JqZWN0XCIgfHwgYWN0aW9uQ3JlYXRvcnMgPT09IG51bGwpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiID8gZm9ybWF0UHJvZEVycm9yTWVzc2FnZSgxNikgOiBgYmluZEFjdGlvbkNyZWF0b3JzIGV4cGVjdGVkIGFuIG9iamVjdCBvciBhIGZ1bmN0aW9uLCBidXQgaW5zdGVhZCByZWNlaXZlZDogJyR7a2luZE9mKGFjdGlvbkNyZWF0b3JzKX0nLiBEaWQgeW91IHdyaXRlIFwiaW1wb3J0IEFjdGlvbkNyZWF0b3JzIGZyb21cIiBpbnN0ZWFkIG9mIFwiaW1wb3J0ICogYXMgQWN0aW9uQ3JlYXRvcnMgZnJvbVwiP2ApO1xuICB9XG4gIGNvbnN0IGJvdW5kQWN0aW9uQ3JlYXRvcnMgPSB7fTtcbiAgZm9yIChjb25zdCBrZXkgaW4gYWN0aW9uQ3JlYXRvcnMpIHtcbiAgICBjb25zdCBhY3Rpb25DcmVhdG9yID0gYWN0aW9uQ3JlYXRvcnNba2V5XTtcbiAgICBpZiAodHlwZW9mIGFjdGlvbkNyZWF0b3IgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgYm91bmRBY3Rpb25DcmVhdG9yc1trZXldID0gYmluZEFjdGlvbkNyZWF0b3IoYWN0aW9uQ3JlYXRvciwgZGlzcGF0Y2gpO1xuICAgIH1cbiAgfVxuICByZXR1cm4gYm91bmRBY3Rpb25DcmVhdG9ycztcbn1cblxuLy8gc3JjL2NvbXBvc2UudHNcbmZ1bmN0aW9uIGNvbXBvc2UoLi4uZnVuY3MpIHtcbiAgaWYgKGZ1bmNzLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiAoYXJnKSA9PiBhcmc7XG4gIH1cbiAgaWYgKGZ1bmNzLmxlbmd0aCA9PT0gMSkge1xuICAgIHJldHVybiBmdW5jc1swXTtcbiAgfVxuICByZXR1cm4gZnVuY3MucmVkdWNlKChhLCBiKSA9PiAoLi4uYXJncykgPT4gYShiKC4uLmFyZ3MpKSk7XG59XG5cbi8vIHNyYy9hcHBseU1pZGRsZXdhcmUudHNcbmZ1bmN0aW9uIGFwcGx5TWlkZGxld2FyZSguLi5taWRkbGV3YXJlcykge1xuICByZXR1cm4gKGNyZWF0ZVN0b3JlMikgPT4gKHJlZHVjZXIsIHByZWxvYWRlZFN0YXRlKSA9PiB7XG4gICAgY29uc3Qgc3RvcmUgPSBjcmVhdGVTdG9yZTIocmVkdWNlciwgcHJlbG9hZGVkU3RhdGUpO1xuICAgIGxldCBkaXNwYXRjaCA9ICgpID0+IHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJwcm9kdWN0aW9uXCIgPyBmb3JtYXRQcm9kRXJyb3JNZXNzYWdlKDE1KSA6IFwiRGlzcGF0Y2hpbmcgd2hpbGUgY29uc3RydWN0aW5nIHlvdXIgbWlkZGxld2FyZSBpcyBub3QgYWxsb3dlZC4gT3RoZXIgbWlkZGxld2FyZSB3b3VsZCBub3QgYmUgYXBwbGllZCB0byB0aGlzIGRpc3BhdGNoLlwiKTtcbiAgICB9O1xuICAgIGNvbnN0IG1pZGRsZXdhcmVBUEkgPSB7XG4gICAgICBnZXRTdGF0ZTogc3RvcmUuZ2V0U3RhdGUsXG4gICAgICBkaXNwYXRjaDogKGFjdGlvbiwgLi4uYXJncykgPT4gZGlzcGF0Y2goYWN0aW9uLCAuLi5hcmdzKVxuICAgIH07XG4gICAgY29uc3QgY2hhaW4gPSBtaWRkbGV3YXJlcy5tYXAoKG1pZGRsZXdhcmUpID0+IG1pZGRsZXdhcmUobWlkZGxld2FyZUFQSSkpO1xuICAgIGRpc3BhdGNoID0gY29tcG9zZSguLi5jaGFpbikoc3RvcmUuZGlzcGF0Y2gpO1xuICAgIHJldHVybiB7XG4gICAgICAuLi5zdG9yZSxcbiAgICAgIGRpc3BhdGNoXG4gICAgfTtcbiAgfTtcbn1cblxuLy8gc3JjL3V0aWxzL2lzQWN0aW9uLnRzXG5mdW5jdGlvbiBpc0FjdGlvbihhY3Rpb24pIHtcbiAgcmV0dXJuIGlzUGxhaW5PYmplY3QoYWN0aW9uKSAmJiBcInR5cGVcIiBpbiBhY3Rpb24gJiYgdHlwZW9mIGFjdGlvbi50eXBlID09PSBcInN0cmluZ1wiO1xufVxuZXhwb3J0IHtcbiAgYWN0aW9uVHlwZXNfZGVmYXVsdCBhcyBfX0RPX05PVF9VU0VfX0FjdGlvblR5cGVzLFxuICBhcHBseU1pZGRsZXdhcmUsXG4gIGJpbmRBY3Rpb25DcmVhdG9ycyxcbiAgY29tYmluZVJlZHVjZXJzLFxuICBjb21wb3NlLFxuICBjcmVhdGVTdG9yZSxcbiAgaXNBY3Rpb24sXG4gIGlzUGxhaW5PYmplY3QsXG4gIGxlZ2FjeV9jcmVhdGVTdG9yZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlZHV4Lm1qcy5tYXAiXSwibmFtZXMiOlsiZm9ybWF0UHJvZEVycm9yTWVzc2FnZSIsImNvZGUiLCIkJG9ic2VydmFibGUiLCJTeW1ib2wiLCJvYnNlcnZhYmxlIiwic3ltYm9sX29ic2VydmFibGVfZGVmYXVsdCIsInJhbmRvbVN0cmluZyIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0cmluZyIsInNwbGl0Iiwiam9pbiIsIkFjdGlvblR5cGVzIiwiSU5JVCIsIlJFUExBQ0UiLCJQUk9CRV9VTktOT1dOX0FDVElPTiIsImFjdGlvblR5cGVzX2RlZmF1bHQiLCJpc1BsYWluT2JqZWN0Iiwib2JqIiwicHJvdG8iLCJPYmplY3QiLCJnZXRQcm90b3R5cGVPZiIsIm1pbmlLaW5kT2YiLCJ2YWwiLCJ0eXBlIiwiQXJyYXkiLCJpc0FycmF5IiwiaXNEYXRlIiwiaXNFcnJvciIsImNvbnN0cnVjdG9yTmFtZSIsImN0b3JOYW1lIiwicHJvdG90eXBlIiwiY2FsbCIsInNsaWNlIiwidG9Mb3dlckNhc2UiLCJyZXBsYWNlIiwiY29uc3RydWN0b3IiLCJuYW1lIiwiRXJyb3IiLCJtZXNzYWdlIiwic3RhY2tUcmFjZUxpbWl0IiwiRGF0ZSIsInRvRGF0ZVN0cmluZyIsImdldERhdGUiLCJzZXREYXRlIiwia2luZE9mIiwidHlwZU9mVmFsIiwicHJvY2VzcyIsImNyZWF0ZVN0b3JlIiwicmVkdWNlciIsInByZWxvYWRlZFN0YXRlIiwiZW5oYW5jZXIiLCJhcmd1bWVudHMiLCJjdXJyZW50UmVkdWNlciIsImN1cnJlbnRTdGF0ZSIsImN1cnJlbnRMaXN0ZW5lcnMiLCJNYXAiLCJuZXh0TGlzdGVuZXJzIiwibGlzdGVuZXJJZENvdW50ZXIiLCJpc0Rpc3BhdGNoaW5nIiwiZW5zdXJlQ2FuTXV0YXRlTmV4dExpc3RlbmVycyIsImZvckVhY2giLCJsaXN0ZW5lciIsImtleSIsInNldCIsImdldFN0YXRlIiwic3Vic2NyaWJlIiwiaXNTdWJzY3JpYmVkIiwibGlzdGVuZXJJZCIsInVuc3Vic2NyaWJlIiwiZGVsZXRlIiwiZGlzcGF0Y2giLCJhY3Rpb24iLCJsaXN0ZW5lcnMiLCJyZXBsYWNlUmVkdWNlciIsIm5leHRSZWR1Y2VyIiwib3V0ZXJTdWJzY3JpYmUiLCJvYnNlcnZlciIsIm9ic2VydmVTdGF0ZSIsIm9ic2VydmVyQXNPYnNlcnZlciIsIm5leHQiLCJzdG9yZSIsImxlZ2FjeV9jcmVhdGVTdG9yZSIsIndhcm5pbmciLCJjb25zb2xlIiwiZXJyb3IiLCJlIiwiZ2V0VW5leHBlY3RlZFN0YXRlU2hhcGVXYXJuaW5nTWVzc2FnZSIsImlucHV0U3RhdGUiLCJyZWR1Y2VycyIsInVuZXhwZWN0ZWRLZXlDYWNoZSIsInJlZHVjZXJLZXlzIiwia2V5cyIsImFyZ3VtZW50TmFtZSIsImxlbmd0aCIsInVuZXhwZWN0ZWRLZXlzIiwiZmlsdGVyIiwiaGFzT3duUHJvcGVydHkiLCJhc3NlcnRSZWR1Y2VyU2hhcGUiLCJpbml0aWFsU3RhdGUiLCJjb21iaW5lUmVkdWNlcnMiLCJmaW5hbFJlZHVjZXJzIiwiaSIsImZpbmFsUmVkdWNlcktleXMiLCJzaGFwZUFzc2VydGlvbkVycm9yIiwiY29tYmluYXRpb24iLCJzdGF0ZSIsIndhcm5pbmdNZXNzYWdlIiwiaGFzQ2hhbmdlZCIsIm5leHRTdGF0ZSIsInByZXZpb3VzU3RhdGVGb3JLZXkiLCJuZXh0U3RhdGVGb3JLZXkiLCJhY3Rpb25UeXBlIiwiU3RyaW5nIiwiYmluZEFjdGlvbkNyZWF0b3IiLCJhY3Rpb25DcmVhdG9yIiwiYXJncyIsImFwcGx5IiwiYmluZEFjdGlvbkNyZWF0b3JzIiwiYWN0aW9uQ3JlYXRvcnMiLCJib3VuZEFjdGlvbkNyZWF0b3JzIiwiY29tcG9zZSIsImZ1bmNzIiwiYXJnIiwicmVkdWNlIiwiYSIsImIiLCJhcHBseU1pZGRsZXdhcmUiLCJtaWRkbGV3YXJlcyIsImNyZWF0ZVN0b3JlMiIsIm1pZGRsZXdhcmVBUEkiLCJjaGFpbiIsIm1hcCIsIm1pZGRsZXdhcmUiLCJpc0FjdGlvbiIsIl9fRE9fTk9UX1VTRV9fQWN0aW9uVHlwZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/redux/dist/redux.mjs\n");

/***/ })

};
;