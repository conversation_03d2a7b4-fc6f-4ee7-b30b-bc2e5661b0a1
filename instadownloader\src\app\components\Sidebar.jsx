import Image from "next/image";
import Link from "next/link";
import React from "react";
import LoadingComponent from "../Loading";

const SideBar = ({
    sideToolsDetails,
    header = "RECENTLY UPDATED TOOLS",
    isLoading,
}) => {

    return (
        <div className="my-3.5 p-5 bg-white rounded-md shadow-md ">
            <h2 className="mb-2.5 font-normal text-slate-500 tracking-wider uppercase">
                {header}
            </h2>
            {isLoading ? (
                <LoadingComponent length={5} md={1} lg={1} />
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-1 gap-4 p-4">
                    {sideToolsDetails?.map((app) => (
                        <div
                            key={app.appId}
                            className="hover:bg-gray-100 p-4 shadow-sm border rounded-md border-gray-200"
                        >
                            <Link href={`/tool/${app.appId}`} target="_blank" prefetch={false}>
                                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                                    <Image
                                        className="rounded-2xl w-full h-auto sm:w-20 sm:h-20"
                                        width={75}
                                        height={75}
                                         src={`/${app.iconLocalPath}`}
                                        alt={`${app.title} Icon`}
                                        priority={true}
                                    />

                                    <div className="mt-4  w-full sm:mt-0 sm:ml-4 flex-1">
                                        <div className="flex justify-between items-start">
                                            <h4 className="text-sm sm:text-base font-medium">{app.title}</h4>
                                            <p className="px-3 py-1 text-xs sm:text-sm cursor-text font-semibold text-white bg-gradient-to-r from-green-400 to-blue-500 rounded-full border border-transparent shadow-lg dark:bg-gradient-to-r dark:from-green-500 dark:to-blue-600">
                                                {app.price}
                                            </p>

                                        </div>
                                        <p className="text-xs text-slate-400 tracking-wider mt-1">
                                            {app.category}
                                        </p>
                                    </div>
                                </div>
                            </Link>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default SideBar;
