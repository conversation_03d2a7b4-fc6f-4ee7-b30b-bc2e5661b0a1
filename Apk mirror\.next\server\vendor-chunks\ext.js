/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ext";
exports.ids = ["vendor-chunks/ext"];
exports.modules = {

/***/ "(rsc)/./node_modules/ext/global-this/implementation.js":
/*!********************************************************!*\
  !*** ./node_modules/ext/global-this/implementation.js ***!
  \********************************************************/
/***/ ((module) => {

eval("var naiveFallback = function () {\n\tif (typeof self === \"object\" && self) return self;\n\tif (typeof window === \"object\" && window) return window;\n\tthrow new Error(\"Unable to resolve global `this`\");\n};\n\nmodule.exports = (function () {\n\tif (this) return this;\n\n\t// Unexpected strict mode (may happen if e.g. bundled into ESM module)\n\n\t// Thanks @mathiasbynens -> https://mathiasbynens.be/notes/globalthis\n\t// In all ES5+ engines global object inherits from Object.prototype\n\t// (if you approached one that doesn't please report)\n\ttry {\n\t\tObject.defineProperty(Object.prototype, \"__global__\", {\n\t\t\tget: function () { return this; },\n\t\t\tconfigurable: true\n\t\t});\n\t} catch (error) {\n\t\t// Unfortunate case of Object.prototype being sealed (via preventExtensions, seal or freeze)\n\t\treturn naiveFallback();\n\t}\n\ttry {\n\t\t// Safari case (window.__global__ is resolved with global context, but __global__ does not)\n\t\tif (!__global__) return naiveFallback();\n\t\treturn __global__;\n\t} finally {\n\t\tdelete Object.prototype.__global__;\n\t}\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZXh0L2dsb2JhbC10aGlzL2ltcGxlbWVudGF0aW9uLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLGNBQWM7QUFDcEM7QUFDQSxHQUFHO0FBQ0gsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXBrLW1pcnJvci8uL25vZGVfbW9kdWxlcy9leHQvZ2xvYmFsLXRoaXMvaW1wbGVtZW50YXRpb24uanM/ZGYxZSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgbmFpdmVGYWxsYmFjayA9IGZ1bmN0aW9uICgpIHtcblx0aWYgKHR5cGVvZiBzZWxmID09PSBcIm9iamVjdFwiICYmIHNlbGYpIHJldHVybiBzZWxmO1xuXHRpZiAodHlwZW9mIHdpbmRvdyA9PT0gXCJvYmplY3RcIiAmJiB3aW5kb3cpIHJldHVybiB3aW5kb3c7XG5cdHRocm93IG5ldyBFcnJvcihcIlVuYWJsZSB0byByZXNvbHZlIGdsb2JhbCBgdGhpc2BcIik7XG59O1xuXG5tb2R1bGUuZXhwb3J0cyA9IChmdW5jdGlvbiAoKSB7XG5cdGlmICh0aGlzKSByZXR1cm4gdGhpcztcblxuXHQvLyBVbmV4cGVjdGVkIHN0cmljdCBtb2RlIChtYXkgaGFwcGVuIGlmIGUuZy4gYnVuZGxlZCBpbnRvIEVTTSBtb2R1bGUpXG5cblx0Ly8gVGhhbmtzIEBtYXRoaWFzYnluZW5zIC0+IGh0dHBzOi8vbWF0aGlhc2J5bmVucy5iZS9ub3Rlcy9nbG9iYWx0aGlzXG5cdC8vIEluIGFsbCBFUzUrIGVuZ2luZXMgZ2xvYmFsIG9iamVjdCBpbmhlcml0cyBmcm9tIE9iamVjdC5wcm90b3R5cGVcblx0Ly8gKGlmIHlvdSBhcHByb2FjaGVkIG9uZSB0aGF0IGRvZXNuJ3QgcGxlYXNlIHJlcG9ydClcblx0dHJ5IHtcblx0XHRPYmplY3QuZGVmaW5lUHJvcGVydHkoT2JqZWN0LnByb3RvdHlwZSwgXCJfX2dsb2JhbF9fXCIsIHtcblx0XHRcdGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gdGhpczsgfSxcblx0XHRcdGNvbmZpZ3VyYWJsZTogdHJ1ZVxuXHRcdH0pO1xuXHR9IGNhdGNoIChlcnJvcikge1xuXHRcdC8vIFVuZm9ydHVuYXRlIGNhc2Ugb2YgT2JqZWN0LnByb3RvdHlwZSBiZWluZyBzZWFsZWQgKHZpYSBwcmV2ZW50RXh0ZW5zaW9ucywgc2VhbCBvciBmcmVlemUpXG5cdFx0cmV0dXJuIG5haXZlRmFsbGJhY2soKTtcblx0fVxuXHR0cnkge1xuXHRcdC8vIFNhZmFyaSBjYXNlICh3aW5kb3cuX19nbG9iYWxfXyBpcyByZXNvbHZlZCB3aXRoIGdsb2JhbCBjb250ZXh0LCBidXQgX19nbG9iYWxfXyBkb2VzIG5vdClcblx0XHRpZiAoIV9fZ2xvYmFsX18pIHJldHVybiBuYWl2ZUZhbGxiYWNrKCk7XG5cdFx0cmV0dXJuIF9fZ2xvYmFsX187XG5cdH0gZmluYWxseSB7XG5cdFx0ZGVsZXRlIE9iamVjdC5wcm90b3R5cGUuX19nbG9iYWxfXztcblx0fVxufSkoKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ext/global-this/implementation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ext/global-this/index.js":
/*!***********************************************!*\
  !*** ./node_modules/ext/global-this/index.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nmodule.exports = __webpack_require__(/*! ./is-implemented */ \"(rsc)/./node_modules/ext/global-this/is-implemented.js\")() ? globalThis : __webpack_require__(/*! ./implementation */ \"(rsc)/./node_modules/ext/global-this/implementation.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZXh0L2dsb2JhbC10aGlzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGlCQUFpQixtQkFBTyxDQUFDLGdGQUFrQixtQkFBbUIsbUJBQU8sQ0FBQyxnRkFBa0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vbm9kZV9tb2R1bGVzL2V4dC9nbG9iYWwtdGhpcy9pbmRleC5qcz84NTc2Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoXCIuL2lzLWltcGxlbWVudGVkXCIpKCkgPyBnbG9iYWxUaGlzIDogcmVxdWlyZShcIi4vaW1wbGVtZW50YXRpb25cIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ext/global-this/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/ext/global-this/is-implemented.js":
/*!********************************************************!*\
  !*** ./node_modules/ext/global-this/is-implemented.js ***!
  \********************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nmodule.exports = function () {\n\tif (typeof globalThis !== \"object\") return false;\n\tif (!globalThis) return false;\n\treturn globalThis.Array === Array;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZXh0L2dsb2JhbC10aGlzL2lzLWltcGxlbWVudGVkLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcGstbWlycm9yLy4vbm9kZV9tb2R1bGVzL2V4dC9nbG9iYWwtdGhpcy9pcy1pbXBsZW1lbnRlZC5qcz81N2VjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uICgpIHtcblx0aWYgKHR5cGVvZiBnbG9iYWxUaGlzICE9PSBcIm9iamVjdFwiKSByZXR1cm4gZmFsc2U7XG5cdGlmICghZ2xvYmFsVGhpcykgcmV0dXJuIGZhbHNlO1xuXHRyZXR1cm4gZ2xvYmFsVGhpcy5BcnJheSA9PT0gQXJyYXk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ext/global-this/is-implemented.js\n");

/***/ })

};
;