"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-geo";
exports.ids = ["vendor-chunks/d3-geo"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-geo/src/area.js":
/*!*****************************************!*\
  !*** ./node_modules/d3-geo/src/area.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areaRingSum: () => (/* binding */ areaRingSum),\n/* harmony export */   areaStream: () => (/* binding */ areaStream),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n\n\n\n\nvar areaRingSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n// hello?\nvar areaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder(), lambda00, phi00, lambda0, cosPhi0, sinPhi0;\nvar areaStream = {\n    point: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    lineStart: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    polygonStart: function() {\n        areaRingSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n        areaStream.lineStart = areaRingStart;\n        areaStream.lineEnd = areaRingEnd;\n    },\n    polygonEnd: function() {\n        var areaRing = +areaRingSum;\n        areaSum.add(areaRing < 0 ? _math_js__WEBPACK_IMPORTED_MODULE_2__.tau + areaRing : areaRing);\n        this.lineStart = this.lineEnd = this.point = _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n    },\n    sphere: function() {\n        areaSum.add(_math_js__WEBPACK_IMPORTED_MODULE_2__.tau);\n    }\n};\nfunction areaRingStart() {\n    areaStream.point = areaPointFirst;\n}\nfunction areaRingEnd() {\n    areaPoint(lambda00, phi00);\n}\nfunction areaPointFirst(lambda, phi) {\n    areaStream.point = areaPoint;\n    lambda00 = lambda, phi00 = phi;\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_2__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_2__.radians;\n    lambda0 = lambda, cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.cos)(phi = phi / 2 + _math_js__WEBPACK_IMPORTED_MODULE_2__.quarterPi), sinPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.sin)(phi);\n}\nfunction areaPoint(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_2__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_2__.radians;\n    phi = phi / 2 + _math_js__WEBPACK_IMPORTED_MODULE_2__.quarterPi; // half the angular distance from south pole\n    // Spherical excess E for a spherical triangle with vertices: south pole,\n    // previous point, current point.  Uses a formula derived from Cagnoli’s\n    // theorem.  See Todhunter, Spherical Trig. (1871), Sec. 103, Eq. (2).\n    var dLambda = lambda - lambda0, sdLambda = dLambda >= 0 ? 1 : -1, adLambda = sdLambda * dLambda, cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.cos)(phi), sinPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.sin)(phi), k = sinPhi0 * sinPhi, u = cosPhi0 * cosPhi + k * (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.cos)(adLambda), v = k * sdLambda * (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.sin)(adLambda);\n    areaRingSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.atan2)(v, u));\n    // Advance the previous points.\n    lambda0 = lambda, cosPhi0 = cosPhi, sinPhi0 = sinPhi;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object) {\n    areaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(object, areaStream);\n    return areaSum * 2;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/area.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/bounds.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-geo/src/bounds.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-geo/src/area.js\");\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cartesian.js */ \"(ssr)/./node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n\n\n\n\n\nvar lambda0, phi0, lambda1, phi1, lambda2, lambda00, phi00, p0, deltaSum, ranges, range;\nvar boundsStream = {\n    point: boundsPoint,\n    lineStart: boundsLineStart,\n    lineEnd: boundsLineEnd,\n    polygonStart: function() {\n        boundsStream.point = boundsRingPoint;\n        boundsStream.lineStart = boundsRingStart;\n        boundsStream.lineEnd = boundsRingEnd;\n        deltaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n        _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.polygonStart();\n    },\n    polygonEnd: function() {\n        _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.polygonEnd();\n        boundsStream.point = boundsPoint;\n        boundsStream.lineStart = boundsLineStart;\n        boundsStream.lineEnd = boundsLineEnd;\n        if (_area_js__WEBPACK_IMPORTED_MODULE_1__.areaRingSum < 0) lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n        else if (deltaSum > _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon) phi1 = 90;\n        else if (deltaSum < -_math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon) phi0 = -90;\n        range[0] = lambda0, range[1] = lambda1;\n    },\n    sphere: function() {\n        lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n    }\n};\nfunction boundsPoint(lambda, phi) {\n    ranges.push(range = [\n        lambda0 = lambda,\n        lambda1 = lambda\n    ]);\n    if (phi < phi0) phi0 = phi;\n    if (phi > phi1) phi1 = phi;\n}\nfunction linePoint(lambda, phi) {\n    var p = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesian)([\n        lambda * _math_js__WEBPACK_IMPORTED_MODULE_2__.radians,\n        phi * _math_js__WEBPACK_IMPORTED_MODULE_2__.radians\n    ]);\n    if (p0) {\n        var normal = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianCross)(p0, p), equatorial = [\n            normal[1],\n            -normal[0],\n            0\n        ], inflection = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianCross)(equatorial, normal);\n        (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianNormalizeInPlace)(inflection);\n        inflection = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.spherical)(inflection);\n        var delta = lambda - lambda2, sign = delta > 0 ? 1 : -1, lambdai = inflection[0] * _math_js__WEBPACK_IMPORTED_MODULE_2__.degrees * sign, phii, antimeridian = (0,_math_js__WEBPACK_IMPORTED_MODULE_2__.abs)(delta) > 180;\n        if (antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n            phii = inflection[1] * _math_js__WEBPACK_IMPORTED_MODULE_2__.degrees;\n            if (phii > phi1) phi1 = phii;\n        } else if (lambdai = (lambdai + 360) % 360 - 180, antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n            phii = -inflection[1] * _math_js__WEBPACK_IMPORTED_MODULE_2__.degrees;\n            if (phii < phi0) phi0 = phii;\n        } else {\n            if (phi < phi0) phi0 = phi;\n            if (phi > phi1) phi1 = phi;\n        }\n        if (antimeridian) {\n            if (lambda < lambda2) {\n                if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n            } else {\n                if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n            }\n        } else {\n            if (lambda1 >= lambda0) {\n                if (lambda < lambda0) lambda0 = lambda;\n                if (lambda > lambda1) lambda1 = lambda;\n            } else {\n                if (lambda > lambda2) {\n                    if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n                } else {\n                    if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n                }\n            }\n        }\n    } else {\n        ranges.push(range = [\n            lambda0 = lambda,\n            lambda1 = lambda\n        ]);\n    }\n    if (phi < phi0) phi0 = phi;\n    if (phi > phi1) phi1 = phi;\n    p0 = p, lambda2 = lambda;\n}\nfunction boundsLineStart() {\n    boundsStream.point = linePoint;\n}\nfunction boundsLineEnd() {\n    range[0] = lambda0, range[1] = lambda1;\n    boundsStream.point = boundsPoint;\n    p0 = null;\n}\nfunction boundsRingPoint(lambda, phi) {\n    if (p0) {\n        var delta = lambda - lambda2;\n        deltaSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.abs)(delta) > 180 ? delta + (delta > 0 ? 360 : -360) : delta);\n    } else {\n        lambda00 = lambda, phi00 = phi;\n    }\n    _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.point(lambda, phi);\n    linePoint(lambda, phi);\n}\nfunction boundsRingStart() {\n    _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.lineStart();\n}\nfunction boundsRingEnd() {\n    boundsRingPoint(lambda00, phi00);\n    _area_js__WEBPACK_IMPORTED_MODULE_1__.areaStream.lineEnd();\n    if ((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.abs)(deltaSum) > _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon) lambda0 = -(lambda1 = 180);\n    range[0] = lambda0, range[1] = lambda1;\n    p0 = null;\n}\n// Finds the left-right distance between two longitudes.\n// This is almost the same as (lambda1 - lambda0 + 360°) % 360°, except that we want\n// the distance between ±180° to be 360°.\nfunction angle(lambda0, lambda1) {\n    return (lambda1 -= lambda0) < 0 ? lambda1 + 360 : lambda1;\n}\nfunction rangeCompare(a, b) {\n    return a[0] - b[0];\n}\nfunction rangeContains(range, x) {\n    return range[0] <= range[1] ? range[0] <= x && x <= range[1] : x < range[0] || range[1] < x;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(feature) {\n    var i, n, a, b, merged, deltaMax, delta;\n    phi1 = lambda1 = -(lambda0 = phi0 = Infinity);\n    ranges = [];\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(feature, boundsStream);\n    // First, sort ranges by their minimum longitudes.\n    if (n = ranges.length) {\n        ranges.sort(rangeCompare);\n        // Then, merge any ranges that overlap.\n        for(i = 1, a = ranges[0], merged = [\n            a\n        ]; i < n; ++i){\n            b = ranges[i];\n            if (rangeContains(a, b[0]) || rangeContains(a, b[1])) {\n                if (angle(a[0], b[1]) > angle(a[0], a[1])) a[1] = b[1];\n                if (angle(b[0], a[1]) > angle(a[0], a[1])) a[0] = b[0];\n            } else {\n                merged.push(a = b);\n            }\n        }\n        // Finally, find the largest gap between the merged ranges.\n        // The final bounding box will be the inverse of this gap.\n        for(deltaMax = -Infinity, n = merged.length - 1, i = 0, a = merged[n]; i <= n; a = b, ++i){\n            b = merged[i];\n            if ((delta = angle(a[1], b[0])) > deltaMax) deltaMax = delta, lambda0 = b[0], lambda1 = a[1];\n        }\n    }\n    ranges = range = null;\n    return lambda0 === Infinity || phi0 === Infinity ? [\n        [\n            NaN,\n            NaN\n        ],\n        [\n            NaN,\n            NaN\n        ]\n    ] : [\n        [\n            lambda0,\n            phi0\n        ],\n        [\n            lambda1,\n            phi1\n        ]\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/bounds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/cartesian.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-geo/src/cartesian.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cartesian: () => (/* binding */ cartesian),\n/* harmony export */   cartesianAddInPlace: () => (/* binding */ cartesianAddInPlace),\n/* harmony export */   cartesianCross: () => (/* binding */ cartesianCross),\n/* harmony export */   cartesianDot: () => (/* binding */ cartesianDot),\n/* harmony export */   cartesianNormalizeInPlace: () => (/* binding */ cartesianNormalizeInPlace),\n/* harmony export */   cartesianScale: () => (/* binding */ cartesianScale),\n/* harmony export */   spherical: () => (/* binding */ spherical)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\nfunction spherical(cartesian) {\n    return [\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(cartesian[1], cartesian[0]),\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(cartesian[2])\n    ];\n}\nfunction cartesian(spherical) {\n    var lambda = spherical[0], phi = spherical[1], cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi);\n    return [\n        cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda),\n        cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(lambda),\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi)\n    ];\n}\nfunction cartesianDot(a, b) {\n    return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\nfunction cartesianCross(a, b) {\n    return [\n        a[1] * b[2] - a[2] * b[1],\n        a[2] * b[0] - a[0] * b[2],\n        a[0] * b[1] - a[1] * b[0]\n    ];\n}\n// TODO return a\nfunction cartesianAddInPlace(a, b) {\n    a[0] += b[0], a[1] += b[1], a[2] += b[2];\n}\nfunction cartesianScale(vector, k) {\n    return [\n        vector[0] * k,\n        vector[1] * k,\n        vector[2] * k\n    ];\n}\n// TODO return d\nfunction cartesianNormalizeInPlace(d) {\n    var l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n    d[0] /= l, d[1] /= l, d[2] /= l;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/cartesian.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/centroid.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-geo/src/centroid.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n\n\n\n\nvar W0, W1, X0, Y0, Z0, X1, Y1, Z1, X2, Y2, Z2, lambda00, phi00, x0, y0, z0; // previous point\nvar centroidStream = {\n    sphere: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    point: centroidPoint,\n    lineStart: centroidLineStart,\n    lineEnd: centroidLineEnd,\n    polygonStart: function() {\n        centroidStream.lineStart = centroidRingStart;\n        centroidStream.lineEnd = centroidRingEnd;\n    },\n    polygonEnd: function() {\n        centroidStream.lineStart = centroidLineStart;\n        centroidStream.lineEnd = centroidLineEnd;\n    }\n};\n// Arithmetic mean of Cartesian vectors.\nfunction centroidPoint(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi);\n    centroidPointCartesian(cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda), cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda), (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi));\n}\nfunction centroidPointCartesian(x, y, z) {\n    ++W0;\n    X0 += (x - X0) / W0;\n    Y0 += (y - Y0) / W0;\n    Z0 += (z - Z0) / W0;\n}\nfunction centroidLineStart() {\n    centroidStream.point = centroidLinePointFirst;\n}\nfunction centroidLinePointFirst(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi);\n    x0 = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda);\n    y0 = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda);\n    z0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi);\n    centroidStream.point = centroidLinePoint;\n    centroidPointCartesian(x0, y0, z0);\n}\nfunction centroidLinePoint(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi), x = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda), y = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda), z = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi), w = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.atan2)((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sqrt)((w = y0 * z - z0 * y) * w + (w = z0 * x - x0 * z) * w + (w = x0 * y - y0 * x) * w), x0 * x + y0 * y + z0 * z);\n    W1 += w;\n    X1 += w * (x0 + (x0 = x));\n    Y1 += w * (y0 + (y0 = y));\n    Z1 += w * (z0 + (z0 = z));\n    centroidPointCartesian(x0, y0, z0);\n}\nfunction centroidLineEnd() {\n    centroidStream.point = centroidPoint;\n}\n// See J. E. Brock, The Inertia Tensor for a Spherical Triangle,\n// J. Applied Mechanics 42, 239 (1975).\nfunction centroidRingStart() {\n    centroidStream.point = centroidRingPointFirst;\n}\nfunction centroidRingEnd() {\n    centroidRingPoint(lambda00, phi00);\n    centroidStream.point = centroidPoint;\n}\nfunction centroidRingPointFirst(lambda, phi) {\n    lambda00 = lambda, phi00 = phi;\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    centroidStream.point = centroidRingPoint;\n    var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi);\n    x0 = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda);\n    y0 = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda);\n    z0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi);\n    centroidPointCartesian(x0, y0, z0);\n}\nfunction centroidRingPoint(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi), x = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(lambda), y = cosPhi * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda), z = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi), cx = y0 * z - z0 * y, cy = z0 * x - x0 * z, cz = x0 * y - y0 * x, m = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.hypot)(cx, cy, cz), w = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.asin)(m), v = m && -w / m; // area weight multiplier\n    X2.add(v * cx);\n    Y2.add(v * cy);\n    Z2.add(v * cz);\n    W1 += w;\n    X1 += w * (x0 + (x0 = x));\n    Y1 += w * (y0 + (y0 = y));\n    Z1 += w * (z0 + (z0 = z));\n    centroidPointCartesian(x0, y0, z0);\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object) {\n    W0 = W1 = X0 = Y0 = Z0 = X1 = Y1 = Z1 = 0;\n    X2 = new d3_array__WEBPACK_IMPORTED_MODULE_2__.Adder();\n    Y2 = new d3_array__WEBPACK_IMPORTED_MODULE_2__.Adder();\n    Z2 = new d3_array__WEBPACK_IMPORTED_MODULE_2__.Adder();\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(object, centroidStream);\n    var x = +X2, y = +Y2, z = +Z2, m = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.hypot)(x, y, z);\n    // If the area-weighted ccentroid is undefined, fall back to length-weighted ccentroid.\n    if (m < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon2) {\n        x = X1, y = Y1, z = Z1;\n        // If the feature has zero length, fall back to arithmetic mean of point vectors.\n        if (W1 < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) x = X0, y = Y0, z = Z0;\n        m = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.hypot)(x, y, z);\n        // If the feature still has an undefined ccentroid, then return.\n        if (m < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon2) return [\n            NaN,\n            NaN\n        ];\n    }\n    return [\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.atan2)(y, x) * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees,\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.asin)(z / m) * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/centroid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/circle.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-geo/src/circle.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   circleStream: () => (/* binding */ circleStream),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cartesian.js */ \"(ssr)/./node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-geo/src/constant.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _rotation_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./rotation.js */ \"(ssr)/./node_modules/d3-geo/src/rotation.js\");\n\n\n\n\n// Generates a circle centered at [0°, 0°], with a given radius and precision.\nfunction circleStream(stream, radius, delta, direction, t0, t1) {\n    if (!delta) return;\n    var cosRadius = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(radius), sinRadius = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(radius), step = direction * delta;\n    if (t0 == null) {\n        t0 = radius + direction * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n        t1 = radius - step / 2;\n    } else {\n        t0 = circleRadius(cosRadius, t0);\n        t1 = circleRadius(cosRadius, t1);\n        if (direction > 0 ? t0 < t1 : t0 > t1) t0 += direction * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n    }\n    for(var point, t = t0; direction > 0 ? t > t1 : t < t1; t -= step){\n        point = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_1__.spherical)([\n            cosRadius,\n            -sinRadius * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(t),\n            -sinRadius * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(t)\n        ]);\n        stream.point(point[0], point[1]);\n    }\n}\n// Returns the signed angle of a cartesian point relative to [cosRadius, 0, 0].\nfunction circleRadius(cosRadius, point) {\n    point = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_1__.cartesian)(point), point[0] -= cosRadius;\n    (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_1__.cartesianNormalizeInPlace)(point);\n    var radius = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.acos)(-point[1]);\n    return ((-point[2] < 0 ? -radius : radius) + _math_js__WEBPACK_IMPORTED_MODULE_0__.tau - _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) % _math_js__WEBPACK_IMPORTED_MODULE_0__.tau;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var center = (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])([\n        0,\n        0\n    ]), radius = (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(90), precision = (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(6), ring, rotate, stream = {\n        point: point\n    };\n    function point(x, y) {\n        ring.push(x = rotate(x, y));\n        x[0] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, x[1] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees;\n    }\n    function circle() {\n        var c = center.apply(this, arguments), r = radius.apply(this, arguments) * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, p = precision.apply(this, arguments) * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians;\n        ring = [];\n        rotate = (0,_rotation_js__WEBPACK_IMPORTED_MODULE_3__.rotateRadians)(-c[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, -c[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, 0).invert;\n        circleStream(stream, r, p, 1);\n        c = {\n            type: \"Polygon\",\n            coordinates: [\n                ring\n            ]\n        };\n        ring = rotate = null;\n        return c;\n    }\n    circle.center = function(_) {\n        return arguments.length ? (center = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])([\n            +_[0],\n            +_[1]\n        ]), circle) : center;\n    };\n    circle.radius = function(_) {\n        return arguments.length ? (radius = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(+_), circle) : radius;\n    };\n    circle.precision = function(_) {\n        return arguments.length ? (precision = typeof _ === \"function\" ? _ : (0,_constant_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(+_), circle) : precision;\n    };\n    return circle;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/circle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/antimeridian.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-geo/src/clip/antimeridian.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/clip/index.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(function() {\n    return true;\n}, clipAntimeridianLine, clipAntimeridianInterpolate, [\n    -_math_js__WEBPACK_IMPORTED_MODULE_1__.pi,\n    -_math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi\n]));\n// Takes a line and cuts into visible segments. Return values: 0 - there were\n// intersections or the line was empty; 1 - no intersections; 2 - there were\n// intersections, and the first and last segments should be rejoined.\nfunction clipAntimeridianLine(stream) {\n    var lambda0 = NaN, phi0 = NaN, sign0 = NaN, clean; // no intersections\n    return {\n        lineStart: function() {\n            stream.lineStart();\n            clean = 1;\n        },\n        point: function(lambda1, phi1) {\n            var sign1 = lambda1 > 0 ? _math_js__WEBPACK_IMPORTED_MODULE_1__.pi : -_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, delta = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(lambda1 - lambda0);\n            if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(delta - _math_js__WEBPACK_IMPORTED_MODULE_1__.pi) < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) {\n                stream.point(lambda0, phi0 = (phi0 + phi1) / 2 > 0 ? _math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi : -_math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi);\n                stream.point(sign0, phi0);\n                stream.lineEnd();\n                stream.lineStart();\n                stream.point(sign1, phi0);\n                stream.point(lambda1, phi0);\n                clean = 0;\n            } else if (sign0 !== sign1 && delta >= _math_js__WEBPACK_IMPORTED_MODULE_1__.pi) {\n                if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(lambda0 - sign0) < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) lambda0 -= sign0 * _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon; // handle degeneracies\n                if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(lambda1 - sign1) < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) lambda1 -= sign1 * _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon;\n                phi0 = clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1);\n                stream.point(sign0, phi0);\n                stream.lineEnd();\n                stream.lineStart();\n                stream.point(sign1, phi0);\n                clean = 0;\n            }\n            stream.point(lambda0 = lambda1, phi0 = phi1);\n            sign0 = sign1;\n        },\n        lineEnd: function() {\n            stream.lineEnd();\n            lambda0 = phi0 = NaN;\n        },\n        clean: function() {\n            return 2 - clean; // if intersections, rejoin first and last segments\n        }\n    };\n}\nfunction clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1) {\n    var cosPhi0, cosPhi1, sinLambda0Lambda1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda0 - lambda1);\n    return (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(sinLambda0Lambda1) > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon ? (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.atan)(((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi0) * (cosPhi1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi1)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda1) - (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi1) * (cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi0)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(lambda0)) / (cosPhi0 * cosPhi1 * sinLambda0Lambda1)) : (phi0 + phi1) / 2;\n}\nfunction clipAntimeridianInterpolate(from, to, direction, stream) {\n    var phi;\n    if (from == null) {\n        phi = direction * _math_js__WEBPACK_IMPORTED_MODULE_1__.halfPi;\n        stream.point(-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, phi);\n        stream.point(0, phi);\n        stream.point(_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, phi);\n        stream.point(_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, 0);\n        stream.point(_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, -phi);\n        stream.point(0, -phi);\n        stream.point(-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, -phi);\n        stream.point(-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, 0);\n        stream.point(-_math_js__WEBPACK_IMPORTED_MODULE_1__.pi, phi);\n    } else if ((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(from[0] - to[0]) > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon) {\n        var lambda = from[0] < to[0] ? _math_js__WEBPACK_IMPORTED_MODULE_1__.pi : -_math_js__WEBPACK_IMPORTED_MODULE_1__.pi;\n        phi = direction * lambda / 2;\n        stream.point(-lambda, phi);\n        stream.point(0, phi);\n        stream.point(lambda, phi);\n    } else {\n        stream.point(to[0], to[1]);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/antimeridian.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/buffer.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/clip/buffer.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var lines = [], line;\n    return {\n        point: function(x, y, m) {\n            line.push([\n                x,\n                y,\n                m\n            ]);\n        },\n        lineStart: function() {\n            lines.push(line = []);\n        },\n        lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        rejoin: function() {\n            if (lines.length > 1) lines.push(lines.pop().concat(lines.shift()));\n        },\n        result: function() {\n            var result = lines;\n            lines = [];\n            line = null;\n            return result;\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jbGlwL2J1ZmZlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QjtBQUU5Qiw2QkFBZSxzQ0FBVztJQUN4QixJQUFJQyxRQUFRLEVBQUUsRUFDVkM7SUFDSixPQUFPO1FBQ0xDLE9BQU8sU0FBU0MsQ0FBQyxFQUFFQyxDQUFDLEVBQUVDLENBQUM7WUFDckJKLEtBQUtLLElBQUksQ0FBQztnQkFBQ0g7Z0JBQUdDO2dCQUFHQzthQUFFO1FBQ3JCO1FBQ0FFLFdBQVc7WUFDVFAsTUFBTU0sSUFBSSxDQUFDTCxPQUFPLEVBQUU7UUFDdEI7UUFDQU8sU0FBU1QsZ0RBQUlBO1FBQ2JVLFFBQVE7WUFDTixJQUFJVCxNQUFNVSxNQUFNLEdBQUcsR0FBR1YsTUFBTU0sSUFBSSxDQUFDTixNQUFNVyxHQUFHLEdBQUdDLE1BQU0sQ0FBQ1osTUFBTWEsS0FBSztRQUNqRTtRQUNBQyxRQUFRO1lBQ04sSUFBSUEsU0FBU2Q7WUFDYkEsUUFBUSxFQUFFO1lBQ1ZDLE9BQU87WUFDUCxPQUFPYTtRQUNUO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvY2xpcC9idWZmZXIuanM/MTNmNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbm9vcCBmcm9tIFwiLi4vbm9vcC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgdmFyIGxpbmVzID0gW10sXG4gICAgICBsaW5lO1xuICByZXR1cm4ge1xuICAgIHBvaW50OiBmdW5jdGlvbih4LCB5LCBtKSB7XG4gICAgICBsaW5lLnB1c2goW3gsIHksIG1dKTtcbiAgICB9LFxuICAgIGxpbmVTdGFydDogZnVuY3Rpb24oKSB7XG4gICAgICBsaW5lcy5wdXNoKGxpbmUgPSBbXSk7XG4gICAgfSxcbiAgICBsaW5lRW5kOiBub29wLFxuICAgIHJlam9pbjogZnVuY3Rpb24oKSB7XG4gICAgICBpZiAobGluZXMubGVuZ3RoID4gMSkgbGluZXMucHVzaChsaW5lcy5wb3AoKS5jb25jYXQobGluZXMuc2hpZnQoKSkpO1xuICAgIH0sXG4gICAgcmVzdWx0OiBmdW5jdGlvbigpIHtcbiAgICAgIHZhciByZXN1bHQgPSBsaW5lcztcbiAgICAgIGxpbmVzID0gW107XG4gICAgICBsaW5lID0gbnVsbDtcbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfVxuICB9O1xufVxuIl0sIm5hbWVzIjpbIm5vb3AiLCJsaW5lcyIsImxpbmUiLCJwb2ludCIsIngiLCJ5IiwibSIsInB1c2giLCJsaW5lU3RhcnQiLCJsaW5lRW5kIiwicmVqb2luIiwibGVuZ3RoIiwicG9wIiwiY29uY2F0Iiwic2hpZnQiLCJyZXN1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/buffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/circle.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/clip/circle.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../cartesian.js */ \"(ssr)/./node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _circle_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../circle.js */ \"(ssr)/./node_modules/d3-geo/src/circle.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _pointEqual_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../pointEqual.js */ \"(ssr)/./node_modules/d3-geo/src/pointEqual.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/clip/index.js\");\n\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(radius) {\n    var cr = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(radius), delta = 6 * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, smallRadius = cr > 0, notHemisphere = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(cr) > _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon; // TODO optimise for this common case\n    function interpolate(from, to, direction, stream) {\n        (0,_circle_js__WEBPACK_IMPORTED_MODULE_1__.circleStream)(stream, radius, delta, direction, from, to);\n    }\n    function visible(lambda, phi) {\n        return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi) > cr;\n    }\n    // Takes a line and cuts into visible segments. Return values used for polygon\n    // clipping: 0 - there were intersections or the line was empty; 1 - no\n    // intersections 2 - there were intersections, and the first and last segments\n    // should be rejoined.\n    function clipLine(stream) {\n        var point0, c0, v0, v00, clean; // no intersections\n        return {\n            lineStart: function() {\n                v00 = v0 = false;\n                clean = 1;\n            },\n            point: function(lambda, phi) {\n                var point1 = [\n                    lambda,\n                    phi\n                ], point2, v = visible(lambda, phi), c = smallRadius ? v ? 0 : code(lambda, phi) : v ? code(lambda + (lambda < 0 ? _math_js__WEBPACK_IMPORTED_MODULE_0__.pi : -_math_js__WEBPACK_IMPORTED_MODULE_0__.pi), phi) : 0;\n                if (!point0 && (v00 = v0 = v)) stream.lineStart();\n                if (v !== v0) {\n                    point2 = intersect(point0, point1);\n                    if (!point2 || (0,_pointEqual_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(point0, point2) || (0,_pointEqual_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(point1, point2)) point1[2] = 1;\n                }\n                if (v !== v0) {\n                    clean = 0;\n                    if (v) {\n                        // outside going in\n                        stream.lineStart();\n                        point2 = intersect(point1, point0);\n                        stream.point(point2[0], point2[1]);\n                    } else {\n                        // inside going out\n                        point2 = intersect(point0, point1);\n                        stream.point(point2[0], point2[1], 2);\n                        stream.lineEnd();\n                    }\n                    point0 = point2;\n                } else if (notHemisphere && point0 && smallRadius ^ v) {\n                    var t;\n                    // If the codes for two points are different, or are both zero,\n                    // and there this segment intersects with the small circle.\n                    if (!(c & c0) && (t = intersect(point1, point0, true))) {\n                        clean = 0;\n                        if (smallRadius) {\n                            stream.lineStart();\n                            stream.point(t[0][0], t[0][1]);\n                            stream.point(t[1][0], t[1][1]);\n                            stream.lineEnd();\n                        } else {\n                            stream.point(t[1][0], t[1][1]);\n                            stream.lineEnd();\n                            stream.lineStart();\n                            stream.point(t[0][0], t[0][1], 3);\n                        }\n                    }\n                }\n                if (v && (!point0 || !(0,_pointEqual_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(point0, point1))) {\n                    stream.point(point1[0], point1[1]);\n                }\n                point0 = point1, v0 = v, c0 = c;\n            },\n            lineEnd: function() {\n                if (v0) stream.lineEnd();\n                point0 = null;\n            },\n            // Rejoin first and last segments if there were intersections and the first\n            // and last points were visible.\n            clean: function() {\n                return clean | (v00 && v0) << 1;\n            }\n        };\n    }\n    // Intersects the great circle between a and b with the clip circle.\n    function intersect(a, b, two) {\n        var pa = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesian)(a), pb = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesian)(b);\n        // We have two planes, n1.p = d1 and n2.p = d2.\n        // Find intersection line p(t) = c1 n1 + c2 n2 + t (n1 ⨯ n2).\n        var n1 = [\n            1,\n            0,\n            0\n        ], n2 = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianCross)(pa, pb), n2n2 = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianDot)(n2, n2), n1n2 = n2[0], determinant = n2n2 - n1n2 * n1n2;\n        // Two polar points.\n        if (!determinant) return !two && a;\n        var c1 = cr * n2n2 / determinant, c2 = -cr * n1n2 / determinant, n1xn2 = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianCross)(n1, n2), A = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianScale)(n1, c1), B = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianScale)(n2, c2);\n        (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianAddInPlace)(A, B);\n        // Solve |p(t)|^2 = 1.\n        var u = n1xn2, w = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianDot)(A, u), uu = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianDot)(u, u), t2 = w * w - uu * ((0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianDot)(A, A) - 1);\n        if (t2 < 0) return;\n        var t = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(t2), q = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianScale)(u, (-w - t) / uu);\n        (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianAddInPlace)(q, A);\n        q = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.spherical)(q);\n        if (!two) return q;\n        // Two intersection points.\n        var lambda0 = a[0], lambda1 = b[0], phi0 = a[1], phi1 = b[1], z;\n        if (lambda1 < lambda0) z = lambda0, lambda0 = lambda1, lambda1 = z;\n        var delta = lambda1 - lambda0, polar = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(delta - _math_js__WEBPACK_IMPORTED_MODULE_0__.pi) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon, meridian = polar || delta < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n        if (!polar && phi1 < phi0) z = phi0, phi0 = phi1, phi1 = z;\n        // Check that the first point is between a and b.\n        if (meridian ? polar ? phi0 + phi1 > 0 ^ q[1] < ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(q[0] - lambda0) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? phi0 : phi1) : phi0 <= q[1] && q[1] <= phi1 : delta > _math_js__WEBPACK_IMPORTED_MODULE_0__.pi ^ (lambda0 <= q[0] && q[0] <= lambda1)) {\n            var q1 = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianScale)(u, (-w + t) / uu);\n            (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.cartesianAddInPlace)(q1, A);\n            return [\n                q,\n                (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_3__.spherical)(q1)\n            ];\n        }\n    }\n    // Generates a 4-bit vector representing the location of a point relative to\n    // the small circle's bounding box.\n    function code(lambda, phi) {\n        var r = smallRadius ? radius : _math_js__WEBPACK_IMPORTED_MODULE_0__.pi - radius, code = 0;\n        if (lambda < -r) code |= 1; // left\n        else if (lambda > r) code |= 2; // right\n        if (phi < -r) code |= 4; // below\n        else if (phi > r) code |= 8; // above\n        return code;\n    }\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(visible, clipLine, interpolate, smallRadius ? [\n        0,\n        -radius\n    ] : [\n        -_math_js__WEBPACK_IMPORTED_MODULE_0__.pi,\n        radius - _math_js__WEBPACK_IMPORTED_MODULE_0__.pi\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/circle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/extent.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/clip/extent.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _rectangle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rectangle.js */ \"(ssr)/./node_modules/d3-geo/src/clip/rectangle.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var x0 = 0, y0 = 0, x1 = 960, y1 = 500, cache, cacheStream, clip;\n    return clip = {\n        stream: function(stream) {\n            return cache && cacheStream === stream ? cache : cache = (0,_rectangle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(x0, y0, x1, y1)(cacheStream = stream);\n        },\n        extent: function(_) {\n            return arguments.length ? (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1], cache = cacheStream = null, clip) : [\n                [\n                    x0,\n                    y0\n                ],\n                [\n                    x1,\n                    y1\n                ]\n            ];\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jbGlwL2V4dGVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQztBQUUzQyw2QkFBZSxzQ0FBVztJQUN4QixJQUFJQyxLQUFLLEdBQ0xDLEtBQUssR0FDTEMsS0FBSyxLQUNMQyxLQUFLLEtBQ0xDLE9BQ0FDLGFBQ0FDO0lBRUosT0FBT0EsT0FBTztRQUNaQyxRQUFRLFNBQVNBLE1BQU07WUFDckIsT0FBT0gsU0FBU0MsZ0JBQWdCRSxTQUFTSCxRQUFRQSxRQUFRTCx5REFBYUEsQ0FBQ0MsSUFBSUMsSUFBSUMsSUFBSUMsSUFBSUUsY0FBY0U7UUFDdkc7UUFDQUMsUUFBUSxTQUFTQyxDQUFDO1lBQ2hCLE9BQU9DLFVBQVVDLE1BQU0sR0FBSVgsQ0FBQUEsS0FBSyxDQUFDUyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRVIsS0FBSyxDQUFDUSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRVAsS0FBSyxDQUFDTyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRU4sS0FBSyxDQUFDTSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRUwsUUFBUUMsY0FBYyxNQUFNQyxJQUFHLElBQUs7Z0JBQUM7b0JBQUNOO29CQUFJQztpQkFBRztnQkFBRTtvQkFBQ0M7b0JBQUlDO2lCQUFHO2FBQUM7UUFDako7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jbGlwL2V4dGVudC5qcz81NGUwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjbGlwUmVjdGFuZ2xlIGZyb20gXCIuL3JlY3RhbmdsZS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgdmFyIHgwID0gMCxcbiAgICAgIHkwID0gMCxcbiAgICAgIHgxID0gOTYwLFxuICAgICAgeTEgPSA1MDAsXG4gICAgICBjYWNoZSxcbiAgICAgIGNhY2hlU3RyZWFtLFxuICAgICAgY2xpcDtcblxuICByZXR1cm4gY2xpcCA9IHtcbiAgICBzdHJlYW06IGZ1bmN0aW9uKHN0cmVhbSkge1xuICAgICAgcmV0dXJuIGNhY2hlICYmIGNhY2hlU3RyZWFtID09PSBzdHJlYW0gPyBjYWNoZSA6IGNhY2hlID0gY2xpcFJlY3RhbmdsZSh4MCwgeTAsIHgxLCB5MSkoY2FjaGVTdHJlYW0gPSBzdHJlYW0pO1xuICAgIH0sXG4gICAgZXh0ZW50OiBmdW5jdGlvbihfKSB7XG4gICAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/ICh4MCA9ICtfWzBdWzBdLCB5MCA9ICtfWzBdWzFdLCB4MSA9ICtfWzFdWzBdLCB5MSA9ICtfWzFdWzFdLCBjYWNoZSA9IGNhY2hlU3RyZWFtID0gbnVsbCwgY2xpcCkgOiBbW3gwLCB5MF0sIFt4MSwgeTFdXTtcbiAgICB9XG4gIH07XG59XG4iXSwibmFtZXMiOlsiY2xpcFJlY3RhbmdsZSIsIngwIiwieTAiLCJ4MSIsInkxIiwiY2FjaGUiLCJjYWNoZVN0cmVhbSIsImNsaXAiLCJzdHJlYW0iLCJleHRlbnQiLCJfIiwiYXJndW1lbnRzIiwibGVuZ3RoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/extent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/index.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-geo/src/clip/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _buffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./buffer.js */ \"(ssr)/./node_modules/d3-geo/src/clip/buffer.js\");\n/* harmony import */ var _rejoin_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./rejoin.js */ \"(ssr)/./node_modules/d3-geo/src/clip/rejoin.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _polygonContains_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../polygonContains.js */ \"(ssr)/./node_modules/d3-geo/src/polygonContains.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/merge.js\");\n\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(pointVisible, clipLine, interpolate, start) {\n    return function(sink) {\n        var line = clipLine(sink), ringBuffer = (0,_buffer_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(), ringSink = clipLine(ringBuffer), polygonStarted = false, polygon, segments, ring;\n        var clip = {\n            point: point,\n            lineStart: lineStart,\n            lineEnd: lineEnd,\n            polygonStart: function() {\n                clip.point = pointRing;\n                clip.lineStart = ringStart;\n                clip.lineEnd = ringEnd;\n                segments = [];\n                polygon = [];\n            },\n            polygonEnd: function() {\n                clip.point = point;\n                clip.lineStart = lineStart;\n                clip.lineEnd = lineEnd;\n                segments = (0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(segments);\n                var startInside = (0,_polygonContains_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(polygon, start);\n                if (segments.length) {\n                    if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n                    (0,_rejoin_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(segments, compareIntersection, startInside, interpolate, sink);\n                } else if (startInside) {\n                    if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n                    sink.lineStart();\n                    interpolate(null, null, 1, sink);\n                    sink.lineEnd();\n                }\n                if (polygonStarted) sink.polygonEnd(), polygonStarted = false;\n                segments = polygon = null;\n            },\n            sphere: function() {\n                sink.polygonStart();\n                sink.lineStart();\n                interpolate(null, null, 1, sink);\n                sink.lineEnd();\n                sink.polygonEnd();\n            }\n        };\n        function point(lambda, phi) {\n            if (pointVisible(lambda, phi)) sink.point(lambda, phi);\n        }\n        function pointLine(lambda, phi) {\n            line.point(lambda, phi);\n        }\n        function lineStart() {\n            clip.point = pointLine;\n            line.lineStart();\n        }\n        function lineEnd() {\n            clip.point = point;\n            line.lineEnd();\n        }\n        function pointRing(lambda, phi) {\n            ring.push([\n                lambda,\n                phi\n            ]);\n            ringSink.point(lambda, phi);\n        }\n        function ringStart() {\n            ringSink.lineStart();\n            ring = [];\n        }\n        function ringEnd() {\n            pointRing(ring[0][0], ring[0][1]);\n            ringSink.lineEnd();\n            var clean = ringSink.clean(), ringSegments = ringBuffer.result(), i, n = ringSegments.length, m, segment, point;\n            ring.pop();\n            polygon.push(ring);\n            ring = null;\n            if (!n) return;\n            // No intersections.\n            if (clean & 1) {\n                segment = ringSegments[0];\n                if ((m = segment.length - 1) > 0) {\n                    if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n                    sink.lineStart();\n                    for(i = 0; i < m; ++i)sink.point((point = segment[i])[0], point[1]);\n                    sink.lineEnd();\n                }\n                return;\n            }\n            // Rejoin connected segments.\n            // TODO reuse ringBuffer.rejoin()?\n            if (n > 1 && clean & 2) ringSegments.push(ringSegments.pop().concat(ringSegments.shift()));\n            segments.push(ringSegments.filter(validSegment));\n        }\n        return clip;\n    };\n}\nfunction validSegment(segment) {\n    return segment.length > 1;\n}\n// Intersections are sorted along the clip edge. For both antimeridian cutting\n// and circle clipping, the same comparison is used.\nfunction compareIntersection(a, b) {\n    return ((a = a.x)[0] < 0 ? a[1] - _math_js__WEBPACK_IMPORTED_MODULE_4__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_4__.epsilon : _math_js__WEBPACK_IMPORTED_MODULE_4__.halfPi - a[1]) - ((b = b.x)[0] < 0 ? b[1] - _math_js__WEBPACK_IMPORTED_MODULE_4__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_4__.epsilon : _math_js__WEBPACK_IMPORTED_MODULE_4__.halfPi - b[1]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/line.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-geo/src/clip/line.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b, x0, y0, x1, y1) {\n    var ax = a[0], ay = a[1], bx = b[0], by = b[1], t0 = 0, t1 = 1, dx = bx - ax, dy = by - ay, r;\n    r = x0 - ax;\n    if (!dx && r > 0) return;\n    r /= dx;\n    if (dx < 0) {\n        if (r < t0) return;\n        if (r < t1) t1 = r;\n    } else if (dx > 0) {\n        if (r > t1) return;\n        if (r > t0) t0 = r;\n    }\n    r = x1 - ax;\n    if (!dx && r < 0) return;\n    r /= dx;\n    if (dx < 0) {\n        if (r > t1) return;\n        if (r > t0) t0 = r;\n    } else if (dx > 0) {\n        if (r < t0) return;\n        if (r < t1) t1 = r;\n    }\n    r = y0 - ay;\n    if (!dy && r > 0) return;\n    r /= dy;\n    if (dy < 0) {\n        if (r < t0) return;\n        if (r < t1) t1 = r;\n    } else if (dy > 0) {\n        if (r > t1) return;\n        if (r > t0) t0 = r;\n    }\n    r = y1 - ay;\n    if (!dy && r < 0) return;\n    r /= dy;\n    if (dy < 0) {\n        if (r > t1) return;\n        if (r > t0) t0 = r;\n    } else if (dy > 0) {\n        if (r < t0) return;\n        if (r < t1) t1 = r;\n    }\n    if (t0 > 0) a[0] = ax + t0 * dx, a[1] = ay + t0 * dy;\n    if (t1 < 1) b[0] = ax + t1 * dx, b[1] = ay + t1 * dy;\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jbGlwL2xpbmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxDQUFDLEVBQUVDLENBQUMsRUFBRUMsRUFBRSxFQUFFQyxFQUFFLEVBQUVDLEVBQUUsRUFBRUMsRUFBRTtJQUMxQyxJQUFJQyxLQUFLTixDQUFDLENBQUMsRUFBRSxFQUNUTyxLQUFLUCxDQUFDLENBQUMsRUFBRSxFQUNUUSxLQUFLUCxDQUFDLENBQUMsRUFBRSxFQUNUUSxLQUFLUixDQUFDLENBQUMsRUFBRSxFQUNUUyxLQUFLLEdBQ0xDLEtBQUssR0FDTEMsS0FBS0osS0FBS0YsSUFDVk8sS0FBS0osS0FBS0YsSUFDVk87SUFFSkEsSUFBSVosS0FBS0k7SUFDVCxJQUFJLENBQUNNLE1BQU1FLElBQUksR0FBRztJQUNsQkEsS0FBS0Y7SUFDTCxJQUFJQSxLQUFLLEdBQUc7UUFDVixJQUFJRSxJQUFJSixJQUFJO1FBQ1osSUFBSUksSUFBSUgsSUFBSUEsS0FBS0c7SUFDbkIsT0FBTyxJQUFJRixLQUFLLEdBQUc7UUFDakIsSUFBSUUsSUFBSUgsSUFBSTtRQUNaLElBQUlHLElBQUlKLElBQUlBLEtBQUtJO0lBQ25CO0lBRUFBLElBQUlWLEtBQUtFO0lBQ1QsSUFBSSxDQUFDTSxNQUFNRSxJQUFJLEdBQUc7SUFDbEJBLEtBQUtGO0lBQ0wsSUFBSUEsS0FBSyxHQUFHO1FBQ1YsSUFBSUUsSUFBSUgsSUFBSTtRQUNaLElBQUlHLElBQUlKLElBQUlBLEtBQUtJO0lBQ25CLE9BQU8sSUFBSUYsS0FBSyxHQUFHO1FBQ2pCLElBQUlFLElBQUlKLElBQUk7UUFDWixJQUFJSSxJQUFJSCxJQUFJQSxLQUFLRztJQUNuQjtJQUVBQSxJQUFJWCxLQUFLSTtJQUNULElBQUksQ0FBQ00sTUFBTUMsSUFBSSxHQUFHO0lBQ2xCQSxLQUFLRDtJQUNMLElBQUlBLEtBQUssR0FBRztRQUNWLElBQUlDLElBQUlKLElBQUk7UUFDWixJQUFJSSxJQUFJSCxJQUFJQSxLQUFLRztJQUNuQixPQUFPLElBQUlELEtBQUssR0FBRztRQUNqQixJQUFJQyxJQUFJSCxJQUFJO1FBQ1osSUFBSUcsSUFBSUosSUFBSUEsS0FBS0k7SUFDbkI7SUFFQUEsSUFBSVQsS0FBS0U7SUFDVCxJQUFJLENBQUNNLE1BQU1DLElBQUksR0FBRztJQUNsQkEsS0FBS0Q7SUFDTCxJQUFJQSxLQUFLLEdBQUc7UUFDVixJQUFJQyxJQUFJSCxJQUFJO1FBQ1osSUFBSUcsSUFBSUosSUFBSUEsS0FBS0k7SUFDbkIsT0FBTyxJQUFJRCxLQUFLLEdBQUc7UUFDakIsSUFBSUMsSUFBSUosSUFBSTtRQUNaLElBQUlJLElBQUlILElBQUlBLEtBQUtHO0lBQ25CO0lBRUEsSUFBSUosS0FBSyxHQUFHVixDQUFDLENBQUMsRUFBRSxHQUFHTSxLQUFLSSxLQUFLRSxJQUFJWixDQUFDLENBQUMsRUFBRSxHQUFHTyxLQUFLRyxLQUFLRztJQUNsRCxJQUFJRixLQUFLLEdBQUdWLENBQUMsQ0FBQyxFQUFFLEdBQUdLLEtBQUtLLEtBQUtDLElBQUlYLENBQUMsQ0FBQyxFQUFFLEdBQUdNLEtBQUtJLEtBQUtFO0lBQ2xELE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvY2xpcC9saW5lLmpzPzUyNGEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oYSwgYiwgeDAsIHkwLCB4MSwgeTEpIHtcbiAgdmFyIGF4ID0gYVswXSxcbiAgICAgIGF5ID0gYVsxXSxcbiAgICAgIGJ4ID0gYlswXSxcbiAgICAgIGJ5ID0gYlsxXSxcbiAgICAgIHQwID0gMCxcbiAgICAgIHQxID0gMSxcbiAgICAgIGR4ID0gYnggLSBheCxcbiAgICAgIGR5ID0gYnkgLSBheSxcbiAgICAgIHI7XG5cbiAgciA9IHgwIC0gYXg7XG4gIGlmICghZHggJiYgciA+IDApIHJldHVybjtcbiAgciAvPSBkeDtcbiAgaWYgKGR4IDwgMCkge1xuICAgIGlmIChyIDwgdDApIHJldHVybjtcbiAgICBpZiAociA8IHQxKSB0MSA9IHI7XG4gIH0gZWxzZSBpZiAoZHggPiAwKSB7XG4gICAgaWYgKHIgPiB0MSkgcmV0dXJuO1xuICAgIGlmIChyID4gdDApIHQwID0gcjtcbiAgfVxuXG4gIHIgPSB4MSAtIGF4O1xuICBpZiAoIWR4ICYmIHIgPCAwKSByZXR1cm47XG4gIHIgLz0gZHg7XG4gIGlmIChkeCA8IDApIHtcbiAgICBpZiAociA+IHQxKSByZXR1cm47XG4gICAgaWYgKHIgPiB0MCkgdDAgPSByO1xuICB9IGVsc2UgaWYgKGR4ID4gMCkge1xuICAgIGlmIChyIDwgdDApIHJldHVybjtcbiAgICBpZiAociA8IHQxKSB0MSA9IHI7XG4gIH1cblxuICByID0geTAgLSBheTtcbiAgaWYgKCFkeSAmJiByID4gMCkgcmV0dXJuO1xuICByIC89IGR5O1xuICBpZiAoZHkgPCAwKSB7XG4gICAgaWYgKHIgPCB0MCkgcmV0dXJuO1xuICAgIGlmIChyIDwgdDEpIHQxID0gcjtcbiAgfSBlbHNlIGlmIChkeSA+IDApIHtcbiAgICBpZiAociA+IHQxKSByZXR1cm47XG4gICAgaWYgKHIgPiB0MCkgdDAgPSByO1xuICB9XG5cbiAgciA9IHkxIC0gYXk7XG4gIGlmICghZHkgJiYgciA8IDApIHJldHVybjtcbiAgciAvPSBkeTtcbiAgaWYgKGR5IDwgMCkge1xuICAgIGlmIChyID4gdDEpIHJldHVybjtcbiAgICBpZiAociA+IHQwKSB0MCA9IHI7XG4gIH0gZWxzZSBpZiAoZHkgPiAwKSB7XG4gICAgaWYgKHIgPCB0MCkgcmV0dXJuO1xuICAgIGlmIChyIDwgdDEpIHQxID0gcjtcbiAgfVxuXG4gIGlmICh0MCA+IDApIGFbMF0gPSBheCArIHQwICogZHgsIGFbMV0gPSBheSArIHQwICogZHk7XG4gIGlmICh0MSA8IDEpIGJbMF0gPSBheCArIHQxICogZHgsIGJbMV0gPSBheSArIHQxICogZHk7XG4gIHJldHVybiB0cnVlO1xufVxuIl0sIm5hbWVzIjpbImEiLCJiIiwieDAiLCJ5MCIsIngxIiwieTEiLCJheCIsImF5IiwiYngiLCJieSIsInQwIiwidDEiLCJkeCIsImR5IiwiciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/line.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/rectangle.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-geo/src/clip/rectangle.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ clipRectangle)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _buffer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./buffer.js */ \"(ssr)/./node_modules/d3-geo/src/clip/buffer.js\");\n/* harmony import */ var _line_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./line.js */ \"(ssr)/./node_modules/d3-geo/src/clip/line.js\");\n/* harmony import */ var _rejoin_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./rejoin.js */ \"(ssr)/./node_modules/d3-geo/src/clip/rejoin.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/merge.js\");\n\n\n\n\n\nvar clipMax = 1e9, clipMin = -clipMax;\n// TODO Use d3-polygon’s polygonContains here for the ring check?\n// TODO Eliminate duplicate buffering in clipBuffer and polygon.push?\nfunction clipRectangle(x0, y0, x1, y1) {\n    function visible(x, y) {\n        return x0 <= x && x <= x1 && y0 <= y && y <= y1;\n    }\n    function interpolate(from, to, direction, stream) {\n        var a = 0, a1 = 0;\n        if (from == null || (a = corner(from, direction)) !== (a1 = corner(to, direction)) || comparePoint(from, to) < 0 ^ direction > 0) {\n            do stream.point(a === 0 || a === 3 ? x0 : x1, a > 1 ? y1 : y0);\n            while ((a = (a + direction + 4) % 4) !== a1);\n        } else {\n            stream.point(to[0], to[1]);\n        }\n    }\n    function corner(p, direction) {\n        return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(p[0] - x0) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? direction > 0 ? 0 : 3 : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(p[0] - x1) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? direction > 0 ? 2 : 1 : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(p[1] - y0) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? direction > 0 ? 1 : 0 : direction > 0 ? 3 : 2; // abs(p[1] - y1) < epsilon\n    }\n    function compareIntersection(a, b) {\n        return comparePoint(a.x, b.x);\n    }\n    function comparePoint(a, b) {\n        var ca = corner(a, 1), cb = corner(b, 1);\n        return ca !== cb ? ca - cb : ca === 0 ? b[1] - a[1] : ca === 1 ? a[0] - b[0] : ca === 2 ? a[1] - b[1] : b[0] - a[0];\n    }\n    return function(stream) {\n        var activeStream = stream, bufferStream = (0,_buffer_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(), segments, polygon, ring, x__, y__, v__, x_, y_, v_, first, clean;\n        var clipStream = {\n            point: point,\n            lineStart: lineStart,\n            lineEnd: lineEnd,\n            polygonStart: polygonStart,\n            polygonEnd: polygonEnd\n        };\n        function point(x, y) {\n            if (visible(x, y)) activeStream.point(x, y);\n        }\n        function polygonInside() {\n            var winding = 0;\n            for(var i = 0, n = polygon.length; i < n; ++i){\n                for(var ring = polygon[i], j = 1, m = ring.length, point = ring[0], a0, a1, b0 = point[0], b1 = point[1]; j < m; ++j){\n                    a0 = b0, a1 = b1, point = ring[j], b0 = point[0], b1 = point[1];\n                    if (a1 <= y1) {\n                        if (b1 > y1 && (b0 - a0) * (y1 - a1) > (b1 - a1) * (x0 - a0)) ++winding;\n                    } else {\n                        if (b1 <= y1 && (b0 - a0) * (y1 - a1) < (b1 - a1) * (x0 - a0)) --winding;\n                    }\n                }\n            }\n            return winding;\n        }\n        // Buffer geometry within a polygon and then clip it en masse.\n        function polygonStart() {\n            activeStream = bufferStream, segments = [], polygon = [], clean = true;\n        }\n        function polygonEnd() {\n            var startInside = polygonInside(), cleanInside = clean && startInside, visible = (segments = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(segments)).length;\n            if (cleanInside || visible) {\n                stream.polygonStart();\n                if (cleanInside) {\n                    stream.lineStart();\n                    interpolate(null, null, 1, stream);\n                    stream.lineEnd();\n                }\n                if (visible) {\n                    (0,_rejoin_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(segments, compareIntersection, startInside, interpolate, stream);\n                }\n                stream.polygonEnd();\n            }\n            activeStream = stream, segments = polygon = ring = null;\n        }\n        function lineStart() {\n            clipStream.point = linePoint;\n            if (polygon) polygon.push(ring = []);\n            first = true;\n            v_ = false;\n            x_ = y_ = NaN;\n        }\n        // TODO rather than special-case polygons, simply handle them separately.\n        // Ideally, coincident intersection points should be jittered to avoid\n        // clipping issues.\n        function lineEnd() {\n            if (segments) {\n                linePoint(x__, y__);\n                if (v__ && v_) bufferStream.rejoin();\n                segments.push(bufferStream.result());\n            }\n            clipStream.point = point;\n            if (v_) activeStream.lineEnd();\n        }\n        function linePoint(x, y) {\n            var v = visible(x, y);\n            if (polygon) ring.push([\n                x,\n                y\n            ]);\n            if (first) {\n                x__ = x, y__ = y, v__ = v;\n                first = false;\n                if (v) {\n                    activeStream.lineStart();\n                    activeStream.point(x, y);\n                }\n            } else {\n                if (v && v_) activeStream.point(x, y);\n                else {\n                    var a = [\n                        x_ = Math.max(clipMin, Math.min(clipMax, x_)),\n                        y_ = Math.max(clipMin, Math.min(clipMax, y_))\n                    ], b = [\n                        x = Math.max(clipMin, Math.min(clipMax, x)),\n                        y = Math.max(clipMin, Math.min(clipMax, y))\n                    ];\n                    if ((0,_line_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(a, b, x0, y0, x1, y1)) {\n                        if (!v_) {\n                            activeStream.lineStart();\n                            activeStream.point(a[0], a[1]);\n                        }\n                        activeStream.point(b[0], b[1]);\n                        if (!v) activeStream.lineEnd();\n                        clean = false;\n                    } else if (v) {\n                        activeStream.lineStart();\n                        activeStream.point(x, y);\n                        clean = false;\n                    }\n                }\n            }\n            x_ = x, y_ = y, v_ = v;\n        }\n        return clipStream;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/rectangle.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/clip/rejoin.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/clip/rejoin.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _pointEqual_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../pointEqual.js */ \"(ssr)/./node_modules/d3-geo/src/pointEqual.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\nfunction Intersection(point, points, other, entry) {\n    this.x = point;\n    this.z = points;\n    this.o = other; // another intersection\n    this.e = entry; // is an entry?\n    this.v = false; // visited\n    this.n = this.p = null; // next & previous\n}\n// A generalized polygon clipping algorithm: given a polygon that has been cut\n// into its visible line segments, and rejoins the segments by interpolating\n// along the clip edge.\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(segments, compareIntersection, startInside, interpolate, stream) {\n    var subject = [], clip = [], i, n;\n    segments.forEach(function(segment) {\n        if ((n = segment.length - 1) <= 0) return;\n        var n, p0 = segment[0], p1 = segment[n], x;\n        if ((0,_pointEqual_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(p0, p1)) {\n            if (!p0[2] && !p1[2]) {\n                stream.lineStart();\n                for(i = 0; i < n; ++i)stream.point((p0 = segment[i])[0], p0[1]);\n                stream.lineEnd();\n                return;\n            }\n            // handle degenerate cases by moving the point\n            p1[0] += 2 * _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon;\n        }\n        subject.push(x = new Intersection(p0, segment, null, true));\n        clip.push(x.o = new Intersection(p0, null, x, false));\n        subject.push(x = new Intersection(p1, segment, null, false));\n        clip.push(x.o = new Intersection(p1, null, x, true));\n    });\n    if (!subject.length) return;\n    clip.sort(compareIntersection);\n    link(subject);\n    link(clip);\n    for(i = 0, n = clip.length; i < n; ++i){\n        clip[i].e = startInside = !startInside;\n    }\n    var start = subject[0], points, point;\n    while(1){\n        // Find first unvisited intersection.\n        var current = start, isSubject = true;\n        while(current.v)if ((current = current.n) === start) return;\n        points = current.z;\n        stream.lineStart();\n        do {\n            current.v = current.o.v = true;\n            if (current.e) {\n                if (isSubject) {\n                    for(i = 0, n = points.length; i < n; ++i)stream.point((point = points[i])[0], point[1]);\n                } else {\n                    interpolate(current.x, current.n.x, 1, stream);\n                }\n                current = current.n;\n            } else {\n                if (isSubject) {\n                    points = current.p.z;\n                    for(i = points.length - 1; i >= 0; --i)stream.point((point = points[i])[0], point[1]);\n                } else {\n                    interpolate(current.x, current.p.x, -1, stream);\n                }\n                current = current.p;\n            }\n            current = current.o;\n            points = current.z;\n            isSubject = !isSubject;\n        }while (!current.v);\n        stream.lineEnd();\n    }\n}\nfunction link(array) {\n    if (!(n = array.length)) return;\n    var n, i = 0, a = array[0], b;\n    while(++i < n){\n        a.n = b = array[i];\n        b.p = a;\n        a = b;\n    }\n    a.n = b = array[0];\n    b.p = a;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/clip/rejoin.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/compose.js":
/*!********************************************!*\
  !*** ./node_modules/d3-geo/src/compose.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n    function compose(x, y) {\n        return x = a(x, y), b(x[0], x[1]);\n    }\n    if (a.invert && b.invert) compose.invert = function(x, y) {\n        return x = b.invert(x, y), x && a.invert(x[0], x[1]);\n    };\n    return compose;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jb21wb3NlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2QkFBZSxvQ0FBU0EsQ0FBQyxFQUFFQyxDQUFDO0lBRTFCLFNBQVNDLFFBQVFDLENBQUMsRUFBRUMsQ0FBQztRQUNuQixPQUFPRCxJQUFJSCxFQUFFRyxHQUFHQyxJQUFJSCxFQUFFRSxDQUFDLENBQUMsRUFBRSxFQUFFQSxDQUFDLENBQUMsRUFBRTtJQUNsQztJQUVBLElBQUlILEVBQUVLLE1BQU0sSUFBSUosRUFBRUksTUFBTSxFQUFFSCxRQUFRRyxNQUFNLEdBQUcsU0FBU0YsQ0FBQyxFQUFFQyxDQUFDO1FBQ3RELE9BQU9ELElBQUlGLEVBQUVJLE1BQU0sQ0FBQ0YsR0FBR0MsSUFBSUQsS0FBS0gsRUFBRUssTUFBTSxDQUFDRixDQUFDLENBQUMsRUFBRSxFQUFFQSxDQUFDLENBQUMsRUFBRTtJQUNyRDtJQUVBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL2NvbXBvc2UuanM/YmRhYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihhLCBiKSB7XG5cbiAgZnVuY3Rpb24gY29tcG9zZSh4LCB5KSB7XG4gICAgcmV0dXJuIHggPSBhKHgsIHkpLCBiKHhbMF0sIHhbMV0pO1xuICB9XG5cbiAgaWYgKGEuaW52ZXJ0ICYmIGIuaW52ZXJ0KSBjb21wb3NlLmludmVydCA9IGZ1bmN0aW9uKHgsIHkpIHtcbiAgICByZXR1cm4geCA9IGIuaW52ZXJ0KHgsIHkpLCB4ICYmIGEuaW52ZXJ0KHhbMF0sIHhbMV0pO1xuICB9O1xuXG4gIHJldHVybiBjb21wb3NlO1xufVxuIl0sIm5hbWVzIjpbImEiLCJiIiwiY29tcG9zZSIsIngiLCJ5IiwiaW52ZXJ0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/compose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/constant.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-geo/src/constant.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n    return function() {\n        return x;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9jb25zdGFudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkJBQWUsb0NBQVNBLENBQUM7SUFDdkIsT0FBTztRQUNMLE9BQU9BO0lBQ1Q7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvY29uc3RhbnQuanM/MTU5YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbih4KSB7XG4gIHJldHVybiBmdW5jdGlvbigpIHtcbiAgICByZXR1cm4geDtcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJ4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/constant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/contains.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-geo/src/contains.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _polygonContains_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./polygonContains.js */ \"(ssr)/./node_modules/d3-geo/src/polygonContains.js\");\n/* harmony import */ var _distance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./distance.js */ \"(ssr)/./node_modules/d3-geo/src/distance.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n\nvar containsObjectType = {\n    Feature: function(object, point) {\n        return containsGeometry(object.geometry, point);\n    },\n    FeatureCollection: function(object, point) {\n        var features = object.features, i = -1, n = features.length;\n        while(++i < n)if (containsGeometry(features[i].geometry, point)) return true;\n        return false;\n    }\n};\nvar containsGeometryType = {\n    Sphere: function() {\n        return true;\n    },\n    Point: function(object, point) {\n        return containsPoint(object.coordinates, point);\n    },\n    MultiPoint: function(object, point) {\n        var coordinates = object.coordinates, i = -1, n = coordinates.length;\n        while(++i < n)if (containsPoint(coordinates[i], point)) return true;\n        return false;\n    },\n    LineString: function(object, point) {\n        return containsLine(object.coordinates, point);\n    },\n    MultiLineString: function(object, point) {\n        var coordinates = object.coordinates, i = -1, n = coordinates.length;\n        while(++i < n)if (containsLine(coordinates[i], point)) return true;\n        return false;\n    },\n    Polygon: function(object, point) {\n        return containsPolygon(object.coordinates, point);\n    },\n    MultiPolygon: function(object, point) {\n        var coordinates = object.coordinates, i = -1, n = coordinates.length;\n        while(++i < n)if (containsPolygon(coordinates[i], point)) return true;\n        return false;\n    },\n    GeometryCollection: function(object, point) {\n        var geometries = object.geometries, i = -1, n = geometries.length;\n        while(++i < n)if (containsGeometry(geometries[i], point)) return true;\n        return false;\n    }\n};\nfunction containsGeometry(geometry, point) {\n    return geometry && containsGeometryType.hasOwnProperty(geometry.type) ? containsGeometryType[geometry.type](geometry, point) : false;\n}\nfunction containsPoint(coordinates, point) {\n    return (0,_distance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(coordinates, point) === 0;\n}\nfunction containsLine(coordinates, point) {\n    var ao, bo, ab;\n    for(var i = 0, n = coordinates.length; i < n; i++){\n        bo = (0,_distance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(coordinates[i], point);\n        if (bo === 0) return true;\n        if (i > 0) {\n            ab = (0,_distance_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(coordinates[i], coordinates[i - 1]);\n            if (ab > 0 && ao <= ab && bo <= ab && (ao + bo - ab) * (1 - Math.pow((ao - bo) / ab, 2)) < _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon2 * ab) return true;\n        }\n        ao = bo;\n    }\n    return false;\n}\nfunction containsPolygon(coordinates, point) {\n    return !!(0,_polygonContains_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(coordinates.map(ringRadians), pointRadians(point));\n}\nfunction ringRadians(ring) {\n    return ring = ring.map(pointRadians), ring.pop(), ring;\n}\nfunction pointRadians(point) {\n    return [\n        point[0] * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians,\n        point[1] * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians\n    ];\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object, point) {\n    return (object && containsObjectType.hasOwnProperty(object.type) ? containsObjectType[object.type] : containsGeometry)(object, point);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/contains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/distance.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-geo/src/distance.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _length_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./length.js */ \"(ssr)/./node_modules/d3-geo/src/length.js\");\n\nvar coordinates = [\n    null,\n    null\n], object = {\n    type: \"LineString\",\n    coordinates: coordinates\n};\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n    coordinates[0] = a;\n    coordinates[1] = b;\n    return (0,_length_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9kaXN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpQztBQUVqQyxJQUFJQyxjQUFjO0lBQUM7SUFBTTtDQUFLLEVBQzFCQyxTQUFTO0lBQUNDLE1BQU07SUFBY0YsYUFBYUE7QUFBVztBQUUxRCw2QkFBZSxvQ0FBU0csQ0FBQyxFQUFFQyxDQUFDO0lBQzFCSixXQUFXLENBQUMsRUFBRSxHQUFHRztJQUNqQkgsV0FBVyxDQUFDLEVBQUUsR0FBR0k7SUFDakIsT0FBT0wsc0RBQU1BLENBQUNFO0FBQ2hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9kaXN0YW5jZS5qcz8wZDQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBsZW5ndGggZnJvbSBcIi4vbGVuZ3RoLmpzXCI7XG5cbnZhciBjb29yZGluYXRlcyA9IFtudWxsLCBudWxsXSxcbiAgICBvYmplY3QgPSB7dHlwZTogXCJMaW5lU3RyaW5nXCIsIGNvb3JkaW5hdGVzOiBjb29yZGluYXRlc307XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKGEsIGIpIHtcbiAgY29vcmRpbmF0ZXNbMF0gPSBhO1xuICBjb29yZGluYXRlc1sxXSA9IGI7XG4gIHJldHVybiBsZW5ndGgob2JqZWN0KTtcbn1cbiJdLCJuYW1lcyI6WyJsZW5ndGgiLCJjb29yZGluYXRlcyIsIm9iamVjdCIsInR5cGUiLCJhIiwiYiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/distance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/graticule.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-geo/src/graticule.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ graticule),\n/* harmony export */   graticule10: () => (/* binding */ graticule10)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/range.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\nfunction graticuleX(y0, y1, dy) {\n    var y = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(y0, y1 - _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon, dy).concat(y1);\n    return function(x) {\n        return y.map(function(y) {\n            return [\n                x,\n                y\n            ];\n        });\n    };\n}\nfunction graticuleY(x0, x1, dx) {\n    var x = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(x0, x1 - _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon, dx).concat(x1);\n    return function(y) {\n        return x.map(function(x) {\n            return [\n                x,\n                y\n            ];\n        });\n    };\n}\nfunction graticule() {\n    var x1, x0, X1, X0, y1, y0, Y1, Y0, dx = 10, dy = dx, DX = 90, DY = 360, x, y, X, Y, precision = 2.5;\n    function graticule() {\n        return {\n            type: \"MultiLineString\",\n            coordinates: lines()\n        };\n    }\n    function lines() {\n        return (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.ceil)(X0 / DX) * DX, X1, DX).map(X).concat((0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.ceil)(Y0 / DY) * DY, Y1, DY).map(Y)).concat((0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.ceil)(x0 / dx) * dx, x1, dx).filter(function(x) {\n            return (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(x % DX) > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon;\n        }).map(x)).concat((0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.ceil)(y0 / dy) * dy, y1, dy).filter(function(y) {\n            return (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(y % DY) > _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon;\n        }).map(y));\n    }\n    graticule.lines = function() {\n        return lines().map(function(coordinates) {\n            return {\n                type: \"LineString\",\n                coordinates: coordinates\n            };\n        });\n    };\n    graticule.outline = function() {\n        return {\n            type: \"Polygon\",\n            coordinates: [\n                X(X0).concat(Y(Y1).slice(1), X(X1).reverse().slice(1), Y(Y0).reverse().slice(1))\n            ]\n        };\n    };\n    graticule.extent = function(_) {\n        if (!arguments.length) return graticule.extentMinor();\n        return graticule.extentMajor(_).extentMinor(_);\n    };\n    graticule.extentMajor = function(_) {\n        if (!arguments.length) return [\n            [\n                X0,\n                Y0\n            ],\n            [\n                X1,\n                Y1\n            ]\n        ];\n        X0 = +_[0][0], X1 = +_[1][0];\n        Y0 = +_[0][1], Y1 = +_[1][1];\n        if (X0 > X1) _ = X0, X0 = X1, X1 = _;\n        if (Y0 > Y1) _ = Y0, Y0 = Y1, Y1 = _;\n        return graticule.precision(precision);\n    };\n    graticule.extentMinor = function(_) {\n        if (!arguments.length) return [\n            [\n                x0,\n                y0\n            ],\n            [\n                x1,\n                y1\n            ]\n        ];\n        x0 = +_[0][0], x1 = +_[1][0];\n        y0 = +_[0][1], y1 = +_[1][1];\n        if (x0 > x1) _ = x0, x0 = x1, x1 = _;\n        if (y0 > y1) _ = y0, y0 = y1, y1 = _;\n        return graticule.precision(precision);\n    };\n    graticule.step = function(_) {\n        if (!arguments.length) return graticule.stepMinor();\n        return graticule.stepMajor(_).stepMinor(_);\n    };\n    graticule.stepMajor = function(_) {\n        if (!arguments.length) return [\n            DX,\n            DY\n        ];\n        DX = +_[0], DY = +_[1];\n        return graticule;\n    };\n    graticule.stepMinor = function(_) {\n        if (!arguments.length) return [\n            dx,\n            dy\n        ];\n        dx = +_[0], dy = +_[1];\n        return graticule;\n    };\n    graticule.precision = function(_) {\n        if (!arguments.length) return precision;\n        precision = +_;\n        x = graticuleX(y0, y1, 90);\n        y = graticuleY(x0, x1, precision);\n        X = graticuleX(Y0, Y1, 90);\n        Y = graticuleY(X0, X1, precision);\n        return graticule;\n    };\n    return graticule.extentMajor([\n        [\n            -180,\n            -90 + _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon\n        ],\n        [\n            180,\n            90 - _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon\n        ]\n    ]).extentMinor([\n        [\n            -180,\n            -80 - _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon\n        ],\n        [\n            180,\n            80 + _math_js__WEBPACK_IMPORTED_MODULE_1__.epsilon\n        ]\n    ]);\n}\nfunction graticule10() {\n    return graticule()();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/graticule.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/identity.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-geo/src/identity.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((x)=>x);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9pZGVudGl0eS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWVBLENBQUFBLElBQUtBLENBQUFBLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL2lkZW50aXR5LmpzPzE2YWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgeCA9PiB4O1xuIl0sIm5hbWVzIjpbIngiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/index.js":
/*!******************************************!*\
  !*** ./node_modules/d3-geo/src/index.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   geoAlbers: () => (/* reexport safe */ _projection_albers_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   geoAlbersUsa: () => (/* reexport safe */ _projection_albersUsa_js__WEBPACK_IMPORTED_MODULE_15__[\"default\"]),\n/* harmony export */   geoArea: () => (/* reexport safe */ _area_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   geoAzimuthalEqualArea: () => (/* reexport safe */ _projection_azimuthalEqualArea_js__WEBPACK_IMPORTED_MODULE_16__[\"default\"]),\n/* harmony export */   geoAzimuthalEqualAreaRaw: () => (/* reexport safe */ _projection_azimuthalEqualArea_js__WEBPACK_IMPORTED_MODULE_16__.azimuthalEqualAreaRaw),\n/* harmony export */   geoAzimuthalEquidistant: () => (/* reexport safe */ _projection_azimuthalEquidistant_js__WEBPACK_IMPORTED_MODULE_17__[\"default\"]),\n/* harmony export */   geoAzimuthalEquidistantRaw: () => (/* reexport safe */ _projection_azimuthalEquidistant_js__WEBPACK_IMPORTED_MODULE_17__.azimuthalEquidistantRaw),\n/* harmony export */   geoBounds: () => (/* reexport safe */ _bounds_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   geoCentroid: () => (/* reexport safe */ _centroid_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   geoCircle: () => (/* reexport safe */ _circle_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   geoClipAntimeridian: () => (/* reexport safe */ _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   geoClipCircle: () => (/* reexport safe */ _clip_circle_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   geoClipExtent: () => (/* reexport safe */ _clip_extent_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   geoClipRectangle: () => (/* reexport safe */ _clip_rectangle_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   geoConicConformal: () => (/* reexport safe */ _projection_conicConformal_js__WEBPACK_IMPORTED_MODULE_18__[\"default\"]),\n/* harmony export */   geoConicConformalRaw: () => (/* reexport safe */ _projection_conicConformal_js__WEBPACK_IMPORTED_MODULE_18__.conicConformalRaw),\n/* harmony export */   geoConicEqualArea: () => (/* reexport safe */ _projection_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_19__[\"default\"]),\n/* harmony export */   geoConicEqualAreaRaw: () => (/* reexport safe */ _projection_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_19__.conicEqualAreaRaw),\n/* harmony export */   geoConicEquidistant: () => (/* reexport safe */ _projection_conicEquidistant_js__WEBPACK_IMPORTED_MODULE_20__[\"default\"]),\n/* harmony export */   geoConicEquidistantRaw: () => (/* reexport safe */ _projection_conicEquidistant_js__WEBPACK_IMPORTED_MODULE_20__.conicEquidistantRaw),\n/* harmony export */   geoContains: () => (/* reexport safe */ _contains_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   geoDistance: () => (/* reexport safe */ _distance_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   geoEqualEarth: () => (/* reexport safe */ _projection_equalEarth_js__WEBPACK_IMPORTED_MODULE_21__[\"default\"]),\n/* harmony export */   geoEqualEarthRaw: () => (/* reexport safe */ _projection_equalEarth_js__WEBPACK_IMPORTED_MODULE_21__.equalEarthRaw),\n/* harmony export */   geoEquirectangular: () => (/* reexport safe */ _projection_equirectangular_js__WEBPACK_IMPORTED_MODULE_22__[\"default\"]),\n/* harmony export */   geoEquirectangularRaw: () => (/* reexport safe */ _projection_equirectangular_js__WEBPACK_IMPORTED_MODULE_22__.equirectangularRaw),\n/* harmony export */   geoGnomonic: () => (/* reexport safe */ _projection_gnomonic_js__WEBPACK_IMPORTED_MODULE_23__[\"default\"]),\n/* harmony export */   geoGnomonicRaw: () => (/* reexport safe */ _projection_gnomonic_js__WEBPACK_IMPORTED_MODULE_23__.gnomonicRaw),\n/* harmony export */   geoGraticule: () => (/* reexport safe */ _graticule_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   geoGraticule10: () => (/* reexport safe */ _graticule_js__WEBPACK_IMPORTED_MODULE_10__.graticule10),\n/* harmony export */   geoIdentity: () => (/* reexport safe */ _projection_identity_js__WEBPACK_IMPORTED_MODULE_24__[\"default\"]),\n/* harmony export */   geoInterpolate: () => (/* reexport safe */ _interpolate_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   geoLength: () => (/* reexport safe */ _length_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   geoMercator: () => (/* reexport safe */ _projection_mercator_js__WEBPACK_IMPORTED_MODULE_26__[\"default\"]),\n/* harmony export */   geoMercatorRaw: () => (/* reexport safe */ _projection_mercator_js__WEBPACK_IMPORTED_MODULE_26__.mercatorRaw),\n/* harmony export */   geoNaturalEarth1: () => (/* reexport safe */ _projection_naturalEarth1_js__WEBPACK_IMPORTED_MODULE_27__[\"default\"]),\n/* harmony export */   geoNaturalEarth1Raw: () => (/* reexport safe */ _projection_naturalEarth1_js__WEBPACK_IMPORTED_MODULE_27__.naturalEarth1Raw),\n/* harmony export */   geoOrthographic: () => (/* reexport safe */ _projection_orthographic_js__WEBPACK_IMPORTED_MODULE_28__[\"default\"]),\n/* harmony export */   geoOrthographicRaw: () => (/* reexport safe */ _projection_orthographic_js__WEBPACK_IMPORTED_MODULE_28__.orthographicRaw),\n/* harmony export */   geoPath: () => (/* reexport safe */ _path_index_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   geoProjection: () => (/* reexport safe */ _projection_index_js__WEBPACK_IMPORTED_MODULE_25__[\"default\"]),\n/* harmony export */   geoProjectionMutator: () => (/* reexport safe */ _projection_index_js__WEBPACK_IMPORTED_MODULE_25__.projectionMutator),\n/* harmony export */   geoRotation: () => (/* reexport safe */ _rotation_js__WEBPACK_IMPORTED_MODULE_31__[\"default\"]),\n/* harmony export */   geoStereographic: () => (/* reexport safe */ _projection_stereographic_js__WEBPACK_IMPORTED_MODULE_29__[\"default\"]),\n/* harmony export */   geoStereographicRaw: () => (/* reexport safe */ _projection_stereographic_js__WEBPACK_IMPORTED_MODULE_29__.stereographicRaw),\n/* harmony export */   geoStream: () => (/* reexport safe */ _stream_js__WEBPACK_IMPORTED_MODULE_32__[\"default\"]),\n/* harmony export */   geoTransform: () => (/* reexport safe */ _transform_js__WEBPACK_IMPORTED_MODULE_33__[\"default\"]),\n/* harmony export */   geoTransverseMercator: () => (/* reexport safe */ _projection_transverseMercator_js__WEBPACK_IMPORTED_MODULE_30__[\"default\"]),\n/* harmony export */   geoTransverseMercatorRaw: () => (/* reexport safe */ _projection_transverseMercator_js__WEBPACK_IMPORTED_MODULE_30__.transverseMercatorRaw)\n/* harmony export */ });\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-geo/src/area.js\");\n/* harmony import */ var _bounds_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./bounds.js */ \"(ssr)/./node_modules/d3-geo/src/bounds.js\");\n/* harmony import */ var _centroid_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./centroid.js */ \"(ssr)/./node_modules/d3-geo/src/centroid.js\");\n/* harmony import */ var _circle_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./circle.js */ \"(ssr)/./node_modules/d3-geo/src/circle.js\");\n/* harmony import */ var _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./clip/antimeridian.js */ \"(ssr)/./node_modules/d3-geo/src/clip/antimeridian.js\");\n/* harmony import */ var _clip_circle_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./clip/circle.js */ \"(ssr)/./node_modules/d3-geo/src/clip/circle.js\");\n/* harmony import */ var _clip_extent_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./clip/extent.js */ \"(ssr)/./node_modules/d3-geo/src/clip/extent.js\");\n/* harmony import */ var _clip_rectangle_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./clip/rectangle.js */ \"(ssr)/./node_modules/d3-geo/src/clip/rectangle.js\");\n/* harmony import */ var _contains_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./contains.js */ \"(ssr)/./node_modules/d3-geo/src/contains.js\");\n/* harmony import */ var _distance_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./distance.js */ \"(ssr)/./node_modules/d3-geo/src/distance.js\");\n/* harmony import */ var _graticule_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./graticule.js */ \"(ssr)/./node_modules/d3-geo/src/graticule.js\");\n/* harmony import */ var _interpolate_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./interpolate.js */ \"(ssr)/./node_modules/d3-geo/src/interpolate.js\");\n/* harmony import */ var _length_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./length.js */ \"(ssr)/./node_modules/d3-geo/src/length.js\");\n/* harmony import */ var _path_index_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./path/index.js */ \"(ssr)/./node_modules/d3-geo/src/path/index.js\");\n/* harmony import */ var _projection_albers_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./projection/albers.js */ \"(ssr)/./node_modules/d3-geo/src/projection/albers.js\");\n/* harmony import */ var _projection_albersUsa_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./projection/albersUsa.js */ \"(ssr)/./node_modules/d3-geo/src/projection/albersUsa.js\");\n/* harmony import */ var _projection_azimuthalEqualArea_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./projection/azimuthalEqualArea.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthalEqualArea.js\");\n/* harmony import */ var _projection_azimuthalEquidistant_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./projection/azimuthalEquidistant.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthalEquidistant.js\");\n/* harmony import */ var _projection_conicConformal_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./projection/conicConformal.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conicConformal.js\");\n/* harmony import */ var _projection_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./projection/conicEqualArea.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conicEqualArea.js\");\n/* harmony import */ var _projection_conicEquidistant_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./projection/conicEquidistant.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conicEquidistant.js\");\n/* harmony import */ var _projection_equalEarth_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./projection/equalEarth.js */ \"(ssr)/./node_modules/d3-geo/src/projection/equalEarth.js\");\n/* harmony import */ var _projection_equirectangular_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./projection/equirectangular.js */ \"(ssr)/./node_modules/d3-geo/src/projection/equirectangular.js\");\n/* harmony import */ var _projection_gnomonic_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./projection/gnomonic.js */ \"(ssr)/./node_modules/d3-geo/src/projection/gnomonic.js\");\n/* harmony import */ var _projection_identity_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./projection/identity.js */ \"(ssr)/./node_modules/d3-geo/src/projection/identity.js\");\n/* harmony import */ var _projection_index_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./projection/index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n/* harmony import */ var _projection_mercator_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./projection/mercator.js */ \"(ssr)/./node_modules/d3-geo/src/projection/mercator.js\");\n/* harmony import */ var _projection_naturalEarth1_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./projection/naturalEarth1.js */ \"(ssr)/./node_modules/d3-geo/src/projection/naturalEarth1.js\");\n/* harmony import */ var _projection_orthographic_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./projection/orthographic.js */ \"(ssr)/./node_modules/d3-geo/src/projection/orthographic.js\");\n/* harmony import */ var _projection_stereographic_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./projection/stereographic.js */ \"(ssr)/./node_modules/d3-geo/src/projection/stereographic.js\");\n/* harmony import */ var _projection_transverseMercator_js__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./projection/transverseMercator.js */ \"(ssr)/./node_modules/d3-geo/src/projection/transverseMercator.js\");\n/* harmony import */ var _rotation_js__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./rotation.js */ \"(ssr)/./node_modules/d3-geo/src/rotation.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n/* harmony import */ var _transform_js__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ./transform.js */ \"(ssr)/./node_modules/d3-geo/src/transform.js\");\n\n\n\n\n\n\n // DEPRECATED! Use d3.geoIdentity().clipExtent(…).\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/interpolate.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/interpolate.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n    var x0 = a[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, y0 = a[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, x1 = b[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, y1 = b[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, cy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y0), sy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y0), cy1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y1), sy1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y1), kx0 = cy0 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x0), ky0 = cy0 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x0), kx1 = cy1 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x1), ky1 = cy1 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x1), d = 2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.haversin)(y1 - y0) + cy0 * cy1 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.haversin)(x1 - x0))), k = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(d);\n    var interpolate = d ? function(t) {\n        var B = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(t *= d) / k, A = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(d - t) / k, x = A * kx0 + B * kx1, y = A * ky0 + B * ky1, z = A * sy0 + B * sy1;\n        return [\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(y, x) * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees,\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(z, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(x * x + y * y)) * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees\n        ];\n    } : function() {\n        return [\n            x0 * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees,\n            y0 * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees\n        ];\n    };\n    interpolate.distance = d;\n    return interpolate;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/interpolate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/length.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-geo/src/length.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n\n\n\n\nvar lengthSum, lambda0, sinPhi0, cosPhi0;\nvar lengthStream = {\n    sphere: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    point: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    lineStart: lengthLineStart,\n    lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    polygonStart: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    polygonEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]\n};\nfunction lengthLineStart() {\n    lengthStream.point = lengthPointFirst;\n    lengthStream.lineEnd = lengthLineEnd;\n}\nfunction lengthLineEnd() {\n    lengthStream.point = lengthStream.lineEnd = _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n}\nfunction lengthPointFirst(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    lambda0 = lambda, sinPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi), cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi);\n    lengthStream.point = lengthPoint;\n}\nfunction lengthPoint(lambda, phi) {\n    lambda *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi *= _math_js__WEBPACK_IMPORTED_MODULE_1__.radians;\n    var sinPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(phi), cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(phi), delta = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.abs)(lambda - lambda0), cosDelta = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(delta), sinDelta = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(delta), x = cosPhi * sinDelta, y = cosPhi0 * sinPhi - sinPhi0 * cosPhi * cosDelta, z = sinPhi0 * sinPhi + cosPhi0 * cosPhi * cosDelta;\n    lengthSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.atan2)((0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sqrt)(x * x + y * y), z));\n    lambda0 = lambda, sinPhi0 = sinPhi, cosPhi0 = cosPhi;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object) {\n    lengthSum = new d3_array__WEBPACK_IMPORTED_MODULE_2__.Adder();\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(object, lengthStream);\n    return +lengthSum;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/length.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/math.js":
/*!*****************************************!*\
  !*** ./node_modules/d3-geo/src/math.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   abs: () => (/* binding */ abs),\n/* harmony export */   acos: () => (/* binding */ acos),\n/* harmony export */   asin: () => (/* binding */ asin),\n/* harmony export */   atan: () => (/* binding */ atan),\n/* harmony export */   atan2: () => (/* binding */ atan2),\n/* harmony export */   ceil: () => (/* binding */ ceil),\n/* harmony export */   cos: () => (/* binding */ cos),\n/* harmony export */   degrees: () => (/* binding */ degrees),\n/* harmony export */   epsilon: () => (/* binding */ epsilon),\n/* harmony export */   epsilon2: () => (/* binding */ epsilon2),\n/* harmony export */   exp: () => (/* binding */ exp),\n/* harmony export */   floor: () => (/* binding */ floor),\n/* harmony export */   halfPi: () => (/* binding */ halfPi),\n/* harmony export */   haversin: () => (/* binding */ haversin),\n/* harmony export */   hypot: () => (/* binding */ hypot),\n/* harmony export */   log: () => (/* binding */ log),\n/* harmony export */   pi: () => (/* binding */ pi),\n/* harmony export */   pow: () => (/* binding */ pow),\n/* harmony export */   quarterPi: () => (/* binding */ quarterPi),\n/* harmony export */   radians: () => (/* binding */ radians),\n/* harmony export */   sign: () => (/* binding */ sign),\n/* harmony export */   sin: () => (/* binding */ sin),\n/* harmony export */   sqrt: () => (/* binding */ sqrt),\n/* harmony export */   tan: () => (/* binding */ tan),\n/* harmony export */   tau: () => (/* binding */ tau)\n/* harmony export */ });\nvar epsilon = 1e-6;\nvar epsilon2 = 1e-12;\nvar pi = Math.PI;\nvar halfPi = pi / 2;\nvar quarterPi = pi / 4;\nvar tau = pi * 2;\nvar degrees = 180 / pi;\nvar radians = pi / 180;\nvar abs = Math.abs;\nvar atan = Math.atan;\nvar atan2 = Math.atan2;\nvar cos = Math.cos;\nvar ceil = Math.ceil;\nvar exp = Math.exp;\nvar floor = Math.floor;\nvar hypot = Math.hypot;\nvar log = Math.log;\nvar pow = Math.pow;\nvar sin = Math.sin;\nvar sign = Math.sign || function(x) {\n    return x > 0 ? 1 : x < 0 ? -1 : 0;\n};\nvar sqrt = Math.sqrt;\nvar tan = Math.tan;\nfunction acos(x) {\n    return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\nfunction asin(x) {\n    return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\nfunction haversin(x) {\n    return (x = sin(x / 2)) * x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/math.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/noop.js":
/*!*****************************************!*\
  !*** ./node_modules/d3-geo/src/noop.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ noop)\n/* harmony export */ });\nfunction noop() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9ub29wLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxRQUFRIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9ub29wLmpzPzBiYTgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbm9vcCgpIHt9XG4iXSwibmFtZXMiOlsibm9vcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/noop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/area.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-geo/src/path/area.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n\n\n\nvar areaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder(), areaRingSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder(), x00, y00, x0, y0;\nvar areaStream = {\n    point: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    lineStart: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    polygonStart: function() {\n        areaStream.lineStart = areaRingStart;\n        areaStream.lineEnd = areaRingEnd;\n    },\n    polygonEnd: function() {\n        areaStream.lineStart = areaStream.lineEnd = areaStream.point = _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        areaSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.abs)(areaRingSum));\n        areaRingSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n    },\n    result: function() {\n        var area = areaSum / 2;\n        areaSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n        return area;\n    }\n};\nfunction areaRingStart() {\n    areaStream.point = areaPointFirst;\n}\nfunction areaPointFirst(x, y) {\n    areaStream.point = areaPoint;\n    x00 = x0 = x, y00 = y0 = y;\n}\nfunction areaPoint(x, y) {\n    areaRingSum.add(y0 * x - x0 * y);\n    x0 = x, y0 = y;\n}\nfunction areaRingEnd() {\n    areaPoint(x00, y00);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (areaStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/area.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/bounds.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/path/bounds.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n\nvar x0 = Infinity, y0 = x0, x1 = -x0, y1 = x1;\nvar boundsStream = {\n    point: boundsPoint,\n    lineStart: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    lineEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    polygonStart: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    polygonEnd: _noop_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    result: function() {\n        var bounds = [\n            [\n                x0,\n                y0\n            ],\n            [\n                x1,\n                y1\n            ]\n        ];\n        x1 = y1 = -(y0 = x0 = Infinity);\n        return bounds;\n    }\n};\nfunction boundsPoint(x, y) {\n    if (x < x0) x0 = x;\n    if (x > x1) x1 = x;\n    if (y < y0) y0 = y;\n    if (y > y1) y1 = y;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (boundsStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wYXRoL2JvdW5kcy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QjtBQUU5QixJQUFJQyxLQUFLQyxVQUNMQyxLQUFLRixJQUNMRyxLQUFLLENBQUNILElBQ05JLEtBQUtEO0FBRVQsSUFBSUUsZUFBZTtJQUNqQkMsT0FBT0M7SUFDUEMsV0FBV1QsZ0RBQUlBO0lBQ2ZVLFNBQVNWLGdEQUFJQTtJQUNiVyxjQUFjWCxnREFBSUE7SUFDbEJZLFlBQVlaLGdEQUFJQTtJQUNoQmEsUUFBUTtRQUNOLElBQUlDLFNBQVM7WUFBQztnQkFBQ2I7Z0JBQUlFO2FBQUc7WUFBRTtnQkFBQ0M7Z0JBQUlDO2FBQUc7U0FBQztRQUNqQ0QsS0FBS0MsS0FBSyxDQUFFRixDQUFBQSxLQUFLRixLQUFLQyxRQUFPO1FBQzdCLE9BQU9ZO0lBQ1Q7QUFDRjtBQUVBLFNBQVNOLFlBQVlPLENBQUMsRUFBRUMsQ0FBQztJQUN2QixJQUFJRCxJQUFJZCxJQUFJQSxLQUFLYztJQUNqQixJQUFJQSxJQUFJWCxJQUFJQSxLQUFLVztJQUNqQixJQUFJQyxJQUFJYixJQUFJQSxLQUFLYTtJQUNqQixJQUFJQSxJQUFJWCxJQUFJQSxLQUFLVztBQUNuQjtBQUVBLGlFQUFlVixZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wYXRoL2JvdW5kcy5qcz9mZDIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBub29wIGZyb20gXCIuLi9ub29wLmpzXCI7XG5cbnZhciB4MCA9IEluZmluaXR5LFxuICAgIHkwID0geDAsXG4gICAgeDEgPSAteDAsXG4gICAgeTEgPSB4MTtcblxudmFyIGJvdW5kc1N0cmVhbSA9IHtcbiAgcG9pbnQ6IGJvdW5kc1BvaW50LFxuICBsaW5lU3RhcnQ6IG5vb3AsXG4gIGxpbmVFbmQ6IG5vb3AsXG4gIHBvbHlnb25TdGFydDogbm9vcCxcbiAgcG9seWdvbkVuZDogbm9vcCxcbiAgcmVzdWx0OiBmdW5jdGlvbigpIHtcbiAgICB2YXIgYm91bmRzID0gW1t4MCwgeTBdLCBbeDEsIHkxXV07XG4gICAgeDEgPSB5MSA9IC0oeTAgPSB4MCA9IEluZmluaXR5KTtcbiAgICByZXR1cm4gYm91bmRzO1xuICB9XG59O1xuXG5mdW5jdGlvbiBib3VuZHNQb2ludCh4LCB5KSB7XG4gIGlmICh4IDwgeDApIHgwID0geDtcbiAgaWYgKHggPiB4MSkgeDEgPSB4O1xuICBpZiAoeSA8IHkwKSB5MCA9IHk7XG4gIGlmICh5ID4geTEpIHkxID0geTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgYm91bmRzU3RyZWFtO1xuIl0sIm5hbWVzIjpbIm5vb3AiLCJ4MCIsIkluZmluaXR5IiwieTAiLCJ4MSIsInkxIiwiYm91bmRzU3RyZWFtIiwicG9pbnQiLCJib3VuZHNQb2ludCIsImxpbmVTdGFydCIsImxpbmVFbmQiLCJwb2x5Z29uU3RhcnQiLCJwb2x5Z29uRW5kIiwicmVzdWx0IiwiYm91bmRzIiwieCIsInkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/bounds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/centroid.js":
/*!**************************************************!*\
  !*** ./node_modules/d3-geo/src/path/centroid.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n// TODO Enforce positive area for exterior, negative area for interior?\nvar X0 = 0, Y0 = 0, Z0 = 0, X1 = 0, Y1 = 0, Z1 = 0, X2 = 0, Y2 = 0, Z2 = 0, x00, y00, x0, y0;\nvar centroidStream = {\n    point: centroidPoint,\n    lineStart: centroidLineStart,\n    lineEnd: centroidLineEnd,\n    polygonStart: function() {\n        centroidStream.lineStart = centroidRingStart;\n        centroidStream.lineEnd = centroidRingEnd;\n    },\n    polygonEnd: function() {\n        centroidStream.point = centroidPoint;\n        centroidStream.lineStart = centroidLineStart;\n        centroidStream.lineEnd = centroidLineEnd;\n    },\n    result: function() {\n        var centroid = Z2 ? [\n            X2 / Z2,\n            Y2 / Z2\n        ] : Z1 ? [\n            X1 / Z1,\n            Y1 / Z1\n        ] : Z0 ? [\n            X0 / Z0,\n            Y0 / Z0\n        ] : [\n            NaN,\n            NaN\n        ];\n        X0 = Y0 = Z0 = X1 = Y1 = Z1 = X2 = Y2 = Z2 = 0;\n        return centroid;\n    }\n};\nfunction centroidPoint(x, y) {\n    X0 += x;\n    Y0 += y;\n    ++Z0;\n}\nfunction centroidLineStart() {\n    centroidStream.point = centroidPointFirstLine;\n}\nfunction centroidPointFirstLine(x, y) {\n    centroidStream.point = centroidPointLine;\n    centroidPoint(x0 = x, y0 = y);\n}\nfunction centroidPointLine(x, y) {\n    var dx = x - x0, dy = y - y0, z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(dx * dx + dy * dy);\n    X1 += z * (x0 + x) / 2;\n    Y1 += z * (y0 + y) / 2;\n    Z1 += z;\n    centroidPoint(x0 = x, y0 = y);\n}\nfunction centroidLineEnd() {\n    centroidStream.point = centroidPoint;\n}\nfunction centroidRingStart() {\n    centroidStream.point = centroidPointFirstRing;\n}\nfunction centroidRingEnd() {\n    centroidPointRing(x00, y00);\n}\nfunction centroidPointFirstRing(x, y) {\n    centroidStream.point = centroidPointRing;\n    centroidPoint(x00 = x0 = x, y00 = y0 = y);\n}\nfunction centroidPointRing(x, y) {\n    var dx = x - x0, dy = y - y0, z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(dx * dx + dy * dy);\n    X1 += z * (x0 + x) / 2;\n    Y1 += z * (y0 + y) / 2;\n    Z1 += z;\n    z = y0 * x - x0 * y;\n    X2 += z * (x0 + x);\n    Y2 += z * (y0 + y);\n    Z2 += z * 3;\n    centroidPoint(x0 = x, y0 = y);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (centroidStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/centroid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/context.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-geo/src/path/context.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PathContext)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n\n\nfunction PathContext(context) {\n    this._context = context;\n}\nPathContext.prototype = {\n    _radius: 4.5,\n    pointRadius: function(_) {\n        return this._radius = _, this;\n    },\n    polygonStart: function() {\n        this._line = 0;\n    },\n    polygonEnd: function() {\n        this._line = NaN;\n    },\n    lineStart: function() {\n        this._point = 0;\n    },\n    lineEnd: function() {\n        if (this._line === 0) this._context.closePath();\n        this._point = NaN;\n    },\n    point: function(x, y) {\n        switch(this._point){\n            case 0:\n                {\n                    this._context.moveTo(x, y);\n                    this._point = 1;\n                    break;\n                }\n            case 1:\n                {\n                    this._context.lineTo(x, y);\n                    break;\n                }\n            default:\n                {\n                    this._context.moveTo(x + this._radius, y);\n                    this._context.arc(x, y, this._radius, 0, _math_js__WEBPACK_IMPORTED_MODULE_0__.tau);\n                    break;\n                }\n        }\n    },\n    result: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wYXRoL2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStCO0FBQ0Q7QUFFZixTQUFTRSxZQUFZQyxPQUFPO0lBQ3pDLElBQUksQ0FBQ0MsUUFBUSxHQUFHRDtBQUNsQjtBQUVBRCxZQUFZRyxTQUFTLEdBQUc7SUFDdEJDLFNBQVM7SUFDVEMsYUFBYSxTQUFTQyxDQUFDO1FBQ3JCLE9BQU8sSUFBSSxDQUFDRixPQUFPLEdBQUdFLEdBQUcsSUFBSTtJQUMvQjtJQUNBQyxjQUFjO1FBQ1osSUFBSSxDQUFDQyxLQUFLLEdBQUc7SUFDZjtJQUNBQyxZQUFZO1FBQ1YsSUFBSSxDQUFDRCxLQUFLLEdBQUdFO0lBQ2Y7SUFDQUMsV0FBVztRQUNULElBQUksQ0FBQ0MsTUFBTSxHQUFHO0lBQ2hCO0lBQ0FDLFNBQVM7UUFDUCxJQUFJLElBQUksQ0FBQ0wsS0FBSyxLQUFLLEdBQUcsSUFBSSxDQUFDTixRQUFRLENBQUNZLFNBQVM7UUFDN0MsSUFBSSxDQUFDRixNQUFNLEdBQUdGO0lBQ2hCO0lBQ0FLLE9BQU8sU0FBU0MsQ0FBQyxFQUFFQyxDQUFDO1FBQ2xCLE9BQVEsSUFBSSxDQUFDTCxNQUFNO1lBQ2pCLEtBQUs7Z0JBQUc7b0JBQ04sSUFBSSxDQUFDVixRQUFRLENBQUNnQixNQUFNLENBQUNGLEdBQUdDO29CQUN4QixJQUFJLENBQUNMLE1BQU0sR0FBRztvQkFDZDtnQkFDRjtZQUNBLEtBQUs7Z0JBQUc7b0JBQ04sSUFBSSxDQUFDVixRQUFRLENBQUNpQixNQUFNLENBQUNILEdBQUdDO29CQUN4QjtnQkFDRjtZQUNBO2dCQUFTO29CQUNQLElBQUksQ0FBQ2YsUUFBUSxDQUFDZ0IsTUFBTSxDQUFDRixJQUFJLElBQUksQ0FBQ1osT0FBTyxFQUFFYTtvQkFDdkMsSUFBSSxDQUFDZixRQUFRLENBQUNrQixHQUFHLENBQUNKLEdBQUdDLEdBQUcsSUFBSSxDQUFDYixPQUFPLEVBQUUsR0FBR04seUNBQUdBO29CQUM1QztnQkFDRjtRQUNGO0lBQ0Y7SUFDQXVCLFFBQVF0QixnREFBSUE7QUFDZCIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcGF0aC9jb250ZXh0LmpzPzZiNjYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHt0YXV9IGZyb20gXCIuLi9tYXRoLmpzXCI7XG5pbXBvcnQgbm9vcCBmcm9tIFwiLi4vbm9vcC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQYXRoQ29udGV4dChjb250ZXh0KSB7XG4gIHRoaXMuX2NvbnRleHQgPSBjb250ZXh0O1xufVxuXG5QYXRoQ29udGV4dC5wcm90b3R5cGUgPSB7XG4gIF9yYWRpdXM6IDQuNSxcbiAgcG9pbnRSYWRpdXM6IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gdGhpcy5fcmFkaXVzID0gXywgdGhpcztcbiAgfSxcbiAgcG9seWdvblN0YXJ0OiBmdW5jdGlvbigpIHtcbiAgICB0aGlzLl9saW5lID0gMDtcbiAgfSxcbiAgcG9seWdvbkVuZDogZnVuY3Rpb24oKSB7XG4gICAgdGhpcy5fbGluZSA9IE5hTjtcbiAgfSxcbiAgbGluZVN0YXJ0OiBmdW5jdGlvbigpIHtcbiAgICB0aGlzLl9wb2ludCA9IDA7XG4gIH0sXG4gIGxpbmVFbmQ6IGZ1bmN0aW9uKCkge1xuICAgIGlmICh0aGlzLl9saW5lID09PSAwKSB0aGlzLl9jb250ZXh0LmNsb3NlUGF0aCgpO1xuICAgIHRoaXMuX3BvaW50ID0gTmFOO1xuICB9LFxuICBwb2ludDogZnVuY3Rpb24oeCwgeSkge1xuICAgIHN3aXRjaCAodGhpcy5fcG9pbnQpIHtcbiAgICAgIGNhc2UgMDoge1xuICAgICAgICB0aGlzLl9jb250ZXh0Lm1vdmVUbyh4LCB5KTtcbiAgICAgICAgdGhpcy5fcG9pbnQgPSAxO1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGNhc2UgMToge1xuICAgICAgICB0aGlzLl9jb250ZXh0LmxpbmVUbyh4LCB5KTtcbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgICBkZWZhdWx0OiB7XG4gICAgICAgIHRoaXMuX2NvbnRleHQubW92ZVRvKHggKyB0aGlzLl9yYWRpdXMsIHkpO1xuICAgICAgICB0aGlzLl9jb250ZXh0LmFyYyh4LCB5LCB0aGlzLl9yYWRpdXMsIDAsIHRhdSk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgIH1cbiAgfSxcbiAgcmVzdWx0OiBub29wXG59O1xuIl0sIm5hbWVzIjpbInRhdSIsIm5vb3AiLCJQYXRoQ29udGV4dCIsImNvbnRleHQiLCJfY29udGV4dCIsInByb3RvdHlwZSIsIl9yYWRpdXMiLCJwb2ludFJhZGl1cyIsIl8iLCJwb2x5Z29uU3RhcnQiLCJfbGluZSIsInBvbHlnb25FbmQiLCJOYU4iLCJsaW5lU3RhcnQiLCJfcG9pbnQiLCJsaW5lRW5kIiwiY2xvc2VQYXRoIiwicG9pbnQiLCJ4IiwieSIsIm1vdmVUbyIsImxpbmVUbyIsImFyYyIsInJlc3VsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/index.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-geo/src/path/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../identity.js */ \"(ssr)/./node_modules/d3-geo/src/identity.js\");\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n/* harmony import */ var _area_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./area.js */ \"(ssr)/./node_modules/d3-geo/src/path/area.js\");\n/* harmony import */ var _bounds_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./bounds.js */ \"(ssr)/./node_modules/d3-geo/src/path/bounds.js\");\n/* harmony import */ var _centroid_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./centroid.js */ \"(ssr)/./node_modules/d3-geo/src/path/centroid.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/d3-geo/src/path/context.js\");\n/* harmony import */ var _measure_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./measure.js */ \"(ssr)/./node_modules/d3-geo/src/path/measure.js\");\n/* harmony import */ var _string_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./string.js */ \"(ssr)/./node_modules/d3-geo/src/path/string.js\");\n\n\n\n\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(projection, context) {\n    var pointRadius = 4.5, projectionStream, contextStream;\n    function path(object) {\n        if (object) {\n            if (typeof pointRadius === \"function\") contextStream.pointRadius(+pointRadius.apply(this, arguments));\n            (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(contextStream));\n        }\n        return contextStream.result();\n    }\n    path.area = function(object) {\n        (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(_area_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]));\n        return _area_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].result();\n    };\n    path.measure = function(object) {\n        (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(_measure_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]));\n        return _measure_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].result();\n    };\n    path.bounds = function(object) {\n        (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(_bounds_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]));\n        return _bounds_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].result();\n    };\n    path.centroid = function(object) {\n        (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projectionStream(_centroid_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]));\n        return _centroid_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].result();\n    };\n    path.projection = function(_) {\n        return arguments.length ? (projectionStream = _ == null ? (projection = null, _identity_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]) : (projection = _).stream, path) : projection;\n    };\n    path.context = function(_) {\n        if (!arguments.length) return context;\n        contextStream = _ == null ? (context = null, new _string_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]) : new _context_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"](context = _);\n        if (typeof pointRadius !== \"function\") contextStream.pointRadius(pointRadius);\n        return path;\n    };\n    path.pointRadius = function(_) {\n        if (!arguments.length) return pointRadius;\n        pointRadius = typeof _ === \"function\" ? _ : (contextStream.pointRadius(+_), +_);\n        return path;\n    };\n    return path.projection(projection).context(context);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/measure.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-geo/src/path/measure.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _noop_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../noop.js */ \"(ssr)/./node_modules/d3-geo/src/noop.js\");\n\n\n\nvar lengthSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder(), lengthRing, x00, y00, x0, y0;\nvar lengthStream = {\n    point: _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    lineStart: function() {\n        lengthStream.point = lengthPointFirst;\n    },\n    lineEnd: function() {\n        if (lengthRing) lengthPoint(x00, y00);\n        lengthStream.point = _noop_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n    },\n    polygonStart: function() {\n        lengthRing = true;\n    },\n    polygonEnd: function() {\n        lengthRing = null;\n    },\n    result: function() {\n        var length = +lengthSum;\n        lengthSum = new d3_array__WEBPACK_IMPORTED_MODULE_0__.Adder();\n        return length;\n    }\n};\nfunction lengthPointFirst(x, y) {\n    lengthStream.point = lengthPoint;\n    x00 = x0 = x, y00 = y0 = y;\n}\nfunction lengthPoint(x, y) {\n    x0 -= x, y0 -= y;\n    lengthSum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_2__.sqrt)(x0 * x0 + y0 * y0));\n    x0 = x, y0 = y;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (lengthStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/measure.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/path/string.js":
/*!************************************************!*\
  !*** ./node_modules/d3-geo/src/path/string.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PathString)\n/* harmony export */ });\nfunction PathString() {\n    this._string = [];\n}\nPathString.prototype = {\n    _radius: 4.5,\n    _circle: circle(4.5),\n    pointRadius: function(_) {\n        if ((_ = +_) !== this._radius) this._radius = _, this._circle = null;\n        return this;\n    },\n    polygonStart: function() {\n        this._line = 0;\n    },\n    polygonEnd: function() {\n        this._line = NaN;\n    },\n    lineStart: function() {\n        this._point = 0;\n    },\n    lineEnd: function() {\n        if (this._line === 0) this._string.push(\"Z\");\n        this._point = NaN;\n    },\n    point: function(x, y) {\n        switch(this._point){\n            case 0:\n                {\n                    this._string.push(\"M\", x, \",\", y);\n                    this._point = 1;\n                    break;\n                }\n            case 1:\n                {\n                    this._string.push(\"L\", x, \",\", y);\n                    break;\n                }\n            default:\n                {\n                    if (this._circle == null) this._circle = circle(this._radius);\n                    this._string.push(\"M\", x, \",\", y, this._circle);\n                    break;\n                }\n        }\n    },\n    result: function() {\n        if (this._string.length) {\n            var result = this._string.join(\"\");\n            this._string = [];\n            return result;\n        } else {\n            return null;\n        }\n    }\n};\nfunction circle(radius) {\n    return \"m0,\" + radius + \"a\" + radius + \",\" + radius + \" 0 1,1 0,\" + -2 * radius + \"a\" + radius + \",\" + radius + \" 0 1,1 0,\" + 2 * radius + \"z\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/path/string.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/pointEqual.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-geo/src/pointEqual.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(a, b) {\n    return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(a[0] - b[0]) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon && (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(a[1] - b[1]) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wb2ludEVxdWFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDO0FBRXZDLDZCQUFlLG9DQUFTRSxDQUFDLEVBQUVDLENBQUM7SUFDMUIsT0FBT0gsNkNBQUdBLENBQUNFLENBQUMsQ0FBQyxFQUFFLEdBQUdDLENBQUMsQ0FBQyxFQUFFLElBQUlGLDZDQUFPQSxJQUFJRCw2Q0FBR0EsQ0FBQ0UsQ0FBQyxDQUFDLEVBQUUsR0FBR0MsQ0FBQyxDQUFDLEVBQUUsSUFBSUYsNkNBQU9BO0FBQ2pFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wb2ludEVxdWFsLmpzP2MxN2EiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHthYnMsIGVwc2lsb259IGZyb20gXCIuL21hdGguanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oYSwgYikge1xuICByZXR1cm4gYWJzKGFbMF0gLSBiWzBdKSA8IGVwc2lsb24gJiYgYWJzKGFbMV0gLSBiWzFdKSA8IGVwc2lsb247XG59XG4iXSwibmFtZXMiOlsiYWJzIiwiZXBzaWxvbiIsImEiLCJiIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/pointEqual.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/polygonContains.js":
/*!****************************************************!*\
  !*** ./node_modules/d3-geo/src/polygonContains.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/fsum.js\");\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cartesian.js */ \"(ssr)/./node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n\nfunction longitude(point) {\n    if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(point[0]) <= _math_js__WEBPACK_IMPORTED_MODULE_0__.pi) return point[0];\n    else return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(point[0]) * (((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(point[0]) + _math_js__WEBPACK_IMPORTED_MODULE_0__.pi) % _math_js__WEBPACK_IMPORTED_MODULE_0__.tau - _math_js__WEBPACK_IMPORTED_MODULE_0__.pi);\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(polygon, point) {\n    var lambda = longitude(point), phi = point[1], sinPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi), normal = [\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(lambda),\n        -(0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda),\n        0\n    ], angle = 0, winding = 0;\n    var sum = new d3_array__WEBPACK_IMPORTED_MODULE_1__.Adder();\n    if (sinPhi === 1) phi = _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n    else if (sinPhi === -1) phi = -_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n    for(var i = 0, n = polygon.length; i < n; ++i){\n        if (!(m = (ring = polygon[i]).length)) continue;\n        var ring, m, point0 = ring[m - 1], lambda0 = longitude(point0), phi0 = point0[1] / 2 + _math_js__WEBPACK_IMPORTED_MODULE_0__.quarterPi, sinPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi0), cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi0);\n        for(var j = 0; j < m; ++j, lambda0 = lambda1, sinPhi0 = sinPhi1, cosPhi0 = cosPhi1, point0 = point1){\n            var point1 = ring[j], lambda1 = longitude(point1), phi1 = point1[1] / 2 + _math_js__WEBPACK_IMPORTED_MODULE_0__.quarterPi, sinPhi1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi1), cosPhi1 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi1), delta = lambda1 - lambda0, sign = delta >= 0 ? 1 : -1, absDelta = sign * delta, antimeridian = absDelta > _math_js__WEBPACK_IMPORTED_MODULE_0__.pi, k = sinPhi0 * sinPhi1;\n            sum.add((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(k * sign * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(absDelta), cosPhi0 * cosPhi1 + k * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(absDelta)));\n            angle += antimeridian ? delta + sign * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau : delta;\n            // Are the longitudes either side of the point’s meridian (lambda),\n            // and are the latitudes smaller than the parallel (phi)?\n            if (antimeridian ^ lambda0 >= lambda ^ lambda1 >= lambda) {\n                var arc = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesianCross)((0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesian)(point0), (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesian)(point1));\n                (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesianNormalizeInPlace)(arc);\n                var intersection = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesianCross)(normal, arc);\n                (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesianNormalizeInPlace)(intersection);\n                var phiArc = (antimeridian ^ delta >= 0 ? -1 : 1) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(intersection[2]);\n                if (phi > phiArc || phi === phiArc && (arc[0] || arc[1])) {\n                    winding += antimeridian ^ delta >= 0 ? 1 : -1;\n                }\n            }\n        }\n    }\n    // First, determine whether the South pole is inside or outside:\n    //\n    // It is inside if:\n    // * the polygon winds around it in a clockwise direction.\n    // * the polygon does not (cumulatively) wind around it, but has a negative\n    //   (counter-clockwise) area.\n    //\n    // Second, count the (signed) number of times a segment crosses a lambda\n    // from the point to the South pole.  If it is zero, then the point is the\n    // same side as the South pole.\n    return (angle < -_math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon || angle < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon && sum < -_math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon2) ^ winding & 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/polygonContains.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/albers.js":
/*!******************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/albers.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _conicEqualArea_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./conicEqualArea.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conicEqualArea.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().parallels([\n        29.5,\n        45.5\n    ]).scale(1070).translate([\n        480,\n        250\n    ]).rotate([\n        96,\n        0\n    ]).center([\n        -0.6,\n        38.7\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2FsYmVycy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUVqRCw2QkFBZSxzQ0FBVztJQUN4QixPQUFPQSw4REFBY0EsR0FDaEJDLFNBQVMsQ0FBQztRQUFDO1FBQU07S0FBSyxFQUN0QkMsS0FBSyxDQUFDLE1BQ05DLFNBQVMsQ0FBQztRQUFDO1FBQUs7S0FBSSxFQUNwQkMsTUFBTSxDQUFDO1FBQUM7UUFBSTtLQUFFLEVBQ2RDLE1BQU0sQ0FBQztRQUFDLENBQUM7UUFBSztLQUFLO0FBQzFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2FsYmVycy5qcz8zNGE2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjb25pY0VxdWFsQXJlYSBmcm9tIFwiLi9jb25pY0VxdWFsQXJlYS5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIGNvbmljRXF1YWxBcmVhKClcbiAgICAgIC5wYXJhbGxlbHMoWzI5LjUsIDQ1LjVdKVxuICAgICAgLnNjYWxlKDEwNzApXG4gICAgICAudHJhbnNsYXRlKFs0ODAsIDI1MF0pXG4gICAgICAucm90YXRlKFs5NiwgMF0pXG4gICAgICAuY2VudGVyKFstMC42LCAzOC43XSk7XG59XG4iXSwibmFtZXMiOlsiY29uaWNFcXVhbEFyZWEiLCJwYXJhbGxlbHMiLCJzY2FsZSIsInRyYW5zbGF0ZSIsInJvdGF0ZSIsImNlbnRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/albers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/albersUsa.js":
/*!*********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/albersUsa.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _albers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./albers.js */ \"(ssr)/./node_modules/d3-geo/src/projection/albers.js\");\n/* harmony import */ var _conicEqualArea_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./conicEqualArea.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conicEqualArea.js\");\n/* harmony import */ var _fit_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./fit.js */ \"(ssr)/./node_modules/d3-geo/src/projection/fit.js\");\n\n\n\n\n// The projections must have mutually exclusive clip regions on the sphere,\n// as this will avoid emitting interleaving lines and polygons.\nfunction multiplex(streams) {\n    var n = streams.length;\n    return {\n        point: function(x, y) {\n            var i = -1;\n            while(++i < n)streams[i].point(x, y);\n        },\n        sphere: function() {\n            var i = -1;\n            while(++i < n)streams[i].sphere();\n        },\n        lineStart: function() {\n            var i = -1;\n            while(++i < n)streams[i].lineStart();\n        },\n        lineEnd: function() {\n            var i = -1;\n            while(++i < n)streams[i].lineEnd();\n        },\n        polygonStart: function() {\n            var i = -1;\n            while(++i < n)streams[i].polygonStart();\n        },\n        polygonEnd: function() {\n            var i = -1;\n            while(++i < n)streams[i].polygonEnd();\n        }\n    };\n}\n// A composite projection for the United States, configured by default for\n// 960×500. The projection also works quite well at 960×600 if you change the\n// scale to 1285 and adjust the translate accordingly. The set of standard\n// parallels for each region comes from USGS, which is published here:\n// http://egsc.usgs.gov/isb/pubs/MapProjections/projections.html#albers\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var cache, cacheStream, lower48 = (0,_albers_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(), lower48Point, alaska = (0,_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])().rotate([\n        154,\n        0\n    ]).center([\n        -2,\n        58.5\n    ]).parallels([\n        55,\n        65\n    ]), alaskaPoint, hawaii = (0,_conicEqualArea_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])().rotate([\n        157,\n        0\n    ]).center([\n        -3,\n        19.9\n    ]).parallels([\n        8,\n        18\n    ]), hawaiiPoint, point, pointStream = {\n        point: function(x, y) {\n            point = [\n                x,\n                y\n            ];\n        }\n    };\n    function albersUsa(coordinates) {\n        var x = coordinates[0], y = coordinates[1];\n        return point = null, (lower48Point.point(x, y), point) || (alaskaPoint.point(x, y), point) || (hawaiiPoint.point(x, y), point);\n    }\n    albersUsa.invert = function(coordinates) {\n        var k = lower48.scale(), t = lower48.translate(), x = (coordinates[0] - t[0]) / k, y = (coordinates[1] - t[1]) / k;\n        return (y >= 0.120 && y < 0.234 && x >= -0.425 && x < -0.214 ? alaska : y >= 0.166 && y < 0.234 && x >= -0.214 && x < -0.115 ? hawaii : lower48).invert(coordinates);\n    };\n    albersUsa.stream = function(stream) {\n        return cache && cacheStream === stream ? cache : cache = multiplex([\n            lower48.stream(cacheStream = stream),\n            alaska.stream(stream),\n            hawaii.stream(stream)\n        ]);\n    };\n    albersUsa.precision = function(_) {\n        if (!arguments.length) return lower48.precision();\n        lower48.precision(_), alaska.precision(_), hawaii.precision(_);\n        return reset();\n    };\n    albersUsa.scale = function(_) {\n        if (!arguments.length) return lower48.scale();\n        lower48.scale(_), alaska.scale(_ * 0.35), hawaii.scale(_);\n        return albersUsa.translate(lower48.translate());\n    };\n    albersUsa.translate = function(_) {\n        if (!arguments.length) return lower48.translate();\n        var k = lower48.scale(), x = +_[0], y = +_[1];\n        lower48Point = lower48.translate(_).clipExtent([\n            [\n                x - 0.455 * k,\n                y - 0.238 * k\n            ],\n            [\n                x + 0.455 * k,\n                y + 0.238 * k\n            ]\n        ]).stream(pointStream);\n        alaskaPoint = alaska.translate([\n            x - 0.307 * k,\n            y + 0.201 * k\n        ]).clipExtent([\n            [\n                x - 0.425 * k + _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon,\n                y + 0.120 * k + _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon\n            ],\n            [\n                x - 0.214 * k - _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon,\n                y + 0.234 * k - _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon\n            ]\n        ]).stream(pointStream);\n        hawaiiPoint = hawaii.translate([\n            x - 0.205 * k,\n            y + 0.212 * k\n        ]).clipExtent([\n            [\n                x - 0.214 * k + _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon,\n                y + 0.166 * k + _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon\n            ],\n            [\n                x - 0.115 * k - _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon,\n                y + 0.234 * k - _math_js__WEBPACK_IMPORTED_MODULE_2__.epsilon\n            ]\n        ]).stream(pointStream);\n        return reset();\n    };\n    albersUsa.fitExtent = function(extent, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_3__.fitExtent)(albersUsa, extent, object);\n    };\n    albersUsa.fitSize = function(size, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_3__.fitSize)(albersUsa, size, object);\n    };\n    albersUsa.fitWidth = function(width, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_3__.fitWidth)(albersUsa, width, object);\n    };\n    albersUsa.fitHeight = function(height, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_3__.fitHeight)(albersUsa, height, object);\n    };\n    function reset() {\n        cache = cacheStream = null;\n        return albersUsa;\n    }\n    return albersUsa.scale(1070);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2FsYmVyc1VzYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFtQztBQUNGO0FBQ2dCO0FBQ2dCO0FBRWpFLDJFQUEyRTtBQUMzRSwrREFBK0Q7QUFDL0QsU0FBU08sVUFBVUMsT0FBTztJQUN4QixJQUFJQyxJQUFJRCxRQUFRRSxNQUFNO0lBQ3RCLE9BQU87UUFDTEMsT0FBTyxTQUFTQyxDQUFDLEVBQUVDLENBQUM7WUFBSSxJQUFJQyxJQUFJLENBQUM7WUFBRyxNQUFPLEVBQUVBLElBQUlMLEVBQUdELE9BQU8sQ0FBQ00sRUFBRSxDQUFDSCxLQUFLLENBQUNDLEdBQUdDO1FBQUk7UUFDNUVFLFFBQVE7WUFBYSxJQUFJRCxJQUFJLENBQUM7WUFBRyxNQUFPLEVBQUVBLElBQUlMLEVBQUdELE9BQU8sQ0FBQ00sRUFBRSxDQUFDQyxNQUFNO1FBQUk7UUFDdEVDLFdBQVc7WUFBYSxJQUFJRixJQUFJLENBQUM7WUFBRyxNQUFPLEVBQUVBLElBQUlMLEVBQUdELE9BQU8sQ0FBQ00sRUFBRSxDQUFDRSxTQUFTO1FBQUk7UUFDNUVDLFNBQVM7WUFBYSxJQUFJSCxJQUFJLENBQUM7WUFBRyxNQUFPLEVBQUVBLElBQUlMLEVBQUdELE9BQU8sQ0FBQ00sRUFBRSxDQUFDRyxPQUFPO1FBQUk7UUFDeEVDLGNBQWM7WUFBYSxJQUFJSixJQUFJLENBQUM7WUFBRyxNQUFPLEVBQUVBLElBQUlMLEVBQUdELE9BQU8sQ0FBQ00sRUFBRSxDQUFDSSxZQUFZO1FBQUk7UUFDbEZDLFlBQVk7WUFBYSxJQUFJTCxJQUFJLENBQUM7WUFBRyxNQUFPLEVBQUVBLElBQUlMLEVBQUdELE9BQU8sQ0FBQ00sRUFBRSxDQUFDSyxVQUFVO1FBQUk7SUFDaEY7QUFDRjtBQUVBLDBFQUEwRTtBQUMxRSw2RUFBNkU7QUFDN0UsMEVBQTBFO0FBQzFFLHNFQUFzRTtBQUN0RSx1RUFBdUU7QUFDdkUsNkJBQWUsc0NBQVc7SUFDeEIsSUFBSUMsT0FDQUMsYUFDQUMsVUFBVXJCLHNEQUFNQSxJQUFJc0IsY0FDcEJDLFNBQVN0Qiw4REFBY0EsR0FBR3VCLE1BQU0sQ0FBQztRQUFDO1FBQUs7S0FBRSxFQUFFQyxNQUFNLENBQUM7UUFBQyxDQUFDO1FBQUc7S0FBSyxFQUFFQyxTQUFTLENBQUM7UUFBQztRQUFJO0tBQUcsR0FBR0MsYUFDbkZDLFNBQVMzQiw4REFBY0EsR0FBR3VCLE1BQU0sQ0FBQztRQUFDO1FBQUs7S0FBRSxFQUFFQyxNQUFNLENBQUM7UUFBQyxDQUFDO1FBQUc7S0FBSyxFQUFFQyxTQUFTLENBQUM7UUFBQztRQUFHO0tBQUcsR0FBR0csYUFDbEZuQixPQUFPb0IsY0FBYztRQUFDcEIsT0FBTyxTQUFTQyxDQUFDLEVBQUVDLENBQUM7WUFBSUYsUUFBUTtnQkFBQ0M7Z0JBQUdDO2FBQUU7UUFBRTtJQUFDO0lBRW5FLFNBQVNtQixVQUFVQyxXQUFXO1FBQzVCLElBQUlyQixJQUFJcUIsV0FBVyxDQUFDLEVBQUUsRUFBRXBCLElBQUlvQixXQUFXLENBQUMsRUFBRTtRQUMxQyxPQUFPdEIsUUFBUSxNQUNYLENBQUNZLGFBQWFaLEtBQUssQ0FBQ0MsR0FBR0MsSUFBSUYsS0FBSSxLQUMzQmlCLENBQUFBLFlBQVlqQixLQUFLLENBQUNDLEdBQUdDLElBQUlGLEtBQUksS0FDN0JtQixDQUFBQSxZQUFZbkIsS0FBSyxDQUFDQyxHQUFHQyxJQUFJRixLQUFJO0lBQ3ZDO0lBRUFxQixVQUFVRSxNQUFNLEdBQUcsU0FBU0QsV0FBVztRQUNyQyxJQUFJRSxJQUFJYixRQUFRYyxLQUFLLElBQ2pCQyxJQUFJZixRQUFRZ0IsU0FBUyxJQUNyQjFCLElBQUksQ0FBQ3FCLFdBQVcsQ0FBQyxFQUFFLEdBQUdJLENBQUMsQ0FBQyxFQUFFLElBQUlGLEdBQzlCdEIsSUFBSSxDQUFDb0IsV0FBVyxDQUFDLEVBQUUsR0FBR0ksQ0FBQyxDQUFDLEVBQUUsSUFBSUY7UUFDbEMsT0FBTyxDQUFDdEIsS0FBSyxTQUFTQSxJQUFJLFNBQVNELEtBQUssQ0FBQyxTQUFTQSxJQUFJLENBQUMsUUFBUVksU0FDekRYLEtBQUssU0FBU0EsSUFBSSxTQUFTRCxLQUFLLENBQUMsU0FBU0EsSUFBSSxDQUFDLFFBQVFpQixTQUN2RFAsT0FBTSxFQUFHWSxNQUFNLENBQUNEO0lBQ3hCO0lBRUFELFVBQVVPLE1BQU0sR0FBRyxTQUFTQSxNQUFNO1FBQ2hDLE9BQU9uQixTQUFTQyxnQkFBZ0JrQixTQUFTbkIsUUFBUUEsUUFBUWIsVUFBVTtZQUFDZSxRQUFRaUIsTUFBTSxDQUFDbEIsY0FBY2tCO1lBQVNmLE9BQU9lLE1BQU0sQ0FBQ0E7WUFBU1YsT0FBT1UsTUFBTSxDQUFDQTtTQUFRO0lBQ3pKO0lBRUFQLFVBQVVRLFNBQVMsR0FBRyxTQUFTQyxDQUFDO1FBQzlCLElBQUksQ0FBQ0MsVUFBVWhDLE1BQU0sRUFBRSxPQUFPWSxRQUFRa0IsU0FBUztRQUMvQ2xCLFFBQVFrQixTQUFTLENBQUNDLElBQUlqQixPQUFPZ0IsU0FBUyxDQUFDQyxJQUFJWixPQUFPVyxTQUFTLENBQUNDO1FBQzVELE9BQU9FO0lBQ1Q7SUFFQVgsVUFBVUksS0FBSyxHQUFHLFNBQVNLLENBQUM7UUFDMUIsSUFBSSxDQUFDQyxVQUFVaEMsTUFBTSxFQUFFLE9BQU9ZLFFBQVFjLEtBQUs7UUFDM0NkLFFBQVFjLEtBQUssQ0FBQ0ssSUFBSWpCLE9BQU9ZLEtBQUssQ0FBQ0ssSUFBSSxPQUFPWixPQUFPTyxLQUFLLENBQUNLO1FBQ3ZELE9BQU9ULFVBQVVNLFNBQVMsQ0FBQ2hCLFFBQVFnQixTQUFTO0lBQzlDO0lBRUFOLFVBQVVNLFNBQVMsR0FBRyxTQUFTRyxDQUFDO1FBQzlCLElBQUksQ0FBQ0MsVUFBVWhDLE1BQU0sRUFBRSxPQUFPWSxRQUFRZ0IsU0FBUztRQUMvQyxJQUFJSCxJQUFJYixRQUFRYyxLQUFLLElBQUl4QixJQUFJLENBQUM2QixDQUFDLENBQUMsRUFBRSxFQUFFNUIsSUFBSSxDQUFDNEIsQ0FBQyxDQUFDLEVBQUU7UUFFN0NsQixlQUFlRCxRQUNWZ0IsU0FBUyxDQUFDRyxHQUNWRyxVQUFVLENBQUM7WUFBQztnQkFBQ2hDLElBQUksUUFBUXVCO2dCQUFHdEIsSUFBSSxRQUFRc0I7YUFBRTtZQUFFO2dCQUFDdkIsSUFBSSxRQUFRdUI7Z0JBQUd0QixJQUFJLFFBQVFzQjthQUFFO1NBQUMsRUFDM0VJLE1BQU0sQ0FBQ1I7UUFFWkgsY0FBY0osT0FDVGMsU0FBUyxDQUFDO1lBQUMxQixJQUFJLFFBQVF1QjtZQUFHdEIsSUFBSSxRQUFRc0I7U0FBRSxFQUN4Q1MsVUFBVSxDQUFDO1lBQUM7Z0JBQUNoQyxJQUFJLFFBQVF1QixJQUFJbkMsNkNBQU9BO2dCQUFFYSxJQUFJLFFBQVFzQixJQUFJbkMsNkNBQU9BO2FBQUM7WUFBRTtnQkFBQ1ksSUFBSSxRQUFRdUIsSUFBSW5DLDZDQUFPQTtnQkFBRWEsSUFBSSxRQUFRc0IsSUFBSW5DLDZDQUFPQTthQUFDO1NBQUMsRUFDbkh1QyxNQUFNLENBQUNSO1FBRVpELGNBQWNELE9BQ1RTLFNBQVMsQ0FBQztZQUFDMUIsSUFBSSxRQUFRdUI7WUFBR3RCLElBQUksUUFBUXNCO1NBQUUsRUFDeENTLFVBQVUsQ0FBQztZQUFDO2dCQUFDaEMsSUFBSSxRQUFRdUIsSUFBSW5DLDZDQUFPQTtnQkFBRWEsSUFBSSxRQUFRc0IsSUFBSW5DLDZDQUFPQTthQUFDO1lBQUU7Z0JBQUNZLElBQUksUUFBUXVCLElBQUluQyw2Q0FBT0E7Z0JBQUVhLElBQUksUUFBUXNCLElBQUluQyw2Q0FBT0E7YUFBQztTQUFDLEVBQ25IdUMsTUFBTSxDQUFDUjtRQUVaLE9BQU9ZO0lBQ1Q7SUFFQVgsVUFBVTdCLFNBQVMsR0FBRyxTQUFTMEMsTUFBTSxFQUFFQyxNQUFNO1FBQzNDLE9BQU8zQyxrREFBU0EsQ0FBQzZCLFdBQVdhLFFBQVFDO0lBQ3RDO0lBRUFkLFVBQVU1QixPQUFPLEdBQUcsU0FBUzJDLElBQUksRUFBRUQsTUFBTTtRQUN2QyxPQUFPMUMsZ0RBQU9BLENBQUM0QixXQUFXZSxNQUFNRDtJQUNsQztJQUVBZCxVQUFVM0IsUUFBUSxHQUFHLFNBQVMyQyxLQUFLLEVBQUVGLE1BQU07UUFDekMsT0FBT3pDLGlEQUFRQSxDQUFDMkIsV0FBV2dCLE9BQU9GO0lBQ3BDO0lBRUFkLFVBQVUxQixTQUFTLEdBQUcsU0FBUzJDLE1BQU0sRUFBRUgsTUFBTTtRQUMzQyxPQUFPeEMsa0RBQVNBLENBQUMwQixXQUFXaUIsUUFBUUg7SUFDdEM7SUFFQSxTQUFTSDtRQUNQdkIsUUFBUUMsY0FBYztRQUN0QixPQUFPVztJQUNUO0lBRUEsT0FBT0EsVUFBVUksS0FBSyxDQUFDO0FBQ3pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2FsYmVyc1VzYS5qcz8xYzU2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7ZXBzaWxvbn0gZnJvbSBcIi4uL21hdGguanNcIjtcbmltcG9ydCBhbGJlcnMgZnJvbSBcIi4vYWxiZXJzLmpzXCI7XG5pbXBvcnQgY29uaWNFcXVhbEFyZWEgZnJvbSBcIi4vY29uaWNFcXVhbEFyZWEuanNcIjtcbmltcG9ydCB7Zml0RXh0ZW50LCBmaXRTaXplLCBmaXRXaWR0aCwgZml0SGVpZ2h0fSBmcm9tIFwiLi9maXQuanNcIjtcblxuLy8gVGhlIHByb2plY3Rpb25zIG11c3QgaGF2ZSBtdXR1YWxseSBleGNsdXNpdmUgY2xpcCByZWdpb25zIG9uIHRoZSBzcGhlcmUsXG4vLyBhcyB0aGlzIHdpbGwgYXZvaWQgZW1pdHRpbmcgaW50ZXJsZWF2aW5nIGxpbmVzIGFuZCBwb2x5Z29ucy5cbmZ1bmN0aW9uIG11bHRpcGxleChzdHJlYW1zKSB7XG4gIHZhciBuID0gc3RyZWFtcy5sZW5ndGg7XG4gIHJldHVybiB7XG4gICAgcG9pbnQ6IGZ1bmN0aW9uKHgsIHkpIHsgdmFyIGkgPSAtMTsgd2hpbGUgKCsraSA8IG4pIHN0cmVhbXNbaV0ucG9pbnQoeCwgeSk7IH0sXG4gICAgc3BoZXJlOiBmdW5jdGlvbigpIHsgdmFyIGkgPSAtMTsgd2hpbGUgKCsraSA8IG4pIHN0cmVhbXNbaV0uc3BoZXJlKCk7IH0sXG4gICAgbGluZVN0YXJ0OiBmdW5jdGlvbigpIHsgdmFyIGkgPSAtMTsgd2hpbGUgKCsraSA8IG4pIHN0cmVhbXNbaV0ubGluZVN0YXJ0KCk7IH0sXG4gICAgbGluZUVuZDogZnVuY3Rpb24oKSB7IHZhciBpID0gLTE7IHdoaWxlICgrK2kgPCBuKSBzdHJlYW1zW2ldLmxpbmVFbmQoKTsgfSxcbiAgICBwb2x5Z29uU3RhcnQ6IGZ1bmN0aW9uKCkgeyB2YXIgaSA9IC0xOyB3aGlsZSAoKytpIDwgbikgc3RyZWFtc1tpXS5wb2x5Z29uU3RhcnQoKTsgfSxcbiAgICBwb2x5Z29uRW5kOiBmdW5jdGlvbigpIHsgdmFyIGkgPSAtMTsgd2hpbGUgKCsraSA8IG4pIHN0cmVhbXNbaV0ucG9seWdvbkVuZCgpOyB9XG4gIH07XG59XG5cbi8vIEEgY29tcG9zaXRlIHByb2plY3Rpb24gZm9yIHRoZSBVbml0ZWQgU3RhdGVzLCBjb25maWd1cmVkIGJ5IGRlZmF1bHQgZm9yXG4vLyA5NjDDlzUwMC4gVGhlIHByb2plY3Rpb24gYWxzbyB3b3JrcyBxdWl0ZSB3ZWxsIGF0IDk2MMOXNjAwIGlmIHlvdSBjaGFuZ2UgdGhlXG4vLyBzY2FsZSB0byAxMjg1IGFuZCBhZGp1c3QgdGhlIHRyYW5zbGF0ZSBhY2NvcmRpbmdseS4gVGhlIHNldCBvZiBzdGFuZGFyZFxuLy8gcGFyYWxsZWxzIGZvciBlYWNoIHJlZ2lvbiBjb21lcyBmcm9tIFVTR1MsIHdoaWNoIGlzIHB1Ymxpc2hlZCBoZXJlOlxuLy8gaHR0cDovL2Vnc2MudXNncy5nb3YvaXNiL3B1YnMvTWFwUHJvamVjdGlvbnMvcHJvamVjdGlvbnMuaHRtbCNhbGJlcnNcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICB2YXIgY2FjaGUsXG4gICAgICBjYWNoZVN0cmVhbSxcbiAgICAgIGxvd2VyNDggPSBhbGJlcnMoKSwgbG93ZXI0OFBvaW50LFxuICAgICAgYWxhc2thID0gY29uaWNFcXVhbEFyZWEoKS5yb3RhdGUoWzE1NCwgMF0pLmNlbnRlcihbLTIsIDU4LjVdKS5wYXJhbGxlbHMoWzU1LCA2NV0pLCBhbGFza2FQb2ludCwgLy8gRVBTRzozMzM4XG4gICAgICBoYXdhaWkgPSBjb25pY0VxdWFsQXJlYSgpLnJvdGF0ZShbMTU3LCAwXSkuY2VudGVyKFstMywgMTkuOV0pLnBhcmFsbGVscyhbOCwgMThdKSwgaGF3YWlpUG9pbnQsIC8vIEVTUkk6MTAyMDA3XG4gICAgICBwb2ludCwgcG9pbnRTdHJlYW0gPSB7cG9pbnQ6IGZ1bmN0aW9uKHgsIHkpIHsgcG9pbnQgPSBbeCwgeV07IH19O1xuXG4gIGZ1bmN0aW9uIGFsYmVyc1VzYShjb29yZGluYXRlcykge1xuICAgIHZhciB4ID0gY29vcmRpbmF0ZXNbMF0sIHkgPSBjb29yZGluYXRlc1sxXTtcbiAgICByZXR1cm4gcG9pbnQgPSBudWxsLFxuICAgICAgICAobG93ZXI0OFBvaW50LnBvaW50KHgsIHkpLCBwb2ludClcbiAgICAgICAgfHwgKGFsYXNrYVBvaW50LnBvaW50KHgsIHkpLCBwb2ludClcbiAgICAgICAgfHwgKGhhd2FpaVBvaW50LnBvaW50KHgsIHkpLCBwb2ludCk7XG4gIH1cblxuICBhbGJlcnNVc2EuaW52ZXJ0ID0gZnVuY3Rpb24oY29vcmRpbmF0ZXMpIHtcbiAgICB2YXIgayA9IGxvd2VyNDguc2NhbGUoKSxcbiAgICAgICAgdCA9IGxvd2VyNDgudHJhbnNsYXRlKCksXG4gICAgICAgIHggPSAoY29vcmRpbmF0ZXNbMF0gLSB0WzBdKSAvIGssXG4gICAgICAgIHkgPSAoY29vcmRpbmF0ZXNbMV0gLSB0WzFdKSAvIGs7XG4gICAgcmV0dXJuICh5ID49IDAuMTIwICYmIHkgPCAwLjIzNCAmJiB4ID49IC0wLjQyNSAmJiB4IDwgLTAuMjE0ID8gYWxhc2thXG4gICAgICAgIDogeSA+PSAwLjE2NiAmJiB5IDwgMC4yMzQgJiYgeCA+PSAtMC4yMTQgJiYgeCA8IC0wLjExNSA/IGhhd2FpaVxuICAgICAgICA6IGxvd2VyNDgpLmludmVydChjb29yZGluYXRlcyk7XG4gIH07XG5cbiAgYWxiZXJzVXNhLnN0cmVhbSA9IGZ1bmN0aW9uKHN0cmVhbSkge1xuICAgIHJldHVybiBjYWNoZSAmJiBjYWNoZVN0cmVhbSA9PT0gc3RyZWFtID8gY2FjaGUgOiBjYWNoZSA9IG11bHRpcGxleChbbG93ZXI0OC5zdHJlYW0oY2FjaGVTdHJlYW0gPSBzdHJlYW0pLCBhbGFza2Euc3RyZWFtKHN0cmVhbSksIGhhd2FpaS5zdHJlYW0oc3RyZWFtKV0pO1xuICB9O1xuXG4gIGFsYmVyc1VzYS5wcmVjaXNpb24gPSBmdW5jdGlvbihfKSB7XG4gICAgaWYgKCFhcmd1bWVudHMubGVuZ3RoKSByZXR1cm4gbG93ZXI0OC5wcmVjaXNpb24oKTtcbiAgICBsb3dlcjQ4LnByZWNpc2lvbihfKSwgYWxhc2thLnByZWNpc2lvbihfKSwgaGF3YWlpLnByZWNpc2lvbihfKTtcbiAgICByZXR1cm4gcmVzZXQoKTtcbiAgfTtcblxuICBhbGJlcnNVc2Euc2NhbGUgPSBmdW5jdGlvbihfKSB7XG4gICAgaWYgKCFhcmd1bWVudHMubGVuZ3RoKSByZXR1cm4gbG93ZXI0OC5zY2FsZSgpO1xuICAgIGxvd2VyNDguc2NhbGUoXyksIGFsYXNrYS5zY2FsZShfICogMC4zNSksIGhhd2FpaS5zY2FsZShfKTtcbiAgICByZXR1cm4gYWxiZXJzVXNhLnRyYW5zbGF0ZShsb3dlcjQ4LnRyYW5zbGF0ZSgpKTtcbiAgfTtcblxuICBhbGJlcnNVc2EudHJhbnNsYXRlID0gZnVuY3Rpb24oXykge1xuICAgIGlmICghYXJndW1lbnRzLmxlbmd0aCkgcmV0dXJuIGxvd2VyNDgudHJhbnNsYXRlKCk7XG4gICAgdmFyIGsgPSBsb3dlcjQ4LnNjYWxlKCksIHggPSArX1swXSwgeSA9ICtfWzFdO1xuXG4gICAgbG93ZXI0OFBvaW50ID0gbG93ZXI0OFxuICAgICAgICAudHJhbnNsYXRlKF8pXG4gICAgICAgIC5jbGlwRXh0ZW50KFtbeCAtIDAuNDU1ICogaywgeSAtIDAuMjM4ICoga10sIFt4ICsgMC40NTUgKiBrLCB5ICsgMC4yMzggKiBrXV0pXG4gICAgICAgIC5zdHJlYW0ocG9pbnRTdHJlYW0pO1xuXG4gICAgYWxhc2thUG9pbnQgPSBhbGFza2FcbiAgICAgICAgLnRyYW5zbGF0ZShbeCAtIDAuMzA3ICogaywgeSArIDAuMjAxICoga10pXG4gICAgICAgIC5jbGlwRXh0ZW50KFtbeCAtIDAuNDI1ICogayArIGVwc2lsb24sIHkgKyAwLjEyMCAqIGsgKyBlcHNpbG9uXSwgW3ggLSAwLjIxNCAqIGsgLSBlcHNpbG9uLCB5ICsgMC4yMzQgKiBrIC0gZXBzaWxvbl1dKVxuICAgICAgICAuc3RyZWFtKHBvaW50U3RyZWFtKTtcblxuICAgIGhhd2FpaVBvaW50ID0gaGF3YWlpXG4gICAgICAgIC50cmFuc2xhdGUoW3ggLSAwLjIwNSAqIGssIHkgKyAwLjIxMiAqIGtdKVxuICAgICAgICAuY2xpcEV4dGVudChbW3ggLSAwLjIxNCAqIGsgKyBlcHNpbG9uLCB5ICsgMC4xNjYgKiBrICsgZXBzaWxvbl0sIFt4IC0gMC4xMTUgKiBrIC0gZXBzaWxvbiwgeSArIDAuMjM0ICogayAtIGVwc2lsb25dXSlcbiAgICAgICAgLnN0cmVhbShwb2ludFN0cmVhbSk7XG5cbiAgICByZXR1cm4gcmVzZXQoKTtcbiAgfTtcblxuICBhbGJlcnNVc2EuZml0RXh0ZW50ID0gZnVuY3Rpb24oZXh0ZW50LCBvYmplY3QpIHtcbiAgICByZXR1cm4gZml0RXh0ZW50KGFsYmVyc1VzYSwgZXh0ZW50LCBvYmplY3QpO1xuICB9O1xuXG4gIGFsYmVyc1VzYS5maXRTaXplID0gZnVuY3Rpb24oc2l6ZSwgb2JqZWN0KSB7XG4gICAgcmV0dXJuIGZpdFNpemUoYWxiZXJzVXNhLCBzaXplLCBvYmplY3QpO1xuICB9O1xuXG4gIGFsYmVyc1VzYS5maXRXaWR0aCA9IGZ1bmN0aW9uKHdpZHRoLCBvYmplY3QpIHtcbiAgICByZXR1cm4gZml0V2lkdGgoYWxiZXJzVXNhLCB3aWR0aCwgb2JqZWN0KTtcbiAgfTtcblxuICBhbGJlcnNVc2EuZml0SGVpZ2h0ID0gZnVuY3Rpb24oaGVpZ2h0LCBvYmplY3QpIHtcbiAgICByZXR1cm4gZml0SGVpZ2h0KGFsYmVyc1VzYSwgaGVpZ2h0LCBvYmplY3QpO1xuICB9O1xuXG4gIGZ1bmN0aW9uIHJlc2V0KCkge1xuICAgIGNhY2hlID0gY2FjaGVTdHJlYW0gPSBudWxsO1xuICAgIHJldHVybiBhbGJlcnNVc2E7XG4gIH1cblxuICByZXR1cm4gYWxiZXJzVXNhLnNjYWxlKDEwNzApO1xufVxuIl0sIm5hbWVzIjpbImVwc2lsb24iLCJhbGJlcnMiLCJjb25pY0VxdWFsQXJlYSIsImZpdEV4dGVudCIsImZpdFNpemUiLCJmaXRXaWR0aCIsImZpdEhlaWdodCIsIm11bHRpcGxleCIsInN0cmVhbXMiLCJuIiwibGVuZ3RoIiwicG9pbnQiLCJ4IiwieSIsImkiLCJzcGhlcmUiLCJsaW5lU3RhcnQiLCJsaW5lRW5kIiwicG9seWdvblN0YXJ0IiwicG9seWdvbkVuZCIsImNhY2hlIiwiY2FjaGVTdHJlYW0iLCJsb3dlcjQ4IiwibG93ZXI0OFBvaW50IiwiYWxhc2thIiwicm90YXRlIiwiY2VudGVyIiwicGFyYWxsZWxzIiwiYWxhc2thUG9pbnQiLCJoYXdhaWkiLCJoYXdhaWlQb2ludCIsInBvaW50U3RyZWFtIiwiYWxiZXJzVXNhIiwiY29vcmRpbmF0ZXMiLCJpbnZlcnQiLCJrIiwic2NhbGUiLCJ0IiwidHJhbnNsYXRlIiwic3RyZWFtIiwicHJlY2lzaW9uIiwiXyIsImFyZ3VtZW50cyIsInJlc2V0IiwiY2xpcEV4dGVudCIsImV4dGVudCIsIm9iamVjdCIsInNpemUiLCJ3aWR0aCIsImhlaWdodCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/albersUsa.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js":
/*!*********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/azimuthal.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   azimuthalInvert: () => (/* binding */ azimuthalInvert),\n/* harmony export */   azimuthalRaw: () => (/* binding */ azimuthalRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\nfunction azimuthalRaw(scale) {\n    return function(x, y) {\n        var cx = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x), cy = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y), k = scale(cx * cy);\n        if (k === Infinity) return [\n            2,\n            0\n        ];\n        return [\n            k * cy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x),\n            k * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y)\n        ];\n    };\n}\nfunction azimuthalInvert(angle) {\n    return function(x, y) {\n        var z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(x * x + y * y), c = angle(z), sc = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(c), cc = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(c);\n        return [\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(x * sc, z * cc),\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(z && y * sc / z)\n        ];\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2F6aW11dGhhbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBdUQ7QUFFaEQsU0FBU0ssYUFBYUMsS0FBSztJQUNoQyxPQUFPLFNBQVNDLENBQUMsRUFBRUMsQ0FBQztRQUNsQixJQUFJQyxLQUFLUCw2Q0FBR0EsQ0FBQ0ssSUFDVEcsS0FBS1IsNkNBQUdBLENBQUNNLElBQ1RHLElBQUlMLE1BQU1HLEtBQUtDO1FBQ2YsSUFBSUMsTUFBTUMsVUFBVSxPQUFPO1lBQUM7WUFBRztTQUFFO1FBQ3JDLE9BQU87WUFDTEQsSUFBSUQsS0FBS1AsNkNBQUdBLENBQUNJO1lBQ2JJLElBQUlSLDZDQUFHQSxDQUFDSztTQUNUO0lBQ0g7QUFDRjtBQUVPLFNBQVNLLGdCQUFnQkMsS0FBSztJQUNuQyxPQUFPLFNBQVNQLENBQUMsRUFBRUMsQ0FBQztRQUNsQixJQUFJTyxJQUFJWCw4Q0FBSUEsQ0FBQ0csSUFBSUEsSUFBSUMsSUFBSUEsSUFDckJRLElBQUlGLE1BQU1DLElBQ1ZFLEtBQUtkLDZDQUFHQSxDQUFDYSxJQUNURSxLQUFLaEIsNkNBQUdBLENBQUNjO1FBQ2IsT0FBTztZQUNMZiwrQ0FBS0EsQ0FBQ00sSUFBSVUsSUFBSUYsSUFBSUc7WUFDbEJsQiw4Q0FBSUEsQ0FBQ2UsS0FBS1AsSUFBSVMsS0FBS0Y7U0FDcEI7SUFDSDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2F6aW11dGhhbC5qcz80N2RmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7YXNpbiwgYXRhbjIsIGNvcywgc2luLCBzcXJ0fSBmcm9tIFwiLi4vbWF0aC5qc1wiO1xuXG5leHBvcnQgZnVuY3Rpb24gYXppbXV0aGFsUmF3KHNjYWxlKSB7XG4gIHJldHVybiBmdW5jdGlvbih4LCB5KSB7XG4gICAgdmFyIGN4ID0gY29zKHgpLFxuICAgICAgICBjeSA9IGNvcyh5KSxcbiAgICAgICAgayA9IHNjYWxlKGN4ICogY3kpO1xuICAgICAgICBpZiAoayA9PT0gSW5maW5pdHkpIHJldHVybiBbMiwgMF07XG4gICAgcmV0dXJuIFtcbiAgICAgIGsgKiBjeSAqIHNpbih4KSxcbiAgICAgIGsgKiBzaW4oeSlcbiAgICBdO1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBhemltdXRoYWxJbnZlcnQoYW5nbGUpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKHgsIHkpIHtcbiAgICB2YXIgeiA9IHNxcnQoeCAqIHggKyB5ICogeSksXG4gICAgICAgIGMgPSBhbmdsZSh6KSxcbiAgICAgICAgc2MgPSBzaW4oYyksXG4gICAgICAgIGNjID0gY29zKGMpO1xuICAgIHJldHVybiBbXG4gICAgICBhdGFuMih4ICogc2MsIHogKiBjYyksXG4gICAgICBhc2luKHogJiYgeSAqIHNjIC8geilcbiAgICBdO1xuICB9XG59XG4iXSwibmFtZXMiOlsiYXNpbiIsImF0YW4yIiwiY29zIiwic2luIiwic3FydCIsImF6aW11dGhhbFJhdyIsInNjYWxlIiwieCIsInkiLCJjeCIsImN5IiwiayIsIkluZmluaXR5IiwiYXppbXV0aGFsSW52ZXJ0IiwiYW5nbGUiLCJ6IiwiYyIsInNjIiwiY2MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/azimuthalEqualArea.js":
/*!******************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/azimuthalEqualArea.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   azimuthalEqualAreaRaw: () => (/* binding */ azimuthalEqualAreaRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\nvar azimuthalEqualAreaRaw = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_0__.azimuthalRaw)(function(cxcy) {\n    return (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sqrt)(2 / (1 + cxcy));\n});\nazimuthalEqualAreaRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_0__.azimuthalInvert)(function(z) {\n    return 2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.asin)(z / 2);\n});\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(azimuthalEqualAreaRaw).scale(124.75).clipAngle(180 - 1e-3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2F6aW11dGhhbEVxdWFsQXJlYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFzQztBQUN1QjtBQUN6QjtBQUU3QixJQUFJSyx3QkFBd0JILDJEQUFZQSxDQUFDLFNBQVNJLElBQUk7SUFDM0QsT0FBT0wsOENBQUlBLENBQUMsSUFBSyxLQUFJSyxJQUFHO0FBQzFCLEdBQUc7QUFFSEQsc0JBQXNCRSxNQUFNLEdBQUdKLDhEQUFlQSxDQUFDLFNBQVNLLENBQUM7SUFDdkQsT0FBTyxJQUFJUiw4Q0FBSUEsQ0FBQ1EsSUFBSTtBQUN0QjtBQUVBLDZCQUFlLHNDQUFXO0lBQ3hCLE9BQU9KLHFEQUFVQSxDQUFDQyx1QkFDYkksS0FBSyxDQUFDLFFBQ05DLFNBQVMsQ0FBQyxNQUFNO0FBQ3ZCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2F6aW11dGhhbEVxdWFsQXJlYS5qcz9iZmRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7YXNpbiwgc3FydH0gZnJvbSBcIi4uL21hdGguanNcIjtcbmltcG9ydCB7YXppbXV0aGFsUmF3LCBhemltdXRoYWxJbnZlcnR9IGZyb20gXCIuL2F6aW11dGhhbC5qc1wiO1xuaW1wb3J0IHByb2plY3Rpb24gZnJvbSBcIi4vaW5kZXguanNcIjtcblxuZXhwb3J0IHZhciBhemltdXRoYWxFcXVhbEFyZWFSYXcgPSBhemltdXRoYWxSYXcoZnVuY3Rpb24oY3hjeSkge1xuICByZXR1cm4gc3FydCgyIC8gKDEgKyBjeGN5KSk7XG59KTtcblxuYXppbXV0aGFsRXF1YWxBcmVhUmF3LmludmVydCA9IGF6aW11dGhhbEludmVydChmdW5jdGlvbih6KSB7XG4gIHJldHVybiAyICogYXNpbih6IC8gMik7XG59KTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHJldHVybiBwcm9qZWN0aW9uKGF6aW11dGhhbEVxdWFsQXJlYVJhdylcbiAgICAgIC5zY2FsZSgxMjQuNzUpXG4gICAgICAuY2xpcEFuZ2xlKDE4MCAtIDFlLTMpO1xufVxuIl0sIm5hbWVzIjpbImFzaW4iLCJzcXJ0IiwiYXppbXV0aGFsUmF3IiwiYXppbXV0aGFsSW52ZXJ0IiwicHJvamVjdGlvbiIsImF6aW11dGhhbEVxdWFsQXJlYVJhdyIsImN4Y3kiLCJpbnZlcnQiLCJ6Iiwic2NhbGUiLCJjbGlwQW5nbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/azimuthalEqualArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/azimuthalEquidistant.js":
/*!********************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/azimuthalEquidistant.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   azimuthalEquidistantRaw: () => (/* binding */ azimuthalEquidistantRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\nvar azimuthalEquidistantRaw = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_0__.azimuthalRaw)(function(c) {\n    return (c = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.acos)(c)) && c / (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(c);\n});\nazimuthalEquidistantRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_0__.azimuthalInvert)(function(z) {\n    return z;\n});\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(azimuthalEquidistantRaw).scale(79.4188).clipAngle(180 - 1e-3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2F6aW11dGhhbEVxdWlkaXN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFDO0FBQ3dCO0FBQ3pCO0FBRTdCLElBQUlLLDBCQUEwQkgsMkRBQVlBLENBQUMsU0FBU0ksQ0FBQztJQUMxRCxPQUFPLENBQUNBLElBQUlOLDhDQUFJQSxDQUFDTSxFQUFDLEtBQU1BLElBQUlMLDZDQUFHQSxDQUFDSztBQUNsQyxHQUFHO0FBRUhELHdCQUF3QkUsTUFBTSxHQUFHSiw4REFBZUEsQ0FBQyxTQUFTSyxDQUFDO0lBQ3pELE9BQU9BO0FBQ1Q7QUFFQSw2QkFBZSxzQ0FBVztJQUN4QixPQUFPSixxREFBVUEsQ0FBQ0MseUJBQ2JJLEtBQUssQ0FBQyxTQUNOQyxTQUFTLENBQUMsTUFBTTtBQUN2QiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9hemltdXRoYWxFcXVpZGlzdGFudC5qcz84MWFhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7YWNvcywgc2lufSBmcm9tIFwiLi4vbWF0aC5qc1wiO1xuaW1wb3J0IHthemltdXRoYWxSYXcsIGF6aW11dGhhbEludmVydH0gZnJvbSBcIi4vYXppbXV0aGFsLmpzXCI7XG5pbXBvcnQgcHJvamVjdGlvbiBmcm9tIFwiLi9pbmRleC5qc1wiO1xuXG5leHBvcnQgdmFyIGF6aW11dGhhbEVxdWlkaXN0YW50UmF3ID0gYXppbXV0aGFsUmF3KGZ1bmN0aW9uKGMpIHtcbiAgcmV0dXJuIChjID0gYWNvcyhjKSkgJiYgYyAvIHNpbihjKTtcbn0pO1xuXG5hemltdXRoYWxFcXVpZGlzdGFudFJhdy5pbnZlcnQgPSBhemltdXRoYWxJbnZlcnQoZnVuY3Rpb24oeikge1xuICByZXR1cm4gejtcbn0pO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIHByb2plY3Rpb24oYXppbXV0aGFsRXF1aWRpc3RhbnRSYXcpXG4gICAgICAuc2NhbGUoNzkuNDE4OClcbiAgICAgIC5jbGlwQW5nbGUoMTgwIC0gMWUtMyk7XG59XG4iXSwibmFtZXMiOlsiYWNvcyIsInNpbiIsImF6aW11dGhhbFJhdyIsImF6aW11dGhhbEludmVydCIsInByb2plY3Rpb24iLCJhemltdXRoYWxFcXVpZGlzdGFudFJhdyIsImMiLCJpbnZlcnQiLCJ6Iiwic2NhbGUiLCJjbGlwQW5nbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/azimuthalEquidistant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/conic.js":
/*!*****************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/conic.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conicProjection: () => (/* binding */ conicProjection)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\nfunction conicProjection(projectAt) {\n    var phi0 = 0, phi1 = _math_js__WEBPACK_IMPORTED_MODULE_0__.pi / 3, m = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__.projectionMutator)(projectAt), p = m(phi0, phi1);\n    p.parallels = function(_) {\n        return arguments.length ? m(phi0 = _[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, phi1 = _[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians) : [\n            phi0 * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees,\n            phi1 * _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees\n        ];\n    };\n    return p;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2NvbmljLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNIO0FBRXRDLFNBQVNJLGdCQUFnQkMsU0FBUztJQUN2QyxJQUFJQyxPQUFPLEdBQ1BDLE9BQU9OLHdDQUFFQSxHQUFHLEdBQ1pPLElBQUlMLDREQUFpQkEsQ0FBQ0UsWUFDdEJJLElBQUlELEVBQUVGLE1BQU1DO0lBRWhCRSxFQUFFQyxTQUFTLEdBQUcsU0FBU0MsQ0FBQztRQUN0QixPQUFPQyxVQUFVQyxNQUFNLEdBQUdMLEVBQUVGLE9BQU9LLENBQUMsQ0FBQyxFQUFFLEdBQUdULDZDQUFPQSxFQUFFSyxPQUFPSSxDQUFDLENBQUMsRUFBRSxHQUFHVCw2Q0FBT0EsSUFBSTtZQUFDSSxPQUFPTiw2Q0FBT0E7WUFBRU8sT0FBT1AsNkNBQU9BO1NBQUM7SUFDOUc7SUFFQSxPQUFPUztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2NvbmljLmpzPzA4NmMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtkZWdyZWVzLCBwaSwgcmFkaWFuc30gZnJvbSBcIi4uL21hdGguanNcIjtcbmltcG9ydCB7cHJvamVjdGlvbk11dGF0b3J9IGZyb20gXCIuL2luZGV4LmpzXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBjb25pY1Byb2plY3Rpb24ocHJvamVjdEF0KSB7XG4gIHZhciBwaGkwID0gMCxcbiAgICAgIHBoaTEgPSBwaSAvIDMsXG4gICAgICBtID0gcHJvamVjdGlvbk11dGF0b3IocHJvamVjdEF0KSxcbiAgICAgIHAgPSBtKHBoaTAsIHBoaTEpO1xuXG4gIHAucGFyYWxsZWxzID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gbShwaGkwID0gX1swXSAqIHJhZGlhbnMsIHBoaTEgPSBfWzFdICogcmFkaWFucykgOiBbcGhpMCAqIGRlZ3JlZXMsIHBoaTEgKiBkZWdyZWVzXTtcbiAgfTtcblxuICByZXR1cm4gcDtcbn1cbiJdLCJuYW1lcyI6WyJkZWdyZWVzIiwicGkiLCJyYWRpYW5zIiwicHJvamVjdGlvbk11dGF0b3IiLCJjb25pY1Byb2plY3Rpb24iLCJwcm9qZWN0QXQiLCJwaGkwIiwicGhpMSIsIm0iLCJwIiwicGFyYWxsZWxzIiwiXyIsImFyZ3VtZW50cyIsImxlbmd0aCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/conic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/conicConformal.js":
/*!**************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/conicConformal.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conicConformalRaw: () => (/* binding */ conicConformalRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _conic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conic.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conic.js\");\n/* harmony import */ var _mercator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mercator.js */ \"(ssr)/./node_modules/d3-geo/src/projection/mercator.js\");\n\n\n\nfunction tany(y) {\n    return (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.tan)((_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + y) / 2);\n}\nfunction conicConformalRaw(y0, y1) {\n    var cy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y0), n = y0 === y1 ? (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y0) : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.log)(cy0 / (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y1)) / (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.log)(tany(y1) / tany(y0)), f = cy0 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.pow)(tany(y0), n) / n;\n    if (!n) return _mercator_js__WEBPACK_IMPORTED_MODULE_1__.mercatorRaw;\n    function project(x, y) {\n        if (f > 0) {\n            if (y < -_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) y = -_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n        } else {\n            if (y > _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) y = _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi - _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon;\n        }\n        var r = f / (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.pow)(tany(y), n);\n        return [\n            r * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(n * x),\n            f - r * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(n * x)\n        ];\n    }\n    project.invert = function(x, y) {\n        var fy = f - y, r = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(n) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(x * x + fy * fy), l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(x, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(fy)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(fy);\n        if (fy * n < 0) l -= _math_js__WEBPACK_IMPORTED_MODULE_0__.pi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(x) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(fy);\n        return [\n            l / n,\n            2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.pow)(f / r, 1 / n)) - _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi\n        ];\n    };\n    return project;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_conic_js__WEBPACK_IMPORTED_MODULE_2__.conicProjection)(conicConformalRaw).scale(109.5).parallels([\n        30,\n        30\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/conicConformal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/conicEqualArea.js":
/*!**************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/conicEqualArea.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conicEqualAreaRaw: () => (/* binding */ conicEqualAreaRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _conic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conic.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conic.js\");\n/* harmony import */ var _cylindricalEqualArea_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cylindricalEqualArea.js */ \"(ssr)/./node_modules/d3-geo/src/projection/cylindricalEqualArea.js\");\n\n\n\nfunction conicEqualAreaRaw(y0, y1) {\n    var sy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y0), n = (sy0 + (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y1)) / 2;\n    // Are the parallels symmetrical around the Equator?\n    if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(n) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) return (0,_cylindricalEqualArea_js__WEBPACK_IMPORTED_MODULE_1__.cylindricalEqualAreaRaw)(y0);\n    var c = 1 + sy0 * (2 * n - sy0), r0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(c) / n;\n    function project(x, y) {\n        var r = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(c - 2 * n * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y)) / n;\n        return [\n            r * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x *= n),\n            r0 - r * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x)\n        ];\n    }\n    project.invert = function(x, y) {\n        var r0y = r0 - y, l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(x, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(r0y)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(r0y);\n        if (r0y * n < 0) l -= _math_js__WEBPACK_IMPORTED_MODULE_0__.pi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(x) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(r0y);\n        return [\n            l / n,\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)((c - (x * x + r0y * r0y) * n * n) / (2 * n))\n        ];\n    };\n    return project;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_conic_js__WEBPACK_IMPORTED_MODULE_2__.conicProjection)(conicEqualAreaRaw).scale(155.424).center([\n        0,\n        33.6442\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/conicEqualArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/conicEquidistant.js":
/*!****************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/conicEquidistant.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conicEquidistantRaw: () => (/* binding */ conicEquidistantRaw),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _conic_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conic.js */ \"(ssr)/./node_modules/d3-geo/src/projection/conic.js\");\n/* harmony import */ var _equirectangular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./equirectangular.js */ \"(ssr)/./node_modules/d3-geo/src/projection/equirectangular.js\");\n\n\n\nfunction conicEquidistantRaw(y0, y1) {\n    var cy0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y0), n = y0 === y1 ? (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y0) : (cy0 - (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y1)) / (y1 - y0), g = cy0 / n + y0;\n    if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(n) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon) return _equirectangular_js__WEBPACK_IMPORTED_MODULE_1__.equirectangularRaw;\n    function project(x, y) {\n        var gy = g - y, nx = n * x;\n        return [\n            gy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(nx),\n            g - gy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(nx)\n        ];\n    }\n    project.invert = function(x, y) {\n        var gy = g - y, l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(x, (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(gy)) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(gy);\n        if (gy * n < 0) l -= _math_js__WEBPACK_IMPORTED_MODULE_0__.pi * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(x) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(gy);\n        return [\n            l / n,\n            g - (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sign)(n) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(x * x + gy * gy)\n        ];\n    };\n    return project;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_conic_js__WEBPACK_IMPORTED_MODULE_2__.conicProjection)(conicEquidistantRaw).scale(131.154).center([\n        0,\n        13.9389\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/conicEquidistant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/cylindricalEqualArea.js":
/*!********************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/cylindricalEqualArea.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cylindricalEqualAreaRaw: () => (/* binding */ cylindricalEqualAreaRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\nfunction cylindricalEqualAreaRaw(phi0) {\n    var cosPhi0 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi0);\n    function forward(lambda, phi) {\n        return [\n            lambda * cosPhi0,\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi) / cosPhi0\n        ];\n    }\n    forward.invert = function(x, y) {\n        return [\n            x / cosPhi0,\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(y * cosPhi0)\n        ];\n    };\n    return forward;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2N5bGluZHJpY2FsRXF1YWxBcmVhLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBDO0FBRW5DLFNBQVNHLHdCQUF3QkMsSUFBSTtJQUMxQyxJQUFJQyxVQUFVSiw2Q0FBR0EsQ0FBQ0c7SUFFbEIsU0FBU0UsUUFBUUMsTUFBTSxFQUFFQyxHQUFHO1FBQzFCLE9BQU87WUFBQ0QsU0FBU0Y7WUFBU0gsNkNBQUdBLENBQUNNLE9BQU9IO1NBQVE7SUFDL0M7SUFFQUMsUUFBUUcsTUFBTSxHQUFHLFNBQVNDLENBQUMsRUFBRUMsQ0FBQztRQUM1QixPQUFPO1lBQUNELElBQUlMO1lBQVNMLDhDQUFJQSxDQUFDVyxJQUFJTjtTQUFTO0lBQ3pDO0lBRUEsT0FBT0M7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9jeWxpbmRyaWNhbEVxdWFsQXJlYS5qcz8wYzczIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7YXNpbiwgY29zLCBzaW59IGZyb20gXCIuLi9tYXRoLmpzXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBjeWxpbmRyaWNhbEVxdWFsQXJlYVJhdyhwaGkwKSB7XG4gIHZhciBjb3NQaGkwID0gY29zKHBoaTApO1xuXG4gIGZ1bmN0aW9uIGZvcndhcmQobGFtYmRhLCBwaGkpIHtcbiAgICByZXR1cm4gW2xhbWJkYSAqIGNvc1BoaTAsIHNpbihwaGkpIC8gY29zUGhpMF07XG4gIH1cblxuICBmb3J3YXJkLmludmVydCA9IGZ1bmN0aW9uKHgsIHkpIHtcbiAgICByZXR1cm4gW3ggLyBjb3NQaGkwLCBhc2luKHkgKiBjb3NQaGkwKV07XG4gIH07XG5cbiAgcmV0dXJuIGZvcndhcmQ7XG59XG4iXSwibmFtZXMiOlsiYXNpbiIsImNvcyIsInNpbiIsImN5bGluZHJpY2FsRXF1YWxBcmVhUmF3IiwicGhpMCIsImNvc1BoaTAiLCJmb3J3YXJkIiwibGFtYmRhIiwicGhpIiwiaW52ZXJ0IiwieCIsInkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/cylindricalEqualArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/equalEarth.js":
/*!**********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/equalEarth.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   equalEarthRaw: () => (/* binding */ equalEarthRaw)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\nvar A1 = 1.340264, A2 = -0.081106, A3 = 0.000893, A4 = 0.003796, M = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(3) / 2, iterations = 12;\nfunction equalEarthRaw(lambda, phi) {\n    var l = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(M * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi)), l2 = l * l, l6 = l2 * l2 * l2;\n    return [\n        lambda * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(l) / (M * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2))),\n        l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2))\n    ];\n}\nequalEarthRaw.invert = function(x, y) {\n    var l = y, l2 = l * l, l6 = l2 * l2 * l2;\n    for(var i = 0, delta, fy, fpy; i < iterations; ++i){\n        fy = l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2)) - y;\n        fpy = A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2);\n        l -= delta = fy / fpy, l2 = l * l, l6 = l2 * l2 * l2;\n        if ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(delta) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon2) break;\n    }\n    return [\n        M * x * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2)) / (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(l),\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(l) / M)\n    ];\n};\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(equalEarthRaw).scale(177.158);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/equalEarth.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/equirectangular.js":
/*!***************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/equirectangular.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   equirectangularRaw: () => (/* binding */ equirectangularRaw)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\nfunction equirectangularRaw(lambda, phi) {\n    return [\n        lambda,\n        phi\n    ];\n}\nequirectangularRaw.invert = equirectangularRaw;\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(equirectangularRaw).scale(152.63);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2VxdWlyZWN0YW5ndWxhci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0M7QUFFN0IsU0FBU0MsbUJBQW1CQyxNQUFNLEVBQUVDLEdBQUc7SUFDNUMsT0FBTztRQUFDRDtRQUFRQztLQUFJO0FBQ3RCO0FBRUFGLG1CQUFtQkcsTUFBTSxHQUFHSDtBQUU1Qiw2QkFBZSxzQ0FBVztJQUN4QixPQUFPRCxxREFBVUEsQ0FBQ0Msb0JBQ2JJLEtBQUssQ0FBQztBQUNiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RvcmVzcHkvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2VxdWlyZWN0YW5ndWxhci5qcz81MDBkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBwcm9qZWN0aW9uIGZyb20gXCIuL2luZGV4LmpzXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBlcXVpcmVjdGFuZ3VsYXJSYXcobGFtYmRhLCBwaGkpIHtcbiAgcmV0dXJuIFtsYW1iZGEsIHBoaV07XG59XG5cbmVxdWlyZWN0YW5ndWxhclJhdy5pbnZlcnQgPSBlcXVpcmVjdGFuZ3VsYXJSYXc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gcHJvamVjdGlvbihlcXVpcmVjdGFuZ3VsYXJSYXcpXG4gICAgICAuc2NhbGUoMTUyLjYzKTtcbn1cbiJdLCJuYW1lcyI6WyJwcm9qZWN0aW9uIiwiZXF1aXJlY3Rhbmd1bGFyUmF3IiwibGFtYmRhIiwicGhpIiwiaW52ZXJ0Iiwic2NhbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/equirectangular.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/fit.js":
/*!***************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/fit.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fitExtent: () => (/* binding */ fitExtent),\n/* harmony export */   fitHeight: () => (/* binding */ fitHeight),\n/* harmony export */   fitSize: () => (/* binding */ fitSize),\n/* harmony export */   fitWidth: () => (/* binding */ fitWidth)\n/* harmony export */ });\n/* harmony import */ var _stream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stream.js */ \"(ssr)/./node_modules/d3-geo/src/stream.js\");\n/* harmony import */ var _path_bounds_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../path/bounds.js */ \"(ssr)/./node_modules/d3-geo/src/path/bounds.js\");\n\n\nfunction fit(projection, fitBounds, object) {\n    var clip = projection.clipExtent && projection.clipExtent();\n    projection.scale(150).translate([\n        0,\n        0\n    ]);\n    if (clip != null) projection.clipExtent(null);\n    (0,_stream_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(object, projection.stream(_path_bounds_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]));\n    fitBounds(_path_bounds_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].result());\n    if (clip != null) projection.clipExtent(clip);\n    return projection;\n}\nfunction fitExtent(projection, extent, object) {\n    return fit(projection, function(b) {\n        var w = extent[1][0] - extent[0][0], h = extent[1][1] - extent[0][1], k = Math.min(w / (b[1][0] - b[0][0]), h / (b[1][1] - b[0][1])), x = +extent[0][0] + (w - k * (b[1][0] + b[0][0])) / 2, y = +extent[0][1] + (h - k * (b[1][1] + b[0][1])) / 2;\n        projection.scale(150 * k).translate([\n            x,\n            y\n        ]);\n    }, object);\n}\nfunction fitSize(projection, size, object) {\n    return fitExtent(projection, [\n        [\n            0,\n            0\n        ],\n        size\n    ], object);\n}\nfunction fitWidth(projection, width, object) {\n    return fit(projection, function(b) {\n        var w = +width, k = w / (b[1][0] - b[0][0]), x = (w - k * (b[1][0] + b[0][0])) / 2, y = -k * b[0][1];\n        projection.scale(150 * k).translate([\n            x,\n            y\n        ]);\n    }, object);\n}\nfunction fitHeight(projection, height, object) {\n    return fit(projection, function(b) {\n        var h = +height, k = h / (b[1][1] - b[0][1]), x = -k * b[0][0], y = (h - k * (b[1][1] + b[0][1])) / 2;\n        projection.scale(150 * k).translate([\n            x,\n            y\n        ]);\n    }, object);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2ZpdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBa0Q7QUFDTDtBQUU3QyxTQUFTRyxJQUFJQyxVQUFVLEVBQUVDLFNBQVMsRUFBRUMsTUFBTTtJQUN4QyxJQUFJQyxPQUFPSCxXQUFXSSxVQUFVLElBQUlKLFdBQVdJLFVBQVU7SUFDekRKLFdBQVdLLEtBQUssQ0FBQyxLQUFLQyxTQUFTLENBQUM7UUFBQztRQUFHO0tBQUU7SUFDdEMsSUFBSUgsUUFBUSxNQUFNSCxXQUFXSSxVQUFVLENBQUM7SUFDeENQLHNEQUFTQSxDQUFDSyxRQUFRRixXQUFXTyxNQUFNLENBQUNULHVEQUFZQTtJQUNoREcsVUFBVUgsdURBQVlBLENBQUNVLE1BQU07SUFDN0IsSUFBSUwsUUFBUSxNQUFNSCxXQUFXSSxVQUFVLENBQUNEO0lBQ3hDLE9BQU9IO0FBQ1Q7QUFFTyxTQUFTUyxVQUFVVCxVQUFVLEVBQUVVLE1BQU0sRUFBRVIsTUFBTTtJQUNsRCxPQUFPSCxJQUFJQyxZQUFZLFNBQVNXLENBQUM7UUFDL0IsSUFBSUMsSUFBSUYsTUFBTSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUdBLE1BQU0sQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUMvQkcsSUFBSUgsTUFBTSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUdBLE1BQU0sQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUMvQkksSUFBSUMsS0FBS0MsR0FBRyxDQUFDSixJQUFLRCxDQUFBQSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBR0EsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUdFLElBQUtGLENBQUFBLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUFHQSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsSUFDNURNLElBQUksQ0FBQ1AsTUFBTSxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUcsQ0FBQ0UsSUFBSUUsSUFBS0gsQ0FBQUEsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUdBLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxLQUFLLEdBQ3BETyxJQUFJLENBQUNSLE1BQU0sQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUFHLENBQUNHLElBQUlDLElBQUtILENBQUFBLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUFHQSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsS0FBSztRQUN4RFgsV0FBV0ssS0FBSyxDQUFDLE1BQU1TLEdBQUdSLFNBQVMsQ0FBQztZQUFDVztZQUFHQztTQUFFO0lBQzVDLEdBQUdoQjtBQUNMO0FBRU8sU0FBU2lCLFFBQVFuQixVQUFVLEVBQUVvQixJQUFJLEVBQUVsQixNQUFNO0lBQzlDLE9BQU9PLFVBQVVULFlBQVk7UUFBQztZQUFDO1lBQUc7U0FBRTtRQUFFb0I7S0FBSyxFQUFFbEI7QUFDL0M7QUFFTyxTQUFTbUIsU0FBU3JCLFVBQVUsRUFBRXNCLEtBQUssRUFBRXBCLE1BQU07SUFDaEQsT0FBT0gsSUFBSUMsWUFBWSxTQUFTVyxDQUFDO1FBQy9CLElBQUlDLElBQUksQ0FBQ1UsT0FDTFIsSUFBSUYsSUFBS0QsQ0FBQUEsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUdBLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUMxQk0sSUFBSSxDQUFDTCxJQUFJRSxJQUFLSCxDQUFBQSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBR0EsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEtBQUssR0FDcENPLElBQUksQ0FBQ0osSUFBSUgsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFO1FBQ3BCWCxXQUFXSyxLQUFLLENBQUMsTUFBTVMsR0FBR1IsU0FBUyxDQUFDO1lBQUNXO1lBQUdDO1NBQUU7SUFDNUMsR0FBR2hCO0FBQ0w7QUFFTyxTQUFTcUIsVUFBVXZCLFVBQVUsRUFBRXdCLE1BQU0sRUFBRXRCLE1BQU07SUFDbEQsT0FBT0gsSUFBSUMsWUFBWSxTQUFTVyxDQUFDO1FBQy9CLElBQUlFLElBQUksQ0FBQ1csUUFDTFYsSUFBSUQsSUFBS0YsQ0FBQUEsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUdBLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxHQUMxQk0sSUFBSSxDQUFDSCxJQUFJSCxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFDaEJPLElBQUksQ0FBQ0wsSUFBSUMsSUFBS0gsQ0FBQUEsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEdBQUdBLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxLQUFLO1FBQ3hDWCxXQUFXSyxLQUFLLENBQUMsTUFBTVMsR0FBR1IsU0FBUyxDQUFDO1lBQUNXO1lBQUdDO1NBQUU7SUFDNUMsR0FBR2hCO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL3Byb2plY3Rpb24vZml0LmpzP2Q0ZmIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtkZWZhdWx0IGFzIGdlb1N0cmVhbX0gZnJvbSBcIi4uL3N0cmVhbS5qc1wiO1xuaW1wb3J0IGJvdW5kc1N0cmVhbSBmcm9tIFwiLi4vcGF0aC9ib3VuZHMuanNcIjtcblxuZnVuY3Rpb24gZml0KHByb2plY3Rpb24sIGZpdEJvdW5kcywgb2JqZWN0KSB7XG4gIHZhciBjbGlwID0gcHJvamVjdGlvbi5jbGlwRXh0ZW50ICYmIHByb2plY3Rpb24uY2xpcEV4dGVudCgpO1xuICBwcm9qZWN0aW9uLnNjYWxlKDE1MCkudHJhbnNsYXRlKFswLCAwXSk7XG4gIGlmIChjbGlwICE9IG51bGwpIHByb2plY3Rpb24uY2xpcEV4dGVudChudWxsKTtcbiAgZ2VvU3RyZWFtKG9iamVjdCwgcHJvamVjdGlvbi5zdHJlYW0oYm91bmRzU3RyZWFtKSk7XG4gIGZpdEJvdW5kcyhib3VuZHNTdHJlYW0ucmVzdWx0KCkpO1xuICBpZiAoY2xpcCAhPSBudWxsKSBwcm9qZWN0aW9uLmNsaXBFeHRlbnQoY2xpcCk7XG4gIHJldHVybiBwcm9qZWN0aW9uO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZml0RXh0ZW50KHByb2plY3Rpb24sIGV4dGVudCwgb2JqZWN0KSB7XG4gIHJldHVybiBmaXQocHJvamVjdGlvbiwgZnVuY3Rpb24oYikge1xuICAgIHZhciB3ID0gZXh0ZW50WzFdWzBdIC0gZXh0ZW50WzBdWzBdLFxuICAgICAgICBoID0gZXh0ZW50WzFdWzFdIC0gZXh0ZW50WzBdWzFdLFxuICAgICAgICBrID0gTWF0aC5taW4odyAvIChiWzFdWzBdIC0gYlswXVswXSksIGggLyAoYlsxXVsxXSAtIGJbMF1bMV0pKSxcbiAgICAgICAgeCA9ICtleHRlbnRbMF1bMF0gKyAodyAtIGsgKiAoYlsxXVswXSArIGJbMF1bMF0pKSAvIDIsXG4gICAgICAgIHkgPSArZXh0ZW50WzBdWzFdICsgKGggLSBrICogKGJbMV1bMV0gKyBiWzBdWzFdKSkgLyAyO1xuICAgIHByb2plY3Rpb24uc2NhbGUoMTUwICogaykudHJhbnNsYXRlKFt4LCB5XSk7XG4gIH0sIG9iamVjdCk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmaXRTaXplKHByb2plY3Rpb24sIHNpemUsIG9iamVjdCkge1xuICByZXR1cm4gZml0RXh0ZW50KHByb2plY3Rpb24sIFtbMCwgMF0sIHNpemVdLCBvYmplY3QpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZml0V2lkdGgocHJvamVjdGlvbiwgd2lkdGgsIG9iamVjdCkge1xuICByZXR1cm4gZml0KHByb2plY3Rpb24sIGZ1bmN0aW9uKGIpIHtcbiAgICB2YXIgdyA9ICt3aWR0aCxcbiAgICAgICAgayA9IHcgLyAoYlsxXVswXSAtIGJbMF1bMF0pLFxuICAgICAgICB4ID0gKHcgLSBrICogKGJbMV1bMF0gKyBiWzBdWzBdKSkgLyAyLFxuICAgICAgICB5ID0gLWsgKiBiWzBdWzFdO1xuICAgIHByb2plY3Rpb24uc2NhbGUoMTUwICogaykudHJhbnNsYXRlKFt4LCB5XSk7XG4gIH0sIG9iamVjdCk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmaXRIZWlnaHQocHJvamVjdGlvbiwgaGVpZ2h0LCBvYmplY3QpIHtcbiAgcmV0dXJuIGZpdChwcm9qZWN0aW9uLCBmdW5jdGlvbihiKSB7XG4gICAgdmFyIGggPSAraGVpZ2h0LFxuICAgICAgICBrID0gaCAvIChiWzFdWzFdIC0gYlswXVsxXSksXG4gICAgICAgIHggPSAtayAqIGJbMF1bMF0sXG4gICAgICAgIHkgPSAoaCAtIGsgKiAoYlsxXVsxXSArIGJbMF1bMV0pKSAvIDI7XG4gICAgcHJvamVjdGlvbi5zY2FsZSgxNTAgKiBrKS50cmFuc2xhdGUoW3gsIHldKTtcbiAgfSwgb2JqZWN0KTtcbn1cbiJdLCJuYW1lcyI6WyJkZWZhdWx0IiwiZ2VvU3RyZWFtIiwiYm91bmRzU3RyZWFtIiwiZml0IiwicHJvamVjdGlvbiIsImZpdEJvdW5kcyIsIm9iamVjdCIsImNsaXAiLCJjbGlwRXh0ZW50Iiwic2NhbGUiLCJ0cmFuc2xhdGUiLCJzdHJlYW0iLCJyZXN1bHQiLCJmaXRFeHRlbnQiLCJleHRlbnQiLCJiIiwidyIsImgiLCJrIiwiTWF0aCIsIm1pbiIsIngiLCJ5IiwiZml0U2l6ZSIsInNpemUiLCJmaXRXaWR0aCIsIndpZHRoIiwiZml0SGVpZ2h0IiwiaGVpZ2h0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/fit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/gnomonic.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/gnomonic.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   gnomonicRaw: () => (/* binding */ gnomonicRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\nfunction gnomonicRaw(x, y) {\n    var cy = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y), k = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x) * cy;\n    return [\n        cy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x) / k,\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y) / k\n    ];\n}\ngnomonicRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_1__.azimuthalInvert)(_math_js__WEBPACK_IMPORTED_MODULE_0__.atan);\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(gnomonicRaw).scale(144.049).clipAngle(60);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL2dub21vbmljLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTBDO0FBQ0s7QUFDWDtBQUU3QixTQUFTSyxZQUFZQyxDQUFDLEVBQUVDLENBQUM7SUFDOUIsSUFBSUMsS0FBS1AsNkNBQUdBLENBQUNNLElBQUlFLElBQUlSLDZDQUFHQSxDQUFDSyxLQUFLRTtJQUM5QixPQUFPO1FBQUNBLEtBQUtOLDZDQUFHQSxDQUFDSSxLQUFLRztRQUFHUCw2Q0FBR0EsQ0FBQ0ssS0FBS0U7S0FBRTtBQUN0QztBQUVBSixZQUFZSyxNQUFNLEdBQUdQLDhEQUFlQSxDQUFDSCwwQ0FBSUE7QUFFekMsNkJBQWUsc0NBQVc7SUFDeEIsT0FBT0kscURBQVVBLENBQUNDLGFBQ2JNLEtBQUssQ0FBQyxTQUNOQyxTQUFTLENBQUM7QUFDakIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL3Byb2plY3Rpb24vZ25vbW9uaWMuanM/ZGUyNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2F0YW4sIGNvcywgc2lufSBmcm9tIFwiLi4vbWF0aC5qc1wiO1xuaW1wb3J0IHthemltdXRoYWxJbnZlcnR9IGZyb20gXCIuL2F6aW11dGhhbC5qc1wiO1xuaW1wb3J0IHByb2plY3Rpb24gZnJvbSBcIi4vaW5kZXguanNcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIGdub21vbmljUmF3KHgsIHkpIHtcbiAgdmFyIGN5ID0gY29zKHkpLCBrID0gY29zKHgpICogY3k7XG4gIHJldHVybiBbY3kgKiBzaW4oeCkgLyBrLCBzaW4oeSkgLyBrXTtcbn1cblxuZ25vbW9uaWNSYXcuaW52ZXJ0ID0gYXppbXV0aGFsSW52ZXJ0KGF0YW4pO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIHByb2plY3Rpb24oZ25vbW9uaWNSYXcpXG4gICAgICAuc2NhbGUoMTQ0LjA0OSlcbiAgICAgIC5jbGlwQW5nbGUoNjApO1xufVxuIl0sIm5hbWVzIjpbImF0YW4iLCJjb3MiLCJzaW4iLCJhemltdXRoYWxJbnZlcnQiLCJwcm9qZWN0aW9uIiwiZ25vbW9uaWNSYXciLCJ4IiwieSIsImN5IiwiayIsImludmVydCIsInNjYWxlIiwiY2xpcEFuZ2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/gnomonic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/identity.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/identity.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _clip_rectangle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../clip/rectangle.js */ \"(ssr)/./node_modules/d3-geo/src/clip/rectangle.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../identity.js */ \"(ssr)/./node_modules/d3-geo/src/identity.js\");\n/* harmony import */ var _transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../transform.js */ \"(ssr)/./node_modules/d3-geo/src/transform.js\");\n/* harmony import */ var _fit_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./fit.js */ \"(ssr)/./node_modules/d3-geo/src/projection/fit.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\n\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var k = 1, tx = 0, ty = 0, sx = 1, sy = 1, alpha = 0, ca, sa, x0 = null, y0, x1, y1, kx = 1, ky = 1, transform = (0,_transform_js__WEBPACK_IMPORTED_MODULE_0__.transformer)({\n        point: function(x, y) {\n            var p = projection([\n                x,\n                y\n            ]);\n            this.stream.point(p[0], p[1]);\n        }\n    }), postclip = _identity_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], cache, cacheStream;\n    function reset() {\n        kx = k * sx;\n        ky = k * sy;\n        cache = cacheStream = null;\n        return projection;\n    }\n    function projection(p) {\n        var x = p[0] * kx, y = p[1] * ky;\n        if (alpha) {\n            var t = y * ca - x * sa;\n            x = x * ca + y * sa;\n            y = t;\n        }\n        return [\n            x + tx,\n            y + ty\n        ];\n    }\n    projection.invert = function(p) {\n        var x = p[0] - tx, y = p[1] - ty;\n        if (alpha) {\n            var t = y * ca + x * sa;\n            x = x * ca - y * sa;\n            y = t;\n        }\n        return [\n            x / kx,\n            y / ky\n        ];\n    };\n    projection.stream = function(stream) {\n        return cache && cacheStream === stream ? cache : cache = transform(postclip(cacheStream = stream));\n    };\n    projection.postclip = function(_) {\n        return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n    };\n    projection.clipExtent = function(_) {\n        return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, _identity_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) : (0,_clip_rectangle_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [\n            [\n                x0,\n                y0\n            ],\n            [\n                x1,\n                y1\n            ]\n        ];\n    };\n    projection.scale = function(_) {\n        return arguments.length ? (k = +_, reset()) : k;\n    };\n    projection.translate = function(_) {\n        return arguments.length ? (tx = +_[0], ty = +_[1], reset()) : [\n            tx,\n            ty\n        ];\n    };\n    projection.angle = function(_) {\n        return arguments.length ? (alpha = _ % 360 * _math_js__WEBPACK_IMPORTED_MODULE_3__.radians, sa = (0,_math_js__WEBPACK_IMPORTED_MODULE_3__.sin)(alpha), ca = (0,_math_js__WEBPACK_IMPORTED_MODULE_3__.cos)(alpha), reset()) : alpha * _math_js__WEBPACK_IMPORTED_MODULE_3__.degrees;\n    };\n    projection.reflectX = function(_) {\n        return arguments.length ? (sx = _ ? -1 : 1, reset()) : sx < 0;\n    };\n    projection.reflectY = function(_) {\n        return arguments.length ? (sy = _ ? -1 : 1, reset()) : sy < 0;\n    };\n    projection.fitExtent = function(extent, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_4__.fitExtent)(projection, extent, object);\n    };\n    projection.fitSize = function(size, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_4__.fitSize)(projection, size, object);\n    };\n    projection.fitWidth = function(width, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_4__.fitWidth)(projection, width, object);\n    };\n    projection.fitHeight = function(height, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_4__.fitHeight)(projection, height, object);\n    };\n    return projection;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ projection),\n/* harmony export */   projectionMutator: () => (/* binding */ projectionMutator)\n/* harmony export */ });\n/* harmony import */ var _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../clip/antimeridian.js */ \"(ssr)/./node_modules/d3-geo/src/clip/antimeridian.js\");\n/* harmony import */ var _clip_circle_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../clip/circle.js */ \"(ssr)/./node_modules/d3-geo/src/clip/circle.js\");\n/* harmony import */ var _clip_rectangle_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../clip/rectangle.js */ \"(ssr)/./node_modules/d3-geo/src/clip/rectangle.js\");\n/* harmony import */ var _compose_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../compose.js */ \"(ssr)/./node_modules/d3-geo/src/compose.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../identity.js */ \"(ssr)/./node_modules/d3-geo/src/identity.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _rotation_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../rotation.js */ \"(ssr)/./node_modules/d3-geo/src/rotation.js\");\n/* harmony import */ var _transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../transform.js */ \"(ssr)/./node_modules/d3-geo/src/transform.js\");\n/* harmony import */ var _fit_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./fit.js */ \"(ssr)/./node_modules/d3-geo/src/projection/fit.js\");\n/* harmony import */ var _resample_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./resample.js */ \"(ssr)/./node_modules/d3-geo/src/projection/resample.js\");\n\n\n\n\n\n\n\n\n\n\nvar transformRadians = (0,_transform_js__WEBPACK_IMPORTED_MODULE_0__.transformer)({\n    point: function(x, y) {\n        this.stream.point(x * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, y * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians);\n    }\n});\nfunction transformRotate(rotate) {\n    return (0,_transform_js__WEBPACK_IMPORTED_MODULE_0__.transformer)({\n        point: function(x, y) {\n            var r = rotate(x, y);\n            return this.stream.point(r[0], r[1]);\n        }\n    });\n}\nfunction scaleTranslate(k, dx, dy, sx, sy) {\n    function transform(x, y) {\n        x *= sx;\n        y *= sy;\n        return [\n            dx + k * x,\n            dy - k * y\n        ];\n    }\n    transform.invert = function(x, y) {\n        return [\n            (x - dx) / k * sx,\n            (dy - y) / k * sy\n        ];\n    };\n    return transform;\n}\nfunction scaleTranslateRotate(k, dx, dy, sx, sy, alpha) {\n    if (!alpha) return scaleTranslate(k, dx, dy, sx, sy);\n    var cosAlpha = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.cos)(alpha), sinAlpha = (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sin)(alpha), a = cosAlpha * k, b = sinAlpha * k, ai = cosAlpha / k, bi = sinAlpha / k, ci = (sinAlpha * dy - cosAlpha * dx) / k, fi = (sinAlpha * dx + cosAlpha * dy) / k;\n    function transform(x, y) {\n        x *= sx;\n        y *= sy;\n        return [\n            a * x - b * y + dx,\n            dy - b * x - a * y\n        ];\n    }\n    transform.invert = function(x, y) {\n        return [\n            sx * (ai * x - bi * y + ci),\n            sy * (fi - bi * x - ai * y)\n        ];\n    };\n    return transform;\n}\nfunction projection(project) {\n    return projectionMutator(function() {\n        return project;\n    })();\n}\nfunction projectionMutator(projectAt) {\n    var project, k = 150, x = 480, y = 250, lambda = 0, phi = 0, deltaLambda = 0, deltaPhi = 0, deltaGamma = 0, rotate, alpha = 0, sx = 1, sy = 1, theta = null, preclip = _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"], x0 = null, y0, x1, y1, postclip = _identity_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], delta2 = 0.5, projectResample, projectTransform, projectRotateTransform, cache, cacheStream;\n    function projection(point) {\n        return projectRotateTransform(point[0] * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, point[1] * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians);\n    }\n    function invert(point) {\n        point = projectRotateTransform.invert(point[0], point[1]);\n        return point && [\n            point[0] * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees,\n            point[1] * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees\n        ];\n    }\n    projection.stream = function(stream) {\n        return cache && cacheStream === stream ? cache : cache = transformRadians(transformRotate(rotate)(preclip(projectResample(postclip(cacheStream = stream)))));\n    };\n    projection.preclip = function(_) {\n        return arguments.length ? (preclip = _, theta = undefined, reset()) : preclip;\n    };\n    projection.postclip = function(_) {\n        return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n    };\n    projection.clipAngle = function(_) {\n        return arguments.length ? (preclip = +_ ? (0,_clip_circle_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(theta = _ * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians) : (theta = null, _clip_antimeridian_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]), reset()) : theta * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees;\n    };\n    projection.clipExtent = function(_) {\n        return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, _identity_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]) : (0,_clip_rectangle_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [\n            [\n                x0,\n                y0\n            ],\n            [\n                x1,\n                y1\n            ]\n        ];\n    };\n    projection.scale = function(_) {\n        return arguments.length ? (k = +_, recenter()) : k;\n    };\n    projection.translate = function(_) {\n        return arguments.length ? (x = +_[0], y = +_[1], recenter()) : [\n            x,\n            y\n        ];\n    };\n    projection.center = function(_) {\n        return arguments.length ? (lambda = _[0] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, phi = _[1] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, recenter()) : [\n            lambda * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees,\n            phi * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees\n        ];\n    };\n    projection.rotate = function(_) {\n        return arguments.length ? (deltaLambda = _[0] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, deltaPhi = _[1] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, deltaGamma = _.length > 2 ? _[2] % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians : 0, recenter()) : [\n            deltaLambda * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees,\n            deltaPhi * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees,\n            deltaGamma * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees\n        ];\n    };\n    projection.angle = function(_) {\n        return arguments.length ? (alpha = _ % 360 * _math_js__WEBPACK_IMPORTED_MODULE_1__.radians, recenter()) : alpha * _math_js__WEBPACK_IMPORTED_MODULE_1__.degrees;\n    };\n    projection.reflectX = function(_) {\n        return arguments.length ? (sx = _ ? -1 : 1, recenter()) : sx < 0;\n    };\n    projection.reflectY = function(_) {\n        return arguments.length ? (sy = _ ? -1 : 1, recenter()) : sy < 0;\n    };\n    projection.precision = function(_) {\n        return arguments.length ? (projectResample = (0,_resample_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(projectTransform, delta2 = _ * _), reset()) : (0,_math_js__WEBPACK_IMPORTED_MODULE_1__.sqrt)(delta2);\n    };\n    projection.fitExtent = function(extent, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_7__.fitExtent)(projection, extent, object);\n    };\n    projection.fitSize = function(size, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_7__.fitSize)(projection, size, object);\n    };\n    projection.fitWidth = function(width, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_7__.fitWidth)(projection, width, object);\n    };\n    projection.fitHeight = function(height, object) {\n        return (0,_fit_js__WEBPACK_IMPORTED_MODULE_7__.fitHeight)(projection, height, object);\n    };\n    function recenter() {\n        var center = scaleTranslateRotate(k, 0, 0, sx, sy, alpha).apply(null, project(lambda, phi)), transform = scaleTranslateRotate(k, x - center[0], y - center[1], sx, sy, alpha);\n        rotate = (0,_rotation_js__WEBPACK_IMPORTED_MODULE_8__.rotateRadians)(deltaLambda, deltaPhi, deltaGamma);\n        projectTransform = (0,_compose_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(project, transform);\n        projectRotateTransform = (0,_compose_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(rotate, projectTransform);\n        projectResample = (0,_resample_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(projectTransform, delta2);\n        return reset();\n    }\n    function reset() {\n        cache = cacheStream = null;\n        return projection;\n    }\n    return function() {\n        project = projectAt.apply(this, arguments);\n        projection.invert = project.invert && invert;\n        return recenter();\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/mercator.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/mercator.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   mercatorProjection: () => (/* binding */ mercatorProjection),\n/* harmony export */   mercatorRaw: () => (/* binding */ mercatorRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _rotation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../rotation.js */ \"(ssr)/./node_modules/d3-geo/src/rotation.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\nfunction mercatorRaw(lambda, phi) {\n    return [\n        lambda,\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.log)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.tan)((_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + phi) / 2))\n    ];\n}\nmercatorRaw.invert = function(x, y) {\n    return [\n        x,\n        2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.exp)(y)) - _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi\n    ];\n};\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return mercatorProjection(mercatorRaw).scale(961 / _math_js__WEBPACK_IMPORTED_MODULE_0__.tau);\n}\nfunction mercatorProjection(project) {\n    var m = (0,_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(project), center = m.center, scale = m.scale, translate = m.translate, clipExtent = m.clipExtent, x0 = null, y0, x1, y1; // clip extent\n    m.scale = function(_) {\n        return arguments.length ? (scale(_), reclip()) : scale();\n    };\n    m.translate = function(_) {\n        return arguments.length ? (translate(_), reclip()) : translate();\n    };\n    m.center = function(_) {\n        return arguments.length ? (center(_), reclip()) : center();\n    };\n    m.clipExtent = function(_) {\n        return arguments.length ? (_ == null ? x0 = y0 = x1 = y1 = null : (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reclip()) : x0 == null ? null : [\n            [\n                x0,\n                y0\n            ],\n            [\n                x1,\n                y1\n            ]\n        ];\n    };\n    function reclip() {\n        var k = _math_js__WEBPACK_IMPORTED_MODULE_0__.pi * scale(), t = m((0,_rotation_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(m.rotate()).invert([\n            0,\n            0\n        ]));\n        return clipExtent(x0 == null ? [\n            [\n                t[0] - k,\n                t[1] - k\n            ],\n            [\n                t[0] + k,\n                t[1] + k\n            ]\n        ] : project === mercatorRaw ? [\n            [\n                Math.max(t[0] - k, x0),\n                y0\n            ],\n            [\n                Math.min(t[0] + k, x1),\n                y1\n            ]\n        ] : [\n            [\n                x0,\n                Math.max(t[1] - k, y0)\n            ],\n            [\n                x1,\n                Math.min(t[1] + k, y1)\n            ]\n        ]);\n    }\n    return reclip();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/mercator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/naturalEarth1.js":
/*!*************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/naturalEarth1.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   naturalEarth1Raw: () => (/* binding */ naturalEarth1Raw)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\nfunction naturalEarth1Raw(lambda, phi) {\n    var phi2 = phi * phi, phi4 = phi2 * phi2;\n    return [\n        lambda * (0.8707 - 0.131979 * phi2 + phi4 * (-0.013791 + phi4 * (0.003971 * phi2 - 0.001529 * phi4))),\n        phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4)))\n    ];\n}\nnaturalEarth1Raw.invert = function(x, y) {\n    var phi = y, i = 25, delta;\n    do {\n        var phi2 = phi * phi, phi4 = phi2 * phi2;\n        phi -= delta = (phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4))) - y) / (1.007226 + phi2 * (0.015085 * 3 + phi4 * (-0.044475 * 7 + 0.028874 * 9 * phi2 - 0.005916 * 11 * phi4)));\n    }while ((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(delta) > _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon && --i > 0);\n    return [\n        x / (0.8707 + (phi2 = phi * phi) * (-0.131979 + phi2 * (-0.013791 + phi2 * phi2 * phi2 * (0.003971 - 0.001529 * phi2)))),\n        phi\n    ];\n};\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(naturalEarth1Raw).scale(175.295);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/naturalEarth1.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/orthographic.js":
/*!************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/orthographic.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   orthographicRaw: () => (/* binding */ orthographicRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\nfunction orthographicRaw(x, y) {\n    return [\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y) * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x),\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y)\n    ];\n}\northographicRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_1__.azimuthalInvert)(_math_js__WEBPACK_IMPORTED_MODULE_0__.asin);\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(orthographicRaw).scale(249.5).clipAngle(90 + _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL29ydGhvZ3JhcGhpYy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFtRDtBQUNKO0FBQ1g7QUFFN0IsU0FBU00sZ0JBQWdCQyxDQUFDLEVBQUVDLENBQUM7SUFDbEMsT0FBTztRQUFDUCw2Q0FBR0EsQ0FBQ08sS0FBS0wsNkNBQUdBLENBQUNJO1FBQUlKLDZDQUFHQSxDQUFDSztLQUFHO0FBQ2xDO0FBRUFGLGdCQUFnQkcsTUFBTSxHQUFHTCw4REFBZUEsQ0FBQ0osMENBQUlBO0FBRTdDLDZCQUFlLHNDQUFXO0lBQ3hCLE9BQU9LLHFEQUFVQSxDQUFDQyxpQkFDYkksS0FBSyxDQUFDLE9BQ05DLFNBQVMsQ0FBQyxLQUFLVCw2Q0FBT0E7QUFDN0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdG9yZXNweS8uL25vZGVfbW9kdWxlcy9kMy1nZW8vc3JjL3Byb2plY3Rpb24vb3J0aG9ncmFwaGljLmpzP2E1OGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHthc2luLCBjb3MsIGVwc2lsb24sIHNpbn0gZnJvbSBcIi4uL21hdGguanNcIjtcbmltcG9ydCB7YXppbXV0aGFsSW52ZXJ0fSBmcm9tIFwiLi9hemltdXRoYWwuanNcIjtcbmltcG9ydCBwcm9qZWN0aW9uIGZyb20gXCIuL2luZGV4LmpzXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBvcnRob2dyYXBoaWNSYXcoeCwgeSkge1xuICByZXR1cm4gW2Nvcyh5KSAqIHNpbih4KSwgc2luKHkpXTtcbn1cblxub3J0aG9ncmFwaGljUmF3LmludmVydCA9IGF6aW11dGhhbEludmVydChhc2luKTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oKSB7XG4gIHJldHVybiBwcm9qZWN0aW9uKG9ydGhvZ3JhcGhpY1JhdylcbiAgICAgIC5zY2FsZSgyNDkuNSlcbiAgICAgIC5jbGlwQW5nbGUoOTAgKyBlcHNpbG9uKTtcbn1cbiJdLCJuYW1lcyI6WyJhc2luIiwiY29zIiwiZXBzaWxvbiIsInNpbiIsImF6aW11dGhhbEludmVydCIsInByb2plY3Rpb24iLCJvcnRob2dyYXBoaWNSYXciLCJ4IiwieSIsImludmVydCIsInNjYWxlIiwiY2xpcEFuZ2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/orthographic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/resample.js":
/*!********************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/resample.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cartesian_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../cartesian.js */ \"(ssr)/./node_modules/d3-geo/src/cartesian.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../transform.js */ \"(ssr)/./node_modules/d3-geo/src/transform.js\");\n\n\n\nvar maxDepth = 16, cosMinDistance = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(30 * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians); // cos(minimum angular distance)\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(project, delta2) {\n    return +delta2 ? resample(project, delta2) : resampleNone(project);\n}\nfunction resampleNone(project) {\n    return (0,_transform_js__WEBPACK_IMPORTED_MODULE_1__.transformer)({\n        point: function(x, y) {\n            x = project(x, y);\n            this.stream.point(x[0], x[1]);\n        }\n    });\n}\nfunction resample(project, delta2) {\n    function resampleLineTo(x0, y0, lambda0, a0, b0, c0, x1, y1, lambda1, a1, b1, c1, depth, stream) {\n        var dx = x1 - x0, dy = y1 - y0, d2 = dx * dx + dy * dy;\n        if (d2 > 4 * delta2 && depth--) {\n            var a = a0 + a1, b = b0 + b1, c = c0 + c1, m = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sqrt)(a * a + b * b + c * c), phi2 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(c /= m), lambda2 = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(c) - 1) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon || (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(lambda0 - lambda1) < _math_js__WEBPACK_IMPORTED_MODULE_0__.epsilon ? (lambda0 + lambda1) / 2 : (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(b, a), p = project(lambda2, phi2), x2 = p[0], y2 = p[1], dx2 = x2 - x0, dy2 = y2 - y0, dz = dy * dx2 - dx * dy2;\n            if (dz * dz / d2 > delta2 // perpendicular projected distance\n             || (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)((dx * dx2 + dy * dy2) / d2 - 0.5) > 0.3 // midpoint close to an end\n             || a0 * a1 + b0 * b1 + c0 * c1 < cosMinDistance) {\n                resampleLineTo(x0, y0, lambda0, a0, b0, c0, x2, y2, lambda2, a /= m, b /= m, c, depth, stream);\n                stream.point(x2, y2);\n                resampleLineTo(x2, y2, lambda2, a, b, c, x1, y1, lambda1, a1, b1, c1, depth, stream);\n            }\n        }\n    }\n    return function(stream) {\n        var lambda00, x00, y00, a00, b00, c00, lambda0, x0, y0, a0, b0, c0; // previous point\n        var resampleStream = {\n            point: point,\n            lineStart: lineStart,\n            lineEnd: lineEnd,\n            polygonStart: function() {\n                stream.polygonStart();\n                resampleStream.lineStart = ringStart;\n            },\n            polygonEnd: function() {\n                stream.polygonEnd();\n                resampleStream.lineStart = lineStart;\n            }\n        };\n        function point(x, y) {\n            x = project(x, y);\n            stream.point(x[0], x[1]);\n        }\n        function lineStart() {\n            x0 = NaN;\n            resampleStream.point = linePoint;\n            stream.lineStart();\n        }\n        function linePoint(lambda, phi) {\n            var c = (0,_cartesian_js__WEBPACK_IMPORTED_MODULE_2__.cartesian)([\n                lambda,\n                phi\n            ]), p = project(lambda, phi);\n            resampleLineTo(x0, y0, lambda0, a0, b0, c0, x0 = p[0], y0 = p[1], lambda0 = lambda, a0 = c[0], b0 = c[1], c0 = c[2], maxDepth, stream);\n            stream.point(x0, y0);\n        }\n        function lineEnd() {\n            resampleStream.point = point;\n            stream.lineEnd();\n        }\n        function ringStart() {\n            lineStart();\n            resampleStream.point = ringPoint;\n            resampleStream.lineEnd = ringEnd;\n        }\n        function ringPoint(lambda, phi) {\n            linePoint(lambda00 = lambda, phi), x00 = x0, y00 = y0, a00 = a0, b00 = b0, c00 = c0;\n            resampleStream.point = linePoint;\n        }\n        function ringEnd() {\n            resampleLineTo(x0, y0, lambda0, a0, b0, c0, x00, y00, lambda00, a00, b00, c00, maxDepth, stream);\n            resampleStream.lineEnd = lineEnd;\n            lineEnd();\n        }\n        return resampleStream;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/resample.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/stereographic.js":
/*!*************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/stereographic.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   stereographicRaw: () => (/* binding */ stereographicRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _azimuthal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./azimuthal.js */ \"(ssr)/./node_modules/d3-geo/src/projection/azimuthal.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/d3-geo/src/projection/index.js\");\n\n\n\nfunction stereographicRaw(x, y) {\n    var cy = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(y), k = 1 + (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(x) * cy;\n    return [\n        cy * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(x) / k,\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(y) / k\n    ];\n}\nstereographicRaw.invert = (0,_azimuthal_js__WEBPACK_IMPORTED_MODULE_1__.azimuthalInvert)(function(z) {\n    return 2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan)(z);\n});\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    return (0,_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(stereographicRaw).scale(250).clipAngle(142);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtZ2VvL3NyYy9wcm9qZWN0aW9uL3N0ZXJlb2dyYXBoaWMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEM7QUFDSztBQUNYO0FBRTdCLFNBQVNLLGlCQUFpQkMsQ0FBQyxFQUFFQyxDQUFDO0lBQ25DLElBQUlDLEtBQUtQLDZDQUFHQSxDQUFDTSxJQUFJRSxJQUFJLElBQUlSLDZDQUFHQSxDQUFDSyxLQUFLRTtJQUNsQyxPQUFPO1FBQUNBLEtBQUtOLDZDQUFHQSxDQUFDSSxLQUFLRztRQUFHUCw2Q0FBR0EsQ0FBQ0ssS0FBS0U7S0FBRTtBQUN0QztBQUVBSixpQkFBaUJLLE1BQU0sR0FBR1AsOERBQWVBLENBQUMsU0FBU1EsQ0FBQztJQUNsRCxPQUFPLElBQUlYLDhDQUFJQSxDQUFDVztBQUNsQjtBQUVBLDZCQUFlLHNDQUFXO0lBQ3hCLE9BQU9QLHFEQUFVQSxDQUFDQyxrQkFDYk8sS0FBSyxDQUFDLEtBQ05DLFNBQVMsQ0FBQztBQUNqQiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0b3Jlc3B5Ly4vbm9kZV9tb2R1bGVzL2QzLWdlby9zcmMvcHJvamVjdGlvbi9zdGVyZW9ncmFwaGljLmpzPzAzN2UiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHthdGFuLCBjb3MsIHNpbn0gZnJvbSBcIi4uL21hdGguanNcIjtcbmltcG9ydCB7YXppbXV0aGFsSW52ZXJ0fSBmcm9tIFwiLi9hemltdXRoYWwuanNcIjtcbmltcG9ydCBwcm9qZWN0aW9uIGZyb20gXCIuL2luZGV4LmpzXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBzdGVyZW9ncmFwaGljUmF3KHgsIHkpIHtcbiAgdmFyIGN5ID0gY29zKHkpLCBrID0gMSArIGNvcyh4KSAqIGN5O1xuICByZXR1cm4gW2N5ICogc2luKHgpIC8gaywgc2luKHkpIC8ga107XG59XG5cbnN0ZXJlb2dyYXBoaWNSYXcuaW52ZXJ0ID0gYXppbXV0aGFsSW52ZXJ0KGZ1bmN0aW9uKHopIHtcbiAgcmV0dXJuIDIgKiBhdGFuKHopO1xufSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gcHJvamVjdGlvbihzdGVyZW9ncmFwaGljUmF3KVxuICAgICAgLnNjYWxlKDI1MClcbiAgICAgIC5jbGlwQW5nbGUoMTQyKTtcbn1cbiJdLCJuYW1lcyI6WyJhdGFuIiwiY29zIiwic2luIiwiYXppbXV0aGFsSW52ZXJ0IiwicHJvamVjdGlvbiIsInN0ZXJlb2dyYXBoaWNSYXciLCJ4IiwieSIsImN5IiwiayIsImludmVydCIsInoiLCJzY2FsZSIsImNsaXBBbmdsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/stereographic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/projection/transverseMercator.js":
/*!******************************************************************!*\
  !*** ./node_modules/d3-geo/src/projection/transverseMercator.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   transverseMercatorRaw: () => (/* binding */ transverseMercatorRaw)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n/* harmony import */ var _mercator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mercator.js */ \"(ssr)/./node_modules/d3-geo/src/projection/mercator.js\");\n\n\nfunction transverseMercatorRaw(lambda, phi) {\n    return [\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.log)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.tan)((_math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi + phi) / 2)),\n        -lambda\n    ];\n}\ntransverseMercatorRaw.invert = function(x, y) {\n    return [\n        -y,\n        2 * (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan)((0,_math_js__WEBPACK_IMPORTED_MODULE_0__.exp)(x)) - _math_js__WEBPACK_IMPORTED_MODULE_0__.halfPi\n    ];\n};\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__() {\n    var m = (0,_mercator_js__WEBPACK_IMPORTED_MODULE_1__.mercatorProjection)(transverseMercatorRaw), center = m.center, rotate = m.rotate;\n    m.center = function(_) {\n        return arguments.length ? center([\n            -_[1],\n            _[0]\n        ]) : (_ = center(), [\n            _[1],\n            -_[0]\n        ]);\n    };\n    m.rotate = function(_) {\n        return arguments.length ? rotate([\n            _[0],\n            _[1],\n            _.length > 2 ? _[2] + 90 : 90\n        ]) : (_ = rotate(), [\n            _[0],\n            _[1],\n            _[2] - 90\n        ]);\n    };\n    return rotate([\n        0,\n        0,\n        90\n    ]).scale(159.155);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/projection/transverseMercator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/rotation.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-geo/src/rotation.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   rotateRadians: () => (/* binding */ rotateRadians)\n/* harmony export */ });\n/* harmony import */ var _compose_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./compose.js */ \"(ssr)/./node_modules/d3-geo/src/compose.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/d3-geo/src/math.js\");\n\n\nfunction rotationIdentity(lambda, phi) {\n    return [\n        (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.abs)(lambda) > _math_js__WEBPACK_IMPORTED_MODULE_0__.pi ? lambda + Math.round(-lambda / _math_js__WEBPACK_IMPORTED_MODULE_0__.tau) * _math_js__WEBPACK_IMPORTED_MODULE_0__.tau : lambda,\n        phi\n    ];\n}\nrotationIdentity.invert = rotationIdentity;\nfunction rotateRadians(deltaLambda, deltaPhi, deltaGamma) {\n    return (deltaLambda %= _math_js__WEBPACK_IMPORTED_MODULE_0__.tau) ? deltaPhi || deltaGamma ? (0,_compose_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(rotationLambda(deltaLambda), rotationPhiGamma(deltaPhi, deltaGamma)) : rotationLambda(deltaLambda) : deltaPhi || deltaGamma ? rotationPhiGamma(deltaPhi, deltaGamma) : rotationIdentity;\n}\nfunction forwardRotationLambda(deltaLambda) {\n    return function(lambda, phi) {\n        return lambda += deltaLambda, [\n            lambda > _math_js__WEBPACK_IMPORTED_MODULE_0__.pi ? lambda - _math_js__WEBPACK_IMPORTED_MODULE_0__.tau : lambda < -_math_js__WEBPACK_IMPORTED_MODULE_0__.pi ? lambda + _math_js__WEBPACK_IMPORTED_MODULE_0__.tau : lambda,\n            phi\n        ];\n    };\n}\nfunction rotationLambda(deltaLambda) {\n    var rotation = forwardRotationLambda(deltaLambda);\n    rotation.invert = forwardRotationLambda(-deltaLambda);\n    return rotation;\n}\nfunction rotationPhiGamma(deltaPhi, deltaGamma) {\n    var cosDeltaPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(deltaPhi), sinDeltaPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(deltaPhi), cosDeltaGamma = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(deltaGamma), sinDeltaGamma = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(deltaGamma);\n    function rotation(lambda, phi) {\n        var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi), x = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda) * cosPhi, y = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(lambda) * cosPhi, z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi), k = z * cosDeltaPhi + x * sinDeltaPhi;\n        return [\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(y * cosDeltaGamma - k * sinDeltaGamma, x * cosDeltaPhi - z * sinDeltaPhi),\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(k * cosDeltaGamma + y * sinDeltaGamma)\n        ];\n    }\n    rotation.invert = function(lambda, phi) {\n        var cosPhi = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(phi), x = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.cos)(lambda) * cosPhi, y = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(lambda) * cosPhi, z = (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.sin)(phi), k = z * cosDeltaGamma - y * sinDeltaGamma;\n        return [\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.atan2)(y * cosDeltaGamma + z * sinDeltaGamma, x * cosDeltaPhi + k * sinDeltaPhi),\n            (0,_math_js__WEBPACK_IMPORTED_MODULE_0__.asin)(k * cosDeltaPhi - x * sinDeltaPhi)\n        ];\n    };\n    return rotation;\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(rotate) {\n    rotate = rotateRadians(rotate[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, rotate[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, rotate.length > 2 ? rotate[2] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians : 0);\n    function forward(coordinates) {\n        coordinates = rotate(coordinates[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, coordinates[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians);\n        return coordinates[0] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, coordinates[1] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, coordinates;\n    }\n    forward.invert = function(coordinates) {\n        coordinates = rotate.invert(coordinates[0] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians, coordinates[1] * _math_js__WEBPACK_IMPORTED_MODULE_0__.radians);\n        return coordinates[0] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, coordinates[1] *= _math_js__WEBPACK_IMPORTED_MODULE_0__.degrees, coordinates;\n    };\n    return forward;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/rotation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/stream.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-geo/src/stream.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction streamGeometry(geometry, stream) {\n    if (geometry && streamGeometryType.hasOwnProperty(geometry.type)) {\n        streamGeometryType[geometry.type](geometry, stream);\n    }\n}\nvar streamObjectType = {\n    Feature: function(object, stream) {\n        streamGeometry(object.geometry, stream);\n    },\n    FeatureCollection: function(object, stream) {\n        var features = object.features, i = -1, n = features.length;\n        while(++i < n)streamGeometry(features[i].geometry, stream);\n    }\n};\nvar streamGeometryType = {\n    Sphere: function(object, stream) {\n        stream.sphere();\n    },\n    Point: function(object, stream) {\n        object = object.coordinates;\n        stream.point(object[0], object[1], object[2]);\n    },\n    MultiPoint: function(object, stream) {\n        var coordinates = object.coordinates, i = -1, n = coordinates.length;\n        while(++i < n)object = coordinates[i], stream.point(object[0], object[1], object[2]);\n    },\n    LineString: function(object, stream) {\n        streamLine(object.coordinates, stream, 0);\n    },\n    MultiLineString: function(object, stream) {\n        var coordinates = object.coordinates, i = -1, n = coordinates.length;\n        while(++i < n)streamLine(coordinates[i], stream, 0);\n    },\n    Polygon: function(object, stream) {\n        streamPolygon(object.coordinates, stream);\n    },\n    MultiPolygon: function(object, stream) {\n        var coordinates = object.coordinates, i = -1, n = coordinates.length;\n        while(++i < n)streamPolygon(coordinates[i], stream);\n    },\n    GeometryCollection: function(object, stream) {\n        var geometries = object.geometries, i = -1, n = geometries.length;\n        while(++i < n)streamGeometry(geometries[i], stream);\n    }\n};\nfunction streamLine(coordinates, stream, closed) {\n    var i = -1, n = coordinates.length - closed, coordinate;\n    stream.lineStart();\n    while(++i < n)coordinate = coordinates[i], stream.point(coordinate[0], coordinate[1], coordinate[2]);\n    stream.lineEnd();\n}\nfunction streamPolygon(coordinates, stream) {\n    var i = -1, n = coordinates.length;\n    stream.polygonStart();\n    while(++i < n)streamLine(coordinates[i], stream, 1);\n    stream.polygonEnd();\n}\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(object, stream) {\n    if (object && streamObjectType.hasOwnProperty(object.type)) {\n        streamObjectType[object.type](object, stream);\n    } else {\n        streamGeometry(object, stream);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-geo/src/transform.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-geo/src/transform.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   transformer: () => (/* binding */ transformer)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(methods) {\n    return {\n        stream: transformer(methods)\n    };\n}\nfunction transformer(methods) {\n    return function(stream) {\n        var s = new TransformStream;\n        for(var key in methods)s[key] = methods[key];\n        s.stream = stream;\n        return s;\n    };\n}\nfunction TransformStream() {}\nTransformStream.prototype = {\n    constructor: TransformStream,\n    point: function(x, y) {\n        this.stream.point(x, y);\n    },\n    sphere: function() {\n        this.stream.sphere();\n    },\n    lineStart: function() {\n        this.stream.lineStart();\n    },\n    lineEnd: function() {\n        this.stream.lineEnd();\n    },\n    polygonStart: function() {\n        this.stream.polygonStart();\n    },\n    polygonEnd: function() {\n        this.stream.polygonEnd();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-geo/src/transform.js\n");

/***/ })

};
;