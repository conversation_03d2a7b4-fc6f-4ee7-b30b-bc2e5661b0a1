import React from 'react';

const LoadingComponent = ({ length, md = 2, lg }) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-${md} lg:grid-cols-${lg} gap-4 p-4`}>
      {Array.from({ length: length }).map((_, index) => (
        <div
          key={index}
          className="animate-pulse hover:bg-gray-100 p-4 shadow-sm border rounded-md border-gray-200"
        >
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
            <div className="rounded-md bg-slate-200 h-20 w-20 sm:w-20 sm:h-20"></div>

            <div className="mt-4 sm:mt-0 sm:ml-4 flex-1">
              <div className="h-2.5 bg-gray-200 rounded-full w-48 my-2"></div>
              <div className="h-2 bg-gray-200 rounded-full w-32 mb-2"></div>
              <div className="h-2 bg-gray-200 rounded-full w-48 mb-2"></div>
              <div className="h-2.5 bg-gray-200 rounded-full w-20 mb-4"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default LoadingComponent;
